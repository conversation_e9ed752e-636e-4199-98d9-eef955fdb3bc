import { Test, TestingModule } from '@nestjs/testing';
import { AuthenticationController } from './authentication.controller';
import { AuthenticationService } from '../services/authentication.service';
import { CreateUserDto } from '../dto/create-user.dto';
import { User } from '../entities/user.entity';

describe('AuthenticationController', () => {
  let controller: AuthenticationController;
  let authService: AuthenticationService;

  beforeEach(async () => {
    // Create mock implementation
    const mockAuthService = {
      register: jest.fn(),
      login: jest.fn(),
      getProfile: jest.fn(),
      findByEmail: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthenticationController],
      providers: [
        {
          provide: AuthenticationService,
          useValue: mockAuthService,
        },
      ],
    }).compile();

    controller = module.get<AuthenticationController>(AuthenticationController);
    authService = module.get<AuthenticationService>(AuthenticationService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('register', () => {
    it('should register a new user', async () => {
      // Arrange
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        password: 'password123',
        profile: {
          firstName: 'John',
          lastName: 'Doe',
        },
      };

      const createdUser = {
        id: '123',
        email: createUserDto.email,
      } as User;

      jest.spyOn(authService, 'register').mockResolvedValue(createdUser);

      // Act
      const result = await controller.register(createUserDto);

      // Assert
      expect(result).toEqual(createdUser);
      expect(authService.register).toHaveBeenCalledWith(createUserDto);
    });
  });

  describe('login', () => {
    it('should login a user and return access token', async () => {
      // Arrange
      const user = {
        id: '123',
        email: '<EMAIL>',
      } as User;

      const req = { user };
      const loginResult = {
        accessToken: 'jwt-token',
        user,
      };

      jest.spyOn(authService, 'login').mockResolvedValue(loginResult);

      // Act
      const result = await controller.login(req);

      // Assert
      expect(result).toEqual(loginResult);
      expect(authService.login).toHaveBeenCalledWith(user);
    });
  });

  describe('getProfile', () => {
    it('should return user profile', async () => {
      // Arrange
      const userId = '123';
      const req = { user: { sub: userId } };
      const user = {
        id: userId,
        email: '<EMAIL>',
      } as User;

      jest.spyOn(authService, 'getProfile').mockResolvedValue(user);

      // Act
      const result = await controller.getProfile(req);

      // Assert
      expect(result).toEqual(user);
      expect(authService.getProfile).toHaveBeenCalledWith(userId);
    });
  });

  describe('microservice endpoints', () => {
    it('should register a user via microservice', async () => {
      // Arrange
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const createdUser = {
        id: '123',
        email: createUserDto.email,
      } as User;

      jest.spyOn(authService, 'register').mockResolvedValue(createdUser);

      // Act
      const result = await controller.registerUser(createUserDto);

      // Assert
      expect(result).toEqual(createdUser);
      expect(authService.register).toHaveBeenCalledWith(createUserDto);
    });

    it('should validate a user via microservice', async () => {
      // Arrange
      const data = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const user = {
        id: '123',
        email: data.email,
      } as User;

      jest.spyOn(authService, 'validateUser').mockResolvedValue(user);

      // Act
      const result = await controller.validateUser(data);

      // Assert
      expect(result).toEqual(user);
      expect(authService.validateUser).toHaveBeenCalledWith(data.email, data.password);
    });

    it('should find a user by email via microservice', async () => {
      // Arrange
      const email = '<EMAIL>';
      const user = {
        id: '123',
        email,
      } as User;

      jest.spyOn(authService, 'findByEmail').mockResolvedValue(user);

      // Act
      const result = await controller.findUserByEmail(email);

      // Assert
      expect(result).toEqual(user);
      expect(authService.findByEmail).toHaveBeenCalledWith(email);
    });

    it('should find a user by ID via microservice', async () => {
      // Arrange
      const id = '123';
      const user = {
        id,
        email: '<EMAIL>',
      } as User;

      jest.spyOn(authService, 'getProfile').mockResolvedValue(user);

      // Act
      const result = await controller.findUserById(id);

      // Assert
      expect(result).toEqual(user);
      expect(authService.getProfile).toHaveBeenCalledWith(id);
    });
  });
});
