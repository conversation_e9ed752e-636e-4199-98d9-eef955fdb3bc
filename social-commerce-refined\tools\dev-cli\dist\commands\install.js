"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.installCommand = installCommand;
const chalk = require("chalk");
const inquirer = require("inquirer");
const paths_1 = require("../utils/paths");
const npm_1 = require("../utils/npm");
function installCommand(program) {
    program
        .command('install [service]')
        .description('Install dependencies')
        .option('-a, --all', 'Install dependencies for all services')
        .action(async (service, options) => {
        try {
            if (!service && !options.all) {
                const services = (0, paths_1.getAllServices)();
                if (services.length === 0) {
                    console.log(chalk.yellow('No services found'));
                    return;
                }
                const answers = await inquirer.prompt([
                    {
                        type: 'list',
                        name: 'service',
                        message: 'Which service do you want to install dependencies for?',
                        choices: [...services, 'all'],
                    },
                ]);
                if (answers.service === 'all') {
                    options.all = true;
                }
                else {
                    service = answers.service;
                }
            }
            if (options.all) {
                const services = (0, paths_1.getAllServices)();
                if (services.length === 0) {
                    console.log(chalk.yellow('No services found'));
                    return;
                }
                console.log(chalk.blue(`Installing dependencies for all services: ${services.join(', ')}...`));
                for (const svc of services) {
                    try {
                        await (0, npm_1.installDependencies)(svc);
                    }
                    catch (error) {
                        console.error(chalk.red(`Error installing dependencies for ${svc}-service: ${error.message}`));
                    }
                }
                console.log(chalk.green('Dependencies installed successfully for all services'));
                return;
            }
            if (service) {
                if (!(0, paths_1.serviceExists)(service)) {
                    console.error(chalk.red(`Service ${service}-service does not exist`));
                    return;
                }
                console.log(chalk.blue(`Installing dependencies for ${service}-service...`));
                await (0, npm_1.installDependencies)(service);
                console.log(chalk.green(`Dependencies installed successfully for ${service}-service`));
                return;
            }
        }
        catch (error) {
            console.error(chalk.red(`Error: ${error.message}`));
            process.exit(1);
        }
    });
}
//# sourceMappingURL=install.js.map