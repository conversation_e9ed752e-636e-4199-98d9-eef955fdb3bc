import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Profile } from '../entities/profile.entity';
import { CreateProfileDto } from '../dto/create-profile.dto';
import { UpdateProfileDto } from '../dto/update-profile.dto';

@Injectable()
export class ProfileRepository {
  private readonly logger = new Logger(ProfileRepository.name);

  constructor(
    @InjectRepository(Profile)
    private readonly repository: Repository<Profile>,
  ) {}

  async findAll(): Promise<Profile[]> {
    this.logger.log('Finding all profiles');
    return this.repository.find({ relations: ['user'] });
  }

  async findOne(id: string): Promise<Profile> {
    this.logger.log(`Finding profile with ID: ${id}`);
    const profile = await this.repository.findOne({ 
      where: { id },
      relations: ['user'],
    });

    if (!profile) {
      throw new NotFoundException(`Profile with ID ${id} not found`);
    }

    return profile;
  }

  async findByUserId(userId: string): Promise<Profile> {
    this.logger.log(`Finding profile for user with ID: ${userId}`);
    const profile = await this.repository.findOne({ 
      where: { user: { id: userId } },
      relations: ['user'],
    });

    if (!profile) {
      throw new NotFoundException(`Profile for user with ID ${userId} not found`);
    }

    return profile;
  }

  async create(userId: string, createProfileDto: CreateProfileDto): Promise<Profile> {
    this.logger.log(`Creating profile for user with ID: ${userId}`);
    
    const profile = this.repository.create({
      ...createProfileDto,
      user: { id: userId },
    });
    
    return this.repository.save(profile);
  }

  async update(id: string, updateProfileDto: UpdateProfileDto): Promise<Profile> {
    this.logger.log(`Updating profile with ID: ${id}`);
    
    const profile = await this.findOne(id);
    
    // Update profile properties
    Object.assign(profile, updateProfileDto);
    
    return this.repository.save(profile);
  }

  async updateByUserId(userId: string, updateProfileDto: UpdateProfileDto): Promise<Profile> {
    this.logger.log(`Updating profile for user with ID: ${userId}`);
    
    const profile = await this.findByUserId(userId);
    
    // Update profile properties
    Object.assign(profile, updateProfileDto);
    
    return this.repository.save(profile);
  }

  async remove(id: string): Promise<void> {
    this.logger.log(`Removing profile with ID: ${id}`);
    
    const profile = await this.findOne(id);
    
    await this.repository.remove(profile);
  }
}
