import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { ProfileRepository } from '../repositories/profile.repository';
import { UserRepository } from '../../authentication/repositories/user.repository';
import { CreateProfileDto } from '../dto/create-profile.dto';
import { UpdateProfileDto } from '../dto/update-profile.dto';
import { Profile } from '../entities/profile.entity';

@Injectable()
export class ProfileService {
  private readonly logger = new Logger(ProfileService.name);

  constructor(
    private readonly profileRepository: ProfileRepository,
    private readonly userRepository: UserRepository,
  ) {}

  async findAll(): Promise<Profile[]> {
    this.logger.log('Finding all profiles');
    return this.profileRepository.findAll();
  }

  async findOne(id: string): Promise<Profile> {
    this.logger.log(`Finding profile with ID: ${id}`);
    return this.profileRepository.findOne(id);
  }

  async findByUserId(userId: string): Promise<Profile> {
    this.logger.log(`Finding profile for user with ID: ${userId}`);
    
    // Check if user exists
    await this.userRepository.findOne(userId);
    
    return this.profileRepository.findByUserId(userId);
  }

  async create(userId: string, createProfileDto: CreateProfileDto): Promise<Profile> {
    this.logger.log(`Creating profile for user with ID: ${userId}`);
    
    // Check if user exists
    await this.userRepository.findOne(userId);
    
    // Check if profile already exists
    try {
      await this.profileRepository.findByUserId(userId);
      throw new Error(`Profile for user with ID ${userId} already exists`);
    } catch (error) {
      if (error instanceof NotFoundException) {
        // Profile doesn't exist, so we can create it
        return this.profileRepository.create(userId, createProfileDto);
      }
      throw error;
    }
  }

  async update(id: string, updateProfileDto: UpdateProfileDto): Promise<Profile> {
    this.logger.log(`Updating profile with ID: ${id}`);
    return this.profileRepository.update(id, updateProfileDto);
  }

  async updateByUserId(userId: string, updateProfileDto: UpdateProfileDto): Promise<Profile> {
    this.logger.log(`Updating profile for user with ID: ${userId}`);
    
    // Check if user exists
    await this.userRepository.findOne(userId);
    
    return this.profileRepository.updateByUserId(userId, updateProfileDto);
  }

  async remove(id: string): Promise<void> {
    this.logger.log(`Removing profile with ID: ${id}`);
    return this.profileRepository.remove(id);
  }
}
