# Service Naming Conventions - Compliance Status

## Overview
This document tracks the compliance status of all existing services with the established naming conventions.

## Current Service Inventory

### ✅ Fully Compliant Services

#### **User Service**
- **Directory:** `services/user-service` ✅
- **Database:** `user_service` ✅
- **Docker Image:** `user-service` ✅
- **Container:** `social-commerce-user-service` ✅
- **Queue:** `user_queue` ✅
- **Environment Variables:** `USER_SERVICE_URL`, `DB_DATABASE_USER` ✅
- **Status:** ✅ **FULLY COMPLIANT**

#### **Store Service**
- **Directory:** `services/store-service` ✅
- **Database:** `store_service` ✅
- **Docker Image:** `store-service` ✅
- **Container:** `social-commerce-store-service` ✅
- **Queue:** `store_queue` ✅
- **Environment Variables:** `STORE_SERVICE_URL`, `DB_DATABASE_STORE` ✅
- **Status:** ✅ **FULLY COMPLIANT**

### ✅ Infrastructure Components (Compliant)

#### **API Gateway**
- **Directory:** `services/api-gateway` ✅
- **Docker Image:** `social-commerce-api-gateway` ✅ (Infrastructure pattern)
- **Container:** `social-commerce-api-gateway` ✅
- **Environment Variables:** `API_GATEWAY_PORT` ✅
- **Status:** ✅ **COMPLIANT** (Infrastructure component)

#### **PostgreSQL**
- **Docker Image:** `postgres:latest` ✅ (External image)
- **Container:** `social-commerce-postgres` ✅
- **Database:** Multiple service databases ✅
- **Status:** ✅ **COMPLIANT**

#### **RabbitMQ**
- **Docker Image:** `rabbitmq:3-management` ✅ (External image)
- **Container:** `social-commerce-rabbitmq` ✅
- **Environment Variables:** `RABBITMQ_*` ✅
- **Status:** ✅ **COMPLIANT**

### 📁 Placeholder Services (Not Yet Implemented)

#### **Affiliate Service**
- **Directory:** `services/affiliate-service` ⚠️ (Directory exists but empty)
- **Expected Database:** `affiliate_service`
- **Expected Docker Image:** `affiliate-service`
- **Expected Container:** `social-commerce-affiliate-service`
- **Expected Queue:** `affiliate_queue`
- **Status:** 📋 **PLACEHOLDER** - Needs implementation

#### **Analytics Service**
- **Directory:** `services/analytics-service` ⚠️ (Directory exists but empty)
- **Expected Database:** `analytics_service`
- **Expected Docker Image:** `analytics-service`
- **Expected Container:** `social-commerce-analytics-service`
- **Expected Queue:** `analytics_queue`
- **Status:** 📋 **PLACEHOLDER** - Needs implementation

### 🎨 Frontend Components

#### **Frontend Application**
- **Directory:** `frontend/` ✅
- **Docker Image:** `social-commerce-frontend` ✅ (Infrastructure pattern)
- **Container:** `social-commerce-frontend` ✅
- **Port:** `3003` ✅ (Updated to avoid conflicts)
- **Status:** ✅ **COMPLIANT**

## Compliance Summary

### ✅ **Compliant Services: 6/6 (100%)**
- User Service ✅
- Store Service ✅
- API Gateway ✅
- PostgreSQL ✅
- RabbitMQ ✅
- Frontend ✅

### 📋 **Placeholder Services: 2**
- Affiliate Service (directory exists, not implemented)
- Analytics Service (directory exists, not implemented)

### 🔧 **Recent Fixes Applied**
1. **Frontend Port Change** - Changed from 3000 to 3003 to avoid API Gateway conflict
2. **Environment Variables** - Updated to use naming convention patterns
3. **Docker Compose** - All services follow container naming conventions
4. **Database Names** - All follow `{service_name}_service` pattern

## Future Service Requirements

### **When Creating New Services:**
All new services MUST follow these patterns:

```bash
# Example for "Product Service"
Directory: services/product-service
Database: product_service
Docker Image: product-service
Container: social-commerce-product-service
Queue: product_queue
Environment Variables: PRODUCT_SERVICE_URL, DB_DATABASE_PRODUCT
```

### **Validation Checklist for New Services:**
- [ ] Directory follows `services/{service-name}-service` pattern
- [ ] Database follows `{service_name}_service` pattern
- [ ] Docker image follows `{service-name}-service` pattern
- [ ] Container follows `social-commerce-{service-name}-service` pattern
- [ ] Queue follows `{service_name}_queue` pattern
- [ ] Environment variables follow `{SERVICE_NAME}_*` pattern
- [ ] Added to docker-compose.yml with proper naming
- [ ] Added to POSTGRES_MULTIPLE_DATABASES
- [ ] Environment variables added to .env files

## Grandfathered Exceptions

### **None Currently**
All existing services comply with the naming conventions. No exceptions needed.

## Enforcement

### **Mandatory Compliance**
- ✅ All AI agents must follow naming conventions
- ✅ All new services must use service creation template
- ✅ All new services must pass naming validation checklist
- ✅ Guidelines are referenced in main project documentation

### **Validation Tools**
- **Service Creation Template:** `docs/templates/service-template/`
- **Creation Checklist:** `docs/templates/service-template/SERVICE-CREATION-CHECKLIST.md`
- **Quick Reference:** `NAMING-CONVENTIONS-QUICK-REF.md`
- **Full Guidelines:** `docs/guidelines/SERVICE-NAMING-CONVENTIONS.md`

---

**Status:** ✅ **100% COMPLIANT** - All existing services follow naming conventions
**Last Updated:** 2025-05-26
**Next Review:** When new services are added
