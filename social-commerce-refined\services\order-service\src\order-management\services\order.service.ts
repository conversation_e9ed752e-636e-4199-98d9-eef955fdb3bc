import { Injectable, Logger, NotFoundException, BadRequestException, Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';
import { OrderRepository } from '../repositories/order.repository';
import { Order, OrderStatus, PaymentStatus } from '../entities/order.entity';
import { OrderItem } from '../entities/order-item.entity';
import { CreateOrderDto } from '../dto/create-order.dto';
import { UpdateOrderDto } from '../dto/update-order.dto';

@Injectable()
export class OrderService {
  private readonly logger = new Logger(OrderService.name);

  constructor(
    private readonly orderRepository: OrderRepository,
    @Inject('CART_SERVICE') private readonly cartServiceClient: ClientProxy,
    @Inject('PRODUCT_SERVICE') private readonly productServiceClient: ClientProxy,
    @Inject('USER_SERVICE') private readonly userServiceClient: ClientProxy,
  ) {}

  async createOrder(createOrderDto: CreateOrderDto, userId: string): Promise<Order> {
    this.logger.log(`Creating order for user ${userId}`);

    try {
      let orderItems: any[] = [];
      let storeId: string;

      if (createOrderDto.cartId) {
        // Create order from cart
        const cart = await this.getCartDetails(createOrderDto.cartId);
        if (!cart || cart.items.length === 0) {
          throw new BadRequestException('Cart is empty or not found');
        }

        orderItems = cart.items;
        storeId = cart.items[0].storeId; // Assuming single store per cart
      } else if (createOrderDto.items && createOrderDto.items.length > 0) {
        // Create order from provided items
        orderItems = await this.validateAndEnrichOrderItems(createOrderDto.items);
        storeId = orderItems[0].storeId; // Assuming single store per order
      } else {
        throw new BadRequestException('Either cartId or items must be provided');
      }

      // Validate inventory
      await this.validateInventory(orderItems);

      // Create order entity
      const order = new Order();
      order.orderNumber = order.generateOrderNumber();
      order.userId = userId;
      order.storeId = storeId;
      order.cartId = createOrderDto.cartId;
      order.status = OrderStatus.PENDING;
      order.paymentStatus = PaymentStatus.PENDING;
      order.shippingAddress = createOrderDto.shippingAddress;
      order.billingAddress = createOrderDto.billingAddress || createOrderDto.shippingAddress;
      order.paymentMethodId = createOrderDto.paymentMethodId;
      order.couponCode = createOrderDto.couponCode;
      order.notes = createOrderDto.notes;
      order.metadata = createOrderDto.metadata;

      // Create order items
      order.items = orderItems.map(item => {
        const orderItem = new OrderItem();
        orderItem.productId = item.productId;
        orderItem.variantId = item.variantId;
        orderItem.quantity = item.quantity;
        orderItem.price = item.price;
        orderItem.discount = item.discount || 0;
        orderItem.productName = item.productName;
        orderItem.productDescription = item.productDescription;
        orderItem.productImage = item.productImage;
        orderItem.storeId = item.storeId;
        orderItem.storeName = item.storeName;
        orderItem.selectedOptions = item.selectedOptions;
        orderItem.calculateTotal();
        return orderItem;
      });

      // Calculate order totals
      order.calculateTotals();

      // Apply coupon if provided
      if (createOrderDto.couponCode) {
        await this.applyCoupon(order, createOrderDto.couponCode);
      }

      // Calculate shipping and tax
      await this.calculateShippingAndTax(order);

      // Save order
      const savedOrder = await this.orderRepository.create(order);

      // Clear cart if order was created from cart
      if (createOrderDto.cartId) {
        await this.clearCart(createOrderDto.cartId);
      }

      this.logger.log(`Order created successfully: ${savedOrder.orderNumber}`);
      return savedOrder;

    } catch (error) {
      this.logger.error(`Failed to create order for user ${userId}:`, error.message);
      throw error;
    }
  }

  async findById(id: string): Promise<Order> {
    const order = await this.orderRepository.findById(id);
    if (!order) {
      throw new NotFoundException(`Order with ID ${id} not found`);
    }
    return order;
  }

  async findByOrderNumber(orderNumber: string): Promise<Order> {
    const order = await this.orderRepository.findByOrderNumber(orderNumber);
    if (!order) {
      throw new NotFoundException(`Order with number ${orderNumber} not found`);
    }
    return order;
  }

  async findUserOrders(
    userId: string,
    options: {
      limit?: number;
      offset?: number;
      status?: OrderStatus;
    } = {},
  ): Promise<{ orders: Order[]; total: number }> {
    return this.orderRepository.findByUserId(userId, options);
  }

  async findStoreOrders(
    storeId: string,
    options: {
      limit?: number;
      offset?: number;
      status?: OrderStatus;
    } = {},
  ): Promise<{ orders: Order[]; total: number }> {
    return this.orderRepository.findByStoreId(storeId, options);
  }

  async updateOrder(id: string, updateOrderDto: UpdateOrderDto): Promise<Order> {
    const order = await this.findById(id);

    if (updateOrderDto.status) {
      order.updateStatus(updateOrderDto.status);
    }

    if (updateOrderDto.paymentStatus) {
      order.paymentStatus = updateOrderDto.paymentStatus;
    }

    if (updateOrderDto.trackingNumber) {
      order.trackingNumber = updateOrderDto.trackingNumber;
    }

    if (updateOrderDto.shippingCarrier) {
      order.shippingCarrier = updateOrderDto.shippingCarrier;
    }

    if (updateOrderDto.paymentTransactionId) {
      order.paymentTransactionId = updateOrderDto.paymentTransactionId;
    }

    if (updateOrderDto.notes) {
      order.notes = updateOrderDto.notes;
    }

    if (updateOrderDto.metadata) {
      order.metadata = { ...order.metadata, ...updateOrderDto.metadata };
    }

    return this.orderRepository.save(order);
  }

  async cancelOrder(id: string, userId: string): Promise<Order> {
    const order = await this.findById(id);

    if (order.userId !== userId) {
      throw new BadRequestException('You can only cancel your own orders');
    }

    if (!order.isCancellable) {
      throw new BadRequestException('Order cannot be cancelled in its current status');
    }

    order.updateStatus(OrderStatus.CANCELLED);
    return this.orderRepository.save(order);
  }

  async deleteOrder(id: string): Promise<void> {
    const order = await this.findById(id);
    await this.orderRepository.delete(id);
  }

  // Private helper methods
  private async getCartDetails(cartId: string): Promise<any> {
    try {
      return await firstValueFrom(
        this.cartServiceClient.send('get_cart', cartId),
      );
    } catch (error) {
      this.logger.error(`Failed to get cart details for ${cartId}:`, error.message);
      throw new BadRequestException('Failed to retrieve cart details');
    }
  }

  private async validateAndEnrichOrderItems(items: any[]): Promise<any[]> {
    const enrichedItems = [];

    for (const item of items) {
      const product = await this.getProductDetails(item.productId);
      if (!product) {
        throw new BadRequestException(`Product ${item.productId} not found`);
      }

      enrichedItems.push({
        ...item,
        price: product.price,
        productName: product.name,
        productDescription: product.description,
        productImage: product.images?.[0],
        storeId: product.storeId,
        storeName: product.storeName,
      });
    }

    return enrichedItems;
  }

  private async getProductDetails(productId: string): Promise<any> {
    try {
      return await firstValueFrom(
        this.productServiceClient.send('find_product_by_id', productId),
      );
    } catch (error) {
      this.logger.error(`Failed to get product details for ${productId}:`, error.message);
      return null;
    }
  }

  private async validateInventory(items: any[]): Promise<void> {
    // TODO: Implement inventory validation
    // This would check with the Product Service to ensure items are in stock
    this.logger.log('Inventory validation - TODO: Implement');
  }

  private async applyCoupon(order: Order, couponCode: string): Promise<void> {
    // TODO: Implement coupon validation and application
    // This would check with a Coupon Service or Product Service
    this.logger.log(`Applying coupon ${couponCode} - TODO: Implement`);
  }

  private async calculateShippingAndTax(order: Order): Promise<void> {
    // TODO: Implement shipping and tax calculation
    // This would integrate with shipping providers and tax services
    order.shipping = 9.99; // Default shipping
    order.tax = order.subtotal * 0.08; // 8% tax rate
    order.calculateTotals();
  }

  private async clearCart(cartId: string): Promise<void> {
    try {
      await firstValueFrom(
        this.cartServiceClient.send('clear_cart', cartId),
      );
    } catch (error) {
      this.logger.error(`Failed to clear cart ${cartId}:`, error.message);
      // Don't throw error as order creation succeeded
    }
  }

  // Additional service methods for comprehensive functionality
  async getOrderStatistics(userId?: string, storeId?: string): Promise<any> {
    // TODO: Implement order statistics
    return {
      totalOrders: 0,
      pendingOrders: 0,
      completedOrders: 0,
      cancelledOrders: 0,
      totalRevenue: 0,
    };
  }

  async findAll(options: {
    limit?: number;
    offset?: number;
    status?: OrderStatus;
    userId?: string;
    storeId?: string;
  } = {}): Promise<{ orders: Order[]; total: number }> {
    return this.orderRepository.findAll(options);
  }
}
