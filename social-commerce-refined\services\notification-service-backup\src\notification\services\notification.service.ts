import { Injectable, Logger } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';
import { EmailService } from './email.service';
import { SmsService } from './sms.service';
import { SendEmailDto, SendSmsDto } from '../dto';

@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);

  constructor(
    private readonly emailService: EmailService,
    private readonly smsService: SmsService,
  ) {}

  @MessagePattern('notification.sendEmail')
  async sendEmail(data: SendEmailDto): Promise<{ success: boolean; messageId?: string }> {
    this.logger.log(`Sending email to: ${data.to}`);
    
    try {
      const result = await this.emailService.sendEmail(data);
      this.logger.log(`Email sent successfully to: ${data.to}`);
      return { success: true, messageId: result.messageId };
    } catch (error) {
      this.logger.error(`Failed to send email to ${data.to}: ${error.message}`);
      return { success: false };
    }
  }

  @MessagePattern('notification.sendSms')
  async sendSms(data: SendSmsDto): Promise<{ success: boolean; messageId?: string }> {
    this.logger.log(`Sending SMS to: ${data.to}`);
    
    try {
      const result = await this.smsService.sendSms(data);
      this.logger.log(`SMS sent successfully to: ${data.to}`);
      return { success: true, messageId: result.messageId };
    } catch (error) {
      this.logger.error(`Failed to send SMS to ${data.to}: ${error.message}`);
      return { success: false };
    }
  }

  @MessagePattern('notification.sendWelcomeEmail')
  async sendWelcomeEmail(data: { email: string; firstName?: string }): Promise<{ success: boolean }> {
    this.logger.log(`Sending welcome email to: ${data.email}`);
    
    const emailData: SendEmailDto = {
      to: data.email,
      subject: 'Welcome to Social Commerce Platform!',
      content: `
        <h1>Welcome${data.firstName ? `, ${data.firstName}` : ''}!</h1>
        <p>Thank you for joining our Social Commerce Platform.</p>
        <p>You can now start exploring and creating your own store.</p>
        <p>Best regards,<br>The Social Commerce Team</p>
      `,
      variables: { firstName: data.firstName },
    };

    return this.sendEmail(emailData);
  }

  @MessagePattern('notification.sendVerificationEmail')
  async sendVerificationEmail(data: { 
    email: string; 
    verificationToken: string; 
    firstName?: string 
  }): Promise<{ success: boolean }> {
    this.logger.log(`Sending verification email to: ${data.email}`);
    
    const verificationLink = `${process.env.FRONTEND_URL}/verify-email?token=${data.verificationToken}`;
    
    const emailData: SendEmailDto = {
      to: data.email,
      subject: 'Verify Your Email Address',
      content: `
        <h1>Email Verification</h1>
        <p>Hello${data.firstName ? ` ${data.firstName}` : ''},</p>
        <p>Please click the link below to verify your email address:</p>
        <p><a href="${verificationLink}">Verify Email Address</a></p>
        <p>If you didn't create an account, please ignore this email.</p>
        <p>Best regards,<br>The Social Commerce Team</p>
      `,
      variables: { 
        firstName: data.firstName, 
        verificationLink,
        verificationToken: data.verificationToken 
      },
    };

    return this.sendEmail(emailData);
  }
}
