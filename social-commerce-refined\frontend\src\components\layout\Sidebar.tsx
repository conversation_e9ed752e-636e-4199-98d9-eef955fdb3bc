import React from 'react';
import {
  Box,
  Flex,
  Text,
  Icon,
  Link,
  Stack,
  Divider,
  useColorModeValue,
} from '@chakra-ui/react';
import NextLink from 'next/link';
import { useRouter } from 'next/router';
import { FiHome, FiShoppingBag, FiShoppingCart, FiUser, FiSettings, FiHeart, FiPackage } from 'react-icons/fi';
import { useAuth } from '@/context/AuthContext';

interface NavItemProps {
  icon: React.ElementType;
  children: string;
  href: string;
  isActive?: boolean;
}

const NavItem = ({ icon, children, href, isActive }: NavItemProps) => {
  return (
    <Link
      as={NextLink}
      href={href}
      style={{ textDecoration: 'none' }}
      _focus={{ boxShadow: 'none' }}
    >
      <Flex
        align="center"
        p="3"
        mx="2"
        borderRadius="lg"
        role="group"
        cursor="pointer"
        bg={isActive ? 'brand.50' : 'transparent'}
        color={isActive ? 'brand.500' : 'gray.600'}
        _hover={{
          bg: 'brand.50',
          color: 'brand.500',
        }}
      >
        <Icon
          mr="4"
          fontSize="16"
          as={icon}
          color={isActive ? 'brand.500' : 'gray.500'}
          _groupHover={{
            color: 'brand.500',
          }}
        />
        {children}
      </Flex>
    </Link>
  );
};

const Sidebar = () => {
  const router = useRouter();
  const { user } = useAuth();
  
  const isActive = (path: string) => {
    return router.pathname === path || router.pathname.startsWith(`${path}/`);
  };

  return (
    <Box
      h="full"
      bg={useColorModeValue('white', 'gray.900')}
      overflowY="auto"
    >
      <Flex direction="column" h="full" py={5}>
        <Stack spacing={1} flex="1">
          <NavItem icon={FiHome} href="/dashboard" isActive={isActive('/dashboard')}>
            Dashboard
          </NavItem>
          
          <NavItem icon={FiShoppingBag} href="/stores" isActive={isActive('/stores')}>
            Stores
          </NavItem>
          
          <NavItem icon={FiPackage} href="/products" isActive={isActive('/products')}>
            Products
          </NavItem>
          
          <NavItem icon={FiShoppingCart} href="/orders" isActive={isActive('/orders')}>
            Orders
          </NavItem>
          
          <NavItem icon={FiHeart} href="/wishlist" isActive={isActive('/wishlist')}>
            Wishlist
          </NavItem>
          
          <Divider my={3} />
          
          <NavItem icon={FiUser} href="/profile" isActive={isActive('/profile')}>
            Profile
          </NavItem>
          
          <NavItem icon={FiSettings} href="/settings" isActive={isActive('/settings')}>
            Settings
          </NavItem>
        </Stack>
        
        <Box p={4} mt={4}>
          <Text fontSize="sm" color="gray.500">
            Logged in as:
          </Text>
          <Text fontWeight="medium" fontSize="sm">
            {user?.name || user?.email}
          </Text>
        </Box>
      </Flex>
    </Box>
  );
};

export default Sidebar;
