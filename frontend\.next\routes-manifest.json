{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "locale": false, "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/affiliate/programs/[id]", "regex": "^/affiliate/programs/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/affiliate/programs/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/fok/[id]", "regex": "^/fok/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/fok/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/group-buying/[id]", "regex": "^/group\\-buying/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/group\\-buying/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/hashtag/[tag]", "regex": "^/hashtag/([^/]+?)(?:/)?$", "routeKeys": {"nxtPtag": "nxtPtag"}, "namedRegex": "^/hashtag/(?<nxtPtag>[^/]+?)(?:/)?$"}, {"page": "/products/[id]", "regex": "^/products/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/products/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/profile/[username]", "regex": "^/profile/([^/]+?)(?:/)?$", "routeKeys": {"nxtPusername": "nxtPusername"}, "namedRegex": "^/profile/(?<nxtPusername>[^/]+?)(?:/)?$"}, {"page": "/set-new-password/[token]", "regex": "^/set\\-new\\-password/([^/]+?)(?:/)?$", "routeKeys": {"nxtPtoken": "nxtPtoken"}, "namedRegex": "^/set\\-new\\-password/(?<nxtPtoken>[^/]+?)(?:/)?$"}, {"page": "/social/post/[id]", "regex": "^/social/post/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/social/post/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/social/posts/[id]", "regex": "^/social/posts/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/social/posts/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/store/[id]/affiliate", "regex": "^/store/([^/]+?)/affiliate(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/store/(?<nxtPid>[^/]+?)/affiliate(?:/)?$"}, {"page": "/stores/[username]", "regex": "^/stores/([^/]+?)(?:/)?$", "routeKeys": {"nxtPusername": "nxtPusername"}, "namedRegex": "^/stores/(?<nxtPusername>[^/]+?)(?:/)?$"}, {"page": "/stores/[username]/edit", "regex": "^/stores/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPusername": "nxtPusername"}, "namedRegex": "^/stores/(?<nxtPusername>[^/]+?)/edit(?:/)?$"}, {"page": "/stores/[username]/products/create", "regex": "^/stores/([^/]+?)/products/create(?:/)?$", "routeKeys": {"nxtPusername": "nxtPusername"}, "namedRegex": "^/stores/(?<nxtPusername>[^/]+?)/products/create(?:/)?$"}, {"page": "/stores/[username]/products/[productId]", "regex": "^/stores/([^/]+?)/products/([^/]+?)(?:/)?$", "routeKeys": {"nxtPusername": "nxtPusername", "nxtPproductId": "nxtPproductId"}, "namedRegex": "^/stores/(?<nxtPusername>[^/]+?)/products/(?<nxtPproductId>[^/]+?)(?:/)?$"}, {"page": "/stores/[username]/products/[productId]/edit", "regex": "^/stores/([^/]+?)/products/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPusername": "nxtPusername", "nxtPproductId": "nxtPproductId"}, "namedRegex": "^/stores/(?<nxtPusername>[^/]+?)/products/(?<nxtPproductId>[^/]+?)/edit(?:/)?$"}, {"page": "/wishlist/share/[id]", "regex": "^/wishlist/share/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/wishlist/share/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/wishlist/[id]", "regex": "^/wishlist/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/wishlist/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/activity", "regex": "^/activity(?:/)?$", "routeKeys": {}, "namedRegex": "^/activity(?:/)?$"}, {"page": "/activity/feed", "regex": "^/activity/feed(?:/)?$", "routeKeys": {}, "namedRegex": "^/activity/feed(?:/)?$"}, {"page": "/affiliate", "regex": "^/affiliate(?:/)?$", "routeKeys": {}, "namedRegex": "^/affiliate(?:/)?$"}, {"page": "/affiliate/dashboard", "regex": "^/affiliate/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/affiliate/dashboard(?:/)?$"}, {"page": "/affiliate/dashboard/analytics", "regex": "^/affiliate/dashboard/analytics(?:/)?$", "routeKeys": {}, "namedRegex": "^/affiliate/dashboard/analytics(?:/)?$"}, {"page": "/affiliate/dashboard/commissions", "regex": "^/affiliate/dashboard/commissions(?:/)?$", "routeKeys": {}, "namedRegex": "^/affiliate/dashboard/commissions(?:/)?$"}, {"page": "/affiliate/dashboard/links", "regex": "^/affiliate/dashboard/links(?:/)?$", "routeKeys": {}, "namedRegex": "^/affiliate/dashboard/links(?:/)?$"}, {"page": "/affiliate/dashboard/payments", "regex": "^/affiliate/dashboard/payments(?:/)?$", "routeKeys": {}, "namedRegex": "^/affiliate/dashboard/payments(?:/)?$"}, {"page": "/affiliate/dashboard/settings", "regex": "^/affiliate/dashboard/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/affiliate/dashboard/settings(?:/)?$"}, {"page": "/affiliate/programs", "regex": "^/affiliate/programs(?:/)?$", "routeKeys": {}, "namedRegex": "^/affiliate/programs(?:/)?$"}, {"page": "/cart", "regex": "^/cart(?:/)?$", "routeKeys": {}, "namedRegex": "^/cart(?:/)?$"}, {"page": "/checkout", "regex": "^/checkout(?:/)?$", "routeKeys": {}, "namedRegex": "^/checkout(?:/)?$"}, {"page": "/checkout/success", "regex": "^/checkout/success(?:/)?$", "routeKeys": {}, "namedRegex": "^/checkout/success(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/dashboard/products", "regex": "^/dashboard/products(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/products(?:/)?$"}, {"page": "/dashboard/products/import", "regex": "^/dashboard/products/import(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/products/import(?:/)?$"}, {"page": "/dashboard/products/import/template", "regex": "^/dashboard/products/import/template(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/products/import/template(?:/)?$"}, {"page": "/feed", "regex": "^/feed(?:/)?$", "routeKeys": {}, "namedRegex": "^/feed(?:/)?$"}, {"page": "/fok", "regex": "^/fok(?:/)?$", "routeKeys": {}, "namedRegex": "^/fok(?:/)?$"}, {"page": "/fok/create", "regex": "^/fok/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/fok/create(?:/)?$"}, {"page": "/group-buying", "regex": "^/group\\-buying(?:/)?$", "routeKeys": {}, "namedRegex": "^/group\\-buying(?:/)?$"}, {"page": "/group-buying/create", "regex": "^/group\\-buying/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/group\\-buying/create(?:/)?$"}, {"page": "/i18n-test", "regex": "^/i18n\\-test(?:/)?$", "routeKeys": {}, "namedRegex": "^/i18n\\-test(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/notifications", "regex": "^/notifications(?:/)?$", "routeKeys": {}, "namedRegex": "^/notifications(?:/)?$"}, {"page": "/notifications/settings", "regex": "^/notifications/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/notifications/settings(?:/)?$"}, {"page": "/password-reset", "regex": "^/password\\-reset(?:/)?$", "routeKeys": {}, "namedRegex": "^/password\\-reset(?:/)?$"}, {"page": "/products", "regex": "^/products(?:/)?$", "routeKeys": {}, "namedRegex": "^/products(?:/)?$"}, {"page": "/profile", "regex": "^/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile(?:/)?$"}, {"page": "/profile/edit", "regex": "^/profile/edit(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile/edit(?:/)?$"}, {"page": "/profile/orders", "regex": "^/profile/orders(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile/orders(?:/)?$"}, {"page": "/profile/reviews", "regex": "^/profile/reviews(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile/reviews(?:/)?$"}, {"page": "/register", "regex": "^/register(?:/)?$", "routeKeys": {}, "namedRegex": "^/register(?:/)?$"}, {"page": "/search", "regex": "^/search(?:/)?$", "routeKeys": {}, "namedRegex": "^/search(?:/)?$"}, {"page": "/settings", "regex": "^/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings(?:/)?$"}, {"page": "/social", "regex": "^/social(?:/)?$", "routeKeys": {}, "namedRegex": "^/social(?:/)?$"}, {"page": "/social/feed", "regex": "^/social/feed(?:/)?$", "routeKeys": {}, "namedRegex": "^/social/feed(?:/)?$"}, {"page": "/stores", "regex": "^/stores(?:/)?$", "routeKeys": {}, "namedRegex": "^/stores(?:/)?$"}, {"page": "/stores/create", "regex": "^/stores/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/stores/create(?:/)?$"}, {"page": "/verify-email", "regex": "^/verify\\-email(?:/)?$", "routeKeys": {}, "namedRegex": "^/verify\\-email(?:/)?$"}, {"page": "/wishlist", "regex": "^/wishlist(?:/)?$", "routeKeys": {}, "namedRegex": "^/wishlist(?:/)?$"}, {"page": "/wishlist/create", "regex": "^/wishlist/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/wishlist/create(?:/)?$"}, {"page": "/wishlist/manage", "regex": "^/wishlist/manage(?:/)?$", "routeKeys": {}, "namedRegex": "^/wishlist/manage(?:/)?$"}], "dataRoutes": [], "i18n": {"defaultLocale": "en", "locales": ["en", "fa"], "localeDetection": false}, "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Url", "prefetchHeader": "Next-Router-Prefetch", "contentTypeHeader": "text/x-component"}, "rewrites": []}