# Notification Service

The Notification Service is responsible for managing all types of notifications in the Social Commerce Platform.

## Features

- **Email Notifications**: SMTP integration with <PERSON><PERSON>mail<PERSON>
- **SMS Notifications**: Mock implementation ready for Twilio/AWS SNS
- **Push Notifications**: Framework ready for implementation
- **Template Management**: Dynamic content with template support
- **Notification History**: Complete audit trail of all notifications
- **Microservice Integration**: RabbitMQ message patterns for inter-service communication

## Architecture

The Notification Service follows the established microservice patterns:

```
notification-service/
├── src/
│   ├── main.ts                     # Application entry point
│   ├── app.module.ts               # Root module
│   ├── controllers/                # HTTP API controllers
│   ├── services/                   # Business logic services
│   ├── entities/                   # Database entities
│   ├── dto/                        # Data transfer objects
│   ├── guards/                     # Authentication guards
│   ├── strategies/                 # Passport strategies
│   └── health/                     # Health check endpoints
├── test/                           # Test files
├── Dockerfile                      # Container configuration
├── package.json                    # Dependencies
├── tsconfig.json                   # TypeScript configuration
├── nest-cli.json                   # NestJS CLI configuration
└── jest.config.js                  # Testing configuration
```
