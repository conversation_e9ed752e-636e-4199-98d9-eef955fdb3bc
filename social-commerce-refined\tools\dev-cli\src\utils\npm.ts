import * as childProcess from 'child_process';
import * as chalk from 'chalk';
import { getServiceDir } from './paths';

/**
 * Run an npm command in a service directory
 * @param service Service name
 * @param command npm command
 * @param args Command arguments
 * @returns Promise that resolves when the command completes
 */
export function npmRun(service: string, command: string, args: string[] = []): Promise<void> {
  return new Promise((resolve, reject) => {
    const serviceDir = getServiceDir(service);
    const npmCommand = `npm ${command} ${args.join(' ')}`;
    
    console.log(chalk.blue(`Running in ${service}-service: ${npmCommand}`));
    
    const process = childProcess.exec(npmCommand, { cwd: serviceDir });
    
    process.stdout?.on('data', (data) => {
      console.log(data.toString().trim());
    });
    
    process.stderr?.on('data', (data) => {
      console.error(chalk.red(data.toString().trim()));
    });
    
    process.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`npm command failed with code ${code}`));
      }
    });
  });
}

/**
 * Install dependencies for a service
 * @param service Service name
 * @returns Promise that resolves when the dependencies are installed
 */
export function installDependencies(service: string): Promise<void> {
  return npmRun(service, 'install');
}

/**
 * Build a service
 * @param service Service name
 * @returns Promise that resolves when the service is built
 */
export function buildService(service: string): Promise<void> {
  return npmRun(service, 'run', ['build']);
}

/**
 * Start a service in development mode
 * @param service Service name
 * @returns Promise that resolves when the service is started
 */
export function startService(service: string): Promise<void> {
  return npmRun(service, 'run', ['start:dev']);
}

/**
 * Run tests for a service
 * @param service Service name
 * @param watch Whether to run tests in watch mode
 * @returns Promise that resolves when the tests are complete
 */
export function testService(service: string, watch: boolean = false): Promise<void> {
  const command = watch ? 'run test:watch' : 'test';
  return npmRun(service, command);
}
