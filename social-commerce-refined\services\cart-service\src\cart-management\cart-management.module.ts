import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ClientsModule, Transport } from '@nestjs/microservices';

// Entities
import { Cart } from './entities/cart.entity';
import { CartItem } from './entities/cart-item.entity';

// Controllers
import { CartController } from './controllers/cart.controller';

// Services
import { CartService } from './services/cart.service';

// Repositories
import { CartRepository } from './repositories/cart.repository';

// Strategies
import { JwtStrategy } from '../shared/strategies/jwt.strategy';

@Module({
  imports: [
    TypeOrmModule.forFeature([Cart, CartItem]),
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET', 'your_jwt_secret_key_here'),
        signOptions: { expiresIn: '1h' },
      }),
    }),
    ClientsModule.registerAsync([
      {
        name: 'PRODUCT_SERVICE',
        imports: [ConfigModule],
        inject: [ConfigService],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.RMQ,
          options: {
            urls: [configService.get<string>('RABBITMQ_URL', 'amqp://admin:admin@rabbitmq:5672')],
            queue: configService.get<string>('PRODUCT_QUEUE', 'product_queue'),
            queueOptions: { durable: true },
          },
        }),
      },
    ]),
  ],
  controllers: [CartController],
  providers: [CartService, CartRepository, JwtStrategy],
  exports: [CartService, CartRepository],
})
export class CartManagementModule {}
