(()=>{var e={};e.id=5193,e.ids=[5193],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},52574:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>l});var r=a(67096),s=a(16132),n=a(37284),o=a.n(n),i=a(32564),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);a.d(t,c);let l=["",{children:["notifications",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,85911)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\notifications\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,9291,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\notifications\\page.tsx"],u="/notifications/page",p={require:a,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/notifications/page",pathname:"/notifications",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},87839:(e,t,a)=>{Promise.resolve().then(a.bind(a,40430))},40430:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>NotificationsPage});var r=a(30784);a(9885);var s=a(57114),n=a(27870),o=a(38898),i=a(53717);function NotificationsPage(){let{t:e}=(0,n.$G)("notifications"),t=(0,s.useRouter)(),{data:a=[],isLoading:c}=(0,i.mI)(),[l]=(0,i.j6)(),[d]=(0,i.JF)(),[u]=(0,i.sJ)(),[p]=(0,i.Jm)(),handleMarkAsRead=async e=>{try{await l(e).unwrap()}catch(e){console.error("Failed to mark notification as read:",e)}},handleMarkAllAsRead=async()=>{try{await d().unwrap()}catch(e){console.error("Failed to mark all notifications as read:",e)}},handleDelete=async e=>{try{await u(e).unwrap()}catch(e){console.error("Failed to delete notification:",e)}},handleDeleteAll=async()=>{try{await p().unwrap()}catch(e){console.error("Failed to delete all notifications:",e)}};return r.jsx("div",{className:"min-h-screen p-6",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[r.jsx("h1",{className:"text-3xl font-bold mb-6",children:e("title","Notifications")}),(0,r.jsxs)("div",{className:"mb-6 flex justify-between items-center",children:[r.jsx("div",{className:"flex-1"}),(0,r.jsxs)("button",{onClick:()=>t.push("/notifications/settings"),className:"flex items-center text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300",children:[(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]}),e("tabs.settings","Settings")]})]}),r.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden",children:r.jsx(o.Z,{notifications:a,isLoading:c,onMarkAsRead:handleMarkAsRead,onMarkAllAsRead:handleMarkAllAsRead,onDelete:handleDelete,onDeleteAll:handleDeleteAll})})]})})}},85911:(e,t,a)=>{"use strict";a.r(t),a.d(t,{$$typeof:()=>o,__esModule:()=>n,default:()=>c});var r=a(95153);let s=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\notifications\page.tsx`),{__esModule:n,$$typeof:o}=s,i=s.default,c=i}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),a=t.X(0,[2103,2765],()=>__webpack_exec__(52574));module.exports=a})();