# Standardized Service Templating Rules

## Overview

**Purpose:** Prevent template inconsistency issues and ensure all services follow identical patterns  
**Scope:** All developers and AI agents working on social commerce platform  
**Status:** ✅ **MANDATORY** - Must be followed for all service implementations  
**Last Updated:** May 28, 2025  

## Table of Contents

1. [Mandatory Template Consistency Rules](#mandatory-template-consistency-rules)
2. [Service Creation Process](#service-creation-process)
3. [Configuration Standardization](#configuration-standardization)
4. [Verification Requirements](#verification-requirements)
5. [Template Maintenance](#template-maintenance)
6. [Enforcement Guidelines](#enforcement-guidelines)

## Mandatory Template Consistency Rules

### Rule 1: Single Source of Truth
**RULE:** User Service is the official template for all new services  
**REQUIREMENT:** All new services MUST use User Service as exact template  
**VIOLATION:** Creating services with different base configurations is prohibited  

### Rule 2: Exact Configuration Copying
**RULE:** Configuration files must be copied exactly, not recreated manually  
**FILES AFFECTED:**
- `tsconfig.json`
- `nest-cli.json`
- `package.json` (base structure)
- `Dockerfile` (base structure)
- `.gitignore`
- `.dockerignore`

**REQUIREMENT:** Use file copying commands, not manual typing  

### Rule 3: Zero Configuration Drift Tolerance
**RULE:** Any configuration differences from template must be explicitly justified  
**REQUIREMENT:** Document all differences in service-specific README  
**PROCESS:** Configuration drift is treated as a critical bug  

### Rule 4: Immediate Verification
**RULE:** Template consistency must be verified before first build  
**REQUIREMENT:** Run verification checklist before any development work  
**TOOLS:** Use provided verification scripts  

## Service Creation Process

### Phase 1: Template Copying
```bash
# 1. Copy entire User Service structure
cp -r services/user-service services/[new-service-name]

# 2. Rename directories and update package.json name
# 3. Update service-specific environment variables only
# 4. Update database name and port numbers only
```
**Correct Service Templating Approach**
in above Phase 1: Template Copying is wrong. correct approach exolained here **File:** [`chat-thread-lessons-learned.md`](./chat-thread-lessons-learned.md) section 2. **Correct Service Templating Approach**

### Phase 2: Minimal Customization
**ALLOWED CHANGES:**
- Service name in package.json
- Database name in environment variables
- Port numbers (following port allocation scheme)
- Service-specific environment variables

**PROHIBITED CHANGES:**
- TypeScript configuration
- Build system configuration
- Dependency versions (unless explicitly required)
- Dockerfile structure

### Phase 3: Verification
```bash
# Run template consistency verification
npm run verify-template-consistency [service-name]

# Compare configurations
diff services/user-service/tsconfig.json services/[service-name]/tsconfig.json
diff services/user-service/nest-cli.json services/[service-name]/nest-cli.json
```

### Phase 4: Documentation
**REQUIRED DOCUMENTATION:**
- Service-specific README with justified differences
- Environment variable documentation
- API endpoint documentation
- Database schema documentation

## Configuration Standardization

### TypeScript Configuration (tsconfig.json)
**MANDATORY SETTINGS:**
```json
{
  "compilerOptions": {
    "target": "es2017",
    "outDir": "./dist",
    "webpack": true
  },
  "exclude": [
    "**/*.spec.ts",
    "**/*.test.ts",
    "test/**/*",
    "src/**/*.spec.ts",
    "src/**/*.test.ts"
  ]
}
```

### NestJS CLI Configuration (nest-cli.json)
**MANDATORY SETTINGS:**
```json
{
  "collection": "@nestjs/schematics",
  "sourceRoot": "src",
  "compilerOptions": {
    "deleteOutDir": true,
    "webpack": true
  }
}
```

### Dockerfile Configuration
**MANDATORY STRUCTURE:**
```dockerfile
# Build Stage
FROM node:18-alpine AS build
# ... (exact copy from User Service)

# Production Stage  
FROM node:18-alpine
# ... (exact copy from User Service)

# MANDATORY: Webpack output path
CMD ["node", "dist/main.js"]
```

### Docker Compose Configuration
**MANDATORY PATTERNS:**
```yaml
services:
  [service-name]:
    build:
      context: .
      dockerfile: services/[service-name]/Dockerfile
    container_name: social-commerce-[service-name]
    environment:
      NODE_OPTIONS: "--max-old-space-size=640"  # Mandatory memory optimization
    deploy:
      resources:
        limits:
          memory: 896M  # Standard service memory limit
        reservations:
          memory: 448M
    # volumes: # MUST be commented out for production builds
```

## Verification Requirements

### Pre-Build Verification Checklist
- [ ] All configuration files copied exactly from User Service
- [ ] Only service-specific variables modified
- [ ] No TypeScript or build configuration changes
- [ ] Dockerfile CMD points to `dist/main.js`
- [ ] Memory optimization settings applied
- [ ] Volume mounts disabled for production builds

### Build Verification
```bash
# 1. Verify build output location
docker run --rm [service-image] sh -c "ls -la /app/dist/main.js"

# 2. Verify service starts successfully
docker-compose up -d [service-name]

# 3. Verify health endpoint responds
curl http://localhost:[port]/api/health
```

### Configuration Comparison
```bash
# Automated comparison script (to be created)
./scripts/verify-service-template.sh [service-name]
```

## Template Maintenance

### Template Updates
**PROCESS:**
1. Update User Service template
2. Document changes in template changelog
3. Update all existing services to match
4. Verify all services still work
5. Update this documentation

### Version Control
**REQUIREMENTS:**
- All template changes must be in version control
- Template changes require approval from lead developer
- All services must be updated within 1 sprint of template changes

### Regular Audits
**SCHEDULE:** Monthly template consistency audits  
**PROCESS:** Automated comparison of all services to template  
**REMEDIATION:** Immediate fix of any configuration drift found  

## Enforcement Guidelines

### For Developers
**MANDATORY TRAINING:**
- Template consistency importance
- Service creation process
- Verification procedures
- Strategic thinking about configuration differences

**ACCOUNTABILITY:**
- Template violations are treated as critical bugs
- All configuration changes must be justified
- Peer review required for any service modifications

### For AI Agents
**MANDATORY BEHAVIOR:**
- Always verify template consistency before declaring service-specific issues
- Question configuration differences before accepting them
- Apply template standardization before service-specific analysis
- Document any legitimate differences found

**PROHIBITED BEHAVIOR:**
- Creating services with different base configurations
- Accepting configuration drift without investigation
- Implementing "service-specific" solutions without template verification

### Code Review Requirements
**MANDATORY CHECKS:**
- [ ] Template consistency verified
- [ ] Configuration differences justified
- [ ] Documentation updated
- [ ] Verification tests passed

### CI/CD Integration
**AUTOMATED CHECKS:**
- Template consistency verification in build pipeline
- Configuration drift detection
- Automated remediation suggestions
- Build failure on template violations

---

**CRITICAL SUCCESS FACTOR:** Template consistency is not optional - it's fundamental to maintainable microservices architecture.

**ENFORCEMENT:** These rules are mandatory for all team members and AI agents. Violations will be treated as critical issues requiring immediate remediation.

---

**Last Updated:** May 28, 2025  
**Status:** ✅ **ACTIVE** - All developers and AI agents must follow these rules  
**Next Review:** Monthly template consistency audit
