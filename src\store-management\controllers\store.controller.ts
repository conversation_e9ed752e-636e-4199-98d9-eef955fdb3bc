import { Controller, Get, Post, Body, Param, Put, Delete, UseGuards, Req, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { StoreService } from '../services/store.service';
import { CreateStoreDto } from '../dto/create-store.dto';
import { UpdateStoreDto } from '../dto/update-store.dto';
import { Store } from '../entities/store.entity';
import { JwtAuthGuard } from '../../shared/guards/jwt-auth.guard';
import { MessagePattern } from '@nestjs/microservices';

@ApiTags('stores')
@Controller('stores')
export class StoreController {
  private readonly logger = new Logger(StoreController.name);

  constructor(private readonly storeService: StoreService) {}

  @Get()
  @ApiOperation({ summary: 'Get all stores' })
  @ApiResponse({ status: 200, description: 'Return all stores' })
  async findAll(): Promise<Store[]> {
    this.logger.log('Getting all stores');
    return this.storeService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a store by ID' })
  @ApiResponse({ status: 200, description: 'Return the store' })
  @ApiResponse({ status: 404, description: 'Store not found' })
  async findOne(@Param('id') id: string): Promise<Store> {
    this.logger.log(`Getting store with ID: ${id}`);
    return this.storeService.findOne(id);
  }

  @Get('owner/:ownerId')
  @ApiOperation({ summary: 'Get stores by owner ID' })
  @ApiResponse({ status: 200, description: 'Return the stores' })
  async findByOwnerId(@Param('ownerId') ownerId: string): Promise<Store[]> {
    this.logger.log(`Getting stores for owner with ID: ${ownerId}`);
    return this.storeService.findByOwnerId(ownerId);
  }

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new store' })
  @ApiResponse({ status: 201, description: 'Store created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async create(@Body() createStoreDto: CreateStoreDto, @Req() req): Promise<Store> {
    this.logger.log(`Creating store with name: ${createStoreDto.name}`);
    
    // Set the owner ID from the authenticated user
    createStoreDto.ownerId = req.user.sub;
    
    return this.storeService.create(createStoreDto);
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update a store' })
  @ApiResponse({ status: 200, description: 'Store updated successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Store not found' })
  async update(
    @Param('id') id: string,
    @Body() updateStoreDto: UpdateStoreDto,
    @Req() req,
  ): Promise<Store> {
    this.logger.log(`Updating store with ID: ${id}`);
    return this.storeService.update(id, updateStoreDto, req.user.sub);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete a store' })
  @ApiResponse({ status: 200, description: 'Store deleted successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Store not found' })
  async remove(@Param('id') id: string, @Req() req): Promise<void> {
    this.logger.log(`Removing store with ID: ${id}`);
    return this.storeService.remove(id, req.user.sub);
  }

  // Microservice endpoints

  @MessagePattern('find_all_stores')
  async findAllStores(): Promise<Store[]> {
    this.logger.log('Microservice find all stores request received');
    return this.storeService.findAll();
  }

  @MessagePattern('find_store_by_id')
  async findStoreById(id: string): Promise<Store> {
    this.logger.log(`Microservice find store by ID request received for: ${id}`);
    return this.storeService.findOne(id);
  }

  @MessagePattern('find_stores_by_owner_id')
  async findStoresByOwnerId(ownerId: string): Promise<Store[]> {
    this.logger.log(`Microservice find stores by owner ID request received for: ${ownerId}`);
    return this.storeService.findByOwnerId(ownerId);
  }

  @MessagePattern('create_store')
  async createStore(createStoreDto: CreateStoreDto): Promise<Store> {
    this.logger.log(`Microservice create store request received for: ${createStoreDto.name}`);
    return this.storeService.create(createStoreDto);
  }

  @MessagePattern('update_store')
  async updateStore(data: { id: string; updateStoreDto: UpdateStoreDto; userId: string }): Promise<Store> {
    this.logger.log(`Microservice update store request received for ID: ${data.id}`);
    return this.storeService.update(data.id, data.updateStoreDto, data.userId);
  }

  @MessagePattern('remove_store')
  async removeStore(data: { id: string; userId: string }): Promise<void> {
    this.logger.log(`Microservice remove store request received for ID: ${data.id}`);
    return this.storeService.remove(data.id, data.userId);
  }

  @MessagePattern('increment_follower_count')
  async incrementFollowerCount(id: string): Promise<Store> {
    this.logger.log(`Microservice increment follower count request received for store ID: ${id}`);
    return this.storeService.incrementFollowerCount(id);
  }

  @MessagePattern('decrement_follower_count')
  async decrementFollowerCount(id: string): Promise<Store> {
    this.logger.log(`Microservice decrement follower count request received for store ID: ${id}`);
    return this.storeService.decrementFollowerCount(id);
  }

  @MessagePattern('update_store_rating')
  async updateStoreRating(data: { id: string; rating: number; reviewCount: number }): Promise<Store> {
    this.logger.log(`Microservice update store rating request received for store ID: ${data.id}`);
    return this.storeService.updateRating(data.id, data.rating, data.reviewCount);
  }
}
