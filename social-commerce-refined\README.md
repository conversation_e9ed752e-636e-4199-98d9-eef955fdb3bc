# Social Commerce Platform (Refined Architecture)

A modern, scalable social commerce platform built with a microservices architecture.

## Architecture Overview

This platform follows a domain-driven microservices architecture with feature-based internal organization, providing:

- Clear service boundaries based on business domains
- Feature-focused organization within each service
- Event-driven communication between services
- Independent deployment and scaling
- Technology flexibility per domain

### System Architecture Diagram

```
                                  ┌─────────────┐
                                  │   Clients   │
                                  └──────┬──────┘
                                         │
                                         ▼
┌─────────────────────────────────────────────────────────────────┐
│                          API Gateway                             │
└───┬───────────┬────────────┬────────────┬────────────┬──────────┘
    │           │            │            │            │
    ▼           ▼            ▼            ▼            ▼
┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────────┐
│  User   │ │  Store  │ │ Product │ │  Order  │ │    Cart     │
│ Service │ │ Service │ │ Service │ │ Service │ │   Service   │
└────┬────┘ └────┬────┘ └────┬────┘ └────┬────┘ └──────┬──────┘
     │           │           │           │             │
     │           │           │           │             │
┌────┴────┐ ┌────┴────┐ ┌────┴────┐ ┌────┴────┐ ┌──────┴──────┐
│  User   │ │  Store  │ │ Product │ │  Order  │ │    Cart     │
│   DB    │ │   DB    │ │   DB    │ │   DB    │ │     DB      │
└─────────┘ └─────────┘ └─────────┘ └─────────┘ └─────────────┘
```
### More info
for mor information see [ARCHITECTURE.md](./docs/architecture/ARCHITECTURE.md)

## Implementation-First Approach

This project follows an **implementation-first approach** based on lessons learned from our development process:

### Core Principles

1. **Build Working Code First, Then Extract Guidelines**
   - Focus on creating functional, working services before extensive documentation
   - Use working implementations as the source of truth for patterns and architecture
   - Extract guidelines and documentation from proven, working code

2. **Template-Based Development**
   - Use existing working services (like User Service) as templates for new services
   - Copy and modify proven patterns rather than starting from scratch
   - This approach is 10x faster than building from architectural documents alone

3. **Break Down Tasks into Micro-Steps**
   - Divide complex tasks into extremely small, manageable steps
   - Add verification points after logical groups of steps
   - Execute methodically, one step at a time

4. **Early Integration Testing**
   - Test integration between services early to validate architecture decisions
   - Validate end-to-end workflows before building all services
   - Use the API Gateway as the foundation for service integration

## Key Architecture Principles

### 1. Domain-Driven Microservices

- Services are organized around business domains
- Each service has clear responsibilities and boundaries
- Services communicate through well-defined interfaces

### 2. Feature-Based Internal Organization

- Within each service, code is organized by business feature
- Features contain all related components (controllers, services, repositories)
- Shared code is extracted into a common directory

### 3. Event-Driven Communication

- Services communicate asynchronously via events
- RabbitMQ is used as the message broker
- Event schema registry ensures contract compliance

### 4. Database Per Service

- Each service owns its database
- No direct cross-service database access
- Data consistency maintained through events

### 5. API Gateway Pattern

- Single entry point for all client requests
- Handles routing, authentication, and request transformation
- Implements cross-cutting concerns

### 6. Production-Ready Features

- Security: Helmet, CORS, rate limiting, JWT validation
- Monitoring: Health checks, request logging, service discovery
- Error Recovery: Circuit breaker patterns, graceful degradation
- Environment Consistency: Docker-first development, standardized scripts

## Service Catalog

| Service | Description | Primary Database | Key Features |
|---------|-------------|------------------|-------------|
| User Service | User management and authentication | PostgreSQL | Authentication, Profiles, Verification |
| Store Service | Store management and settings | PostgreSQL | Store CRUD, Settings, Analytics |
| Product Service | Product catalog and inventory | PostgreSQL | Products, Categories, Inventory |
| Order Service | Order processing and payments | PostgreSQL | Orders, Payments, Fulfillment |
| Cart Service | Shopping cart management | Redis/PostgreSQL | Cart CRUD, Pricing, Promotions |
| Social Service | Social interactions | MongoDB | Follows, Comments, Likes |
| Notification Service | User notifications | MongoDB | Email, Push, In-app |
| Search Service | Search functionality | Elasticsearch | Product Search, Autocomplete |
| Analytics Service | Business analytics | PostgreSQL/MongoDB | Reporting, Dashboards |
| Group Buying Service | Group buying functionality | PostgreSQL | Groups, Discounts, Timers |
| Affiliate Service | Affiliate management | PostgreSQL | Tracking, Commission, Payouts |

## Technology Stack

- **Backend**: Node.js with NestJS
- **Frontend**: Next.js with React
- **Databases**: PostgreSQL, MongoDB, Redis, Elasticsearch
- **Message Broker**: RabbitMQ
- **Containerization**: Docker
- **Orchestration**: Kubernetes
- **CI/CD**: GitHub Actions
- **Monitoring**: ELK Stack, Prometheus, Grafana

## Development Workflow

### Setup

1. Clone the repository
2. Install dependencies with `npm install`
3. Start the development environment with `npm run dev`

### Development CLI

The platform includes a custom CLI tool for development:

```bash
# Start all services
dev start

# Start a specific service
dev start user

# Check service status
dev status

# Install dependencies
dev install

# Run tests
dev test
```

## Deployment

The platform can be deployed using:

1. Docker Compose for development
2. Kubernetes for staging and production

## Documentation

### Development Guidelines
- **[AI Agent Guidelines](./docs/guidelines/AI-AGENT-GUIDELINES.md)** - Comprehensive guidelines for AI agents
- **[AI README](./docs/guidelines/AI-README.md)** - Quick reference for AI development
- **[Service Naming Conventions](./docs/guidelines/SERVICE-NAMING-CONVENTIONS.md)** - ⭐ **MANDATORY** naming standards

### Development Documentation
- **[Development Issues & Solutions](./docs/development/README.md)** - Comprehensive issue documentation
- **[Architecture Documentation](./docs/architecture/ARCHITECTURE.md)** - Detailed system architecture
- **[Integration Testing Plan](./docs/testing/integration-testing-plan.md)** - Testing strategies

### Implementation Roadmap
- **[Complete Platform Integration Roadmap](./docs/roadmap/COMPLETE-PLATFORM-INTEGRATION-ROADMAP.md)** - ⭐ **COMPREHENSIVE** implementation plan

### Templates & Tools
- **[Service Creation Template](./docs/templates/service-template/)** - Standardized service creation
- **[Service Creation Checklist](./docs/templates/service-template/SERVICE-CREATION-CHECKLIST.md)** - Step-by-step guide

## Contributing

See [CONTRIBUTING.md](./CONTRIBUTING.md) for contribution guidelines.

## License

This project is licensed under the MIT License - see the [LICENSE](./LICENSE) file for details.
