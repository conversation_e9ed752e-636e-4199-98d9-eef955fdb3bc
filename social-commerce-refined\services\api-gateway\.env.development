# Server Configuration
NODE_ENV=development
HTTP_PORT=3000

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=1h

# Service URLs
USER_SERVICE_URL=http://localhost:3001/api
STORE_SERVICE_URL=http://localhost:3002/api
PRODUCT_SERVICE_URL=http://localhost:3003/api
ORDER_SERVICE_URL=http://localhost:3004/api
CART_SERVICE_URL=http://localhost:3005/api
SOCIAL_SERVICE_URL=http://localhost:3006/api
NOTIFICATION_SERVICE_URL=http://localhost:3007/api
SEARCH_SERVICE_URL=http://localhost:3008/api
ANALYTICS_SERVICE_URL=http://localhost:3009/api

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX=100  # 100 requests per 15 minutes

# Logging
LOG_LEVEL=debug
