import { BaseEvent } from '../base-event.interface';

/**
 * Event emitted when an item is added to a cart
 */
export class CartItemAddedEvent implements BaseEvent<CartItemAddedPayload> {
  id: string;
  type: string = 'cart.item.added';
  version: string = '1.0';
  timestamp: string;
  producer: string = 'cart-service';
  payload: CartItemAddedPayload;

  constructor(payload: CartItemAddedPayload) {
    this.id = `${payload.cartId}-${payload.productId}-${Date.now()}`;
    this.timestamp = new Date().toISOString();
    this.payload = payload;
  }
}

/**
 * Payload for CartItemAddedEvent
 */
export interface CartItemAddedPayload {
  /**
   * Cart ID
   */
  cartId: string;

  /**
   * User ID
   */
  userId: string;

  /**
   * Product ID
   */
  productId: string;

  /**
   * Product name
   */
  productName: string;

  /**
   * Quantity
   */
  quantity: number;

  /**
   * Price per unit
   */
  price: number;

  /**
   * Timestamp
   */
  addedAt: string;
}
