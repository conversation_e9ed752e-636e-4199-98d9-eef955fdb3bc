import { ApiProperty } from '@nestjs/swagger';
import { NotificationType, NotificationStatus } from '../entities/notification.entity';

export class NotificationResponseDto {
  @ApiProperty({ description: 'Notification ID' })
  id: string;

  @ApiProperty({ description: 'User ID' })
  userId: string;

  @ApiProperty({ enum: NotificationType, description: 'Notification type' })
  type: NotificationType;

  @ApiProperty({ description: 'Notification subject' })
  subject: string;

  @ApiProperty({ description: 'Notification content' })
  content: string;

  @ApiProperty({ description: 'Recipient email or phone' })
  recipient: string;

  @ApiProperty({ enum: NotificationStatus, description: 'Notification status' })
  status: NotificationStatus;

  @ApiProperty({ description: 'Additional metadata' })
  metadata: Record<string, any>;

  @ApiProperty({ description: 'Sent timestamp' })
  sentAt: Date;

  @ApiProperty({ description: 'Created timestamp' })
  createdAt: Date;
}
