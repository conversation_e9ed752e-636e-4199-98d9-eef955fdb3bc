"use strict";exports.id=1020,exports.ids=[1020],exports.modules={51020:(t,e,s)=>{s.d(e,{$i:()=>q,CH:()=>c,Mk:()=>v,d6:()=>T,iN:()=>g,jF:()=>m,oR:()=>y,sH:()=>u,so:()=>p,u3:()=>r,ws:()=>a,xG:()=>P,yD:()=>n});var o=s(86372);let i=o.g.injectEndpoints({endpoints:t=>({getPosts:t.query({query:({page:t=1,limit:e=10,filters:s})=>{let o=`/social/posts?page=${t}&limit=${e}`;return s&&(s.following&&(o+="&following=true"),s.type&&(o+=`&type=${s.type}`),s.userId&&(o+=`&userId=${s.userId}`),s.productId&&(o+=`&productId=${s.productId}`),s.storeId&&(o+=`&storeId=${s.storeId}`),s.tags?.length&&(o+=`&tags=${s.tags.join(",")}`),s.startDate&&(o+=`&startDate=${s.startDate}`),s.endDate&&(o+=`&endDate=${s.endDate}`)),o},providesTags:t=>t?[...t.posts.map(({id:t})=>({type:"Posts",id:t})),{type:"Posts",id:"LIST"}]:[{type:"Posts",id:"LIST"}]}),getPost:t.query({query:t=>`/social/posts/${t}`,providesTags:(t,e,s)=>[{type:"Posts",id:s}]}),createPost:t.mutation({query:t=>({url:"/social/posts",method:"POST",body:t}),invalidatesTags:[{type:"Posts",id:"LIST"}]}),updatePost:t.mutation({query:({id:t,data:e})=>({url:`/social/posts/${t}`,method:"PATCH",body:e}),invalidatesTags:(t,e,{id:s})=>[{type:"Posts",id:s}]}),deletePost:t.mutation({query:t=>({url:`/social/posts/${t}`,method:"DELETE"}),invalidatesTags:(t,e,s)=>[{type:"Posts",id:s},{type:"Posts",id:"LIST"}]}),getComments:t.query({query:({postId:t,page:e=1,limit:s=10})=>`/social/posts/${t}/comments?page=${e}&limit=${s}`,providesTags:(t,e,{postId:s})=>t?[...t.comments.map(({id:t})=>({type:"Comments",id:t})),{type:"Comments",id:s}]:[{type:"Comments",id:s}]}),createComment:t.mutation({query:({postId:t,content:e,parentId:s})=>({url:`/social/posts/${t}/comments`,method:"POST",body:{content:e,parentId:s}}),invalidatesTags:(t,e,{postId:s})=>[{type:"Comments",id:s},{type:"Posts",id:s}]}),updateComment:t.mutation({query:({commentId:t,content:e})=>({url:`/social/comments/${t}`,method:"PATCH",body:{content:e}}),invalidatesTags:(t,e,{commentId:s})=>[{type:"Comments",id:s}]}),deleteComment:t.mutation({query:({commentId:t})=>({url:`/social/comments/${t}`,method:"DELETE"}),invalidatesTags:(t,e,{commentId:s,postId:o})=>[{type:"Comments",id:s},{type:"Comments",id:o},{type:"Posts",id:o}]}),reactToPost:t.mutation({query:({postId:t,type:e})=>({url:`/social/posts/${t}/reactions`,method:"POST",body:{type:e}}),invalidatesTags:(t,e,{postId:s})=>[{type:"Posts",id:s}]}),removeReaction:t.mutation({query:({postId:t,type:e})=>({url:`/social/posts/${t}/reactions`,method:"DELETE",body:{type:e}}),invalidatesTags:(t,e,{postId:s})=>[{type:"Posts",id:s}]}),reactToComment:t.mutation({query:({commentId:t,type:e})=>({url:`/social/comments/${t}/reactions`,method:"POST",body:{type:e}}),invalidatesTags:(t,e,{commentId:s,postId:o})=>[{type:"Comments",id:s},{type:"Comments",id:o}]}),removeCommentReaction:t.mutation({query:({commentId:t,type:e})=>({url:`/social/comments/${t}/reactions`,method:"DELETE",body:{type:e}}),invalidatesTags:(t,e,{commentId:s,postId:o})=>[{type:"Comments",id:s},{type:"Comments",id:o}]}),getUserConnections:t.query({query:({userId:t,status:e,page:s=1,limit:o=10})=>{let i=`/social/users/${t}/connections?page=${s}&limit=${o}`;return e&&(i+=`&status=${e}`),i},providesTags:(t,e,{userId:s})=>t?[...t.connections.map(({id:t})=>({type:"Connections",id:t})),{type:"Connections",id:s}]:[{type:"Connections",id:s}]}),followUser:t.mutation({query:t=>({url:`/social/users/${t}/follow`,method:"POST"}),invalidatesTags:(t,e,s)=>[{type:"Connections",id:"LIST"},{type:"UserProfile",id:s}]}),unfollowUser:t.mutation({query:t=>({url:`/social/users/${t}/follow`,method:"DELETE"}),invalidatesTags:(t,e,s)=>[{type:"Connections",id:"LIST"},{type:"UserProfile",id:s}]}),getUserProfile:t.query({query:t=>`/social/users/profile/${t}`,providesTags:(t,e,s)=>[{type:"UserProfile",id:s}]}),getNotifications:t.query({query:({page:t=1,limit:e=10,unreadOnly:s})=>{let o=`/social/notifications?page=${t}&limit=${e}`;return s&&(o+="&unreadOnly=true"),o},providesTags:["Notifications"]}),markNotificationAsRead:t.mutation({query:t=>({url:`/social/notifications/${t}/read`,method:"POST"}),invalidatesTags:["Notifications"]}),markAllNotificationsAsRead:t.mutation({query:()=>({url:"/social/notifications/read-all",method:"POST"}),invalidatesTags:["Notifications"]}),getUserActivities:t.query({query:({page:t=1,limit:e=10,filters:s={}})=>{let o=`/social/activities?page=${t}&limit=${e}`;return s.types&&s.types.length>0&&(o+=`&types=${s.types.join(",")}`),s.userId&&(o+=`&userId=${s.userId}`),s.startDate&&(o+=`&startDate=${s.startDate}`),s.endDate&&(o+=`&endDate=${s.endDate}`),s.visibility&&(o+=`&visibility=${s.visibility}`),o},providesTags:["Activities"]})})}),{useGetPostsQuery:a,useGetPostQuery:n,useCreatePostMutation:r,useUpdatePostMutation:d,useDeletePostMutation:l,useGetCommentsQuery:m,useCreateCommentMutation:u,useUpdateCommentMutation:y,useDeleteCommentMutation:p,useReactToPostMutation:c,useRemoveReactionMutation:T,useReactToCommentMutation:g,useRemoveCommentReactionMutation:$,useGetUserConnectionsQuery:P,useFollowUserMutation:v,useUnfollowUserMutation:q,useGetUserProfileQuery:C,useGetNotificationsQuery:f,useMarkNotificationAsReadMutation:D,useMarkAllNotificationsAsReadMutation:I,useGetUserActivitiesQuery:h}=i}};