import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  Headers,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { RoutingService } from '../services/routing.service';

@ApiTags('carts')
@Controller('carts')
export class CartController {
  private readonly logger = new Logger(CartController.name);
  private readonly SERVICE_NAME = 'cart';

  constructor(private readonly routingService: RoutingService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new cart' })
  @ApiResponse({ status: 201, description: 'Cart created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  createCart(@Body() createCartDto: any, @Headers() headers: any): Observable<any> {
    this.logger.log('Routing cart creation request');
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, '/carts', 'POST', createCartDto, headers)
      .pipe(map((response) => response.data));
  }

  @Get('current')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get or create current user cart' })
  @ApiResponse({ status: 200, description: 'Current cart retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  getCurrentCart(@Headers() headers: any): Observable<any> {
    this.logger.log('Routing get current cart request');
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, '/carts/current', 'GET', null, headers)
      .pipe(map((response) => response.data));
  }

  @Get('guest')
  @ApiOperation({ summary: 'Get or create guest cart' })
  @ApiResponse({ status: 200, description: 'Guest cart retrieved successfully' })
  @ApiResponse({ status: 400, description: 'Session ID is required' })
  getGuestCart(@Query('sessionId') sessionId: string, @Headers() headers: any): Observable<any> {
    this.logger.log('Routing get guest cart request');
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, `/carts/guest?sessionId=${sessionId}`, 'GET', null, headers)
      .pipe(map((response) => response.data));
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get cart by ID' })
  @ApiResponse({ status: 200, description: 'Cart retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Cart not found' })
  getCart(@Param('id') id: string, @Headers() headers: any): Observable<any> {
    this.logger.log(`Routing get cart request for ID: ${id}`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, `/carts/${id}`, 'GET', null, headers)
      .pipe(map((response) => response.data));
  }

  @Post(':id/items')
  @ApiOperation({ summary: 'Add item to cart' })
  @ApiResponse({ status: 201, description: 'Item added to cart successfully' })
  @ApiResponse({ status: 404, description: 'Cart or product not found' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  addToCart(
    @Param('id') cartId: string,
    @Body() addToCartDto: any,
    @Headers() headers: any,
  ): Observable<any> {
    this.logger.log(`Routing add to cart request for cart: ${cartId}`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, `/carts/${cartId}/items`, 'POST', addToCartDto, headers)
      .pipe(map((response) => response.data));
  }

  @Put(':id/items/:itemId')
  @ApiOperation({ summary: 'Update cart item' })
  @ApiResponse({ status: 200, description: 'Cart item updated successfully' })
  @ApiResponse({ status: 404, description: 'Cart or cart item not found' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  updateCartItem(
    @Param('id') cartId: string,
    @Param('itemId') itemId: string,
    @Body() updateCartItemDto: any,
    @Headers() headers: any,
  ): Observable<any> {
    this.logger.log(`Routing update cart item request: ${cartId}/${itemId}`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, `/carts/${cartId}/items/${itemId}`, 'PUT', updateCartItemDto, headers)
      .pipe(map((response) => response.data));
  }

  @Delete(':id/items/:itemId')
  @ApiOperation({ summary: 'Remove item from cart' })
  @ApiResponse({ status: 200, description: 'Item removed from cart successfully' })
  @ApiResponse({ status: 404, description: 'Cart or cart item not found' })
  removeCartItem(
    @Param('id') cartId: string,
    @Param('itemId') itemId: string,
    @Headers() headers: any,
  ): Observable<any> {
    this.logger.log(`Routing remove cart item request: ${cartId}/${itemId}`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, `/carts/${cartId}/items/${itemId}`, 'DELETE', null, headers)
      .pipe(map((response) => response.data));
  }

  @Delete(':id/items')
  @ApiOperation({ summary: 'Clear all items from cart' })
  @ApiResponse({ status: 200, description: 'Cart cleared successfully' })
  @ApiResponse({ status: 404, description: 'Cart not found' })
  clearCart(@Param('id') cartId: string, @Headers() headers: any): Observable<any> {
    this.logger.log(`Routing clear cart request for: ${cartId}`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, `/carts/${cartId}/items`, 'DELETE', null, headers)
      .pipe(map((response) => response.data));
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete cart' })
  @ApiResponse({ status: 204, description: 'Cart deleted successfully' })
  @ApiResponse({ status: 404, description: 'Cart not found' })
  deleteCart(@Param('id') cartId: string, @Headers() headers: any): Observable<any> {
    this.logger.log(`Routing delete cart request for: ${cartId}`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, `/carts/${cartId}`, 'DELETE', null, headers)
      .pipe(map((response) => response.data));
  }
}
