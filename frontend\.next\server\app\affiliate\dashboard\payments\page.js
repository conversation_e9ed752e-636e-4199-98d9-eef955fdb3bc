(()=>{var e={};e.id=6019,e.ids=[6019],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},72143:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>d.a,__next_app__:()=>x,originalPathname:()=>c,pages:()=>o,routeModule:()=>g,tree:()=>m});var r=t(67096),s=t(16132),n=t(37284),d=t.n(n),l=t(32564),i={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);t.d(a,i);let m=["",{children:["affiliate",{children:["dashboard",{children:["payments",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,77394)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\affiliate\\dashboard\\payments\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9291,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\affiliate\\dashboard\\payments\\page.tsx"],c="/affiliate/dashboard/payments/page",x={require:t,loadChunk:()=>Promise.resolve()},g=new r.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/affiliate/dashboard/payments/page",pathname:"/affiliate/dashboard/payments",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:m}})},60299:(e,a,t)=>{Promise.resolve().then(t.bind(t,97123))},97123:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>AffiliatePaymentsPage});var r=t(30784),s=t(9885),n=t(27870),d=t(14379),l=t(11440),i=t.n(l),m=t(59872),o=t(94820),c=t(26352);let affiliate_AffiliatePaymentsTable=({payments:e,isLoading:a=!1,className:t=""})=>{let{t:s}=(0,n.$G)("affiliate"),{isRtl:l}=(0,d.g)(),formatDate=e=>new Date(e).toLocaleDateString(void 0,{year:"numeric",month:"short",day:"numeric"}),formatCurrency=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e),getPaymentMethodLabel=e=>{switch(e){case c.sI.BANK_TRANSFER:return s("payments.bankTransfer","Bank Transfer");case c.sI.PAYPAL:return s("payments.paypal","PayPal");case c.sI.STORE_CREDIT:return s("payments.storeCredit","Store Credit");case c.sI.CRYPTO:return s("payments.crypto","Cryptocurrency");default:return e}},getStatusBadgeClass=e=>{switch(e){case c.DU.PENDING:return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";case c.DU.PROCESSING:return"bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";case c.DU.COMPLETED:return"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";case c.DU.FAILED:return"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400"}};return a?r.jsx("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden ${t}`,children:(0,r.jsxs)("div",{className:"animate-pulse",children:[r.jsx("div",{className:"h-12 bg-gray-100 dark:bg-gray-700"}),Array.from({length:3}).map((e,a)=>r.jsx("div",{className:"h-16 border-t border-gray-200 dark:border-gray-700 px-4 py-3",children:(0,r.jsxs)("div",{className:"grid grid-cols-5 gap-4",children:[r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-600 rounded"}),r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-600 rounded"}),r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-600 rounded"}),r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-600 rounded"}),r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-600 rounded"})]})},a))]})}):0===e.length?(0,r.jsxs)("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 text-center ${t}`,children:[r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-12 w-12 text-gray-400 dark:text-gray-600 mx-auto mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"})}),r.jsx("p",{className:"text-gray-500 dark:text-gray-400 mb-4",children:s("payments.noPayments","No payments found")})]}):r.jsx("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden ${t}`,children:r.jsx("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[r.jsx("thead",{className:"bg-gray-50 dark:bg-gray-900",children:(0,r.jsxs)("tr",{children:[r.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:s("payments.paymentId","Payment ID")}),r.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:s("payments.amount","Amount")}),r.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:s("payments.method","Method")}),r.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:s("payments.status","Status")}),r.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:s("payments.date","Date")})]})}),r.jsx("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:e.map(e=>(0,r.jsxs)("tr",{children:[r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100",children:e.id.substring(0,8)}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-green-600 dark:text-green-400",children:formatCurrency(e.amount)}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:getPaymentMethodLabel(e.paymentMethod)}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm",children:r.jsx("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${getStatusBadgeClass(e.status)}`,children:s(`payments.${e.status.toLowerCase()}`,e.status)})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:formatDate(e.createdAt)})]},e.id))})]})})})},affiliate_AffiliatePaymentRequestForm=({account:e,onSubmit:a,isLoading:t=!1,className:l=""})=>{let{t:i}=(0,n.$G)("affiliate"),{isRtl:o}=(0,d.g)(),[x,g]=(0,s.useState)(e.balance),[y,u]=(0,s.useState)(e.preferredPaymentMethod||c.sI.BANK_TRANSFER),formatCurrency=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e),p=x>0&&x<=e.balance&&x>=(e.program?.minimumPayoutAmount||0);return(0,r.jsxs)("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 ${l}`,children:[r.jsx("h2",{className:"text-xl font-bold text-gray-900 dark:text-gray-100 mb-4",children:i("payments.requestPayment","Request Payment")}),r.jsx("div",{className:"mb-6",children:(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-900 p-4 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[r.jsx("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:i("account.availableBalance","Available Balance")}),r.jsx("span",{className:"text-lg font-bold text-green-600 dark:text-green-400",children:formatCurrency(e.balance)})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[r.jsx("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:i("payments.minimumPayout","Minimum Payout")}),r.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:formatCurrency(e.program?.minimumPayoutAmount||0)})]})]})}),(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),a({amount:x,paymentMethod:y})},children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsxs)("label",{htmlFor:"amount",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[i("payments.amount","Amount")," *"]}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:r.jsx("span",{className:"text-gray-500 dark:text-gray-400 sm:text-sm",children:"$"})}),r.jsx("input",{id:"amount",type:"number",value:x,onChange:e=>g(parseFloat(e.target.value)),min:e.program?.minimumPayoutAmount||0,max:e.balance,step:"0.01",className:"pl-7 w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100",required:!0,disabled:t})]}),x<(e.program?.minimumPayoutAmount||0)&&r.jsx("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:i("payments.amountBelowMinimum","Amount is below the minimum payout threshold")}),x>e.balance&&r.jsx("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:i("payments.amountExceedsBalance","Amount exceeds your available balance")})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("label",{htmlFor:"paymentMethod",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[i("payments.method","Payment Method")," *"]}),r.jsx("select",{id:"paymentMethod",value:y,onChange:e=>u(e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100",required:!0,disabled:t,children:e.program?.availablePaymentMethods.map(e=>r.jsx("option",{value:e,children:i(`payments.${e.toLowerCase()}`,e)},e))})]}),r.jsx("div",{className:"flex justify-end",children:r.jsx(m.Z,{type:"submit",isLoading:t,disabled:t||!p,children:i("payments.requestPayment","Request Payment")})})]})]})};var x=t(3619);function AffiliatePaymentsPage(){let{t:e}=(0,n.$G)("affiliate"),{isRtl:a}=(0,d.g)(),[t,l]=(0,s.useState)(!1),[c,g]=(0,s.useState)("all"),{data:y,isLoading:u}=(0,x.nK)({}),p=y?.accounts?.[0],{data:h,isLoading:b,refetch:f}=(0,x.J9)({accountId:p?.id||"",status:"all"!==c?c:void 0},{skip:!p}),[j,{isLoading:v}]=(0,x.gd)(),k=[{value:"all",label:e("payments.filter.all","All")},{value:"pending",label:e("payments.filter.pending","Pending")},{value:"processing",label:e("payments.filter.processing","Processing")},{value:"completed",label:e("payments.filter.completed","Completed")},{value:"failed",label:e("payments.filter.failed","Failed")}],handleRequestPayment=async e=>{if(p)try{await j({accountId:p.id,amount:e.amount,paymentMethod:e.paymentMethod}).unwrap(),f(),l(!1),console.log("Payment request submitted successfully")}catch(e){console.error("Failed to request payment:",e)}};return r.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2",children:e("payments.title","Payments")}),r.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:e("payments.description","Track your affiliate payments")})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[r.jsx("div",{className:"lg:col-span-1",children:r.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4",children:r.jsx(o.Z,{})})}),r.jsx("div",{className:"lg:col-span-3",children:p?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[r.jsx("h2",{className:"text-xl font-bold text-gray-900 dark:text-gray-100",children:e("payments.yourPayments","Your Payments")}),p?.balance>0&&r.jsx(m.Z,{onClick:()=>l(!t),variant:t?"outline":"primary",children:t?e("common.cancel","Cancel"):e("payments.requestPayment","Request Payment")})]}),t&&p&&r.jsx("div",{className:"mb-6",children:r.jsx(affiliate_AffiliatePaymentRequestForm,{account:p,onSubmit:handleRequestPayment,isLoading:v})}),r.jsx("div",{className:"mb-6",children:r.jsx("div",{className:"flex flex-wrap items-center justify-end",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("label",{htmlFor:"status-filter",className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:[e("payments.status","Status"),":"]}),r.jsx("select",{id:"status-filter",value:c,onChange:e=>g(e.target.value),className:"px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 text-sm",children:k.map(e=>r.jsx("option",{value:e.value,children:e.label},e.value))})]})})}),r.jsx(affiliate_AffiliatePaymentsTable,{payments:h?.payments||[],isLoading:b})]}):r.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6",children:(0,r.jsxs)("div",{className:"text-center py-8",children:[r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-16 w-16 text-gray-400 dark:text-gray-600 mx-auto mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"})}),r.jsx("h3",{className:"text-xl font-medium text-gray-900 dark:text-gray-100 mb-2",children:e("noAffiliateAccount","No Affiliate Account")}),r.jsx("p",{className:"text-gray-500 dark:text-gray-400 mb-6 max-w-md mx-auto",children:e("joinProgramFirst","Join an affiliate program to request payments")}),r.jsx(i(),{href:"/affiliate/programs",children:r.jsx(m.Z,{variant:"primary",children:e("browsePrograms","Browse Programs")})})]})})})]})]})})}},77394:(e,a,t)=>{"use strict";t.r(a),t.d(a,{$$typeof:()=>d,__esModule:()=>n,default:()=>i});var r=t(95153);let s=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\affiliate\dashboard\payments\page.tsx`),{__esModule:n,$$typeof:d}=s,l=s.default,i=l}};var a=require("../../../../webpack-runtime.js");a.C(e);var __webpack_exec__=e=>a(a.s=e),t=a.X(0,[2103,2765,3619,9522,6352],()=>__webpack_exec__(72143));module.exports=t})();