import { IsS<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Optional, IsObject } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class SendSmsDto {
  @ApiProperty({ description: 'Recipient phone number' })
  @IsPhoneNumber()
  to: string;

  @ApiProperty({ description: 'SMS message content' })
  @IsString()
  message: string;

  @ApiPropertyOptional({ description: 'SMS template ID' })
  @IsOptional()
  @IsString()
  templateId?: string;

  @ApiPropertyOptional({ description: 'Template data for dynamic content' })
  @IsOptional()
  @IsObject()
  templateData?: Record<string, any>;

  @ApiPropertyOptional({ description: 'Additional metadata' })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
