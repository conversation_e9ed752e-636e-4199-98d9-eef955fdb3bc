(()=>{var e={};e.id=5470,e.ids=[5470],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},29321:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var t=r(67096),a=r(16132),i=r(37284),n=r.n(i),l=r(32564),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);r.d(s,d);let c=["",{children:["fok",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,54974)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\fok\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9291,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\fok\\[id]\\page.tsx"],m="/fok/[id]/page",u={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/fok/[id]/page",pathname:"/fok/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},5678:(e,s,r)=>{Promise.resolve().then(r.bind(r,6465))},6465:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>GroupBuyDetailPage});var t=r(30784);r(9885);var a=r(57114),i=r(59872),n=r(52451),l=r.n(n),d=r(73531);let groupbuy_GroupBuyDetail=({groupBuy:e,onJoin:s,isJoining:r=!1})=>{var a;let n=(0,d.Z)(new Date(e.expiresAt),{addSuffix:!0});return(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden",children:[(0,t.jsxs)("div",{className:"relative h-64 bg-gray-200 dark:bg-gray-700",children:[e.imageUrl?t.jsx(l(),{src:e.imageUrl,alt:e.title,fill:!0,className:"object-cover"}):t.jsx("div",{className:"flex items-center justify-center h-full text-gray-400",children:"No Image"}),t.jsx("div",{className:"absolute top-4 right-4",children:t.jsx("span",{className:`px-3 py-1 rounded-full text-sm font-medium ${"active"===e.status?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"completed"===e.status?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200":"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"}`,children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})})]}),(0,t.jsxs)("div",{className:"p-6",children:[t.jsx("h1",{className:"text-2xl font-bold",children:e.title}),t.jsx("p",{className:"text-2xl text-primary-600 font-bold mt-2",children:(a=e.price,new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(a))}),e.description&&(0,t.jsxs)("div",{className:"mt-4",children:[t.jsx("h2",{className:"text-lg font-semibold mb-2",children:"Description"}),t.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:e.description})]}),(0,t.jsxs)("div",{className:"mt-6",children:[t.jsx("h2",{className:"text-lg font-semibold mb-2",children:"Group Buy Status"}),(0,t.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[t.jsx("span",{children:"Progress"}),(0,t.jsxs)("span",{children:[e.currentParticipants,"/",e.minParticipants," joined"]})]}),t.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 mb-4",children:t.jsx("div",{className:"bg-primary-600 h-2.5 rounded-full",style:{width:`${Math.min(100,Math.round(e.currentParticipants/e.minParticipants*100))}%`}})}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:justify-between gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:"Minimum Participants:"})," ",e.minParticipants]}),(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:"Maximum Participants:"})," ",e.maxParticipants]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("span",{className:"font-medium",children:["active"===e.status?"Ends":"Ended",":"]})," ",n]})]})]}),"active"===e.status&&s&&t.jsx("div",{className:"mt-6",children:t.jsx(i.Z,{onClick:s,isLoading:r,fullWidth:!0,children:"Join Group Buy"})})]})]})},groupbuy_ParticipantsList=({participants:e,isLoading:s=!1})=>s?(0,t.jsxs)("div",{className:"py-4 text-center",children:[t.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-600 mx-auto"}),t.jsx("p",{className:"mt-2 text-sm text-gray-500",children:"Loading participants..."})]}):e&&0!==e.length?t.jsx("div",{className:"divide-y divide-gray-200 dark:divide-gray-700",children:e.map(e=>(0,t.jsxs)("div",{className:"py-4 flex items-center",children:[t.jsx("div",{className:"relative h-10 w-10 rounded-full overflow-hidden bg-gray-200 dark:bg-gray-700",children:e.profileImageUrl?t.jsx(l(),{src:e.profileImageUrl,alt:e.username,fill:!0,className:"object-cover"}):t.jsx("div",{className:"flex items-center justify-center h-full text-sm font-medium text-gray-500",children:e.username.charAt(0).toUpperCase()})}),(0,t.jsxs)("div",{className:"ml-3",children:[t.jsx("p",{className:"text-sm font-medium",children:e.username}),(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:["Joined ",(0,d.Z)(new Date(e.joinedAt),{addSuffix:!0})]})]})]},e.id))}):t.jsx("div",{className:"py-4 text-center text-gray-500",children:"No participants yet. Be the first to join!"});var c=r(84105);function GroupBuyDetailPage({params:e}){let{id:s}=e,r=(0,a.useRouter)(),{data:n,isLoading:l,error:d}=(0,c.JG)(s),{data:o,isLoading:m}=(0,c._c)({groupBuyId:s}),[u,{isLoading:x}]=(0,c.cJ)(),handleJoinGroupBuy=async()=>{try{await u(s).unwrap(),alert("Successfully joined the group buy!")}catch(e){console.error("Failed to join group buy:",e),alert(e.data?.message||"Failed to join group buy. Please try again.")}};return l?t.jsx("div",{className:"min-h-screen p-6",children:t.jsx("div",{className:"max-w-4xl mx-auto",children:t.jsx("div",{className:"flex items-center justify-center py-12",children:t.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"})})})}):d||!n?t.jsx("div",{className:"min-h-screen p-6",children:t.jsx("div",{className:"max-w-4xl mx-auto",children:(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center",children:[t.jsx("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:"Group Buy Not Found"}),t.jsx("p",{className:"mb-6",children:"The group buy you're looking for doesn't exist or has been removed."}),t.jsx(i.Z,{onClick:()=>r.push("/fok"),children:"Back to Group Buys"})]})})}):t.jsx("div",{className:"min-h-screen p-6",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto",children:[t.jsx("div",{className:"mb-6",children:(0,t.jsxs)("button",{onClick:()=>r.push("/fok"),className:"text-primary-600 hover:text-primary-700 flex items-center",children:[t.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-1",viewBox:"0 0 20 20",fill:"currentColor",children:t.jsx("path",{fillRule:"evenodd",d:"M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z",clipRule:"evenodd"})}),"Back to Group Buys"]})}),t.jsx(groupbuy_GroupBuyDetail,{groupBuy:n,onJoin:handleJoinGroupBuy,isJoining:x}),(0,t.jsxs)("div",{className:"mt-8 bg-white dark:bg-gray-800 rounded-lg shadow-md p-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-semibold mb-4",children:["Participants (",o?.participants.length||0,"/",n.maxParticipants,")"]}),t.jsx(groupbuy_ParticipantsList,{participants:o?.participants||[],isLoading:m})]})]})})}},54974:(e,s,r)=>{"use strict";r.r(s),r.d(s,{$$typeof:()=>n,__esModule:()=>i,default:()=>d});var t=r(95153);let a=(0,t.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\fok\[id]\page.tsx`),{__esModule:i,$$typeof:n}=a,l=a.default,d=l}};var s=require("../../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),r=s.X(0,[2103,2765,4105],()=>__webpack_exec__(29321));module.exports=r})();