import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { StoreController } from './controllers/store.controller';
import { StoreService } from './services/store.service';
import { StoreRepository } from './repositories/store.repository';
import { Store } from './entities/store.entity';
import { HttpModule } from '@nestjs/axios';

@Module({
  imports: [
    TypeOrmModule.forFeature([Store]),
    HttpModule,
  ],
  controllers: [StoreController],
  providers: [
    StoreService,
    StoreRepository,
  ],
  exports: [
    StoreService,
    StoreRepository,
  ],
})
export class StoreManagementModule {}
