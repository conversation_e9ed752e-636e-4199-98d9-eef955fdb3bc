# Build Stage
FROM node:18-alpine AS build

WORKDIR /app

# Copy package.json and package-lock.json
COPY services/api-gateway/package*.json ./

# Copy shared libraries (from project root context)
COPY libs/common ./libs/common

# Install dependencies
RUN npm install --legacy-peer-deps

# Copy source code
COPY services/api-gateway/src ./src
COPY services/api-gateway/tsconfig.json ./
COPY services/api-gateway/nest-cli.json ./

# Build the application
RUN npm run build

# Production Stage
FROM node:18-alpine

WORKDIR /app

# Copy package.json and package-lock.json
COPY services/api-gateway/package*.json ./

# Install production dependencies only
RUN npm install --only=production --legacy-peer-deps

# Copy built application from build stage
COPY --from=build /app/dist ./dist
COPY --from=build /app/libs ./libs

# Set environment variables
ENV NODE_ENV=production

# Create logs directory and set permissions
RUN mkdir -p logs

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001

# Change ownership of logs directory to nestjs user
RUN chown -R nestjs:nodejs logs

USER nestjs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

# Start the application (FIXED: added .js extension)
CMD ["node", "dist/main.js"]
