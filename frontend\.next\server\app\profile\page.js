(()=>{var e={};e.id=4178,e.ids=[4178],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},36213:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>d});var t=s(67096),a=s(16132),i=s(37284),n=s.n(i),l=s(32564),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(r,o);let d=["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,38461)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\profile\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9291,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\profile\\page.tsx"],m="/profile/page",x={require:s,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},43875:(e,r,s)=>{Promise.resolve().then(s.bind(s,55011))},55011:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>ProfilePage});var t=s(30784),a=s(9885),i=s(57114),n=s(27870),l=s(14379),o=s(52451),d=s.n(o),c=s(59872);let user_ProfileHeader=({user:e,onEditProfile:r})=>t.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row items-center",children:[t.jsx("div",{className:"relative w-24 h-24 md:w-32 md:h-32 mb-4 md:mb-0 md:mr-6",children:t.jsx(d(),{src:`https://ui-avatars.com/api/?name=${e.username}&background=random`,alt:e.username,fill:!0,className:"rounded-full object-cover"})}),(0,t.jsxs)("div",{className:"flex-1 text-center md:text-left",children:[t.jsx("h1",{className:"text-2xl font-bold",children:e.username}),(0,t.jsxs)("div",{className:"mt-2 text-gray-600 dark:text-gray-400",children:[e.email&&(0,t.jsxs)("div",{className:"flex items-center justify-center md:justify-start",children:[t.jsx("span",{className:"mr-2",children:"Email:"}),t.jsx("span",{children:e.email}),e.isEmailVerified?t.jsx("span",{className:"ml-2 text-green-500 text-sm",children:"✓ Verified"}):t.jsx("span",{className:"ml-2 text-yellow-500 text-sm",children:"Not verified"})]}),e.phone&&(0,t.jsxs)("div",{className:"flex items-center justify-center md:justify-start mt-1",children:[t.jsx("span",{className:"mr-2",children:"Phone:"}),t.jsx("span",{children:e.phone}),e.isPhoneVerified?t.jsx("span",{className:"ml-2 text-green-500 text-sm",children:"✓ Verified"}):t.jsx("span",{className:"ml-2 text-yellow-500 text-sm",children:"Not verified"})]})]})]}),t.jsx("div",{className:"mt-4 md:mt-0",children:t.jsx(c.Z,{onClick:r,variant:"outline",children:"Edit Profile"})})]})});var m=s(706),x=s(19923);let user_ProfileEditForm=({user:e,onCancel:r,onSuccess:s})=>{let[i,n]=(0,a.useState)({username:e.username,email:e.email||"",phone:e.phone||""}),[l,o]=(0,a.useState)({}),[d,{isLoading:h}]=(0,x.TG)(),handleChange=e=>{let{name:r,value:s}=e.target;n(e=>({...e,[r]:s})),l[r]&&o(e=>({...e,[r]:""}))},validateForm=()=>{let e={};return i.username.trim()||(e.username="Username is required"),i.email&&!/\S+@\S+\.\S+/.test(i.email)&&(e.email="Email is invalid"),i.phone&&!/^\+?[0-9]{10,15}$/.test(i.phone)&&(e.phone="Phone number is invalid"),i.email||i.phone||(e.email="Either email or phone is required",e.phone="Either email or phone is required"),o(e),0===Object.keys(e).length},handleSubmit=async e=>{if(e.preventDefault(),validateForm())try{await d({username:i.username,email:i.email||void 0,phone:i.phone||void 0}).unwrap(),s()}catch(r){let e=r.data?.message||"An error occurred";o({form:e})}},handleResendVerification=async e=>{alert(`Verification ${e} sent!`)};return(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6",children:[t.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Edit Profile"}),l.form&&t.jsx("div",{className:"p-4 mb-4 text-red-700 bg-red-100 rounded-md dark:bg-red-900 dark:text-red-100",children:l.form}),(0,t.jsxs)("form",{onSubmit:handleSubmit,children:[t.jsx(m.Z,{label:"Username",name:"username",value:i.username,onChange:handleChange,error:l.username}),(0,t.jsxs)("div",{className:"flex flex-col md:flex-row md:items-end gap-4",children:[t.jsx("div",{className:"flex-1",children:t.jsx(m.Z,{label:"Email",name:"email",type:"email",value:i.email,onChange:handleChange,error:l.email})}),e.email&&!e.isEmailVerified&&t.jsx(c.Z,{type:"button",variant:"secondary",size:"sm",onClick:()=>handleResendVerification("email"),children:"Resend Verification"})]}),(0,t.jsxs)("div",{className:"flex flex-col md:flex-row md:items-end gap-4",children:[t.jsx("div",{className:"flex-1",children:t.jsx(m.Z,{label:"Phone",name:"phone",type:"tel",value:i.phone,onChange:handleChange,error:l.phone})}),e.phone&&!e.isPhoneVerified&&t.jsx(c.Z,{type:"button",variant:"secondary",size:"sm",onClick:()=>handleResendVerification("phone"),children:"Resend Verification"})]}),(0,t.jsxs)("div",{className:"flex justify-end gap-3 mt-6",children:[t.jsx(c.Z,{type:"button",variant:"outline",onClick:r,children:"Cancel"}),t.jsx(c.Z,{type:"submit",isLoading:h,children:"Save Changes"})]})]})]})};var h=s(70661);function ProfilePage(){let[e,r]=(0,a.useState)(!1),s=(0,i.useRouter)(),{t:o}=(0,n.$G)("auth"),{isRtl:d}=(0,l.g)(),{data:m,isLoading:u,error:p}=(0,x.Mx)(),[f,{isLoading:g}]=(0,x._y)(),handleLogout=async()=>{try{await f().unwrap(),s.push("/")}catch(e){console.error("Failed to log out:",e)}};return u?t.jsx("div",{className:"flex min-h-screen flex-col items-center justify-center p-8 md:p-24",children:(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600 mx-auto"}),t.jsx("p",{className:"mt-4 text-lg",children:o("common.loading")})]})}):p?t.jsx("div",{className:`flex min-h-screen flex-col items-center justify-center p-8 md:p-24 ${d?"text-right":"text-left"}`,children:(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:o("profile.errorLoading","Error Loading Profile")}),t.jsx("p",{className:"mb-6",children:o("profile.errorMessage","There was an error loading your profile. Please try again later.")}),t.jsx(c.Z,{onClick:()=>s.push("/"),children:o("common.backToHome")})]})}):m?t.jsx(h.Z,{children:t.jsx("div",{className:`min-h-screen p-6 md:p-12 ${d?"text-right":"text-left"}`,children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto",children:[t.jsx("h1",{className:"text-3xl font-bold mb-8",children:o("profile.title")}),e?t.jsx(user_ProfileEditForm,{user:m,onCancel:()=>r(!1),onSuccess:()=>r(!1)}):(0,t.jsxs)(t.Fragment,{children:[t.jsx(user_ProfileHeader,{user:m,onEditProfile:()=>r(!0)}),(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6",children:[t.jsx("h2",{className:"text-xl font-semibold mb-4",children:o("profile.accountActions","Account Actions")}),(0,t.jsxs)("div",{className:"space-y-4",children:[t.jsx("div",{children:t.jsx(c.Z,{variant:"outline",onClick:()=>s.push("/stores"),fullWidth:!0,children:o("profile.manageStores","Manage My Stores")})}),t.jsx("div",{children:t.jsx(c.Z,{variant:"outline",onClick:()=>s.push("/orders"),fullWidth:!0,children:o("profile.viewOrders","View My Orders")})}),t.jsx("div",{children:t.jsx(c.Z,{variant:"outline",onClick:()=>s.push("/settings"),fullWidth:!0,children:o("profile.settings","Account Settings")})}),t.jsx("div",{children:t.jsx(c.Z,{variant:"danger",onClick:handleLogout,isLoading:g,fullWidth:!0,children:o("navigation:logout","Logout")})})]})]})]})]})})}):(s.push("/login"),null)}},38461:(e,r,s)=>{"use strict";s.r(r),s.d(r,{$$typeof:()=>n,__esModule:()=>i,default:()=>o});var t=s(95153);let a=(0,t.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\profile\page.tsx`),{__esModule:i,$$typeof:n}=a,l=a.default,o=l}};var r=require("../../webpack-runtime.js");r.C(e);var __webpack_exec__=e=>r(r.s=e),s=r.X(0,[2103,2765,706,661],()=>__webpack_exec__(36213));module.exports=s})();