{"version": 3, "file": "rabbitmq.module.js", "sourceRoot": "", "sources": ["../../src/rabbitmq/rabbitmq.module.ts"], "names": [], "mappings": ";;;;;;;;;;AAAA,2CAAiE;AACjE,yDAAqD;AAI9C,IAAM,cAAc,sBAApB,MAAM,cAAc;IACzB,MAAM,CAAC,QAAQ,CAAC,OAAyB;QACvC,MAAM,SAAS,GAAe;YAC5B;gBACE,OAAO,EAAE,kCAAe;gBACxB,UAAU,EAAE,GAAG,EAAE;oBACf,OAAO,IAAI,kCAAe,CAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,GAAG,CAAC,CAAC;gBAC3C,CAAC;aACF;SACF,CAAC;QAEF,OAAO;YACL,MAAM,EAAE,gBAAc;YACtB,SAAS;YACT,OAAO,EAAE,CAAC,kCAAe,CAAC;SAC3B,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,aAAa,CAAC,OAGpB;QACC,MAAM,SAAS,GAAe;YAC5B;gBACE,OAAO,EAAE,kCAAe;gBACxB,UAAU,EAAE,KAAK,EAAE,GAAG,IAAW,EAAE,EAAE;oBACnC,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,CAAC;oBACjD,OAAO,IAAI,kCAAe,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBACzC,CAAC;gBACD,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,EAAE;aAC7B;SACF,CAAC;QAEF,OAAO;YACL,MAAM,EAAE,gBAAc;YACtB,SAAS;YACT,OAAO,EAAE,CAAC,kCAAe,CAAC;SAC3B,CAAC;IACJ,CAAC;CACF,CAAA;AAvCY,wCAAc;yBAAd,cAAc;IAD1B,IAAA,eAAM,EAAC,EAAE,CAAC;GACE,cAAc,CAuC1B"}