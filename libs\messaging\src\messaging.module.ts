import { Module, DynamicModule, Global } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { EventPublisherService } from './services/event-publisher.service';
import { EventSubscriberService } from './services/event-subscriber.service';

@Global()
@Module({})
export class MessagingModule {
  static register(options: {
    rabbitmqUrl: string;
    serviceName: string;
  }): DynamicModule {
    return {
      module: MessagingModule,
      imports: [
        ClientsModule.register([
          {
            name: 'EVENT_BUS',
            transport: Transport.RMQ,
            options: {
              urls: [options.rabbitmqUrl],
              queue: `${options.serviceName}_events`,
              queueOptions: {
                durable: true,
              },
            },
          },
        ]),
      ],
      providers: [
        {
          provide: 'MESSAGING_OPTIONS',
          useValue: options,
        },
        EventPublisherService,
        EventSubscriberService,
      ],
      exports: [EventPublisherService, EventSubscriberService],
    };
  }
}
