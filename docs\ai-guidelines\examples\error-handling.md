# Error Handling Example

This document provides an example of correctly implemented error handling following the Social Commerce Platform's patterns and best practices.

## Error Handling in Services

```typescript
import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from './entities/user.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { AppError, ErrorCode } from '@app/common';

@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  async create(createUserDto: CreateUserDto): Promise<User> {
    this.logger.log(`Creating user with email: ${createUserDto.email}`);

    try {
      // Check if user with email already exists
      const existingUser = await this.userRepository.findOne({
        where: { email: createUserDto.email },
      });

      if (existingUser) {
        throw new AppError({
          code: ErrorCode.USER_ALREADY_EXISTS,
          message: `User with email ${createUserDto.email} already exists`,
          translationKey: 'errors.user.alreadyExists',
        }, 400);
      }

      // Create new user
      const user = this.userRepository.create(createUserDto);
      return await this.userRepository.save(user);
    } catch (error) {
      // If it's already an AppError, rethrow it
      if (error instanceof AppError) {
        throw error;
      }

      // Handle database-specific errors
      if (error.code === '23505') { // PostgreSQL unique violation
        throw new AppError({
          code: ErrorCode.USER_ALREADY_EXISTS,
          message: `User with email ${createUserDto.email} already exists`,
          translationKey: 'errors.user.alreadyExists',
          details: error
        }, 400);
      }

      // Log and rethrow as a generic error
      this.logger.error(`Error creating user: ${error.message}`, error.stack);
      throw new AppError({
        code: ErrorCode.INTERNAL_ERROR,
        message: 'Failed to create user',
        translationKey: 'errors.user.createFailed',
        details: error
      }, 500);
    }
  }

  async findOne(id: string): Promise<User> {
    this.logger.log(`Finding user with ID: ${id}`);

    try {
      const user = await this.userRepository.findOne({ where: { id } });

      if (!user) {
        throw new AppError({
          code: ErrorCode.USER_NOT_FOUND,
          message: `User with ID ${id} not found`,
          translationKey: 'errors.user.notFound',
        }, 404);
      }

      return user;
    } catch (error) {
      // If it's already an AppError, rethrow it
      if (error instanceof AppError) {
        throw error;
      }

      // Log and rethrow as a generic error
      this.logger.error(`Error finding user: ${error.message}`, error.stack);
      throw new AppError({
        code: ErrorCode.INTERNAL_ERROR,
        message: 'Failed to find user',
        translationKey: 'errors.user.findFailed',
        details: error
      }, 500);
    }
  }
}
```

## Key Points to Note

1. **Custom Error Class**: Using `AppError` for consistent error handling
2. **Error Codes**: Using predefined error codes from `ErrorCode` enum
3. **Translation Keys**: Including translation keys for internationalization
4. **Detailed Error Information**: Including details for debugging
5. **Proper Logging**: Logging errors with stack traces
6. **Error Classification**: Handling different types of errors differently
7. **HTTP Status Codes**: Setting appropriate HTTP status codes

## Common Mistakes to Avoid

1. ❌ Not using try/catch blocks
2. ❌ Not logging errors
3. ❌ Using generic error messages
4. ❌ Not including error details for debugging
5. ❌ Not handling specific error cases
6. ❌ Not using custom error classes
7. ❌ Not setting appropriate HTTP status codes

## Error Handling in Controllers

Controllers should catch errors from services and transform them into appropriate HTTP responses:

```typescript
@Get(':id')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@ApiOperation({ summary: 'Get a user by ID' })
@ApiResponse({ status: 200, description: 'Return the user', type: User })
@ApiResponse({ status: 404, description: 'User not found' })
async findOne(@Param('id') id: string): Promise<User> {
  this.logger.log(`Getting user with ID: ${id}`);
  
  try {
    return await this.userService.findOne(id);
  } catch (error) {
    this.logger.error(`Error getting user: ${error.message}`, error.stack);
    
    // Transform AppError into appropriate HTTP exception
    if (error instanceof AppError) {
      if (error.statusCode === 404) {
        throw new NotFoundException(error.message);
      } else if (error.statusCode === 400) {
        throw new BadRequestException(error.message);
      }
    }
    
    // Rethrow other errors
    throw error;
  }
}
```

By following these patterns, AI assistants can ensure they implement error handling that adheres to the project's standards and best practices.
