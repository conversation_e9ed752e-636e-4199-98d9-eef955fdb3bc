import * as path from 'path';
import * as fs from 'fs-extra';

/**
 * Get the root directory of the project
 * @returns Root directory path
 */
export function getRootDir(): string {
  // Start from the current directory
  let currentDir = process.cwd();
  
  // Go up the directory tree until we find the root directory
  // (which contains the package.json file with the name "social-commerce")
  while (currentDir !== path.parse(currentDir).root) {
    const packageJsonPath = path.join(currentDir, 'package.json');
    
    if (fs.existsSync(packageJsonPath)) {
      const packageJson = fs.readJsonSync(packageJsonPath);
      
      if (packageJson.name === 'social-commerce-refined') {
        return currentDir;
      }
    }
    
    // Go up one directory
    currentDir = path.dirname(currentDir);
  }
  
  // If we can't find the root directory, use the current directory
  return process.cwd();
}

/**
 * Get the services directory
 * @returns Services directory path
 */
export function getServicesDir(): string {
  return path.join(getRootDir(), 'services');
}

/**
 * Get the infrastructure directory
 * @returns Infrastructure directory path
 */
export function getInfrastructureDir(): string {
  return path.join(getRootDir(), 'infrastructure');
}

/**
 * Get the Docker Compose file path
 * @returns Docker Compose file path
 */
export function getDockerComposePath(): string {
  return path.join(getInfrastructureDir(), 'docker', 'docker-compose.yml');
}

/**
 * Get the path to a service directory
 * @param service Service name
 * @returns Service directory path
 */
export function getServiceDir(service: string): string {
  return path.join(getServicesDir(), `${service}-service`);
}

/**
 * Get all service names
 * @returns Array of service names
 */
export function getAllServices(): string[] {
  const servicesDir = getServicesDir();
  
  if (!fs.existsSync(servicesDir)) {
    return [];
  }
  
  return fs
    .readdirSync(servicesDir)
    .filter(dir => dir.endsWith('-service'))
    .map(dir => dir.replace('-service', ''));
}

/**
 * Check if a service exists
 * @param service Service name
 * @returns True if the service exists
 */
export function serviceExists(service: string): boolean {
  const serviceDir = getServiceDir(service);
  return fs.existsSync(serviceDir);
}
