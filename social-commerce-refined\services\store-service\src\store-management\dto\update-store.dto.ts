import { PartialType } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { CreateStoreDto } from './create-store.dto';
import { StoreStatus } from '../entities/store.entity';

export class UpdateStoreDto extends PartialType(CreateStoreDto) {
  @ApiPropertyOptional({
    description: 'Store status',
    enum: StoreStatus,
    example: StoreStatus.ACTIVE,
  })
  @IsEnum(StoreStatus)
  @IsOptional()
  status?: StoreStatus;
}
