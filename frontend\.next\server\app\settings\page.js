(()=>{var e={};e.id=6938,e.ids=[6938],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},44965:(e,i,a)=>{"use strict";a.r(i),a.d(i,{GlobalError:()=>n.a,__next_app__:()=>g,originalPathname:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>c});var t=a(67096),s=a(16132),r=a(37284),n=a.n(r),l=a(32564),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);a.d(i,o);let c=["",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,73859)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\settings\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,9291,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\settings\\page.tsx"],m="/settings/page",g={require:a,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/settings/page",pathname:"/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},9044:(e,i,a)=>{Promise.resolve().then(a.bind(a,47742))},47742:(e,i,a)=>{"use strict";a.r(i),a.d(i,{default:()=>SettingsPage});var t=a(30784),s=a(9885),r=a(57114),n=a(27870);let settings_SettingsTabs=({activeTab:e,onTabChange:i,tabs:a=["profile","account","notifications","privacy"]})=>{let{t:s}=(0,n.$G)("settings"),getTabLabel=e=>{switch(e){case"profile":return s("tabs.profile","Profile");case"account":return s("tabs.account","Account");case"notifications":return s("tabs.notifications","Notifications");case"privacy":return s("tabs.privacy","Privacy");case"security":return s("tabs.security","Security");case"billing":return s("tabs.billing","Billing");default:return String(e).charAt(0).toUpperCase()+String(e).slice(1)}};return t.jsx("div",{className:"border-b border-gray-200 dark:border-gray-700",children:t.jsx("nav",{className:"flex overflow-x-auto",children:a.map(a=>t.jsx("button",{onClick:()=>i(a),className:`py-4 px-6 text-center border-b-2 font-medium text-sm whitespace-nowrap ${e===a?"border-primary-500 text-primary-600 dark:text-primary-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"}`,children:getTabLabel(a)},a))})})},profile_ProfileSettings=({settings:e,isLoading:i=!1,className:a=""})=>{let{t:s}=(0,n.$G)("profile");return i?(0,t.jsxs)("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 ${a}`,children:[t.jsx("div",{className:"h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-6 animate-pulse"}),t.jsx("div",{className:"space-y-4",children:[void 0,void 0,void 0,void 0,void 0].map((e,i)=>(0,t.jsxs)("div",{className:"animate-pulse",children:[t.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-2"}),t.jsx("div",{className:"h-10 bg-gray-200 dark:bg-gray-700 rounded w-full"})]},i))})]}):(0,t.jsxs)("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 ${a}`,children:[t.jsx("h2",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4",children:s("settings","Settings")}),(0,t.jsxs)("div",{className:"mb-6",children:[t.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-3",children:s("emailNotifications","Email Notifications")}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:s("marketingEmails","Marketing emails")}),(0,t.jsxs)("div",{className:"relative inline-block w-10 mr-2 align-middle select-none",children:[t.jsx("input",{type:"checkbox",id:"marketing-emails",name:"marketing-emails",className:"sr-only",checked:e.emailNotifications.marketing,readOnly:!0}),t.jsx("label",{htmlFor:"marketing-emails",className:`block overflow-hidden h-6 rounded-full bg-gray-300 dark:bg-gray-600 cursor-pointer ${e.emailNotifications.marketing?"bg-primary-500":""}`,children:t.jsx("span",{className:`block h-6 w-6 rounded-full bg-white dark:bg-gray-800 shadow transform transition-transform ${e.emailNotifications.marketing?"translate-x-4":"translate-x-0"}`})})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:s("newFollowerEmails","New follower notifications")}),(0,t.jsxs)("div",{className:"relative inline-block w-10 mr-2 align-middle select-none",children:[t.jsx("input",{type:"checkbox",id:"new-follower-emails",name:"new-follower-emails",className:"sr-only",checked:e.emailNotifications.newFollower,readOnly:!0}),t.jsx("label",{htmlFor:"new-follower-emails",className:`block overflow-hidden h-6 rounded-full bg-gray-300 dark:bg-gray-600 cursor-pointer ${e.emailNotifications.newFollower?"bg-primary-500":""}`,children:t.jsx("span",{className:`block h-6 w-6 rounded-full bg-white dark:bg-gray-800 shadow transform transition-transform ${e.emailNotifications.newFollower?"translate-x-4":"translate-x-0"}`})})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:s("commentEmails","Comment notifications")}),(0,t.jsxs)("div",{className:"relative inline-block w-10 mr-2 align-middle select-none",children:[t.jsx("input",{type:"checkbox",id:"comment-emails",name:"comment-emails",className:"sr-only",checked:e.emailNotifications.newComment,readOnly:!0}),t.jsx("label",{htmlFor:"comment-emails",className:`block overflow-hidden h-6 rounded-full bg-gray-300 dark:bg-gray-600 cursor-pointer ${e.emailNotifications.newComment?"bg-primary-500":""}`,children:t.jsx("span",{className:`block h-6 w-6 rounded-full bg-white dark:bg-gray-800 shadow transform transition-transform ${e.emailNotifications.newComment?"translate-x-4":"translate-x-0"}`})})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:s("likeEmails","Like notifications")}),(0,t.jsxs)("div",{className:"relative inline-block w-10 mr-2 align-middle select-none",children:[t.jsx("input",{type:"checkbox",id:"like-emails",name:"like-emails",className:"sr-only",checked:e.emailNotifications.newLike,readOnly:!0}),t.jsx("label",{htmlFor:"like-emails",className:`block overflow-hidden h-6 rounded-full bg-gray-300 dark:bg-gray-600 cursor-pointer ${e.emailNotifications.newLike?"bg-primary-500":""}`,children:t.jsx("span",{className:`block h-6 w-6 rounded-full bg-white dark:bg-gray-800 shadow transform transition-transform ${e.emailNotifications.newLike?"translate-x-4":"translate-x-0"}`})})]})]})]})]}),(0,t.jsxs)("div",{className:"mb-6",children:[t.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-3",children:s("privacySettings","Privacy Settings")}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:s("profileVisibility","Profile Visibility")}),(0,t.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-md p-3 text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center mb-2",children:[t.jsx("input",{type:"radio",id:"profile-public",name:"profile-visibility",className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600",checked:"public"===e.privacySettings.profileVisibility,readOnly:!0}),t.jsx("label",{htmlFor:"profile-public",className:"ml-2 block text-gray-700 dark:text-gray-300",children:s("public","Public")})]}),(0,t.jsxs)("div",{className:"flex items-center mb-2",children:[t.jsx("input",{type:"radio",id:"profile-followers",name:"profile-visibility",className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600",checked:"followers"===e.privacySettings.profileVisibility,readOnly:!0}),t.jsx("label",{htmlFor:"profile-followers",className:"ml-2 block text-gray-700 dark:text-gray-300",children:s("followersOnly","Followers Only")})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("input",{type:"radio",id:"profile-private",name:"profile-visibility",className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600",checked:"private"===e.privacySettings.profileVisibility,readOnly:!0}),t.jsx("label",{htmlFor:"profile-private",className:"ml-2 block text-gray-700 dark:text-gray-300",children:s("private","Private")})]})]})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:s("activityVisibility","Activity Visibility")}),(0,t.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-md p-3 text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center mb-2",children:[t.jsx("input",{type:"radio",id:"activity-public",name:"activity-visibility",className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600",checked:"public"===e.privacySettings.activityVisibility,readOnly:!0}),t.jsx("label",{htmlFor:"activity-public",className:"ml-2 block text-gray-700 dark:text-gray-300",children:s("public","Public")})]}),(0,t.jsxs)("div",{className:"flex items-center mb-2",children:[t.jsx("input",{type:"radio",id:"activity-followers",name:"activity-visibility",className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600",checked:"followers"===e.privacySettings.activityVisibility,readOnly:!0}),t.jsx("label",{htmlFor:"activity-followers",className:"ml-2 block text-gray-700 dark:text-gray-300",children:s("followersOnly","Followers Only")})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("input",{type:"radio",id:"activity-private",name:"activity-visibility",className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600",checked:"private"===e.privacySettings.activityVisibility,readOnly:!0}),t.jsx("label",{htmlFor:"activity-private",className:"ml-2 block text-gray-700 dark:text-gray-300",children:s("private","Private")})]})]})]})]})]}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-3",children:s("preferences","Preferences")}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:s("language","Language")}),t.jsx("div",{className:"relative",children:(0,t.jsxs)("select",{id:"language",name:"language",className:"block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 dark:bg-gray-700 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md",value:e.language,disabled:!0,children:[t.jsx("option",{value:"en",children:"English"}),t.jsx("option",{value:"fa",children:"Persian (فارسی)"}),t.jsx("option",{value:"es",children:"Spanish (Espa\xf1ol)"}),t.jsx("option",{value:"fr",children:"French (Fran\xe7ais)"}),t.jsx("option",{value:"de",children:"German (Deutsch)"})]})})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:s("theme","Theme")}),t.jsx("div",{className:"relative",children:(0,t.jsxs)("select",{id:"theme",name:"theme",className:"block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 dark:bg-gray-700 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md",value:e.theme,disabled:!0,children:[t.jsx("option",{value:"light",children:s("light","Light")}),t.jsx("option",{value:"dark",children:s("dark","Dark")}),t.jsx("option",{value:"system",children:s("system","System")})]})})]})]})]})]})};var l=a(706),o=a(59872),c=a(19923);let settings_AccountSettings=({user:e})=>{let{t:i}=(0,n.$G)("settings"),[a,r]=(0,s.useState)({username:e.username,email:e.email||"",phone:e.phone||"",currentPassword:"",newPassword:"",confirmPassword:""}),[d,m]=(0,s.useState)({}),[g,{isLoading:x}]=(0,c.TG)(),[u,h]=(0,s.useState)(""),handleChange=e=>{let{name:i,value:a}=e.target;r(e=>({...e,[i]:a})),d[i]&&m(e=>({...e,[i]:""})),u&&h("")},validateForm=()=>{let e={};return a.username.trim()?a.username.length<3&&(e.username=i("account.errors.usernameTooShort","Username must be at least 3 characters")):e.username=i("account.errors.usernameRequired","Username is required"),a.email&&!/\S+@\S+\.\S+/.test(a.email)&&(e.email=i("account.errors.invalidEmail","Email is invalid")),a.phone&&!/^\+?[0-9]{10,15}$/.test(a.phone)&&(e.phone=i("account.errors.invalidPhone","Phone number is invalid")),a.email||a.phone||(e.email=i("account.errors.contactRequired","Either email or phone is required"),e.phone=i("account.errors.contactRequired","Either email or phone is required")),a.newPassword&&(a.currentPassword||(e.currentPassword=i("account.errors.currentPasswordRequired","Current password is required")),a.newPassword.length<8&&(e.newPassword=i("account.errors.passwordTooShort","Password must be at least 8 characters")),a.newPassword!==a.confirmPassword&&(e.confirmPassword=i("account.errors.passwordsDoNotMatch","Passwords do not match"))),m(e),0===Object.keys(e).length},handleSubmit=async e=>{if(e.preventDefault(),validateForm())try{await g({username:a.username,email:a.email||void 0,phone:a.phone||void 0}).unwrap(),h(i("account.success","Account updated successfully"))}catch(a){let e=a.data?.message||i("account.errors.updateFailed","Failed to update account");m({form:e})}},handleResendVerification=async e=>{alert(`Verification ${e} sent!`)},handleChangePassword=async e=>{e.preventDefault(),validateForm()&&alert("Password change functionality would be implemented here.")};return(0,t.jsxs)("div",{children:[t.jsx("h2",{className:"text-xl font-semibold mb-6",children:i("account.title","Account Settings")}),u&&t.jsx("div",{className:"p-4 mb-6 text-green-700 bg-green-100 rounded-md dark:bg-green-900 dark:text-green-100",children:u}),d.form&&t.jsx("div",{className:"p-4 mb-6 text-red-700 bg-red-100 rounded-md dark:bg-red-900 dark:text-red-100",children:d.form}),(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"text-lg font-medium mb-4",children:i("account.accountInfo","Account Information")}),(0,t.jsxs)("form",{onSubmit:handleSubmit,children:[t.jsx(l.Z,{label:i("account.username","Username"),name:"username",value:a.username,onChange:handleChange,error:d.username}),(0,t.jsxs)("div",{className:"flex flex-col md:flex-row md:items-end gap-4",children:[t.jsx("div",{className:"flex-1",children:t.jsx(l.Z,{label:i("account.email","Email"),name:"email",type:"email",value:a.email,onChange:handleChange,error:d.email})}),e.email&&!e.isEmailVerified&&t.jsx(o.Z,{type:"button",variant:"secondary",size:"sm",onClick:()=>handleResendVerification("email"),children:i("account.resendVerification","Resend Verification")})]}),(0,t.jsxs)("div",{className:"flex flex-col md:flex-row md:items-end gap-4",children:[t.jsx("div",{className:"flex-1",children:t.jsx(l.Z,{label:i("account.phone","Phone"),name:"phone",type:"tel",value:a.phone,onChange:handleChange,error:d.phone})}),e.phone&&!e.isPhoneVerified&&t.jsx(o.Z,{type:"button",variant:"secondary",size:"sm",onClick:()=>handleResendVerification("phone"),children:i("account.resendVerification","Resend Verification")})]}),t.jsx("div",{className:"flex justify-end mt-6",children:t.jsx(o.Z,{type:"submit",isLoading:x,children:i("common.saveChanges","Save Changes")})})]})]}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"text-lg font-medium mb-4",children:i("account.changePassword","Change Password")}),(0,t.jsxs)("form",{onSubmit:handleChangePassword,children:[t.jsx(l.Z,{label:i("account.currentPassword","Current Password"),name:"currentPassword",type:"password",value:a.currentPassword,onChange:handleChange,error:d.currentPassword}),t.jsx(l.Z,{label:i("account.newPassword","New Password"),name:"newPassword",type:"password",value:a.newPassword,onChange:handleChange,error:d.newPassword}),t.jsx(l.Z,{label:i("account.confirmPassword","Confirm New Password"),name:"confirmPassword",type:"password",value:a.confirmPassword,onChange:handleChange,error:d.confirmPassword}),t.jsx("div",{className:"flex justify-end mt-6",children:t.jsx(o.Z,{type:"submit",children:i("account.updatePassword","Update Password")})})]})]})]})]})};var d=a(29874),m=a(53717);let settings_NotificationSettings=()=>{let{t:e}=(0,n.$G)("settings"),[i,a]=(0,s.useState)(""),[r,l]=(0,s.useState)(""),{data:c,isLoading:g}=(0,m.H9)(),[x,{isLoading:u}]=(0,m.mT)(),[h,p]=(0,s.useState)([{id:"follow",title:e("notifications.newFollower","New Follower"),description:e("notifications.newFollowerDesc","When someone follows you"),email:!0,push:!0,sms:!1},{id:"like",title:e("notifications.newLike","New Like"),description:e("notifications.newLikeDesc","When someone likes your post or product"),email:!1,push:!0,sms:!1},{id:"comment",title:e("notifications.newComment","New Comment"),description:e("notifications.newCommentDesc","When someone comments on your post or product"),email:!0,push:!0,sms:!1},{id:"mention",title:e("notifications.mention","Mentions"),description:e("notifications.mentionDesc","When someone mentions you in a comment or post"),email:!0,push:!0,sms:!1},{id:"order",title:e("notifications.order","Orders"),description:e("notifications.orderDesc","Order updates and notifications"),email:!0,push:!0,sms:!0},{id:"payment",title:e("notifications.payment","Payments"),description:e("notifications.paymentDesc","Payment updates and notifications"),email:!0,push:!0,sms:!0},{id:"system",title:e("notifications.system","System"),description:e("notifications.systemDesc","System updates and important notifications"),email:!0,push:!1,sms:!1}]);(0,s.useEffect)(()=>{if(c){let e=[...h];Object.entries(c.email).forEach(([i,a])=>{let t=e.findIndex(e=>e.id===i);-1!==t&&(e[t].email=a)}),Object.entries(c.push).forEach(([i,a])=>{let t=e.findIndex(e=>e.id===i);-1!==t&&(e[t].push=a)}),p(e)}},[c]);let handleToggle=(e,t,s)=>{p(i=>i.map(i=>i.id===e?{...i,[t]:s}:i)),i&&a(""),r&&l("")},handleSave=async()=>{try{let i={email:{follow:!1,like:!1,comment:!1,mention:!1,order:!1,payment:!1,system:!1},push:{follow:!1,like:!1,comment:!1,mention:!1,order:!1,payment:!1,system:!1},inApp:{follow:!1,like:!1,comment:!1,mention:!1,order:!1,payment:!1,system:!1}};h.forEach(e=>{let a=e.id;i.email[a]=e.email,i.push[a]=e.push,i.inApp[a]=e.push}),await x(i).unwrap(),a(e("notifications.success","Notification settings updated successfully")),l("")}catch(i){console.error("Failed to update notification settings:",i),l(e("notifications.error","Failed to update notification settings. Please try again.")),a("")}};return g?(0,t.jsxs)("div",{className:"animate-pulse space-y-4",children:[t.jsx("div",{className:"h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"}),t.jsx("div",{className:"h-40 bg-gray-200 dark:bg-gray-700 rounded mb-4"}),t.jsx("div",{className:"h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"}),t.jsx("div",{className:"h-40 bg-gray-200 dark:bg-gray-700 rounded mb-4"}),t.jsx("div",{className:"h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"}),t.jsx("div",{className:"h-40 bg-gray-200 dark:bg-gray-700 rounded"})]}):(0,t.jsxs)("div",{children:[t.jsx("h2",{className:"text-xl font-semibold mb-6",children:e("notifications.title","Notification Settings")}),i&&t.jsx("div",{className:"p-4 mb-6 text-green-700 bg-green-100 rounded-md dark:bg-green-900 dark:text-green-100",children:i}),r&&t.jsx("div",{className:"p-4 mb-6 text-red-700 bg-red-100 rounded-md dark:bg-red-900 dark:text-red-100",children:r}),t.jsx("div",{className:"mb-6",children:t.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:e("notifications.description","Choose how you want to be notified about activity on your account.")})}),t.jsx("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[t.jsx("thead",{className:"bg-gray-50 dark:bg-gray-800",children:(0,t.jsxs)("tr",{children:[t.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("notifications.notification","Notification")}),t.jsx("th",{scope:"col",className:"px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("notifications.email","Email")}),t.jsx("th",{scope:"col",className:"px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("notifications.push","Push")}),t.jsx("th",{scope:"col",className:"px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("notifications.sms","SMS")})]})}),t.jsx("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:h.map(e=>(0,t.jsxs)("tr",{children:[(0,t.jsxs)("td",{className:"px-6 py-4",children:[t.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e.title}),t.jsx("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.description})]}),t.jsx("td",{className:"px-6 py-4 text-center",children:t.jsx(d.Z,{checked:e.email,onChange:i=>handleToggle(e.id,"email",i)})}),t.jsx("td",{className:"px-6 py-4 text-center",children:t.jsx(d.Z,{checked:e.push,onChange:i=>handleToggle(e.id,"push",i)})}),t.jsx("td",{className:"px-6 py-4 text-center",children:t.jsx(d.Z,{checked:e.sms,onChange:i=>handleToggle(e.id,"sms",i)})})]},e.id))})]})}),t.jsx("div",{className:"flex justify-end mt-6",children:t.jsx(o.Z,{onClick:handleSave,isLoading:u,disabled:u,children:e("common.saveChanges","Save Changes")})})]})},settings_PrivacySettings=()=>{let{t:e}=(0,n.$G)("settings"),[i,a]=(0,s.useState)(""),[r,l]=(0,s.useState)([{id:"profile_visibility",title:e("privacy.profileVisibility","Profile Visibility"),description:e("privacy.profileVisibilityDesc","Make your profile visible to everyone"),enabled:!0},{id:"activity_visibility",title:e("privacy.activityVisibility","Activity Visibility"),description:e("privacy.activityVisibilityDesc","Show your activity in feeds and on your profile"),enabled:!0},{id:"search_visibility",title:e("privacy.searchVisibility","Search Visibility"),description:e("privacy.searchVisibilityDesc","Allow your profile to appear in search results"),enabled:!0},{id:"location_sharing",title:e("privacy.locationSharing","Location Sharing"),description:e("privacy.locationSharingDesc","Share your location with other users"),enabled:!1},{id:"data_collection",title:e("privacy.dataCollection","Data Collection"),description:e("privacy.dataCollectionDesc","Allow us to collect data to improve your experience"),enabled:!0},{id:"personalized_ads",title:e("privacy.personalizedAds","Personalized Ads"),description:e("privacy.personalizedAdsDesc","Show personalized ads based on your activity"),enabled:!0}]),handleToggle=(e,t)=>{l(i=>i.map(i=>i.id===e?{...i,enabled:t}:i)),i&&a("")};return(0,t.jsxs)("div",{children:[t.jsx("h2",{className:"text-xl font-semibold mb-6",children:e("privacy.title","Privacy Settings")}),i&&t.jsx("div",{className:"p-4 mb-6 text-green-700 bg-green-100 rounded-md dark:bg-green-900 dark:text-green-100",children:i}),(0,t.jsxs)("div",{className:"space-y-6",children:[r.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e.title}),t.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.description})]}),t.jsx(d.Z,{checked:e.enabled,onChange:i=>handleToggle(e.id,i)})]},e.id)),t.jsx("div",{className:"flex justify-end mt-6",children:t.jsx(o.Z,{onClick:()=>{a(e("privacy.success","Privacy settings updated successfully"))},children:e("common.saveChanges","Save Changes")})}),(0,t.jsxs)("div",{className:"border-t border-gray-200 dark:border-gray-700 pt-6 mt-8",children:[t.jsx("h3",{className:"text-lg font-medium text-red-600 dark:text-red-400 mb-4",children:e("privacy.dangerZone","Danger Zone")}),t.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-4",children:e("privacy.deleteWarning","Once you delete your account, there is no going back. Please be certain.")}),t.jsx(o.Z,{variant:"danger",onClick:()=>{window.confirm(e("privacy.deleteConfirm","Are you sure you want to delete your account? This action cannot be undone."))&&alert(e("privacy.deleteSuccess","Account deletion request submitted. You will receive an email with further instructions."))},children:e("privacy.deleteAccount","Delete Account")})]})]})]})};var g=a(70661);function SettingsPage(){let[e,i]=(0,s.useState)("profile"),a=(0,r.useRouter)(),{t:l}=(0,n.$G)("settings"),{data:d,isLoading:m,error:x}=(0,c.Mx)();return m?t.jsx("div",{className:"flex min-h-screen flex-col items-center justify-center p-8 md:p-24",children:(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600 mx-auto"}),t.jsx("p",{className:"mt-4 text-lg",children:l("common.loading")})]})}):x?t.jsx("div",{className:"flex min-h-screen flex-col items-center justify-center p-8 md:p-24",children:(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:l("error.title","Error Loading Settings")}),t.jsx("p",{className:"mb-6",children:l("error.message","There was an error loading your settings. Please try again later.")}),t.jsx(o.Z,{onClick:()=>a.push("/"),children:l("common.backToHome")})]})}):d?t.jsx(g.Z,{children:t.jsx("div",{className:"min-h-screen p-6 md:p-12",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto",children:[t.jsx("h1",{className:"text-3xl font-bold mb-8",children:l("title","Settings")}),(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden",children:[t.jsx(settings_SettingsTabs,{activeTab:e,onTabChange:i}),(0,t.jsxs)("div",{className:"p-6",children:["profile"===e&&(0,t.jsxs)("div",{className:"text-gray-700 dark:text-gray-300",children:[t.jsx("h2",{className:"text-xl font-semibold mb-4",children:l("profile.title","Profile Settings")}),t.jsx("p",{children:l("profile.description","Manage your profile information and preferences.")}),t.jsx(profile_ProfileSettings,{settings:{id:d.id,userId:d.id,emailNotifications:{marketing:!0,newFollower:!0,newComment:!0,newLike:!0,newOrder:!0,orderStatus:!0},privacySettings:{profileVisibility:"public",activityVisibility:"public"},language:"en",theme:"light"}})]}),"account"===e&&t.jsx(settings_AccountSettings,{user:d}),"notifications"===e&&t.jsx(settings_NotificationSettings,{}),"privacy"===e&&t.jsx(settings_PrivacySettings,{})]})]})]})})}):(a.push("/login"),null)}},29874:(e,i,a)=>{"use strict";a.d(i,{Z:()=>__WEBPACK_DEFAULT_EXPORT__});var t=a(30784);a(9885);let __WEBPACK_DEFAULT_EXPORT__=({id:e,checked:i,onChange:a,disabled:s=!1,className:r=""})=>t.jsx("button",{id:e,type:"button",role:"switch","aria-checked":i,disabled:s,className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 ${i?"bg-primary-600":"bg-gray-200 dark:bg-gray-700"} ${s?"opacity-50 cursor-not-allowed":"cursor-pointer"} ${r}`,onClick:()=>!s&&a(!i),children:t.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${i?"translate-x-6":"translate-x-1"}`})})},73859:(e,i,a)=>{"use strict";a.r(i),a.d(i,{$$typeof:()=>n,__esModule:()=>r,default:()=>o});var t=a(95153);let s=(0,t.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\settings\page.tsx`),{__esModule:r,$$typeof:n}=s,l=s.default,o=l}};var i=require("../../webpack-runtime.js");i.C(e);var __webpack_exec__=e=>i(i.s=e),a=i.X(0,[2103,2765,706,661],()=>__webpack_exec__(44965));module.exports=a})();