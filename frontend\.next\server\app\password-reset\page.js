(()=>{var e={};e.id=5178,e.ids=[5178],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},66103:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var s=r(67096),a=r(16132),n=r(37284),i=r.n(n),o=r(32564),d={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let l=["",{children:["password-reset",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,11104)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\password-reset\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9291,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\password-reset\\page.tsx"],u="/password-reset/page",m={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/password-reset/page",pathname:"/password-reset",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},94320:(e,t,r)=>{Promise.resolve().then(r.bind(r,80735))},80735:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>__WEBPACK_DEFAULT_EXPORT__});var s=r(30784),a=r(9885),n=r(57114),i=r(11440),o=r.n(i),d=r(27870),l=r(14379),c=r(706),u=r(59872),m=r(19923);let __WEBPACK_DEFAULT_EXPORT__=()=>{let[e,t]=(0,a.useState)(""),[r,i]=(0,a.useState)(""),[p,x]=(0,a.useState)("");(0,n.useRouter)();let{t:g}=(0,d.$G)("auth"),{isRtl:h}=(0,l.g)(),[_,{isLoading:w}]=(0,m.gL)(),handleSubmit=async t=>{if(t.preventDefault(),!e.trim()){i(g("validation.required",{field:g("passwordReset.email")}));return}if(!/\S+@\S+\.\S+/.test(e)){i(g("validation.email"));return}try{await _({email:e}).unwrap(),x(g("passwordReset.success")),i("")}catch(t){let e=t.data?.message||g("passwordReset.error");i(e),x("")}};return(0,s.jsxs)("div",{className:`w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-md dark:bg-gray-800 ${h?"text-right":"text-left"}`,children:[(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("h1",{className:"text-3xl font-bold",children:g("passwordReset.title")}),s.jsx("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:g("passwordReset.subtitle")})]}),r&&s.jsx("div",{className:"p-4 text-red-700 bg-red-100 rounded-md dark:bg-red-900 dark:text-red-100",children:r}),p&&s.jsx("div",{className:"p-4 text-green-700 bg-green-100 rounded-md dark:bg-green-900 dark:text-green-100",children:p}),(0,s.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:handleSubmit,children:[s.jsx(c.Z,{label:g("passwordReset.email"),name:"email",type:"email",value:e,onChange:e=>t(e.target.value),required:!0}),s.jsx(u.Z,{type:"submit",fullWidth:!0,isLoading:w,children:g("passwordReset.button")})]}),s.jsx("div",{className:"text-center mt-4",children:s.jsx(o(),{href:"/login",className:"text-primary-600 hover:text-primary-500",children:g("passwordReset.backToLogin")})}),s.jsx("div",{className:"text-center mt-4",children:s.jsx(o(),{href:"/",className:"text-primary-600 hover:text-primary-500",children:g("common.backToHome")})})]})}},11104:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>PasswordReset,generateMetadata:()=>generateMetadata});var s=r(4656),a=r(60232),n=r(95153);let i=(0,n.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\components\auth\PasswordResetForm.tsx`),{__esModule:o,$$typeof:d}=i,l=i.default;async function generateMetadata(){let{t:e}=await (0,a.$)("auth");return{title:e("passwordReset.title"),description:e("passwordReset.subtitle")}}async function PasswordReset(){let{isRtl:e}=await (0,a.$)("auth");return s.jsx("div",{className:`flex min-h-screen flex-col items-center justify-center p-8 md:p-24 ${e?"text-right":"text-left"}`,children:s.jsx(l,{})})}},60232:(e,t,r)=>{"use strict";r.d(t,{$:()=>getServerTranslations});var s=r(47420),a=r(94386);async function getServerTranslations(e="common"){let t=(0,s.G)(),{t:r}=await (0,s.i)(t,e);return{t:r,language:t,direction:(0,a.Fp)(t)?"rtl":"ltr",isRtl:(0,a.Fp)(t)}}}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),r=t.X(0,[2103,2765,706],()=>__webpack_exec__(66103));module.exports=r})();