import { BaseEvent } from '../base-event.interface';

/**
 * Event emitted when an order status is updated
 */
export class OrderStatusUpdatedEvent implements BaseEvent<OrderStatusUpdatedPayload> {
  id: string;
  type: string = 'order.status.updated';
  version: string = '1.0';
  timestamp: string;
  producer: string = 'order-service';
  payload: OrderStatusUpdatedPayload;

  constructor(payload: OrderStatusUpdatedPayload) {
    this.id = payload.id;
    this.timestamp = new Date().toISOString();
    this.payload = payload;
  }
}

/**
 * Payload for OrderStatusUpdatedEvent
 */
export interface OrderStatusUpdatedPayload {
  /**
   * Order ID
   */
  id: string;

  /**
   * User ID
   */
  userId: string;

  /**
   * Previous status
   */
  previousStatus: string;

  /**
   * New status
   */
  newStatus: string;

  /**
   * Update timestamp
   */
  updatedAt: string;
}
