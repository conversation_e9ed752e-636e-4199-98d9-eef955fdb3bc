"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.startCommand = startCommand;
const chalk = require("chalk");
const inquirer = require("inquirer");
const paths_1 = require("../utils/paths");
const docker_1 = require("../utils/docker");
const npm_1 = require("../utils/npm");
function startCommand(program) {
    program
        .command('start [service]')
        .description('Start services')
        .option('-a, --all', 'Start all services')
        .option('-i, --infrastructure', 'Start infrastructure services only')
        .action(async (service, options) => {
        try {
            console.log(chalk.blue('Starting infrastructure services...'));
            await (0, docker_1.startServices)();
            console.log(chalk.green('Infrastructure services started successfully'));
            if (options.infrastructure) {
                return;
            }
            if (!service && !options.all) {
                const services = (0, paths_1.getAllServices)();
                if (services.length === 0) {
                    console.log(chalk.yellow('No services found'));
                    return;
                }
                const answers = await inquirer.prompt([
                    {
                        type: 'list',
                        name: 'service',
                        message: 'Which service do you want to start?',
                        choices: [...services, 'all'],
                    },
                ]);
                if (answers.service === 'all') {
                    options.all = true;
                }
                else {
                    service = answers.service;
                }
            }
            if (options.all) {
                const services = (0, paths_1.getAllServices)();
                if (services.length === 0) {
                    console.log(chalk.yellow('No services found'));
                    return;
                }
                console.log(chalk.blue(`Starting all services: ${services.join(', ')}...`));
                for (const svc of services) {
                    try {
                        await (0, npm_1.startService)(svc);
                    }
                    catch (error) {
                        console.error(chalk.red(`Error starting ${svc}-service: ${error.message}`));
                    }
                }
                console.log(chalk.green('All services started successfully'));
                return;
            }
            if (service) {
                if (!(0, paths_1.serviceExists)(service)) {
                    console.error(chalk.red(`Service ${service}-service does not exist`));
                    return;
                }
                console.log(chalk.blue(`Starting ${service}-service...`));
                await (0, npm_1.startService)(service);
                console.log(chalk.green(`${service}-service started successfully`));
                return;
            }
        }
        catch (error) {
            console.error(chalk.red(`Error: ${error.message}`));
            process.exit(1);
        }
    });
}
//# sourceMappingURL=start.js.map