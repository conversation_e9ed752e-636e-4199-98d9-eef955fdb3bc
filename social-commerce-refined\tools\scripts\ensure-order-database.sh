#!/bin/bash

# Script to ensure order_service_db database exists
# This script can be run manually to fix the missing database issue

set -e

echo "🔍 Checking if order_service_db exists..."

# Check if database exists
DB_EXISTS=$(docker exec social-commerce-postgres psql -U postgres -tAc "SELECT 1 FROM pg_database WHERE datname='order_service_db';" 2>/dev/null || echo "")

if [ "$DB_EXISTS" = "1" ]; then
    echo "✅ order_service_db already exists"
else
    echo "❌ order_service_db does not exist. Creating it..."
    
    # Create the database
    docker exec social-commerce-postgres psql -U postgres -c "CREATE DATABASE order_service_db;" 2>/dev/null || {
        echo "❌ Failed to create order_service_db"
        exit 1
    }
    
    # Grant privileges
    docker exec social-commerce-postgres psql -U postgres -c "GRANT ALL PRIVILEGES ON DATABASE order_service_db TO postgres;" 2>/dev/null || {
        echo "⚠️  Warning: Failed to grant privileges, but database was created"
    }
    
    echo "✅ order_service_db created successfully"
fi

# Verify the database exists
echo "🔍 Verifying database creation..."
docker exec social-commerce-postgres psql -U postgres -c "\l" | grep order_service_db && {
    echo "✅ Database verification successful"
} || {
    echo "❌ Database verification failed"
    exit 1
}

echo "🎉 order_service_db is ready!"
