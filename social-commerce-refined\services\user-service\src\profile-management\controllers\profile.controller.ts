import { Controller, Get, Post, Body, Param, Put, Delete, UseGuards, Req, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ProfileService } from '../services/profile.service';
import { CreateProfileDto } from '../dto/create-profile.dto';
import { UpdateProfileDto } from '../dto/update-profile.dto';
import { Profile } from '../entities/profile.entity';
import { JwtAuthGuard } from '../../authentication/guards/jwt-auth.guard';
import { MessagePattern } from '@nestjs/microservices';

@ApiTags('profiles')
@Controller('profiles')
export class ProfileController {
  private readonly logger = new Logger(ProfileController.name);

  constructor(private readonly profileService: ProfileService) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all profiles' })
  @ApiResponse({ status: 200, description: 'Return all profiles' })
  async findAll(): Promise<Profile[]> {
    this.logger.log('Getting all profiles');
    return this.profileService.findAll();
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get a profile by ID' })
  @ApiResponse({ status: 200, description: 'Return the profile' })
  @ApiResponse({ status: 404, description: 'Profile not found' })
  async findOne(@Param('id') id: string): Promise<Profile> {
    this.logger.log(`Getting profile with ID: ${id}`);
    return this.profileService.findOne(id);
  }

  @Get('user/:userId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get a profile by user ID' })
  @ApiResponse({ status: 200, description: 'Return the profile' })
  @ApiResponse({ status: 404, description: 'Profile not found' })
  async findByUserId(@Param('userId') userId: string): Promise<Profile> {
    this.logger.log(`Getting profile for user with ID: ${userId}`);
    return this.profileService.findByUserId(userId);
  }

  @Post('user/:userId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a profile for a user' })
  @ApiResponse({ status: 201, description: 'Profile created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async create(
    @Param('userId') userId: string,
    @Body() createProfileDto: CreateProfileDto,
  ): Promise<Profile> {
    this.logger.log(`Creating profile for user with ID: ${userId}`);
    return this.profileService.create(userId, createProfileDto);
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update a profile' })
  @ApiResponse({ status: 200, description: 'Profile updated successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'Profile not found' })
  async update(
    @Param('id') id: string,
    @Body() updateProfileDto: UpdateProfileDto,
  ): Promise<Profile> {
    this.logger.log(`Updating profile with ID: ${id}`);
    return this.profileService.update(id, updateProfileDto);
  }

  @Put('user/:userId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update a profile by user ID' })
  @ApiResponse({ status: 200, description: 'Profile updated successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'Profile not found' })
  async updateByUserId(
    @Param('userId') userId: string,
    @Body() updateProfileDto: UpdateProfileDto,
  ): Promise<Profile> {
    this.logger.log(`Updating profile for user with ID: ${userId}`);
    return this.profileService.updateByUserId(userId, updateProfileDto);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete a profile' })
  @ApiResponse({ status: 200, description: 'Profile deleted successfully' })
  @ApiResponse({ status: 404, description: 'Profile not found' })
  async remove(@Param('id') id: string): Promise<void> {
    this.logger.log(`Removing profile with ID: ${id}`);
    return this.profileService.remove(id);
  }

  // Microservice endpoints

  @MessagePattern('find_profile_by_user_id')
  async findProfileByUserId(userId: string): Promise<Profile> {
    this.logger.log(`Microservice find profile by user ID request received for: ${userId}`);
    return this.profileService.findByUserId(userId);
  }

  @MessagePattern('create_profile')
  async createProfile(data: { userId: string; profile: CreateProfileDto }): Promise<Profile> {
    this.logger.log(`Microservice create profile request received for user ID: ${data.userId}`);
    return this.profileService.create(data.userId, data.profile);
  }

  @MessagePattern('update_profile')
  async updateProfile(data: { userId: string; profile: UpdateProfileDto }): Promise<Profile> {
    this.logger.log(`Microservice update profile request received for user ID: ${data.userId}`);
    return this.profileService.updateByUserId(data.userId, data.profile);
  }
}
