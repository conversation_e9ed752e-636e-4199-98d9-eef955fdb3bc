# Dependency directories
node_modules/
.pnp/
.pnp.js

# Build outputs
dist/
build/
out/
.next/
.nuxt/
.cache/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Coverage directory
coverage/
.nyc_output/

# IDEs and editors
.idea/
.vscode/
*.swp
*.swo
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Database
*.sqlite
*.sqlite3
*.db

# Docker
.docker/data/

# Temporary files
tmp/
temp/

# Generated files
generated/

# Misc
.DS_Store
Thumbs.db
# SpecStory explanation file
.specstory/.what-is-this.md
