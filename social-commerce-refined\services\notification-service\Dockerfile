# Build Stage
FROM node:18-alpine AS build

WORKDIR /app

# Copy package.json and package-lock.json
COPY services/user-service/package*.json ./

# Copy shared libraries (from project root context)
COPY libs/common ./libs/common
# COPY libs/messaging ./libs/messaging  # Temporarily disabled

# Install dependencies
RUN npm install --legacy-peer-deps

# Copy source code
COPY services/user-service/src ./src
COPY services/user-service/tsconfig.json ./
COPY services/user-service/nest-cli.json ./

# Build the application
RUN npm run build

# Production Stage
FROM node:18-alpine

WORKDIR /app

# Copy package.json and package-lock.json
COPY services/user-service/package*.json ./

# Install production dependencies only
RUN npm install --only=production --legacy-peer-deps

# Copy built application from build stage
COPY --from=build /app/dist ./dist
COPY --from=build /app/libs ./libs

# Set environment variables
ENV NODE_ENV=production

# Expose port
EXPOSE 3001

# Start the application
CMD ["node", "dist/main.js"]
