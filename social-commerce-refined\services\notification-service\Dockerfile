# Build Stage
FROM node:18-alpine 

WORKDIR /app

# Copy package.json and package-lock.json
COPY services/notification-service/package*.json ./

# Copy shared libraries (from project root context)
COPY libs/common ./libs/common
# COPY libs/messaging ./libs/messaging  # Temporarily disabled

# Install dependencies
RUN npm install --legacy-peer-deps

# Copy source code
COPY services/notification-service/src ./src
COPY services/notification-service/tsconfig.json ./
COPY services/notification-service/nest-cli.json ./

# Build the application
RUN npm run build


# Expose port
EXPOSE 3007

# Start the application
CMD ["node", "dist/main.js"]
