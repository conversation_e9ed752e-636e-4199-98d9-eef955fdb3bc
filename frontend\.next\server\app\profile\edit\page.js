(()=>{var e={};e.id=3159,e.ids=[3159],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},16959:(e,a,r)=>{"use strict";r.r(a),r.d(a,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var t=r(67096),s=r(16132),l=r(37284),i=r.n(l),o=r(32564),n={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);r.d(a,n);let d=["",{children:["profile",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,71893)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\profile\\edit\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9291,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\profile\\edit\\page.tsx"],m="/profile/edit/page",p={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/profile/edit/page",pathname:"/profile/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},26604:(e,a,r)=>{Promise.resolve().then(r.bind(r,78873))},78873:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>ProfileEditPage});var t=r(30784),s=r(9885),l=r(57114),i=r(52451),o=r.n(i),n=r(706),d=r(59872);let profile_ProfileEditForm=({user:e,onSave:a,onUploadProfileImage:r,onUploadCoverImage:i})=>{let c=(0,l.useRouter)(),[m,p]=(0,s.useState)({displayName:e.displayName||"",bio:e.bio||"",website:"",twitter:"",instagram:""}),[u,g]=(0,s.useState)(!1),[x,h]=(0,s.useState)(!1),[f,y]=(0,s.useState)(!1),[b,v]=(0,s.useState)(null),handleChange=e=>{let{name:a,value:r}=e.target;p(e=>({...e,[a]:r}))},handleSubmit=async r=>{r.preventDefault(),g(!0),v(null);try{await a({displayName:m.displayName||void 0,bio:m.bio||void 0}),c.push(`/profile/${e.username}`)}catch(e){console.error("Failed to update profile:",e),v("Failed to update profile. Please try again.")}finally{g(!1)}},handleProfileImageChange=async e=>{let a=e.target.files?.[0];if(a){h(!0),v(null);try{await r(a)}catch(e){console.error("Failed to upload profile image:",e),v("Failed to upload profile image. Please try again.")}finally{h(!1)}}},handleCoverImageChange=async e=>{let a=e.target.files?.[0];if(a){y(!0),v(null);try{await i(a)}catch(e){console.error("Failed to upload cover image:",e),v("Failed to upload cover image. Please try again.")}finally{y(!1)}}};return(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden",children:[(0,t.jsxs)("div",{className:"relative h-48 bg-gray-200 dark:bg-gray-700",children:[e.coverImageUrl?t.jsx(o(),{src:e.coverImageUrl,alt:"Cover",fill:!0,className:"object-cover"}):t.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-primary-500 to-secondary-500 opacity-75"}),t.jsx("div",{className:"absolute bottom-4 right-4",children:(0,t.jsxs)("label",{className:"cursor-pointer bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 py-2 px-4 rounded-md shadow-sm",children:[t.jsx("span",{children:f?"Uploading...":"Change Cover"}),t.jsx("input",{type:"file",accept:"image/*",className:"hidden",onChange:handleCoverImageChange,disabled:f})]})})]}),(0,t.jsxs)("div",{className:"px-6 py-4",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-end -mt-16 mb-6",children:[(0,t.jsxs)("div",{className:"relative h-24 w-24 rounded-full border-4 border-white dark:border-gray-800 overflow-hidden bg-gray-200 dark:bg-gray-700",children:[e.profileImageUrl?t.jsx(o(),{src:e.profileImageUrl,alt:e.displayName||e.username,fill:!0,className:"object-cover"}):t.jsx("div",{className:"flex items-center justify-center h-full text-2xl font-bold text-gray-500",children:(e.displayName||e.username).charAt(0).toUpperCase()}),t.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 opacity-0 hover:opacity-100 transition-opacity",children:(0,t.jsxs)("label",{className:"cursor-pointer text-white text-xs font-medium",children:[t.jsx("span",{children:x?"Uploading...":"Change"}),t.jsx("input",{type:"file",accept:"image/*",className:"hidden",onChange:handleProfileImageChange,disabled:x})]})})]}),t.jsx("div",{className:"mt-4 sm:mt-0 sm:ml-4",children:(0,t.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:["@",e.username]})})]}),b&&t.jsx("div",{className:"mb-4 p-3 bg-red-100 text-red-700 rounded-md dark:bg-red-900 dark:text-red-100",children:b}),(0,t.jsxs)("form",{onSubmit:handleSubmit,children:[t.jsx(n.Z,{label:"Display Name",name:"displayName",value:m.displayName,onChange:handleChange,placeholder:"Your display name"}),(0,t.jsxs)("div",{className:"mb-4",children:[t.jsx("label",{htmlFor:"bio",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Bio"}),t.jsx("textarea",{id:"bio",name:"bio",rows:4,className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 border-gray-300 dark:border-gray-700",value:m.bio,onChange:handleChange,placeholder:"Tell us about yourself"})]}),(0,t.jsxs)("div",{className:"flex justify-end gap-3 mt-6",children:[t.jsx(d.Z,{type:"button",variant:"outline",onClick:()=>c.push(`/profile/${e.username}`),children:"Cancel"}),t.jsx(d.Z,{type:"submit",isLoading:u,children:"Save Changes"})]})]})]})]})};var c=r(19923);function ProfileEditPage(){let e=(0,l.useRouter)(),{data:a,isLoading:r,error:s}=(0,c.Mx)(),[i]=(0,c.TG)(),[o]=(0,c.useUploadProfileImageMutation)(),[n]=(0,c.useUploadCoverImageMutation)(),handleSave=async e=>{await i(e).unwrap()},handleUploadProfileImage=async e=>{let a=new FormData;a.append("image",e),await o(a).unwrap()},handleUploadCoverImage=async e=>{let a=new FormData;a.append("image",e),await n(a).unwrap()};return r?t.jsx("div",{className:"min-h-screen p-6 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600 mx-auto"}),t.jsx("p",{className:"mt-4 text-lg",children:"Loading profile..."})]})}):s||!a?t.jsx("div",{className:"min-h-screen p-6 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:"Error"}),t.jsx("p",{className:"mb-6",children:"Failed to load profile. Please try again later."}),t.jsx(d.Z,{onClick:()=>e.push("/"),children:"Back to Home"})]})}):t.jsx("div",{className:"min-h-screen p-6",children:(0,t.jsxs)("div",{className:"max-w-3xl mx-auto",children:[t.jsx("h1",{className:"text-2xl font-bold mb-6",children:"Edit Profile"}),t.jsx(profile_ProfileEditForm,{user:a,onSave:handleSave,onUploadProfileImage:handleUploadProfileImage,onUploadCoverImage:handleUploadCoverImage})]})})}},71893:(e,a,r)=>{"use strict";r.r(a),r.d(a,{$$typeof:()=>i,__esModule:()=>l,default:()=>n});var t=r(95153);let s=(0,t.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\profile\edit\page.tsx`),{__esModule:l,$$typeof:i}=s,o=s.default,n=o}};var a=require("../../../webpack-runtime.js");a.C(e);var __webpack_exec__=e=>a(a.s=e),r=a.X(0,[2103,2765,706],()=>__webpack_exec__(16959));module.exports=r})();