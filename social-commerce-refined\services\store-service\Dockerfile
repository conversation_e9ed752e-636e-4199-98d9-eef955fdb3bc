# Build Stage
FROM node:18-alpine AS build

WORKDIR /app

# Copy package.json and package-lock.json
COPY services/store-service/package*.json ./

# Copy shared libraries (from project root context)
COPY libs/common ./libs/common

# Install dependencies
RUN npm install --legacy-peer-deps

# Copy source code
COPY services/store-service/src ./src
COPY services/store-service/tsconfig.json ./
COPY services/store-service/nest-cli.json ./

# Build the application
RUN npm run build

# Production Stage
FROM node:18-alpine

WORKDIR /app

# Copy package.json and package-lock.json
COPY services/store-service/package*.json ./

# Install production dependencies only
RUN npm install --only=production --legacy-peer-deps

# Copy built application from build stage
COPY --from=build /app/dist ./dist
COPY --from=build /app/libs ./libs

# Set environment variables
ENV NODE_ENV=production

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001
USER nestjs

# Expose port
EXPOSE 3002

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:3002/api/health || exit 1

# Start the application (FIXED: webpack build outputs to dist/main.js)
CMD ["node", "dist/main.js"]
