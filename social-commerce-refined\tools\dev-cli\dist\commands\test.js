"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.testCommand = testCommand;
const chalk = require("chalk");
const inquirer = require("inquirer");
const paths_1 = require("../utils/paths");
const npm_1 = require("../utils/npm");
function testCommand(program) {
    program
        .command('test [service]')
        .description('Run tests')
        .option('-a, --all', 'Test all services')
        .option('-w, --watch', 'Run tests in watch mode')
        .action(async (service, options) => {
        try {
            if (!service && !options.all) {
                const services = (0, paths_1.getAllServices)();
                if (services.length === 0) {
                    console.log(chalk.yellow('No services found'));
                    return;
                }
                const answers = await inquirer.prompt([
                    {
                        type: 'list',
                        name: 'service',
                        message: 'Which service do you want to test?',
                        choices: [...services, 'all'],
                    },
                ]);
                if (answers.service === 'all') {
                    options.all = true;
                }
                else {
                    service = answers.service;
                }
            }
            if (options.all) {
                const services = (0, paths_1.getAllServices)();
                if (services.length === 0) {
                    console.log(chalk.yellow('No services found'));
                    return;
                }
                console.log(chalk.blue(`Testing all services: ${services.join(', ')}...`));
                for (const svc of services) {
                    try {
                        await (0, npm_1.testService)(svc, options.watch);
                    }
                    catch (error) {
                        console.error(chalk.red(`Error testing ${svc}-service: ${error.message}`));
                    }
                }
                console.log(chalk.green('All services tested successfully'));
                return;
            }
            if (service) {
                if (!(0, paths_1.serviceExists)(service)) {
                    console.error(chalk.red(`Service ${service}-service does not exist`));
                    return;
                }
                console.log(chalk.blue(`Testing ${service}-service...`));
                await (0, npm_1.testService)(service, options.watch);
                console.log(chalk.green(`${service}-service tested successfully`));
                return;
            }
        }
        catch (error) {
            console.error(chalk.red(`Error: ${error.message}`));
            process.exit(1);
        }
    });
}
//# sourceMappingURL=test.js.map