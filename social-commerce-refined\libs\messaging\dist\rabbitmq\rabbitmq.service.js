"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RabbitMQService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RabbitMQService = void 0;
const common_1 = require("@nestjs/common");
const amqp = require("amqplib");
const uuid_1 = require("uuid");
let RabbitMQService = RabbitMQService_1 = class RabbitMQService {
    constructor(url) {
        this.logger = new common_1.Logger(RabbitMQService_1.name);
        this.url = url || process.env.RABBITMQ_URL || 'amqp://admin:admin@localhost:5672';
    }
    async onModuleInit() {
        await this.connect();
    }
    async onModuleDestroy() {
        await this.close();
    }
    async connect() {
        try {
            this.logger.log(`Connecting to RabbitMQ at ${this.url}`);
            this.connection = await amqp.connect(this.url);
            this.channel = await this.connection.createChannel();
            this.logger.log('Connected to RabbitMQ');
            this.connection.on('error', (err) => {
                this.logger.error(`RabbitMQ connection error: ${err.message}`);
                this.reconnect();
            });
            this.connection.on('close', () => {
                this.logger.warn('RabbitMQ connection closed');
                this.reconnect();
            });
        }
        catch (error) {
            this.logger.error(`Failed to connect to RabbitMQ: ${error.message}`);
            this.reconnect();
        }
    }
    async reconnect() {
        this.logger.log('Attempting to reconnect to RabbitMQ...');
        setTimeout(async () => {
            try {
                await this.connect();
            }
            catch (error) {
                this.logger.error(`Failed to reconnect to RabbitMQ: ${error.message}`);
                this.reconnect();
            }
        }, 5000);
    }
    async close() {
        try {
            if (this.channel) {
                await this.channel.close();
            }
            if (this.connection) {
                await this.connection.close();
            }
            this.logger.log('Disconnected from RabbitMQ');
        }
        catch (error) {
            this.logger.error(`Error closing RabbitMQ connection: ${error.message}`);
        }
    }
    async createQueue(queue, options) {
        try {
            await this.channel.assertQueue(queue, options);
            this.logger.log(`Queue ${queue} created`);
        }
        catch (error) {
            this.logger.error(`Error creating queue ${queue}: ${error.message}`);
            throw error;
        }
    }
    async createExchange(exchange, type, options) {
        try {
            await this.channel.assertExchange(exchange, type, options);
            this.logger.log(`Exchange ${exchange} created`);
        }
        catch (error) {
            this.logger.error(`Error creating exchange ${exchange}: ${error.message}`);
            throw error;
        }
    }
    async bindQueue(queue, exchange, routingKey) {
        try {
            await this.channel.bindQueue(queue, exchange, routingKey);
            this.logger.log(`Queue ${queue} bound to exchange ${exchange} with routing key ${routingKey}`);
        }
        catch (error) {
            this.logger.error(`Error binding queue ${queue} to exchange ${exchange}: ${error.message}`);
            throw error;
        }
    }
    async publish(exchange, routingKey, message, options) {
        try {
            const messageBuffer = Buffer.from(JSON.stringify(message));
            const defaultOptions = Object.assign({ persistent: true, messageId: (0, uuid_1.v4)(), timestamp: Date.now(), contentType: 'application/json' }, options);
            const result = this.channel.publish(exchange, routingKey, messageBuffer, defaultOptions);
            if (result) {
                this.logger.debug(`Message published to exchange ${exchange} with routing key ${routingKey}`);
            }
            else {
                this.logger.warn(`Channel write buffer is full - exchange ${exchange}, routing key ${routingKey}`);
                await new Promise((resolve) => this.channel.once('drain', resolve));
                this.logger.debug(`Channel drained - exchange ${exchange}, routing key ${routingKey}`);
            }
            return result;
        }
        catch (error) {
            this.logger.error(`Error publishing message to exchange ${exchange}: ${error.message}`);
            throw error;
        }
    }
    async sendToQueue(queue, message, options) {
        try {
            const messageBuffer = Buffer.from(JSON.stringify(message));
            const defaultOptions = Object.assign({ persistent: true, messageId: (0, uuid_1.v4)(), timestamp: Date.now(), contentType: 'application/json' }, options);
            const result = this.channel.sendToQueue(queue, messageBuffer, defaultOptions);
            if (result) {
                this.logger.debug(`Message sent to queue ${queue}`);
            }
            else {
                this.logger.warn(`Channel write buffer is full - queue ${queue}`);
                await new Promise((resolve) => this.channel.once('drain', resolve));
                this.logger.debug(`Channel drained - queue ${queue}`);
            }
            return result;
        }
        catch (error) {
            this.logger.error(`Error sending message to queue ${queue}: ${error.message}`);
            throw error;
        }
    }
    async consume(queue, onMessage, options) {
        try {
            const defaultOptions = Object.assign({ noAck: false }, options);
            await this.channel.consume(queue, async (msg) => {
                if (msg) {
                    try {
                        await onMessage(msg);
                        this.channel.ack(msg);
                    }
                    catch (error) {
                        this.logger.error(`Error processing message from queue ${queue}: ${error.message}`);
                        this.channel.nack(msg, false, true);
                    }
                }
            }, defaultOptions);
            this.logger.log(`Consumer registered for queue ${queue}`);
        }
        catch (error) {
            this.logger.error(`Error consuming from queue ${queue}: ${error.message}`);
            throw error;
        }
    }
    async purgeQueue(queue) {
        try {
            await this.channel.purgeQueue(queue);
            this.logger.log(`Queue ${queue} purged`);
        }
        catch (error) {
            this.logger.error(`Error purging queue ${queue}: ${error.message}`);
            throw error;
        }
    }
    async deleteQueue(queue, options) {
        try {
            await this.channel.deleteQueue(queue, options);
            this.logger.log(`Queue ${queue} deleted`);
        }
        catch (error) {
            this.logger.error(`Error deleting queue ${queue}: ${error.message}`);
            throw error;
        }
    }
    async deleteExchange(exchange, options) {
        try {
            await this.channel.deleteExchange(exchange, options);
            this.logger.log(`Exchange ${exchange} deleted`);
        }
        catch (error) {
            this.logger.error(`Error deleting exchange ${exchange}: ${error.message}`);
            throw error;
        }
    }
};
exports.RabbitMQService = RabbitMQService;
exports.RabbitMQService = RabbitMQService = RabbitMQService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [String])
], RabbitMQService);
//# sourceMappingURL=rabbitmq.service.js.map