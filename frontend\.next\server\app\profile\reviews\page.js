(()=>{var e={};e.id=2771,e.ids=[2771],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},64719:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var t=r(67096),a=r(16132),i=r(37284),n=r.n(i),d=r(32564),l={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);r.d(s,l);let o=["",{children:["profile",{children:["reviews",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,49820)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\profile\\reviews\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9291,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\profile\\reviews\\page.tsx"],m="/profile/reviews/page",u={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/profile/reviews/page",pathname:"/profile/reviews",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},70044:(e,s,r)=>{Promise.resolve().then(r.bind(r,88099))},88099:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>UserReviewsPage});var t=r(30784);r(9885);var a=r(57114),i=r(52451),n=r.n(i),d=r(11440),l=r.n(d),o=r(59872),c=r(74109),m=r(46929),u=r(73531);function UserReviewsPage(){let e=(0,a.useRouter)(),{data:s,isLoading:r}=(0,m.C2)(),[i]=(0,m.hI)(),handleDeleteReview=async e=>{if(window.confirm("Are you sure you want to delete this review?"))try{await i(e).unwrap()}catch(e){console.error("Failed to delete review:",e)}},handleEditReview=s=>{e.push(`/profile/reviews/edit/${s}`)};return r?t.jsx("div",{className:"min-h-screen p-6",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto",children:[t.jsx("h1",{className:"text-2xl font-bold mb-6",children:"My Reviews"}),t.jsx("div",{className:"animate-pulse space-y-6",children:[void 0,void 0,void 0].map((e,s)=>(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6",children:[t.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"}),t.jsx("div",{className:"h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"}),t.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-full mb-2"}),t.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-full mb-2"}),t.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"})]},s))})]})}):t.jsx("div",{className:"min-h-screen p-6",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[t.jsx("h1",{className:"text-2xl font-bold",children:"My Reviews"}),t.jsx(o.Z,{variant:"outline",onClick:()=>e.push("/profile"),children:"Back to Profile"})]}),s&&0!==s.length?t.jsx("div",{className:"space-y-6",children:s.map(e=>(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsxs)(l(),{href:`/products/${e.productId}`,className:"font-medium text-lg hover:text-primary-600",children:["Product #",e.productId]}),(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("span",{className:"text-sm text-gray-500 dark:text-gray-400 mr-4",children:(0,u.Z)(new Date(e.createdAt),{addSuffix:!0})}),t.jsx(c.Z,{rating:e.rating,size:"sm"})]})]}),e.title&&t.jsx("h3",{className:"font-medium mb-2",children:e.title}),t.jsx("p",{className:"text-gray-700 dark:text-gray-300 mb-4",children:e.content}),e.mediaUrls&&e.mediaUrls.length>0&&t.jsx("div",{className:"flex flex-wrap gap-2 mb-4",children:e.mediaUrls.map((e,s)=>t.jsx("div",{className:"relative h-16 w-16 rounded-md overflow-hidden",children:t.jsx(n(),{src:e,alt:`Review image ${s+1}`,fill:!0,className:"object-cover"})},s))}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3",children:[t.jsx(o.Z,{variant:"outline",size:"sm",onClick:()=>handleEditReview(e.id),children:"Edit"}),t.jsx(o.Z,{variant:"outline",size:"sm",onClick:()=>handleDeleteReview(e.id),children:"Delete"})]})]},e.id))}):(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 text-center",children:[t.jsx("p",{className:"text-gray-500 dark:text-gray-400 mb-4",children:"You haven't written any reviews yet."}),t.jsx(o.Z,{onClick:()=>e.push("/products"),children:"Browse Products"})]})]})})}},49820:(e,s,r)=>{"use strict";r.r(s),r.d(s,{$$typeof:()=>n,__esModule:()=>i,default:()=>l});var t=r(95153);let a=(0,t.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\profile\reviews\page.tsx`),{__esModule:i,$$typeof:n}=a,d=a.default,l=d}};var s=require("../../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),r=s.X(0,[2103,2765,2022],()=>__webpack_exec__(64719));module.exports=r})();