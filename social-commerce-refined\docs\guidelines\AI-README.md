# AI Guidelines for Social Commerce Platform

## Introduction

This document provides comprehensive guidelines for AI assistants (including Augment AI Code Assistant) working on the Social Commerce Platform. Following these guidelines is mandatory to ensure consistency, quality, and adherence to the established architecture and development workflow.

## Purpose

The purpose of these guidelines is to:

1. Ensure AI assistants understand the architecture and design principles
2. Provide clear guidance on how to work with the codebase
3. Establish standards for code generation and modification
4. Ensure consistency across all services and features
5. Maintain the integrity of the architecture

## How to Use These Guidelines

AI assistants should:

1. Read and understand these guidelines before working on the codebase
2. Reference specific sections when needed
3. Follow the examples provided
4. Ask for clarification if something is unclear
5. Update these guidelines as the architecture evolves

## Table of Contents

- [Implementation-First Approach](#implementation-first-approach)
- [Architecture Understanding](#architecture-understanding)
- [Development Workflow](#development-workflow)
- [Code Organization](#code-organization)
- [Communication Patterns](#communication-patterns)
- [Data Management](#data-management)
- [Error Handling](#error-handling)
- [Testing](#testing)
- [Documentation](#documentation)
- [Issue & Solution Documentation](#issue--solution-documentation)
- [Production-Ready Features](#production-ready-features)
- [Environment Consistency](#environment-consistency)
- [Examples](#examples)

## Implementation-First Approach

**CRITICAL**: AI assistants must follow the implementation-first approach based on lessons learned from our development process.

### Core Principles

1. **Build Working Code First, Then Extract Guidelines**
   - Focus on creating functional, working services before extensive documentation
   - Use working implementations as the source of truth for patterns and architecture
   - Extract guidelines and documentation from proven, working code
   - **DO NOT** spend excessive time on meta-documentation before implementation

2. **Template-Based Development**
   - Use existing working services (like User Service) as templates for new services
   - Copy and modify proven patterns rather than starting from scratch
   - This approach is 10x faster than building from architectural documents alone
   - Maintain consistency by following established patterns

3. **Break Down Tasks into Micro-Steps**
   - Divide complex tasks into extremely small, manageable steps (max 200 lines of code changes)
   - Add verification points after logical groups of steps
   - Execute methodically, one step at a time
   - **AVOID** excessive nesting and complexity in task breakdown

4. **Early Integration Testing**
   - Test integration between services early to validate architecture decisions
   - Validate end-to-end workflows before building all services
   - Use the API Gateway as the foundation for service integration
   - Identify integration issues when they're easier and cheaper to fix

5. **Feature-Based Organization within Domain-Driven Services**
   - System Level: Use Domain-Driven Microservices to define service boundaries
   - Service Level: Within each microservice, organize code by business feature
   - Maintain clear separation of business concerns
   - **DO NOT** organize code by technical layers within services

### Strategic Decision Making

When making implementation decisions:

1. **Present Options with Pros and Cons**
   - Always provide multiple approaches when applicable
   - Clearly outline advantages and disadvantages of each option
   - Make data-driven recommendations based on business value and time-to-market

2. **Focus on Business Value**
   - Prioritize features that deliver immediate business value
   - Consider time-to-market in all decisions
   - Balance technical debt with delivery speed

3. **Structured Communication**
   - Use clear formatting with headers, bullet points, and emojis for readability
   - Provide immediate action plans with time estimates
   - Report success with detailed metrics and achievements

### Avoiding Common Pitfalls

**DO NOT**:
- Create excessive nesting and complexity in code or documentation
- Focus on documentation over working implementation
- Allow scope creep in guidelines - keep them focused and practical
- Build extensive meta-documentation before proving concepts with working code

**DO**:
- Build one complete, working service that demonstrates all patterns
- Use that service as a template for others
- Test integration early and often
- Extract guidelines from what actually works

## Architecture Understanding

### System Architecture

The Social Commerce Platform follows a domain-driven microservices architecture with feature-based internal organization:

```
                                  ┌─────────────┐
                                  │   Clients   │
                                  └──────┬──────┘
                                         │
                                         ▼
┌─────────────────────────────────────────────────────────────────┐
│                          API Gateway                             │
└───┬───────────┬────────────┬────────────┬────────────┬──────────┘
    │           │            │            │            │
    ▼           ▼            ▼            ▼            ▼
┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────────┐
│  User   │ │  Store  │ │ Product │ │  Order  │ │    Cart     │
│ Service │ │ Service │ │ Service │ │ Service │ │   Service   │
└────┬────┘ └────┬────┘ └────┬────┘ └────┬────┘ └──────┬──────┘
     │           │           │           │             │
     │           │           │           │             │
┌────┴────┐ ┌────┴────┐ ┌────┴────┐ ┌────┴────┐ ┌──────┴──────┐
│  User   │ │  Store  │ │ Product │ │  Order  │ │    Cart     │
│   DB    │ │   DB    │ │   DB    │ │   DB    │ │     DB      │
└─────────┘ └─────────┘ └─────────┘ └─────────┘ └─────────────┘
```
### More info
for mor information see [ARCHITECTURE.md](./docs/architecture/ARCHITECTURE.md)

### Key Architecture Principles

When working with this codebase, AI assistants must understand and respect these principles:

1. **Domain-Driven Microservices**
   - Services are organized around business domains
   - Each service has clear responsibilities and boundaries
   - Services communicate through well-defined interfaces
   - **DO NOT** create cross-service dependencies

2. **Feature-Based Internal Organization**
   - Within each service, code is organized by business feature
   - Features contain all related components (controllers, services, repositories)
   - Shared code is extracted into a common directory
   - **DO NOT** organize code by technical layer

3. **Event-Driven Communication**
   - Services communicate asynchronously via events
   - RabbitMQ is used as the message broker
   - Event schema registry ensures contract compliance
   - **DO NOT** directly call other services' databases

4. **Database Per Service**
   - Each service owns its database
   - No direct cross-service database access
   - Data consistency maintained through events
   - **DO NOT** share databases between services

5. **API Gateway Pattern**
   - Single entry point for all client requests
   - Handles routing, authentication, and request transformation
   - Implements cross-cutting concerns
   - **DO NOT** bypass the API Gateway

### Service Boundaries

AI assistants must respect these service boundaries:

| Service | Responsibility | What NOT to Do |
|---------|----------------|---------------|
| User Service | User management, authentication, profiles | Don't handle cart or order data |
| Store Service | Store management, settings | Don't handle product details |
| Product Service | Product catalog, inventory | Don't handle order processing |
| Order Service | Order processing, payments | Don't handle cart management |
| Cart Service | Shopping cart management | Don't handle user profiles |
| Social Service | Social interactions | Don't handle notifications |
| Notification Service | User notifications | Don't handle user data |
| Search Service | Search functionality | Don't modify product data |
| Analytics Service | Business analytics | Don't modify any data |
| Group Buying Service | Group buying functionality | Don't handle payments |
| Affiliate Service | Affiliate management | Don't handle user profiles |

## Development Workflow

AI assistants must follow the established development workflow:

### Using the Development CLI

Always use the development CLI tool for common tasks:

```bash
# CORRECT
dev start user

# INCORRECT
cd services/user-service && npm run start
```

### Common Development CLI Commands

| Command | Description | Example |
|---------|-------------|---------|
| `dev start [service]` | Start services | `dev start user` |
| `dev stop [service]` | Stop services | `dev stop user` |
| `dev status` | Check service status | `dev status` |
| `dev install [service]` | Install dependencies | `dev install user` |
| `dev build [service]` | Build services | `dev build user` |
| `dev test [service]` | Run tests | `dev test user` |
| `dev generate [type] [name]` | Generate code | `dev generate service cart` |

### Before Making Changes

Before making any changes to the codebase, AI assistants must:

1. **Understand the architecture**: Review the architecture documentation
2. **Check service boundaries**: Ensure changes respect service boundaries
3. **Check service status**: Use `dev status` to check service health
4. **Review existing code**: Understand the current implementation
5. **Plan changes**: Create a clear plan before making changes

### Git Workflow

Follow the established Git workflow:

1. **Create a feature branch**:
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make small, focused commits**:
   ```bash
   git add .
   git commit -m "feat: add specific feature"
   ```

   Use conventional commit messages:
   - `feat:` for new features
   - `fix:` for bug fixes
   - `docs:` for documentation changes
   - `test:` for test additions or changes
   - `refactor:` for code refactoring
   - `style:` for formatting changes
   - `chore:` for maintenance tasks

3. **Push to the repository**:
   ```bash
   git push origin feature/your-feature-name
   ```

4. **Create a Pull Request** with a clear description of changes

## Code Organization

AI assistants must follow the established code organization patterns:

### Feature-Based Organization

Within each service, code is organized by business feature, not by technical layer:

```
// CORRECT
user-service/
├── src/
│   ├── authentication/
│   │   ├── authentication.controller.ts
│   │   ├── authentication.service.ts
│   │   ├── authentication.repository.ts
│   │   ├── dto/
│   │   └── entities/
│   ├── profile-management/
│   │   ├── profile.controller.ts
│   │   ├── profile.service.ts
│   │   ├── profile.repository.ts
│   │   ├── dto/
│   │   └── entities/
│   └── shared/
│       ├── utils/
│       ├── middleware/
│       └── constants/

// INCORRECT
user-service/
├── src/
│   ├── controllers/
│   │   ├── authentication.controller.ts
│   │   └── profile.controller.ts
│   ├── services/
│   │   ├── authentication.service.ts
│   │   └── profile.service.ts
│   ├── repositories/
│   │   ├── authentication.repository.ts
│   │   └── profile.repository.ts
│   ├── dto/
│   └── entities/
```

### Naming Conventions

Follow these naming conventions:

| Type | Convention | Example |
|------|------------|---------|
| Files | kebab-case | `user-profile.service.ts` |
| Classes | PascalCase | `UserProfileService` |
| Methods | camelCase | `getUserProfile()` |
| Variables | camelCase | `userProfile` |
| Constants | UPPER_SNAKE_CASE | `MAX_PROFILE_SIZE` |
| Interfaces | PascalCase with 'I' prefix | `IUserProfile` |
| Enums | PascalCase | `UserRole` |
| Database Tables | snake_case | `user_profiles` |
| Database Columns | snake_case | `first_name` |

### Import Order

Organize imports in the following order:

```typescript
// 1. Node.js built-in modules
import * as fs from 'fs';
import * as path from 'path';

// 2. External dependencies
import { Controller, Get } from '@nestjs/common';
import { Repository } from 'typeorm';

// 3. Internal modules from other services (via libs)
import { CommonModule } from '@app/common';

// 4. Internal modules from the same service
import { UserService } from '../user.service';
import { User } from './entities/user.entity';

// 5. Types and interfaces
import { IUserProfile } from './interfaces/user-profile.interface';
```

### Code Style

Follow these code style guidelines:

1. **Use TypeScript**: Always use TypeScript for type safety
2. **Use async/await**: Prefer async/await over callbacks or raw promises
3. **Use dependency injection**: Follow NestJS dependency injection patterns
4. **Use decorators**: Use NestJS decorators for controllers, services, etc.
5. **Use DTOs**: Use Data Transfer Objects for input validation
6. **Use entities**: Use TypeORM entities for database models
7. **Use interfaces**: Define interfaces for complex types
8. **Use enums**: Use enums for fixed sets of values
9. **Use constants**: Extract magic values into constants
10. **Use meaningful names**: Use descriptive names for variables, methods, and classes

## Communication Patterns

AI assistants must follow the established communication patterns:

### REST API Design

For synchronous communication, follow these REST API design principles:

1. **Resource-Based URLs**:
   ```
   # CORRECT
   GET /users
   GET /users/:id
   POST /users
   PUT /users/:id
   DELETE /users/:id

   # INCORRECT
   GET /getUsers
   GET /getUserById/:id
   POST /createUser
   PUT /updateUser/:id
   DELETE /deleteUser/:id
   ```

2. **HTTP Methods**:
   - `GET`: Retrieve resources
   - `POST`: Create resources
   - `PUT`: Update resources (full update)
   - `PATCH`: Update resources (partial update)
   - `DELETE`: Delete resources

3. **Status Codes**:
   - `200 OK`: Success
   - `201 Created`: Resource created
   - `204 No Content`: Success with no content
   - `400 Bad Request`: Invalid request
   - `401 Unauthorized`: Authentication required
   - `403 Forbidden`: Permission denied
   - `404 Not Found`: Resource not found
   - `500 Internal Server Error`: Server error

4. **Request/Response Format**:
   - Use JSON for request/response bodies
   - Use camelCase for JSON properties
   - Use DTOs for request validation
   - Use consistent response format

### Event-Driven Communication

For asynchronous communication, follow these event-driven patterns:

1. **Event Naming**:
   - Use past tense for events (e.g., `UserCreated`, `OrderPlaced`)
   - Use present tense for commands (e.g., `CreateUser`, `PlaceOrder`)
   - Use service prefix for events (e.g., `user.created`, `order.placed`)

2. **Event Structure**:
   ```typescript
   interface Event<T> {
     id: string;           // Unique event ID
     type: string;         // Event type (e.g., 'user.created')
     version: string;      // Event schema version
     timestamp: string;    // ISO timestamp
     producer: string;     // Service that produced the event
     payload: T;           // Event data
   }
   ```

3. **Message Patterns in NestJS**:
   - Use `@MessagePattern()` for request-response
   - Use `@EventPattern()` for event handling
   - Use `ClientProxy.send()` for request-response
   - Use `ClientProxy.emit()` for events

4. **Event Handling**:
   - Implement idempotent event handlers
   - Handle events in the correct order
   - Implement retry logic for failed events
   - Use dead letter queues for unprocessable events

### API Gateway Communication

For communication through the API Gateway:

1. **Route Registration**:
   - Register routes in the API Gateway
   - Use consistent route naming
   - Group routes by service

2. **Authentication**:
   - Implement authentication in the API Gateway
   - Pass user context to services
   - Validate JWT tokens

3. **Request Transformation**:
   - Transform requests/responses as needed
   - Handle errors consistently
   - Implement request logging

## Data Management

AI assistants must follow the established data management patterns:

### Database Per Service

Each service owns its database:

```
// CORRECT
// User Service
@Entity()
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  email: string;

  @Column()
  password: string;

  // Other user properties
}

// INCORRECT
// Cart Service accessing User data directly
@Entity()
export class Cart {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => User)
  user: User;

  // Other cart properties
}
```

### Data Access Patterns

Follow these data access patterns:

1. **Repository Pattern**:
   ```typescript
   @Injectable()
   export class UserRepository {
     constructor(
       @InjectRepository(User)
       private readonly repository: Repository<User>,
     ) {}

     async findOne(id: string): Promise<User> {
       return this.repository.findOne({ where: { id } });
     }

     // Other repository methods
   }
   ```

2. **DTO Pattern**:
   ```typescript
   export class CreateUserDto {
     @IsEmail()
     @IsNotEmpty()
     email: string;

     @IsString()
     @MinLength(8)
     @MaxLength(100)
     password: string;

     // Other properties
   }
   ```

3. **Entity Pattern**:
   ```typescript
   @Entity()
   export class User {
     @PrimaryGeneratedColumn('uuid')
     id: string;

     @Column({ unique: true })
     email: string;

     @Column()
     password: string;

     // Other properties
   }
   ```

### Data Consistency

Maintain data consistency across services:

1. **Transactions**:
   - Use transactions within service boundaries
   - Don't use distributed transactions across services

2. **Saga Pattern**:
   - Implement compensating transactions for cross-service operations
   - Use events to coordinate sagas

3. **Event Sourcing**:
   - Store events as the source of truth for critical workflows
   - Rebuild state from events when needed

4. **CQRS**:
   - Separate read and write operations for complex domains
   - Use different models for reading and writing

### Database Technologies

Use the appropriate database technology for each service:

1. **PostgreSQL**:
   - For transactional data (users, orders, products)
   - Use TypeORM for database access
   - Implement migrations for schema changes

2. **MongoDB**:
   - For content-heavy data (social posts, comments)
   - Use Mongoose for database access
   - Define schemas for data validation

3. **Redis**:
   - For caching and session management
   - Use ioredis for database access
   - Implement proper TTL for cached data

4. **Elasticsearch**:
   - For search functionality
   - Use elasticsearch.js for database access
   - Define mappings for data indexing

## Error Handling

AI assistants must follow the established error handling patterns:

### Exception Handling

Follow these exception handling patterns:

1. **Use Custom Exceptions**:
   ```typescript
   export class UserNotFoundException extends NotFoundException {
     constructor(userId: string) {
       super(`User with ID ${userId} not found`);
     }
   }
   ```

2. **Use Try/Catch Blocks**:
   ```typescript
   async findOne(id: string): Promise<User> {
     try {
       const user = await this.userRepository.findOne(id);

       if (!user) {
         throw new UserNotFoundException(id);
       }

       return user;
     } catch (error) {
       this.logger.error(`Error finding user: ${error.message}`, error.stack);
       throw error;
     }
   }
   ```

3. **Use NestJS Exception Filters**:
   ```typescript
   @Catch(HttpException)
   export class HttpExceptionFilter implements ExceptionFilter {
     catch(exception: HttpException, host: ArgumentsHost) {
       const ctx = host.switchToHttp();
       const response = ctx.getResponse<Response>();
       const request = ctx.getRequest<Request>();
       const status = exception.getStatus();

       response.status(status).json({
         statusCode: status,
         timestamp: new Date().toISOString(),
         path: request.url,
         message: exception.message,
       });
     }
   }
   ```

### Error Response Format

Use a consistent error response format:

```json
{
  "statusCode": 404,
  "message": "User with ID 123 not found",
  "error": "Not Found",
  "timestamp": "2023-05-22T12:34:56.789Z",
  "path": "/users/123"
}
```

### Logging

Follow these logging patterns:

1. **Use NestJS Logger**:
   ```typescript
   private readonly logger = new Logger(UserService.name);

   async findOne(id: string): Promise<User> {
     this.logger.log(`Finding user with ID: ${id}`);

     try {
       // Implementation
     } catch (error) {
       this.logger.error(`Error finding user: ${error.message}`, error.stack);
       throw error;
     }
   }
   ```

2. **Log Levels**:
   - `log`: General information
   - `debug`: Debugging information
   - `verbose`: Verbose information
   - `warn`: Warning information
   - `error`: Error information

3. **Structured Logging**:
   ```typescript
   this.logger.log({
     message: 'Finding user',
     userId: id,
     timestamp: new Date().toISOString(),
   });
   ```

### Error Handling in Controllers

Handle errors in controllers:

```typescript
@Get(':id')
async findOne(@Param('id') id: string): Promise<User> {
  try {
    return await this.userService.findOne(id);
  } catch (error) {
    // Let NestJS exception filters handle the error
    throw error;
  }
}
```

### Error Handling in Services

Handle errors in services:

```typescript
async findOne(id: string): Promise<User> {
  try {
    const user = await this.userRepository.findOne(id);

    if (!user) {
      throw new UserNotFoundException(id);
    }

    return user;
  } catch (error) {
    this.logger.error(`Error finding user: ${error.message}`, error.stack);

    // Rethrow the error for the controller to handle
    throw error;
  }
}
```

### Error Handling in Repositories

Handle errors in repositories:

```typescript
async findOne(id: string): Promise<User> {
  try {
    return await this.repository.findOne({ where: { id } });
  } catch (error) {
    this.logger.error(`Database error: ${error.message}`, error.stack);
    throw new InternalServerErrorException('Database error');
  }
}
```

## Testing

AI assistants must follow the established testing patterns:

### Unit Testing

Write unit tests for individual components:

```typescript
describe('UserService', () => {
  let service: UserService;
  let repository: MockType<Repository<User>>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserService,
        {
          provide: getRepositoryToken(User),
          useFactory: repositoryMockFactory,
        },
      ],
    }).compile();

    service = module.get<UserService>(UserService);
    repository = module.get(getRepositoryToken(User));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findOne', () => {
    it('should return a user', async () => {
      // Arrange
      const user = new User();
      user.id = '123';
      user.email = '<EMAIL>';
      repository.findOne.mockReturnValue(user);

      // Act
      const result = await service.findOne('123');

      // Assert
      expect(result).toEqual(user);
      expect(repository.findOne).toHaveBeenCalledWith({ where: { id: '123' } });
    });

    it('should throw an error if user not found', async () => {
      // Arrange
      repository.findOne.mockReturnValue(null);

      // Act & Assert
      await expect(service.findOne('123')).rejects.toThrow(UserNotFoundException);
      expect(repository.findOne).toHaveBeenCalledWith({ where: { id: '123' } });
    });
  });
});
```

### Integration Testing

Write integration tests for service interactions:

```typescript
describe('UserController (Integration)', () => {
  let app: INestApplication;
  let userRepository: Repository<User>;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: [User],
          synchronize: true,
        }),
        UserModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    userRepository = moduleFixture.get<Repository<User>>(getRepositoryToken(User));
  });

  afterAll(async () => {
    await app.close();
  });

  describe('GET /users/:id', () => {
    it('should return a user', async () => {
      // Arrange
      const user = new User();
      user.email = '<EMAIL>';
      user.password = 'password';
      await userRepository.save(user);

      // Act
      const response = await request(app.getHttpServer())
        .get(`/users/${user.id}`)
        .expect(200);

      // Assert
      expect(response.body.id).toEqual(user.id);
      expect(response.body.email).toEqual(user.email);
      expect(response.body).not.toHaveProperty('password');
    });

    it('should return 404 if user not found', async () => {
      // Act & Assert
      await request(app.getHttpServer())
        .get('/users/999')
        .expect(404);
    });
  });
});
```

### E2E Testing

Write end-to-end tests for complete workflows:

```typescript
describe('User Workflow (E2E)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  it('should register, login, and get profile', async () => {
    // Register
    const registerResponse = await request(app.getHttpServer())
      .post('/auth/register')
      .send({
        email: '<EMAIL>',
        password: 'password',
        name: 'Test User',
      })
      .expect(201);

    expect(registerResponse.body.id).toBeDefined();
    expect(registerResponse.body.email).toEqual('<EMAIL>');

    // Login
    const loginResponse = await request(app.getHttpServer())
      .post('/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password',
      })
      .expect(200);

    expect(loginResponse.body.token).toBeDefined();
    const token = loginResponse.body.token;

    // Get Profile
    const profileResponse = await request(app.getHttpServer())
      .get('/users/me')
      .set('Authorization', `Bearer ${token}`)
      .expect(200);

    expect(profileResponse.body.id).toEqual(registerResponse.body.id);
    expect(profileResponse.body.email).toEqual('<EMAIL>');
  });
});
```

### Test Coverage

Aim for high test coverage:

- Unit tests: 80%+ coverage
- Integration tests: Cover all critical paths
- E2E tests: Cover main user workflows

### Test Organization

Organize tests by feature:

```
user-service/
├── src/
│   ├── authentication/
│   │   ├── authentication.controller.ts
│   │   ├── authentication.service.ts
│   │   ├── authentication.repository.ts
│   │   ├── dto/
│   │   └── entities/
├── test/
│   ├── unit/
│   │   ├── authentication/
│   │   │   ├── authentication.controller.spec.ts
│   │   │   ├── authentication.service.spec.ts
│   │   │   └── authentication.repository.spec.ts
│   ├── integration/
│   │   ├── authentication/
│   │   │   ├── authentication.integration.spec.ts
│   ├── e2e/
│   │   ├── authentication.e2e-spec.ts
```

### Test Doubles

Use appropriate test doubles:

1. **Mocks**: For complex behavior verification
2. **Stubs**: For providing test data
3. **Spies**: For verifying method calls
4. **Fakes**: For simulating complex behavior

## Documentation

AI assistants must follow the established documentation patterns:

### Code Documentation

Use JSDoc comments for all classes, methods, and properties:

```typescript
/**
 * Service responsible for user management operations
 *
 * This service handles all user-related operations including:
 * - User creation and registration
 * - User profile management
 * - User authentication
 * - Email and phone verification
 *
 * @class UserService
 */
@Injectable()
export class UserService {
  /**
   * Creates a new user account
   *
   * This method:
   * 1. Validates the user input
   * 2. Checks if a user with the same email already exists
   * 3. Hashes the password
   * 4. Creates the user record
   * 5. Sends a verification email
   *
   * @param createUserDto - The data transfer object containing user information
   * @returns The newly created user entity (without password)
   * @throws UserAlreadyExistsException if a user with the same email already exists
   * @throws InternalServerErrorException if there's an error during user creation
   */
  async create(createUserDto: CreateUserDto): Promise<User> {
    // Implementation
  }
}
```

### API Documentation

Use Swagger decorators for API documentation:

```typescript
@ApiTags('users')
@Controller('users')
export class UserController {
  @Post()
  @ApiOperation({ summary: 'Create a new user' })
  @ApiDescription('Creates a new user account and sends a verification email')
  @ApiBody({ type: CreateUserDto })
  @ApiResponse({
    status: 201,
    description: 'User created successfully',
    type: UserResponseDto
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid input or email already exists',
    type: ErrorResponseDto
  })
  async create(@Body() createUserDto: CreateUserDto): Promise<UserResponseDto> {
    // Implementation
  }
}
```

### README Documentation

Create README.md files for each service:

```markdown
# User Service

This service handles all user-related functionality in the Social Commerce Platform.

## Features

- User registration and authentication
- Profile management
- Email and phone verification
- Password reset

## API Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|--------------|
| POST | /users | Create a new user | No |
| GET | /users | Get all users | Yes |
| GET | /users/:id | Get a user by ID | Yes |
| PUT | /users/:id | Update a user | Yes |
| DELETE | /users/:id | Delete a user | Yes |

## Getting Started

1. Install dependencies:
   ```bash
   npm install
   ```

2. Start the service:
   ```bash
   npm run start
   ```

## Testing

Run the tests:
```bash
npm run test
```

## Dependencies

- NestJS
- TypeORM
- PostgreSQL
- JWT
```

### Architecture Documentation

Document the architecture of each service:

```markdown
# User Service Architecture

## Overview

The User Service is responsible for user management, authentication, and profile management.

## Features

- User registration and authentication
- Profile management
- Email and phone verification
- Password reset

## Internal Structure

The service is organized by features:

- **Authentication**: Handles user authentication and authorization
- **Profile Management**: Handles user profile management
- **Verification**: Handles email and phone verification

## Data Model

### User Entity

```typescript
@Entity()
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  email: string;

  @Column()
  password: string;

  @Column()
  name: string;

  @Column({ default: false })
  isEmailVerified: boolean;

  @Column({ nullable: true })
  phone: string;

  @Column({ default: false })
  isPhoneVerified: boolean;

  @Column({ nullable: true })
  profilePicture: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
```

## API Endpoints

### Authentication

- `POST /auth/register`: Register a new user
- `POST /auth/login`: Login a user
- `POST /auth/refresh`: Refresh a JWT token
- `POST /auth/logout`: Logout a user

### Profile Management

- `GET /users/:id`: Get a user by ID
- `PUT /users/:id`: Update a user
- `DELETE /users/:id`: Delete a user
- `GET /users/me`: Get the current user

### Verification

- `POST /auth/verify-email`: Verify a user's email
- `POST /auth/verify-phone`: Verify a user's phone
- `POST /auth/resend-verification`: Resend verification email/SMS
```

### Inline Comments

Use inline comments for complex logic:

```typescript
// Calculate the discount based on the group size
// - 2-5 members: 5% discount
// - 6-10 members: 10% discount
// - 11+ members: 15% discount
let discount = 0;
if (groupSize >= 2 && groupSize <= 5) {
  discount = 0.05;
} else if (groupSize >= 6 && groupSize <= 10) {
  discount = 0.1;
} else if (groupSize >= 11) {
  discount = 0.15;
}

## Issue & Solution Documentation

**MANDATORY**: AI assistants must document every issue encountered and solution implemented during development.

### Documentation Requirements

All AI agents must follow the **Issue & Solution Documentation Protocol**:

1. **Document ALL issues encountered** during development
2. **Document ALL solutions implemented** with code examples
3. **Create comprehensive documentation files** in `docs/development/`
4. **Update documentation index** after adding new files
5. **Include technical details** (code before/after, root causes, impact)

### Documentation Structure

```
social-commerce-refined/docs/development/
├── README.md                              ← Index of all documentation
├── authentication-issues-solutions.md    ← Auth system issues & fixes
├── circular-reference-issues-solutions.md ← Dependency issues & fixes
├── docker-images-usage.md               ← Docker setup & troubleshooting
├── [feature-name]-issues-solutions.md   ← Feature-specific documentation
└── [component-name]-debugging.md        ← Component-specific debugging
```

### Documentation Template

```markdown
# [Feature/Component] - Issues & Solutions Documentation

## Overview
Brief description of the feature/component and issues encountered.

## Critical Issues & Solutions

### 1. **[Issue Name]** ⭐ **CRITICAL FIX** (if critical)

**Problem:** Clear description of the issue
**Symptoms:** What the user/developer experiences
**Root Cause:** Technical explanation of why it happens
**Code Before (Problematic):** [code example]
**Code After (Fixed):** [code example]
**Solution Details:** Step-by-step explanation
**Impact:** What this fix enables

## Files Modified
List all files changed with brief description

## Testing Results
What was verified to work after the fix

## Future Improvements
Recommendations for enhancement
```

### Mandatory Triggers

Document when you encounter:
- ❌ **Any error or failure** during implementation
- 🔧 **Any bug fix or workaround** applied
- 🔄 **Any circular dependency resolution**
- 🐳 **Any Docker/infrastructure issue**
- 🔐 **Any authentication/security fix**
- 📊 **Any database schema change**
- 🎯 **Any architectural decision or change**

### Documentation Workflow

1. **Encounter issue** → Document immediately
2. **Implement solution** → Update documentation with fix
3. **Verify solution** → Add testing results
4. **Complete feature** → Update `docs/development/README.md` index
5. **Continue development** → Repeat for next issues

### Location Requirements

- **All documentation** → `social-commerce-refined/docs/development/`
- **Index file** → `social-commerce-refined/docs/development/README.md`
- **Follow naming** → `[component]-issues-solutions.md`

## Production-Ready Features

AI assistants must implement production-ready features based on lessons learned from our development process:

### Security Implementation

1. **Helmet for Security Headers**
   ```typescript
   import helmet from 'helmet';

   // In main.ts
   app.use(helmet());
   ```

2. **CORS Configuration**
   ```typescript
   app.enableCors({
     origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
     credentials: true,
   });
   ```

3. **Rate Limiting**
   ```typescript
   import { ThrottlerModule } from '@nestjs/throttler';

   @Module({
     imports: [
       ThrottlerModule.forRoot({
         ttl: 60,
         limit: 10,
       }),
     ],
   })
   ```

4. **JWT Validation**
   ```typescript
   @Injectable()
   export class JwtStrategy extends PassportStrategy(Strategy) {
     constructor() {
       super({
         jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
         ignoreExpiration: false,
         secretOrKey: process.env.JWT_SECRET,
       });
     }
   }
   ```

### Monitoring and Observability

1. **Health Check Endpoints**
   ```typescript
   @Controller('health')
   export class HealthController {
     @Get()
     @HealthCheck()
     check() {
       return this.health.check([
         () => this.db.pingCheck('database'),
         () => this.memory.checkHeap('memory_heap', 150 * 1024 * 1024),
       ]);
     }
   }
   ```

2. **Request Logging**
   ```typescript
   @Injectable()
   export class LoggingInterceptor implements NestInterceptor {
     intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
       const request = context.switchToHttp().getRequest();
       const startTime = Date.now();

       return next.handle().pipe(
         tap(() => {
           const duration = Date.now() - startTime;
           this.logger.log(`${request.method} ${request.url} - ${duration}ms`);
         }),
       );
     }
   }
   ```

3. **Service Discovery**
   ```typescript
   // Register service with discovery
   @Injectable()
   export class ServiceDiscovery {
     async registerService(name: string, port: number) {
       // Implementation for service registration
     }
   }
   ```

### Error Recovery

1. **Circuit Breaker Pattern**
   ```typescript
   @Injectable()
   export class CircuitBreakerService {
     private breakers = new Map();

     async execute(serviceName: string, operation: () => Promise<any>) {
       const breaker = this.getBreaker(serviceName);
       return breaker.fire(operation);
     }
   }
   ```

2. **Graceful Degradation**
   ```typescript
   async getRecommendations(userId: string) {
     try {
       return await this.recommendationService.getRecommendations(userId);
     } catch (error) {
       this.logger.warn('Recommendation service unavailable, using fallback');
       return this.getFallbackRecommendations(userId);
     }
   }
   ```

## Environment Consistency

AI assistants must ensure environment consistency based on lessons learned from service startup issues:

### Docker-First Development

1. **Use Docker for All Services**
   ```dockerfile
   FROM node:18-alpine

   WORKDIR /app

   COPY package*.json ./
   RUN npm ci --only=production

   COPY . .

   EXPOSE 3000

   CMD ["npm", "run", "start:prod"]
   ```

2. **Docker Compose for Development**
   ```yaml
   version: '3.8'
   services:
     user-service:
       build: ./services/user-service
       ports:
         - "3001:3000"
       environment:
         - DATABASE_URL=******************************/userdb
       depends_on:
         - db
   ```

### Comprehensive Health Monitoring

1. **Health Check Implementation**
   ```typescript
   @Get('health')
   async healthCheck() {
     return {
       status: 'ok',
       timestamp: new Date().toISOString(),
       uptime: process.uptime(),
       memory: process.memoryUsage(),
       database: await this.checkDatabaseConnection(),
     };
   }
   ```

2. **Centralized Health Dashboard**
   - Implement a health monitoring dashboard that checks all services
   - Provide clear error messages when services fail
   - Include dependency status in health checks

### Standardized Scripts and Documentation

1. **Consistent Script Naming**
   ```json
   {
     "scripts": {
       "start": "node dist/main",
       "start:dev": "nest start --watch",
       "start:debug": "nest start --debug --watch",
       "start:prod": "node dist/main",
       "build": "nest build",
       "test": "jest",
       "test:watch": "jest --watch",
       "test:cov": "jest --coverage",
       "test:e2e": "jest --config ./test/jest-e2e.json"
     }
   }
   ```

2. **Automated Dependency Management**
   ```bash
   #!/bin/bash
   # install-deps.sh

   echo "Installing dependencies for all services..."

   for service in services/*/; do
     if [ -f "$service/package.json" ]; then
       echo "Installing dependencies for $service"
       cd "$service" && npm install && cd ../..
     fi
   done
   ```

### Clear Development Workflows

1. **Service Startup Sequence**
   ```bash
   # Start services in correct order
   docker-compose up -d database
   docker-compose up -d user-service
   docker-compose up -d store-service
   docker-compose up -d api-gateway
   ```

2. **Development Environment Setup**
   ```markdown
   ## Development Setup

   1. Clone the repository
   2. Run `./tools/scripts/setup-dev.sh`
   3. Start services: `docker-compose up`
   4. Verify health: `./tools/scripts/health-check.sh`
   ```

### Microservices Complexity Management

1. **Service Dependencies Documentation**
   ```yaml
   # service-dependencies.yml
   user-service:
     depends_on:
       - database
       - redis

   store-service:
     depends_on:
       - database
       - user-service
   ```

2. **Automated Service Management**
   ```typescript
   // Service manager for handling startup sequence
   @Injectable()
   export class ServiceManager {
     async startServices() {
       await this.startDatabase();
       await this.startCoreServices();
       await this.startDependentServices();
     }
   }
   ```

## Examples

### Example: User Service Controller

```typescript
import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Put,
  UseGuards,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { UserService } from './user.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { User } from './entities/user.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { Public } from '@app/common';

@ApiTags('users')
@Controller('users')
export class UserController {
  private readonly logger = new Logger(UserController.name);

  constructor(private readonly userService: UserService) {}

  @Post()
  @Public()
  @ApiOperation({ summary: 'Create a new user' })
  @ApiResponse({ status: 201, description: 'User created successfully', type: User })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async create(@Body() createUserDto: CreateUserDto): Promise<User> {
    this.logger.log(`Creating new user with email: ${createUserDto.email}`);

    try {
      return await this.userService.create(createUserDto);
    } catch (error) {
      this.logger.error(`Error creating user: ${error.message}`, error.stack);

      if (error.code === '23505') { // PostgreSQL unique violation
        throw new BadRequestException('Email already exists');
      }

      throw error;
    }
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all users' })
  @ApiResponse({ status: 200, description: 'Return all users', type: [User] })
  async findAll(): Promise<User[]> {
    this.logger.log('Getting all users');

    try {
      return await this.userService.findAll();
    } catch (error) {
      this.logger.error(`Error getting all users: ${error.message}`, error.stack);
      throw error;
    }
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get a user by ID' })
  @ApiResponse({ status: 200, description: 'Return the user', type: User })
  @ApiResponse({ status: 404, description: 'User not found' })
  async findOne(@Param('id') id: string): Promise<User> {
    this.logger.log(`Getting user with ID: ${id}`);

    try {
      const user = await this.userService.findOne(id);

      if (!user) {
        throw new NotFoundException(`User with ID ${id} not found`);
      }

      return user;
    } catch (error) {
      this.logger.error(`Error getting user: ${error.message}`, error.stack);
      throw error;
    }
  }
}
```

### Example: User Service Implementation

```typescript
import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from './entities/user.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { hash } from 'bcrypt';
import { EmailService } from '../shared/email.service';

@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly emailService: EmailService,
  ) {}

  async create(createUserDto: CreateUserDto): Promise<User> {
    this.logger.log(`Creating user with email: ${createUserDto.email}`);

    try {
      // Check if user with email already exists
      const existingUser = await this.userRepository.findOne({
        where: { email: createUserDto.email },
      });

      if (existingUser) {
        throw new BadRequestException(`User with email ${createUserDto.email} already exists`);
      }

      // Hash password
      const hashedPassword = await hash(createUserDto.password, 10);

      // Create user
      const user = this.userRepository.create({
        ...createUserDto,
        password: hashedPassword,
      });

      // Save user
      const savedUser = await this.userRepository.save(user);

      // Send verification email
      await this.emailService.sendVerificationEmail(savedUser.email);

      // Return user without password
      const { password, ...result } = savedUser;
      return result as User;
    } catch (error) {
      this.logger.error(`Error creating user: ${error.message}`, error.stack);
      throw error;
    }
  }

  async findAll(): Promise<User[]> {
    this.logger.log('Finding all users');

    try {
      const users = await this.userRepository.find();
      return users.map(({ password, ...result }) => result as User);
    } catch (error) {
      this.logger.error(`Error finding all users: ${error.message}`, error.stack);
      throw error;
    }
  }

  async findOne(id: string): Promise<User> {
    this.logger.log(`Finding user with ID: ${id}`);

    try {
      const user = await this.userRepository.findOne({ where: { id } });

      if (!user) {
        throw new NotFoundException(`User with ID ${id} not found`);
      }

      const { password, ...result } = user;
      return result as User;
    } catch (error) {
      this.logger.error(`Error finding user: ${error.message}`, error.stack);
      throw error;
    }
  }
}
```

### Example: Event-Driven Communication

```typescript
// User Service
@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @Inject('EVENT_EMITTER') private readonly eventEmitter: ClientProxy,
  ) {}

  async create(createUserDto: CreateUserDto): Promise<User> {
    // Create user logic...

    // Emit user created event
    this.eventEmitter.emit('user.created', {
      id: user.id,
      email: user.email,
      name: user.name,
    });

    return user;
  }
}

// Notification Service
@Controller()
export class NotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  @EventPattern('user.created')
  async handleUserCreated(data: any) {
    await this.notificationService.sendWelcomeEmail(data.email, data.name);
  }
}
```

### Example: API Gateway Configuration

```typescript
// Main Gateway Module
@Module({
  imports: [
    ClientsModule.register([
      {
        name: 'USER_SERVICE',
        transport: Transport.TCP,
        options: {
          host: process.env.USER_SERVICE_HOST || 'localhost',
          port: parseInt(process.env.USER_SERVICE_PORT || '3001'),
        },
      },
      {
        name: 'PRODUCT_SERVICE',
        transport: Transport.TCP,
        options: {
          host: process.env.PRODUCT_SERVICE_HOST || 'localhost',
          port: parseInt(process.env.PRODUCT_SERVICE_PORT || '3002'),
        },
      },
    ]),
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'secret',
      signOptions: { expiresIn: '1h' },
    }),
  ],
  controllers: [AppController, AuthController, UserController, ProductController],
  providers: [AppService, AuthService, JwtStrategy],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(LoggerMiddleware)
      .forRoutes('*');
  }
}
```

## 📏 **MANDATORY SERVICE NAMING CONVENTIONS**

**ALL AI AGENTS MUST FOLLOW ESTABLISHED NAMING CONVENTIONS:**

### **🔍 NAMING CONVENTION COMPLIANCE RULE**

**📋 NAMING CONVENTION REFERENCE:**
- **Full Guidelines:** `docs/guidelines/SERVICE-NAMING-CONVENTIONS.md`
- **Must be consulted** before creating any new service, database, container, or queue

**🎯 QUICK REFERENCE PATTERNS:**

**Database Names:**
- Pattern: `{service_name}_service`
- Examples: `user_service`, `store_service`, `product_service`

**Docker Images:**
- Services: `{service-name}-service`
- Infrastructure: `social-commerce-{component}`
- Examples: `user-service`, `store-service`, `social-commerce-frontend`

**Container Names:**
- Pattern: `social-commerce-{component}`
- Examples: `social-commerce-user-service`, `social-commerce-postgres`

**Queue Names:**
- Pattern: `{service_name}_queue`
- Examples: `user_queue`, `store_queue`, `product_queue`

**Environment Variables:**
- Service URLs: `{SERVICE_NAME}_SERVICE_URL`
- Database: `DB_DATABASE_{SERVICE_NAME}`
- Examples: `USER_SERVICE_URL`, `DB_DATABASE_USER`

**✅ MANDATORY TRIGGERS:**
Apply naming conventions when creating:
- ✅ **New microservices** → Follow service naming patterns
- ✅ **New databases** → Follow database naming patterns
- ✅ **New Docker containers** → Follow container naming patterns
- ✅ **New message queues** → Follow queue naming patterns
- ✅ **New environment variables** → Follow variable naming patterns

**🚫 FORBIDDEN ACTIONS:**
- ❌ Creating services without checking naming guidelines
- ❌ Using inconsistent naming patterns
- ❌ Ignoring established conventions

**✅ MANDATORY:** Check `SERVICE-NAMING-CONVENTIONS.md` before creating any new service or component.