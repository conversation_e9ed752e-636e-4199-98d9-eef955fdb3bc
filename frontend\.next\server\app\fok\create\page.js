(()=>{var e={};e.id=5045,e.ids=[5045],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},1550:(e,r,a)=>{"use strict";a.r(r),a.d(r,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>l,routeModule:()=>m,tree:()=>c});var t=a(67096),s=a(16132),i=a(37284),n=a.n(i),o=a(32564),d={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);a.d(r,d);let c=["",{children:["fok",{children:["create",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,91203)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\fok\\create\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,9291,23)),"next/dist/client/components/not-found-error"]}],l=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\fok\\create\\page.tsx"],u="/fok/create/page",p={require:a,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/fok/create/page",pathname:"/fok/create",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},53525:(e,r,a)=>{Promise.resolve().then(a.bind(a,22109))},22109:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>CreateGroupBuyPage});var t=a(30784),s=a(9885),i=a(57114),n=a(706),o=a(59872),d=a(84105),c=a(48042);let groupbuy_GroupBuyForm=({productId:e,storeId:r,isEditing:a=!1,onSuccess:l})=>{let u=(0,i.useRouter)(),[p,{isLoading:m}]=(0,d.ZD)(),{data:x,isLoading:h}=(0,c.C$)({}),[g,b]=(0,s.useState)({productId:e||"",price:"",minParticipants:"5",maxParticipants:"20",durationDays:"7"}),[y,f]=(0,s.useState)({}),[P,v]=(0,s.useState)(!1),handleChange=e=>{let{name:r,value:a}=e.target;b(e=>({...e,[r]:a})),y[r]&&f(e=>({...e,[r]:""}))},validateForm=()=>{let e={};return g.productId||(e.productId="Please select a product"),(!g.price||isNaN(parseFloat(g.price))||0>=parseFloat(g.price))&&(e.price="Valid price is required"),(!g.minParticipants||isNaN(parseInt(g.minParticipants,10))||2>parseInt(g.minParticipants,10))&&(e.minParticipants="Minimum participants must be at least 2"),(!g.maxParticipants||isNaN(parseInt(g.maxParticipants,10)))&&(e.maxParticipants="Valid maximum participants is required"),parseInt(g.minParticipants,10)>parseInt(g.maxParticipants,10)&&(e.minParticipants="Minimum participants cannot be greater than maximum"),(!g.durationDays||isNaN(parseInt(g.durationDays,10))||1>parseInt(g.durationDays,10))&&(e.durationDays="Duration must be at least 1 day"),f(e),0===Object.keys(e).length},handleSubmit=async e=>{if(e.preventDefault(),validateForm()){v(!0);try{let e=new Date;e.setDate(e.getDate()+parseInt(g.durationDays,10)),await p({productId:g.productId,price:parseFloat(g.price),minParticipants:parseInt(g.minParticipants,10),maxParticipants:parseInt(g.maxParticipants,10),expiresAt:e.toISOString()}).unwrap(),alert("Group buy created successfully!"),l?l():u.push("/fok")}catch(e){console.error("Failed to create group buy:",e),f({form:e.data?.message||"Failed to create group buy. Please try again."})}finally{v(!1)}}};return(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6",children:[t.jsx("h2",{className:"text-xl font-semibold mb-4",children:a?"Edit Group Buy":"Create Group Buy"}),y.form&&t.jsx("div",{className:"p-4 mb-4 text-red-700 bg-red-100 rounded-md dark:bg-red-900 dark:text-red-100",children:y.form}),(0,t.jsxs)("form",{onSubmit:handleSubmit,children:[(0,t.jsxs)("div",{className:"mb-4",children:[t.jsx("label",{htmlFor:"productId",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Product"}),(0,t.jsxs)("select",{id:"productId",name:"productId",value:g.productId,onChange:handleChange,className:`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 ${y.productId?"border-red-500 dark:border-red-500":"border-gray-300 dark:border-gray-700"} dark:bg-gray-800`,disabled:P||a,required:!0,children:[t.jsx("option",{value:"",children:"Select a product"}),h?t.jsx("option",{disabled:!0,children:"Loading products..."}):x?.products.map(e=>t.jsx("option",{value:e.id,children:e.name},e.id))]}),y.productId&&t.jsx("p",{className:"mt-1 text-sm text-red-600",children:y.productId})]}),t.jsx(n.Z,{label:"Price",name:"price",type:"number",step:"0.01",min:"0.01",value:g.price,onChange:handleChange,error:y.price,required:!0}),(0,t.jsxs)("div",{className:"flex gap-4",children:[t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{label:"Minimum Participants",name:"minParticipants",type:"number",min:"2",value:g.minParticipants,onChange:handleChange,error:y.minParticipants,required:!0})}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{label:"Maximum Participants",name:"maxParticipants",type:"number",min:"2",value:g.maxParticipants,onChange:handleChange,error:y.maxParticipants,required:!0})})]}),t.jsx(n.Z,{label:"Duration (days)",name:"durationDays",type:"number",min:"1",max:"30",value:g.durationDays,onChange:handleChange,error:y.durationDays,required:!0}),(0,t.jsxs)("div",{className:"flex justify-end gap-3 mt-6",children:[t.jsx(o.Z,{type:"button",variant:"outline",onClick:()=>u.push("/fok"),disabled:P||m,children:"Cancel"}),t.jsx(o.Z,{type:"submit",isLoading:P||m,disabled:P||m,children:a?"Save Changes":"Create Group Buy"})]})]})]})};function CreateGroupBuyPage(){let e=(0,i.useRouter)();return t.jsx("div",{className:"min-h-screen p-6",children:(0,t.jsxs)("div",{className:"max-w-3xl mx-auto",children:[t.jsx("div",{className:"mb-6",children:(0,t.jsxs)("button",{onClick:()=>e.push("/fok"),className:"text-primary-600 hover:text-primary-700 flex items-center",children:[t.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-1",viewBox:"0 0 20 20",fill:"currentColor",children:t.jsx("path",{fillRule:"evenodd",d:"M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z",clipRule:"evenodd"})}),"Back to Group Buys"]})}),t.jsx("h1",{className:"text-3xl font-bold mb-6",children:"Create Group Buy"}),t.jsx(groupbuy_GroupBuyForm,{})]})})}},91203:(e,r,a)=>{"use strict";a.r(r),a.d(r,{$$typeof:()=>n,__esModule:()=>i,default:()=>d});var t=a(95153);let s=(0,t.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\fok\create\page.tsx`),{__esModule:i,$$typeof:n}=s,o=s.default,d=o}};var r=require("../../../webpack-runtime.js");r.C(e);var __webpack_exec__=e=>r(r.s=e),a=r.X(0,[2103,2765,706,8042,4105],()=>__webpack_exec__(1550));module.exports=a})();