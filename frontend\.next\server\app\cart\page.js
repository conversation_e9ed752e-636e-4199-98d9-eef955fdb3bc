(()=>{var e={};e.id=1565,e.ids=[1565],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},13880:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>d});var a=r(67096),s=r(16132),n=r(37284),i=r.n(n),l=r(32564),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);r.d(t,c);let d=["",{children:["cart",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,95850)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\cart\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9291,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\cart\\page.tsx"],m="/cart/page",u={require:r,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/cart/page",pathname:"/cart",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},51875:(e,t,r)=>{Promise.resolve().then(r.bind(r,33208))},33208:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>CartPage});var a=r(30784);r(9885);var s=r(57114),n=r(87771),i=r(21233),l=r(59872),c=r(52451),d=r.n(c);let cart_CartItem=({item:e})=>{var t;let r=(0,n.I0)();return(0,a.jsxs)("div",{className:"flex py-4 border-b border-gray-200 dark:border-gray-700",children:[a.jsx("div",{className:"w-20 h-20 flex-shrink-0 bg-gray-100 dark:bg-gray-800 rounded overflow-hidden relative",children:e.imageUrl?a.jsx(d(),{src:e.imageUrl,alt:e.title,fill:!0,className:"object-cover"}):a.jsx("div",{className:"flex items-center justify-center h-full text-gray-400",children:"No Image"})}),(0,a.jsxs)("div",{className:"ml-4 flex-1",children:[a.jsx("h3",{className:"text-sm font-medium",children:e.title}),e.variantName&&(0,a.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:["Variant: ",e.variantName]}),(0,a.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("label",{htmlFor:`quantity-${e.id}`,className:"sr-only",children:"Quantity"}),a.jsx("select",{id:`quantity-${e.id}`,value:e.quantity,onChange:t=>{let a=parseInt(t.target.value,10);r((0,i.$R)({id:e.id,quantity:a}))},className:"rounded border border-gray-300 dark:border-gray-600 text-sm py-1 pl-2 pr-8 bg-white dark:bg-gray-800",children:[1,2,3,4,5,6,7,8,9,10].map(e=>a.jsx("option",{value:e,children:e},e))}),a.jsx("p",{className:"ml-4 text-sm font-medium",children:(t=e.price*e.quantity,new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(t))})]}),a.jsx("button",{type:"button",onClick:()=>{r((0,i.cl)(e.id))},className:"text-red-500 hover:text-red-700 text-sm",children:"Remove"})]})]})]})};function CartPage(){let{items:e}=(0,n.v9)(e=>e.cart),t=(0,n.I0)(),r=(0,s.useRouter)(),calculateTotal=()=>e.reduce((e,t)=>e+t.price*t.quantity,0),formatPrice=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e);return a.jsx("div",{className:"min-h-screen p-6 md:p-12",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[a.jsx("h1",{className:"text-3xl font-bold mb-8",children:"Your Cart"}),0===e.length?(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 text-center",children:[a.jsx("p",{className:"text-lg text-gray-600 dark:text-gray-300 mb-6",children:"Your cart is empty"}),a.jsx(l.Z,{onClick:()=>r.push("/products"),children:"Browse Products"})]}):(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden",children:[a.jsx("div",{className:"divide-y divide-gray-200 dark:divide-gray-700",children:e.map(e=>a.jsx("div",{className:"p-4",children:a.jsx(cart_CartItem,{item:e})},e.id))}),(0,a.jsxs)("div",{className:"p-6 bg-gray-50 dark:bg-gray-900",children:[(0,a.jsxs)("div",{className:"flex justify-between mb-4",children:[a.jsx("span",{className:"font-medium",children:"Subtotal"}),a.jsx("span",{children:formatPrice(calculateTotal())})]}),(0,a.jsxs)("div",{className:"flex justify-between mb-4",children:[a.jsx("span",{className:"font-medium",children:"Shipping"}),a.jsx("span",{children:"Calculated at checkout"})]}),(0,a.jsxs)("div",{className:"flex justify-between mb-6 text-lg font-bold",children:[a.jsx("span",{children:"Total"}),a.jsx("span",{children:formatPrice(calculateTotal())})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[a.jsx(l.Z,{onClick:()=>{r.push("/checkout")},fullWidth:!0,children:"Proceed to Checkout"}),a.jsx(l.Z,{variant:"outline",onClick:()=>{t((0,i.LL)())},fullWidth:!0,children:"Clear Cart"}),a.jsx(l.Z,{variant:"outline",onClick:()=>r.push("/products"),fullWidth:!0,children:"Continue Shopping"})]})]})]})]})})}},95850:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>i,__esModule:()=>n,default:()=>c});var a=r(95153);let s=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\cart\page.tsx`),{__esModule:n,$$typeof:i}=s,l=s.default,c=l}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),r=t.X(0,[2103,2765],()=>__webpack_exec__(13880));module.exports=r})();