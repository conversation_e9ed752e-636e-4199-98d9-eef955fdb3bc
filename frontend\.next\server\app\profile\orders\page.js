(()=>{var e={};e.id=1653,e.ids=[1653],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},62828:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var s=t(67096),a=t(16132),d=t(37284),i=t.n(d),n=t(32564),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(r,l);let o=["",{children:["profile",{children:["orders",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,85592)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\profile\\orders\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9291,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\profile\\orders\\page.tsx"],m="/profile/orders/page",u={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/profile/orders/page",pathname:"/profile/orders",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},68052:(e,r,t)=>{Promise.resolve().then(t.bind(t,22702))},22702:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>OrdersPage});var s=t(30784);t(9885);var a=t(57114),d=t(52451),i=t.n(d),n=t(11440),l=t.n(n),o=t(59872),c=t(86372);let m=c.g.injectEndpoints({endpoints:e=>({getUserAddresses:e.query({query:()=>"/addresses",providesTags:["Addresses"]}),getAddressById:e.query({query:e=>`/addresses/${e}`,providesTags:(e,r,t)=>[{type:"Address",id:t}]}),createAddress:e.mutation({query:e=>({url:"/addresses",method:"POST",body:e}),invalidatesTags:["Addresses"]}),updateAddress:e.mutation({query:({id:e,...r})=>({url:`/addresses/${e}`,method:"PUT",body:r}),invalidatesTags:(e,r,{id:t})=>[{type:"Address",id:t},"Addresses"]}),deleteAddress:e.mutation({query:e=>({url:`/addresses/${e}`,method:"DELETE"}),invalidatesTags:["Addresses"]}),setDefaultAddress:e.mutation({query:e=>({url:`/addresses/${e}/default`,method:"PUT"}),invalidatesTags:["Addresses"]}),getUserPaymentMethods:e.query({query:()=>"/payment-methods",providesTags:["PaymentMethods"]}),getPaymentMethodById:e.query({query:e=>`/payment-methods/${e}`,providesTags:(e,r,t)=>[{type:"PaymentMethod",id:t}]}),createPaymentMethod:e.mutation({query:e=>({url:"/payment-methods",method:"POST",body:e}),invalidatesTags:["PaymentMethods"]}),updatePaymentMethod:e.mutation({query:({id:e,...r})=>({url:`/payment-methods/${e}`,method:"PUT",body:r}),invalidatesTags:(e,r,{id:t})=>[{type:"PaymentMethod",id:t},"PaymentMethods"]}),deletePaymentMethod:e.mutation({query:e=>({url:`/payment-methods/${e}`,method:"DELETE"}),invalidatesTags:["PaymentMethods"]}),setDefaultPaymentMethod:e.mutation({query:e=>({url:`/payment-methods/${e}/default`,method:"PUT"}),invalidatesTags:["PaymentMethods"]}),getShippingMethods:e.query({query:()=>"/shipping-methods",providesTags:["ShippingMethods"]}),createOrder:e.mutation({query:e=>({url:"/orders",method:"POST",body:e}),invalidatesTags:["Orders","Cart"]}),getOrderById:e.query({query:e=>`/orders/${e}`,providesTags:(e,r,t)=>[{type:"Order",id:t}]}),getUserOrders:e.query({query:()=>"/orders",providesTags:["Orders"]})})}),{useGetUserAddressesQuery:u,useGetAddressByIdQuery:x,useCreateAddressMutation:g,useUpdateAddressMutation:p,useDeleteAddressMutation:y,useSetDefaultAddressMutation:h,useGetUserPaymentMethodsQuery:b,useGetPaymentMethodByIdQuery:v,useCreatePaymentMethodMutation:f,useUpdatePaymentMethodMutation:j,useDeletePaymentMethodMutation:N,useSetDefaultPaymentMethodMutation:P,useGetShippingMethodsQuery:k,useCreateOrderMutation:w,useGetOrderByIdQuery:q,useGetUserOrdersQuery:T}=m;var _=t(34087);function OrdersPage(){let e=(0,a.useRouter)(),{data:r,isLoading:t,error:d}=T(),formatDate=e=>{let r=new Date(e);return r.toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})},formatPrice=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e),getStatusColor=e=>{switch(e){case _.i.PENDING:return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";case _.i.PROCESSING:return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";case _.i.SHIPPED:return"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300";case _.i.DELIVERED:return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";case _.i.CANCELLED:return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";case _.i.REFUNDED:default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"}},getStatusLabel=e=>e.charAt(0).toUpperCase()+e.slice(1);return t?s.jsx("div",{className:"min-h-screen p-6",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[s.jsx("h1",{className:"text-2xl font-bold mb-6",children:"My Orders"}),s.jsx("div",{className:"animate-pulse space-y-6",children:[void 0,void 0,void 0].map((e,r)=>(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6",children:[s.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"}),s.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"}),s.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-full mb-2"}),s.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-full mb-2"}),s.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"})]},r))})]})}):d?s.jsx("div",{className:"min-h-screen p-6",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[s.jsx("h1",{className:"text-2xl font-bold mb-6",children:"My Orders"}),(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 text-center",children:[s.jsx("p",{className:"text-red-500 dark:text-red-400 mb-4",children:"There was an error loading your orders. Please try again."}),s.jsx(o.Z,{onClick:()=>window.location.reload(),children:"Retry"})]})]})}):s.jsx("div",{className:"min-h-screen p-6",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[s.jsx("h1",{className:"text-2xl font-bold",children:"My Orders"}),s.jsx(o.Z,{variant:"outline",onClick:()=>e.push("/profile"),children:"Back to Profile"})]}),r&&0!==r.length?s.jsx("div",{className:"space-y-6",children:r.map(e=>(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden",children:[s.jsx("div",{className:"p-4 border-b border-gray-200 dark:border-gray-700",children:(0,s.jsxs)("div",{className:"flex flex-wrap justify-between items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Order #",e.orderNumber]}),(0,s.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Placed on ",formatDate(e.createdAt)]})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(e.status)}`,children:getStatusLabel(e.status)}),s.jsx(l(),{href:`/profile/orders/${e.id}`,className:"ml-4 text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 text-sm font-medium",children:"View Details"})]})]})}),(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[e.items.slice(0,3).map(e=>(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("div",{className:"flex-shrink-0 h-16 w-16 bg-gray-200 dark:bg-gray-700 rounded-md overflow-hidden relative",children:e.productImage?s.jsx(i(),{src:e.productImage,alt:e.productTitle,fill:!0,className:"object-cover"}):s.jsx("div",{className:"flex items-center justify-center h-full text-gray-400",children:"No image"})}),(0,s.jsxs)("div",{className:"ml-4 flex-1 min-w-0",children:[s.jsx(l(),{href:`/products/${e.productId}`,className:"text-sm font-medium text-gray-900 dark:text-gray-100 hover:text-primary-600 dark:hover:text-primary-400",children:e.productTitle}),(0,s.jsxs)("div",{className:"mt-1 flex justify-between text-sm text-gray-500 dark:text-gray-400",children:[(0,s.jsxs)("p",{children:["Qty: ",e.quantity]}),s.jsx("p",{children:formatPrice(e.price*e.quantity)})]})]})]},e.id)),e.items.length>3&&(0,s.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400 text-center",children:["+ ",e.items.length-3," more items"]})]}),(0,s.jsxs)("div",{className:"mt-4 flex justify-between border-t border-gray-200 dark:border-gray-700 pt-4",children:[s.jsx("span",{className:"text-sm font-medium",children:"Total"}),s.jsx("span",{className:"text-sm font-medium",children:formatPrice(e.summary.total)})]})]})]},e.id))}):(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 text-center",children:[s.jsx("p",{className:"text-gray-500 dark:text-gray-400 mb-4",children:"You haven't placed any orders yet."}),s.jsx(o.Z,{onClick:()=>e.push("/products"),children:"Browse Products"})]})]})})}},85592:(e,r,t)=>{"use strict";t.r(r),t.d(r,{$$typeof:()=>i,__esModule:()=>d,default:()=>l});var s=t(95153);let a=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\profile\orders\page.tsx`),{__esModule:d,$$typeof:i}=a,n=a.default,l=n}};var r=require("../../../webpack-runtime.js");r.C(e);var __webpack_exec__=e=>r(r.s=e),t=r.X(0,[2103,2765],()=>__webpack_exec__(62828));module.exports=t})();