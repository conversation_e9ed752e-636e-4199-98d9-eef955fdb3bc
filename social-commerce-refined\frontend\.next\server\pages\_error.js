"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_error";
exports.ids = ["pages/_error"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! private-next-pages/_error */ \"./node_modules/next/dist/pages/_error.js\");\n/* harmony import */ var private_next_pages_error__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__]);\nprivate_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/_error\",\n        pathname: \"/_error\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/context/AuthContext.tsx":
/*!*************************************!*\
  !*** ./src/context/AuthContext.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/api */ \"./src/services/api.ts\");\n/* harmony import */ var jwt_decode__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! jwt-decode */ \"jwt-decode\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_services_api__WEBPACK_IMPORTED_MODULE_3__, jwt_decode__WEBPACK_IMPORTED_MODULE_4__]);\n([_services_api__WEBPACK_IMPORTED_MODULE_3__, jwt_decode__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check if user is logged in on initial load\n        const token = localStorage.getItem(\"token\");\n        if (token) {\n            try {\n                // Decode token to get user info\n                const decoded = (0,jwt_decode__WEBPACK_IMPORTED_MODULE_4__.jwtDecode)(token);\n                // Check if token is expired\n                const currentTime = Date.now() / 1000;\n                if (decoded.exp && decoded.exp < currentTime) {\n                    // Token is expired\n                    localStorage.removeItem(\"token\");\n                    setUser(null);\n                    setIsLoading(false);\n                    return;\n                }\n                // Fetch user profile\n                fetchUserProfile();\n            } catch (error) {\n                console.error(\"Invalid token:\", error);\n                localStorage.removeItem(\"token\");\n                setUser(null);\n                setIsLoading(false);\n            }\n        } else {\n            setIsLoading(false);\n        }\n    }, []);\n    const fetchUserProfile = async ()=>{\n        try {\n            const userData = await _services_api__WEBPACK_IMPORTED_MODULE_3__.userApi.getProfile();\n            setUser(userData);\n        } catch (error) {\n            console.error(\"Error fetching user profile:\", error);\n            localStorage.removeItem(\"token\");\n            setUser(null);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const login = async (email, password)=>{\n        console.log(\"AuthContext: Login attempt started\", {\n            email\n        });\n        console.log(\"AuthContext: Password details\", {\n            length: password.length,\n            firstChar: password.charAt(0),\n            lastChar: password.charAt(password.length - 1),\n            hasUppercase: /[A-Z]/.test(password),\n            hasLowercase: /[a-z]/.test(password),\n            hasNumbers: /[0-9]/.test(password),\n            hasSpecialChars: /[!@#$%^&*]/.test(password)\n        });\n        setIsLoading(true);\n        try {\n            console.log(\"AuthContext: Calling authApi.login\");\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__.authApi.login(email, password);\n            console.log(\"AuthContext: Login response received\", response);\n            const token = response.accessToken || response.token; // Handle both formats\n            console.log(\"AuthContext: Token extracted\", {\n                hasToken: !!token\n            });\n            localStorage.setItem(\"token\", token);\n            // Decode token to get user info\n            const decoded = (0,jwt_decode__WEBPACK_IMPORTED_MODULE_4__.jwtDecode)(token);\n            console.log(\"AuthContext: Token decoded\", decoded);\n            setUser({\n                id: decoded.sub,\n                email: decoded.email,\n                name: decoded.name || \"\",\n                role: decoded.role || \"user\"\n            });\n            console.log(\"AuthContext: User set, redirecting to dashboard\");\n            router.push(\"/dashboard\");\n        } catch (error) {\n            console.error(\"AuthContext: Login error:\", error);\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const register = async (userData)=>{\n        setIsLoading(true);\n        try {\n            await _services_api__WEBPACK_IMPORTED_MODULE_3__.authApi.register(userData);\n            // After registration, redirect to login\n            router.push(\"/login?registered=true\");\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = ()=>{\n        localStorage.removeItem(\"token\");\n        setUser(null);\n        router.push(\"/login\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            isLoading,\n            isAuthenticated: !!user,\n            login,\n            register,\n            logout\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\context\\\\AuthContext.tsx\",\n        lineNumber: 137,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuthContext);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/context/AuthContext.tsx\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @chakra-ui/react */ \"@chakra-ui/react\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/AuthContext */ \"./src/context/AuthContext.tsx\");\n/* harmony import */ var _styles_theme__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/styles/theme */ \"./src/styles/theme.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__, _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__, _styles_theme__WEBPACK_IMPORTED_MODULE_3__]);\n([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__, _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__, _styles_theme__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.ChakraProvider, {\n        theme: _styles_theme__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 10,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUNrRDtBQUNHO0FBQ2xCO0FBRXBCLFNBQVNHLElBQUksRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQVk7SUFDNUQscUJBQ0UsOERBQUNMLDREQUFjQTtRQUFDRSxPQUFPQSxxREFBS0E7a0JBQzFCLDRFQUFDRCw4REFBWUE7c0JBQ1gsNEVBQUNHO2dCQUFXLEdBQUdDLFNBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJaEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zb2NpYWwtY29tbWVyY2UtZnJvbnRlbmQvLi9zcmMvcGFnZXMvX2FwcC50c3g/ZjlkNiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IEFwcFByb3BzIH0gZnJvbSAnbmV4dC9hcHAnO1xuaW1wb3J0IHsgQ2hha3JhUHJvdmlkZXIgfSBmcm9tICdAY2hha3JhLXVpL3JlYWN0JztcbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gJ0AvY29udGV4dC9BdXRoQ29udGV4dCc7XG5pbXBvcnQgdGhlbWUgZnJvbSAnQC9zdHlsZXMvdGhlbWUnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHAoeyBDb21wb25lbnQsIHBhZ2VQcm9wcyB9OiBBcHBQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxDaGFrcmFQcm92aWRlciB0aGVtZT17dGhlbWV9PlxuICAgICAgPEF1dGhQcm92aWRlcj5cbiAgICAgICAgPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxuICAgICAgPC9BdXRoUHJvdmlkZXI+XG4gICAgPC9DaGFrcmFQcm92aWRlcj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJDaGFrcmFQcm92aWRlciIsIkF1dGhQcm92aWRlciIsInRoZW1lIiwiQXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/services/api.ts":
/*!*****************************!*\
  !*** ./src/services/api.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   productApi: () => (/* binding */ productApi),\n/* harmony export */   storeApi: () => (/* binding */ storeApi),\n/* harmony export */   userApi: () => (/* binding */ userApi)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"axios\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_0__]);\naxios__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n// Create axios instance\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: \"http://localhost:3001\",\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// Request interceptor for adding the auth token\napi.interceptors.request.use((config)=>{\n    const token = localStorage.getItem(\"token\");\n    if (token) {\n        config.headers[\"Authorization\"] = `Bearer ${token}`;\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor for handling errors\napi.interceptors.response.use((response)=>response, (error)=>{\n    // Handle 401 Unauthorized errors (token expired)\n    if (error.response?.status === 401) {\n        // Clear token and redirect to login\n        localStorage.removeItem(\"token\");\n        window.location.href = \"/login\";\n    }\n    return Promise.reject(error);\n});\n// Generic request function\nconst request = async (config)=>{\n    try {\n        const response = await api(config);\n        return response.data;\n    } catch (error) {\n        if (axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].isAxiosError(error)) {\n            // Handle specific error cases\n            const errorMessage = error.response?.data?.message || error.message;\n            console.error(`API Error: ${errorMessage}`);\n            throw new Error(errorMessage);\n        }\n        throw error;\n    }\n};\n// API endpoints\nconst authApi = {\n    login: (email, password)=>request({\n            url: \"/api/auth/login\",\n            method: \"POST\",\n            data: {\n                email,\n                password\n            }\n        }),\n    register: (userData)=>request({\n            url: \"/api/auth/register\",\n            method: \"POST\",\n            data: userData\n        }),\n    verifyEmail: (token)=>request({\n            url: `/api/auth/verify-email/${token}`,\n            method: \"GET\"\n        }),\n    forgotPassword: (email)=>request({\n            url: \"/api/auth/forgot-password\",\n            method: \"POST\",\n            data: {\n                email\n            }\n        }),\n    resetPassword: (token, password)=>request({\n            url: \"/api/auth/reset-password\",\n            method: \"POST\",\n            data: {\n                token,\n                password\n            }\n        })\n};\nconst userApi = {\n    getProfile: ()=>request({\n            url: \"/users/profile\",\n            method: \"GET\"\n        }),\n    updateProfile: (profileData)=>request({\n            url: \"/users/profile\",\n            method: \"PUT\",\n            data: profileData\n        })\n};\nconst storeApi = {\n    getAllStores: ()=>request({\n            url: \"/stores\",\n            method: \"GET\"\n        }),\n    getStoreById: (id)=>request({\n            url: `/stores/${id}`,\n            method: \"GET\"\n        }),\n    createStore: (storeData)=>request({\n            url: \"/stores\",\n            method: \"POST\",\n            data: storeData\n        }),\n    updateStore: (id, storeData)=>request({\n            url: `/stores/${id}`,\n            method: \"PUT\",\n            data: storeData\n        }),\n    deleteStore: (id)=>request({\n            url: `/stores/${id}`,\n            method: \"DELETE\"\n        })\n};\nconst productApi = {\n    getProductsByStore: (storeId)=>request({\n            url: `/stores/${storeId}/products`,\n            method: \"GET\"\n        }),\n    getProductById: (storeId, productId)=>request({\n            url: `/stores/${storeId}/products/${productId}`,\n            method: \"GET\"\n        }),\n    createProduct: (storeId, productData)=>request({\n            url: `/stores/${storeId}/products`,\n            method: \"POST\",\n            data: productData\n        }),\n    updateProduct: (storeId, productId, productData)=>request({\n            url: `/stores/${storeId}/products/${productId}`,\n            method: \"PUT\",\n            data: productData\n        }),\n    deleteProduct: (storeId, productId)=>request({\n            url: `/stores/${storeId}/products/${productId}`,\n            method: \"DELETE\"\n        })\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvc2VydmljZXMvYXBpLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUE2RTtBQUU3RSx3QkFBd0I7QUFDeEIsTUFBTUMsTUFBTUQsb0RBQVksQ0FBQztJQUN2QkcsU0FBUztJQUNUQyxTQUFTO1FBQ1AsZ0JBQWdCO0lBQ2xCO0FBQ0Y7QUFFQSxnREFBZ0Q7QUFDaERILElBQUlJLFlBQVksQ0FBQ0MsT0FBTyxDQUFDQyxHQUFHLENBQzFCLENBQUNDO0lBQ0MsTUFBTUMsUUFBUUMsYUFBYUMsT0FBTyxDQUFDO0lBQ25DLElBQUlGLE9BQU87UUFDVEQsT0FBT0osT0FBTyxDQUFDLGdCQUFnQixHQUFHLENBQUMsT0FBTyxFQUFFSyxNQUFNLENBQUM7SUFDckQ7SUFDQSxPQUFPRDtBQUNULEdBQ0EsQ0FBQ0k7SUFDQyxPQUFPQyxRQUFRQyxNQUFNLENBQUNGO0FBQ3hCO0FBR0YsMkNBQTJDO0FBQzNDWCxJQUFJSSxZQUFZLENBQUNVLFFBQVEsQ0FBQ1IsR0FBRyxDQUMzQixDQUFDUSxXQUFhQSxVQUNkLENBQUNIO0lBQ0MsaURBQWlEO0lBQ2pELElBQUlBLE1BQU1HLFFBQVEsRUFBRUMsV0FBVyxLQUFLO1FBQ2xDLG9DQUFvQztRQUNwQ04sYUFBYU8sVUFBVSxDQUFDO1FBQ3hCQyxPQUFPQyxRQUFRLENBQUNDLElBQUksR0FBRztJQUN6QjtJQUNBLE9BQU9QLFFBQVFDLE1BQU0sQ0FBQ0Y7QUFDeEI7QUFHRiwyQkFBMkI7QUFDM0IsTUFBTU4sVUFBVSxPQUFVRTtJQUN4QixJQUFJO1FBQ0YsTUFBTU8sV0FBNkIsTUFBTWQsSUFBSU87UUFDN0MsT0FBT08sU0FBU00sSUFBSTtJQUN0QixFQUFFLE9BQU9ULE9BQU87UUFDZCxJQUFJWiwwREFBa0IsQ0FBQ1ksUUFBUTtZQUM3Qiw4QkFBOEI7WUFDOUIsTUFBTVcsZUFBZVgsTUFBTUcsUUFBUSxFQUFFTSxNQUFNRyxXQUFXWixNQUFNWSxPQUFPO1lBQ25FQyxRQUFRYixLQUFLLENBQUMsQ0FBQyxXQUFXLEVBQUVXLGFBQWEsQ0FBQztZQUMxQyxNQUFNLElBQUlHLE1BQU1IO1FBQ2xCO1FBQ0EsTUFBTVg7SUFDUjtBQUNGO0FBRUEsZ0JBQWdCO0FBQ1QsTUFBTWUsVUFBVTtJQUNyQkMsT0FBTyxDQUFDQyxPQUFlQyxXQUNyQnhCLFFBQVE7WUFDTnlCLEtBQUs7WUFDTEMsUUFBUTtZQUNSWCxNQUFNO2dCQUFFUTtnQkFBT0M7WUFBUztRQUMxQjtJQUVGRyxVQUFVLENBQUNDLFdBQ1Q1QixRQUFRO1lBQ055QixLQUFLO1lBQ0xDLFFBQVE7WUFDUlgsTUFBTWE7UUFDUjtJQUVGQyxhQUFhLENBQUMxQixRQUNaSCxRQUFRO1lBQ055QixLQUFLLENBQUMsdUJBQXVCLEVBQUV0QixNQUFNLENBQUM7WUFDdEN1QixRQUFRO1FBQ1Y7SUFFRkksZ0JBQWdCLENBQUNQLFFBQ2Z2QixRQUFRO1lBQ055QixLQUFLO1lBQ0xDLFFBQVE7WUFDUlgsTUFBTTtnQkFBRVE7WUFBTTtRQUNoQjtJQUVGUSxlQUFlLENBQUM1QixPQUFlcUIsV0FDN0J4QixRQUFRO1lBQ055QixLQUFLO1lBQ0xDLFFBQVE7WUFDUlgsTUFBTTtnQkFBRVo7Z0JBQU9xQjtZQUFTO1FBQzFCO0FBQ0osRUFBRTtBQUVLLE1BQU1RLFVBQVU7SUFDckJDLFlBQVksSUFDVmpDLFFBQVE7WUFDTnlCLEtBQUs7WUFDTEMsUUFBUTtRQUNWO0lBRUZRLGVBQWUsQ0FBQ0MsY0FDZG5DLFFBQVE7WUFDTnlCLEtBQUs7WUFDTEMsUUFBUTtZQUNSWCxNQUFNb0I7UUFDUjtBQUNKLEVBQUU7QUFFSyxNQUFNQyxXQUFXO0lBQ3RCQyxjQUFjLElBQ1pyQyxRQUFRO1lBQ055QixLQUFLO1lBQ0xDLFFBQVE7UUFDVjtJQUVGWSxjQUFjLENBQUNDLEtBQ2J2QyxRQUFRO1lBQ055QixLQUFLLENBQUMsUUFBUSxFQUFFYyxHQUFHLENBQUM7WUFDcEJiLFFBQVE7UUFDVjtJQUVGYyxhQUFhLENBQUNDLFlBQ1p6QyxRQUFRO1lBQ055QixLQUFLO1lBQ0xDLFFBQVE7WUFDUlgsTUFBTTBCO1FBQ1I7SUFFRkMsYUFBYSxDQUFDSCxJQUFZRSxZQUN4QnpDLFFBQVE7WUFDTnlCLEtBQUssQ0FBQyxRQUFRLEVBQUVjLEdBQUcsQ0FBQztZQUNwQmIsUUFBUTtZQUNSWCxNQUFNMEI7UUFDUjtJQUVGRSxhQUFhLENBQUNKLEtBQ1p2QyxRQUFRO1lBQ055QixLQUFLLENBQUMsUUFBUSxFQUFFYyxHQUFHLENBQUM7WUFDcEJiLFFBQVE7UUFDVjtBQUNKLEVBQUU7QUFFSyxNQUFNa0IsYUFBYTtJQUN4QkMsb0JBQW9CLENBQUNDLFVBQ25COUMsUUFBUTtZQUNOeUIsS0FBSyxDQUFDLFFBQVEsRUFBRXFCLFFBQVEsU0FBUyxDQUFDO1lBQ2xDcEIsUUFBUTtRQUNWO0lBRUZxQixnQkFBZ0IsQ0FBQ0QsU0FBaUJFLFlBQ2hDaEQsUUFBUTtZQUNOeUIsS0FBSyxDQUFDLFFBQVEsRUFBRXFCLFFBQVEsVUFBVSxFQUFFRSxVQUFVLENBQUM7WUFDL0N0QixRQUFRO1FBQ1Y7SUFFRnVCLGVBQWUsQ0FBQ0gsU0FBaUJJLGNBQy9CbEQsUUFBUTtZQUNOeUIsS0FBSyxDQUFDLFFBQVEsRUFBRXFCLFFBQVEsU0FBUyxDQUFDO1lBQ2xDcEIsUUFBUTtZQUNSWCxNQUFNbUM7UUFDUjtJQUVGQyxlQUFlLENBQUNMLFNBQWlCRSxXQUFtQkUsY0FDbERsRCxRQUFRO1lBQ055QixLQUFLLENBQUMsUUFBUSxFQUFFcUIsUUFBUSxVQUFVLEVBQUVFLFVBQVUsQ0FBQztZQUMvQ3RCLFFBQVE7WUFDUlgsTUFBTW1DO1FBQ1I7SUFFRkUsZUFBZSxDQUFDTixTQUFpQkUsWUFDL0JoRCxRQUFRO1lBQ055QixLQUFLLENBQUMsUUFBUSxFQUFFcUIsUUFBUSxVQUFVLEVBQUVFLFVBQVUsQ0FBQztZQUMvQ3RCLFFBQVE7UUFDVjtBQUNKLEVBQUU7QUFFRixpRUFBZS9CLEdBQUdBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zb2NpYWwtY29tbWVyY2UtZnJvbnRlbmQvLi9zcmMvc2VydmljZXMvYXBpLnRzPzk1NmUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGF4aW9zLCB7IEF4aW9zRXJyb3IsIEF4aW9zUmVxdWVzdENvbmZpZywgQXhpb3NSZXNwb25zZSB9IGZyb20gJ2F4aW9zJztcblxuLy8gQ3JlYXRlIGF4aW9zIGluc3RhbmNlXG5jb25zdCBhcGkgPSBheGlvcy5jcmVhdGUoe1xuICBiYXNlVVJMOiAnaHR0cDovL2xvY2FsaG9zdDozMDAxJywgLy8gRGlyZWN0IGNvbm5lY3Rpb24gdG8gYmFja2VuZFxuICBoZWFkZXJzOiB7XG4gICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgfSxcbn0pO1xuXG4vLyBSZXF1ZXN0IGludGVyY2VwdG9yIGZvciBhZGRpbmcgdGhlIGF1dGggdG9rZW5cbmFwaS5pbnRlcmNlcHRvcnMucmVxdWVzdC51c2UoXG4gIChjb25maWcpID0+IHtcbiAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpO1xuICAgIGlmICh0b2tlbikge1xuICAgICAgY29uZmlnLmhlYWRlcnNbJ0F1dGhvcml6YXRpb24nXSA9IGBCZWFyZXIgJHt0b2tlbn1gO1xuICAgIH1cbiAgICByZXR1cm4gY29uZmlnO1xuICB9LFxuICAoZXJyb3IpID0+IHtcbiAgICByZXR1cm4gUHJvbWlzZS5yZWplY3QoZXJyb3IpO1xuICB9XG4pO1xuXG4vLyBSZXNwb25zZSBpbnRlcmNlcHRvciBmb3IgaGFuZGxpbmcgZXJyb3JzXG5hcGkuaW50ZXJjZXB0b3JzLnJlc3BvbnNlLnVzZShcbiAgKHJlc3BvbnNlKSA9PiByZXNwb25zZSxcbiAgKGVycm9yOiBBeGlvc0Vycm9yKSA9PiB7XG4gICAgLy8gSGFuZGxlIDQwMSBVbmF1dGhvcml6ZWQgZXJyb3JzICh0b2tlbiBleHBpcmVkKVxuICAgIGlmIChlcnJvci5yZXNwb25zZT8uc3RhdHVzID09PSA0MDEpIHtcbiAgICAgIC8vIENsZWFyIHRva2VuIGFuZCByZWRpcmVjdCB0byBsb2dpblxuICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ3Rva2VuJyk7XG4gICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9ICcvbG9naW4nO1xuICAgIH1cbiAgICByZXR1cm4gUHJvbWlzZS5yZWplY3QoZXJyb3IpO1xuICB9XG4pO1xuXG4vLyBHZW5lcmljIHJlcXVlc3QgZnVuY3Rpb25cbmNvbnN0IHJlcXVlc3QgPSBhc3luYyA8VD4oY29uZmlnOiBBeGlvc1JlcXVlc3RDb25maWcpOiBQcm9taXNlPFQ+ID0+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCByZXNwb25zZTogQXhpb3NSZXNwb25zZTxUPiA9IGF3YWl0IGFwaShjb25maWcpO1xuICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGlmIChheGlvcy5pc0F4aW9zRXJyb3IoZXJyb3IpKSB7XG4gICAgICAvLyBIYW5kbGUgc3BlY2lmaWMgZXJyb3IgY2FzZXNcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8IGVycm9yLm1lc3NhZ2U7XG4gICAgICBjb25zb2xlLmVycm9yKGBBUEkgRXJyb3I6ICR7ZXJyb3JNZXNzYWdlfWApO1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yTWVzc2FnZSk7XG4gICAgfVxuICAgIHRocm93IGVycm9yO1xuICB9XG59O1xuXG4vLyBBUEkgZW5kcG9pbnRzXG5leHBvcnQgY29uc3QgYXV0aEFwaSA9IHtcbiAgbG9naW46IChlbWFpbDogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nKSA9PlxuICAgIHJlcXVlc3Qoe1xuICAgICAgdXJsOiAnL2FwaS9hdXRoL2xvZ2luJyxcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgZGF0YTogeyBlbWFpbCwgcGFzc3dvcmQgfSxcbiAgICB9KSxcblxuICByZWdpc3RlcjogKHVzZXJEYXRhOiBhbnkpID0+XG4gICAgcmVxdWVzdCh7XG4gICAgICB1cmw6ICcvYXBpL2F1dGgvcmVnaXN0ZXInLFxuICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICBkYXRhOiB1c2VyRGF0YSxcbiAgICB9KSxcblxuICB2ZXJpZnlFbWFpbDogKHRva2VuOiBzdHJpbmcpID0+XG4gICAgcmVxdWVzdCh7XG4gICAgICB1cmw6IGAvYXBpL2F1dGgvdmVyaWZ5LWVtYWlsLyR7dG9rZW59YCxcbiAgICAgIG1ldGhvZDogJ0dFVCcsXG4gICAgfSksXG5cbiAgZm9yZ290UGFzc3dvcmQ6IChlbWFpbDogc3RyaW5nKSA9PlxuICAgIHJlcXVlc3Qoe1xuICAgICAgdXJsOiAnL2FwaS9hdXRoL2ZvcmdvdC1wYXNzd29yZCcsXG4gICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgIGRhdGE6IHsgZW1haWwgfSxcbiAgICB9KSxcblxuICByZXNldFBhc3N3b3JkOiAodG9rZW46IHN0cmluZywgcGFzc3dvcmQ6IHN0cmluZykgPT5cbiAgICByZXF1ZXN0KHtcbiAgICAgIHVybDogJy9hcGkvYXV0aC9yZXNldC1wYXNzd29yZCcsXG4gICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgIGRhdGE6IHsgdG9rZW4sIHBhc3N3b3JkIH0sXG4gICAgfSksXG59O1xuXG5leHBvcnQgY29uc3QgdXNlckFwaSA9IHtcbiAgZ2V0UHJvZmlsZTogKCkgPT5cbiAgICByZXF1ZXN0KHtcbiAgICAgIHVybDogJy91c2Vycy9wcm9maWxlJyxcbiAgICAgIG1ldGhvZDogJ0dFVCcsXG4gICAgfSksXG5cbiAgdXBkYXRlUHJvZmlsZTogKHByb2ZpbGVEYXRhOiBhbnkpID0+XG4gICAgcmVxdWVzdCh7XG4gICAgICB1cmw6ICcvdXNlcnMvcHJvZmlsZScsXG4gICAgICBtZXRob2Q6ICdQVVQnLFxuICAgICAgZGF0YTogcHJvZmlsZURhdGEsXG4gICAgfSksXG59O1xuXG5leHBvcnQgY29uc3Qgc3RvcmVBcGkgPSB7XG4gIGdldEFsbFN0b3JlczogKCkgPT5cbiAgICByZXF1ZXN0KHtcbiAgICAgIHVybDogJy9zdG9yZXMnLFxuICAgICAgbWV0aG9kOiAnR0VUJyxcbiAgICB9KSxcblxuICBnZXRTdG9yZUJ5SWQ6IChpZDogc3RyaW5nKSA9PlxuICAgIHJlcXVlc3Qoe1xuICAgICAgdXJsOiBgL3N0b3Jlcy8ke2lkfWAsXG4gICAgICBtZXRob2Q6ICdHRVQnLFxuICAgIH0pLFxuXG4gIGNyZWF0ZVN0b3JlOiAoc3RvcmVEYXRhOiBhbnkpID0+XG4gICAgcmVxdWVzdCh7XG4gICAgICB1cmw6ICcvc3RvcmVzJyxcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgZGF0YTogc3RvcmVEYXRhLFxuICAgIH0pLFxuXG4gIHVwZGF0ZVN0b3JlOiAoaWQ6IHN0cmluZywgc3RvcmVEYXRhOiBhbnkpID0+XG4gICAgcmVxdWVzdCh7XG4gICAgICB1cmw6IGAvc3RvcmVzLyR7aWR9YCxcbiAgICAgIG1ldGhvZDogJ1BVVCcsXG4gICAgICBkYXRhOiBzdG9yZURhdGEsXG4gICAgfSksXG5cbiAgZGVsZXRlU3RvcmU6IChpZDogc3RyaW5nKSA9PlxuICAgIHJlcXVlc3Qoe1xuICAgICAgdXJsOiBgL3N0b3Jlcy8ke2lkfWAsXG4gICAgICBtZXRob2Q6ICdERUxFVEUnLFxuICAgIH0pLFxufTtcblxuZXhwb3J0IGNvbnN0IHByb2R1Y3RBcGkgPSB7XG4gIGdldFByb2R1Y3RzQnlTdG9yZTogKHN0b3JlSWQ6IHN0cmluZykgPT5cbiAgICByZXF1ZXN0KHtcbiAgICAgIHVybDogYC9zdG9yZXMvJHtzdG9yZUlkfS9wcm9kdWN0c2AsXG4gICAgICBtZXRob2Q6ICdHRVQnLFxuICAgIH0pLFxuXG4gIGdldFByb2R1Y3RCeUlkOiAoc3RvcmVJZDogc3RyaW5nLCBwcm9kdWN0SWQ6IHN0cmluZykgPT5cbiAgICByZXF1ZXN0KHtcbiAgICAgIHVybDogYC9zdG9yZXMvJHtzdG9yZUlkfS9wcm9kdWN0cy8ke3Byb2R1Y3RJZH1gLFxuICAgICAgbWV0aG9kOiAnR0VUJyxcbiAgICB9KSxcblxuICBjcmVhdGVQcm9kdWN0OiAoc3RvcmVJZDogc3RyaW5nLCBwcm9kdWN0RGF0YTogYW55KSA9PlxuICAgIHJlcXVlc3Qoe1xuICAgICAgdXJsOiBgL3N0b3Jlcy8ke3N0b3JlSWR9L3Byb2R1Y3RzYCxcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgZGF0YTogcHJvZHVjdERhdGEsXG4gICAgfSksXG5cbiAgdXBkYXRlUHJvZHVjdDogKHN0b3JlSWQ6IHN0cmluZywgcHJvZHVjdElkOiBzdHJpbmcsIHByb2R1Y3REYXRhOiBhbnkpID0+XG4gICAgcmVxdWVzdCh7XG4gICAgICB1cmw6IGAvc3RvcmVzLyR7c3RvcmVJZH0vcHJvZHVjdHMvJHtwcm9kdWN0SWR9YCxcbiAgICAgIG1ldGhvZDogJ1BVVCcsXG4gICAgICBkYXRhOiBwcm9kdWN0RGF0YSxcbiAgICB9KSxcblxuICBkZWxldGVQcm9kdWN0OiAoc3RvcmVJZDogc3RyaW5nLCBwcm9kdWN0SWQ6IHN0cmluZykgPT5cbiAgICByZXF1ZXN0KHtcbiAgICAgIHVybDogYC9zdG9yZXMvJHtzdG9yZUlkfS9wcm9kdWN0cy8ke3Byb2R1Y3RJZH1gLFxuICAgICAgbWV0aG9kOiAnREVMRVRFJyxcbiAgICB9KSxcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGFwaTtcbiJdLCJuYW1lcyI6WyJheGlvcyIsImFwaSIsImNyZWF0ZSIsImJhc2VVUkwiLCJoZWFkZXJzIiwiaW50ZXJjZXB0b3JzIiwicmVxdWVzdCIsInVzZSIsImNvbmZpZyIsInRva2VuIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsImVycm9yIiwiUHJvbWlzZSIsInJlamVjdCIsInJlc3BvbnNlIiwic3RhdHVzIiwicmVtb3ZlSXRlbSIsIndpbmRvdyIsImxvY2F0aW9uIiwiaHJlZiIsImRhdGEiLCJpc0F4aW9zRXJyb3IiLCJlcnJvck1lc3NhZ2UiLCJtZXNzYWdlIiwiY29uc29sZSIsIkVycm9yIiwiYXV0aEFwaSIsImxvZ2luIiwiZW1haWwiLCJwYXNzd29yZCIsInVybCIsIm1ldGhvZCIsInJlZ2lzdGVyIiwidXNlckRhdGEiLCJ2ZXJpZnlFbWFpbCIsImZvcmdvdFBhc3N3b3JkIiwicmVzZXRQYXNzd29yZCIsInVzZXJBcGkiLCJnZXRQcm9maWxlIiwidXBkYXRlUHJvZmlsZSIsInByb2ZpbGVEYXRhIiwic3RvcmVBcGkiLCJnZXRBbGxTdG9yZXMiLCJnZXRTdG9yZUJ5SWQiLCJpZCIsImNyZWF0ZVN0b3JlIiwic3RvcmVEYXRhIiwidXBkYXRlU3RvcmUiLCJkZWxldGVTdG9yZSIsInByb2R1Y3RBcGkiLCJnZXRQcm9kdWN0c0J5U3RvcmUiLCJzdG9yZUlkIiwiZ2V0UHJvZHVjdEJ5SWQiLCJwcm9kdWN0SWQiLCJjcmVhdGVQcm9kdWN0IiwicHJvZHVjdERhdGEiLCJ1cGRhdGVQcm9kdWN0IiwiZGVsZXRlUHJvZHVjdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/services/api.ts\n");

/***/ }),

/***/ "./src/styles/theme.ts":
/*!*****************************!*\
  !*** ./src/styles/theme.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @chakra-ui/react */ \"@chakra-ui/react\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_0__]);\n_chakra_ui_react__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n// Color mode config\nconst config = {\n    initialColorMode: \"light\",\n    useSystemColorMode: false\n};\n// Custom colors\nconst colors = {\n    brand: {\n        50: \"#e6f7ff\",\n        100: \"#b3e0ff\",\n        200: \"#80caff\",\n        300: \"#4db3ff\",\n        400: \"#1a9dff\",\n        500: \"#0080ff\",\n        600: \"#0066cc\",\n        700: \"#004d99\",\n        800: \"#003366\",\n        900: \"#001a33\"\n    },\n    accent: {\n        50: \"#fff0e6\",\n        100: \"#ffd6b3\",\n        200: \"#ffbd80\",\n        300: \"#ffa34d\",\n        400: \"#ff8a1a\",\n        500: \"#ff7000\",\n        600: \"#cc5a00\",\n        700: \"#994300\",\n        800: \"#662d00\",\n        900: \"#331600\"\n    }\n};\n// Font configuration\nconst fonts = {\n    heading: 'Inter, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Helvetica, Arial, sans-serif',\n    body: 'Inter, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Helvetica, Arial, sans-serif'\n};\n// Component style overrides\nconst components = {\n    Button: {\n        baseStyle: {\n            fontWeight: \"semibold\",\n            borderRadius: \"md\"\n        },\n        variants: {\n            solid: {\n                bg: \"brand.500\",\n                color: \"white\",\n                _hover: {\n                    bg: \"brand.600\"\n                }\n            },\n            outline: {\n                borderColor: \"brand.500\",\n                color: \"brand.500\",\n                _hover: {\n                    bg: \"brand.50\"\n                }\n            },\n            ghost: {\n                color: \"brand.500\",\n                _hover: {\n                    bg: \"brand.50\"\n                }\n            }\n        }\n    },\n    Card: {\n        baseStyle: {\n            container: {\n                borderRadius: \"lg\",\n                boxShadow: \"md\"\n            }\n        }\n    },\n    Input: {\n        variants: {\n            outline: {\n                field: {\n                    borderColor: \"gray.300\",\n                    _focus: {\n                        borderColor: \"brand.500\",\n                        boxShadow: \"0 0 0 1px var(--chakra-colors-brand-500)\"\n                    }\n                }\n            }\n        }\n    }\n};\n// Create the theme\nconst theme = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_0__.extendTheme)({\n    config,\n    colors,\n    fonts,\n    components,\n    styles: {\n        global: {\n            body: {\n                bg: \"gray.50\"\n            }\n        }\n    }\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (theme);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/styles/theme.ts\n");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "@chakra-ui/react":
/*!***********************************!*\
  !*** external "@chakra-ui/react" ***!
  \***********************************/
/***/ ((module) => {

module.exports = import("@chakra-ui/react");;

/***/ }),

/***/ "axios":
/*!************************!*\
  !*** external "axios" ***!
  \************************/
/***/ ((module) => {

module.exports = import("axios");;

/***/ }),

/***/ "jwt-decode":
/*!*****************************!*\
  !*** external "jwt-decode" ***!
  \*****************************/
/***/ ((module) => {

module.exports = import("jwt-decode");;

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();