import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { Cart } from './cart.entity';

@Entity('cart_items')
@Index(['cartId'])
@Index(['productId'])
export class CartItem {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid')
  cartId: string;

  @ManyToOne(() => Cart, (cart) => cart.items, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'cartId' })
  cart: Cart;

  @Column('uuid')
  productId: string;

  @Column({ nullable: true })
  variantId: string;

  @Column('int')
  quantity: number;

  @Column('decimal', { precision: 10, scale: 2 })
  price: number;

  @Column('decimal', { precision: 10, scale: 2, default: 0 })
  discount: number;

  @Column('decimal', { precision: 10, scale: 2 })
  total: number;

  // Product information cached for performance
  @Column({ nullable: true })
  productName: string;

  @Column({ nullable: true })
  productImage: string;

  @Column('uuid', { nullable: true })
  storeId: string;

  @Column({ nullable: true })
  storeName: string;

  // Additional options (size, color, etc.)
  @Column('jsonb', { nullable: true })
  selectedOptions: Record<string, any>;

  @Column('jsonb', { nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn({ type: 'timestamp with time zone' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone' })
  updatedAt: Date;

  // Helper methods
  calculateTotal(): void {
    const price = this.price || 0;
    const discount = this.discount || 0;
    const quantity = this.quantity || 0;
    this.total = (price - discount) * quantity;
  }

  updateQuantity(newQuantity: number): void {
    this.quantity = newQuantity;
    this.calculateTotal();
  }

  updatePrice(newPrice: number): void {
    this.price = newPrice;
    this.calculateTotal();
  }
}
