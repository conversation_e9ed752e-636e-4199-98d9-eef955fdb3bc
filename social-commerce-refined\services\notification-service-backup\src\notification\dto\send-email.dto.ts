import { IsString, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ptional, <PERSON><PERSON>rray } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class SendEmailDto {
  @ApiProperty({
    description: 'Recipient email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  to: string;

  @ApiProperty({
    description: 'Email subject',
    example: 'Welcome to Social Commerce Platform',
  })
  @IsString()
  subject: string;

  @ApiProperty({
    description: 'Email content (HTML or plain text)',
    example: '<h1>Welcome!</h1><p>Thank you for joining our platform.</p>',
  })
  @IsString()
  content: string;

  @ApiPropertyOptional({
    description: 'Sender email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsOptional()
  from?: string;

  @ApiPropertyOptional({
    description: 'Email template variables',
    example: { userName: '<PERSON>', verificationLink: 'https://...' },
  })
  @IsOptional()
  variables?: Record<string, any>;
}
