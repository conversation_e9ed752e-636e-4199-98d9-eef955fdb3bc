{"name": "@app/messaging", "version": "0.1.0", "description": "Messaging library for Social Commerce Platform", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "test": "jest"}, "dependencies": {"@nestjs/common": "^11.1.1", "@nestjs/core": "^11.1.1", "@nestjs/microservices": "^11.1.1", "rxjs": "^7.8.1"}, "devDependencies": {"@types/node": "^22.15.21", "typescript": "^5.8.3"}, "peerDependencies": {"@nestjs/common": "^11.0.0", "@nestjs/core": "^11.0.0", "@nestjs/microservices": "^11.0.0"}}