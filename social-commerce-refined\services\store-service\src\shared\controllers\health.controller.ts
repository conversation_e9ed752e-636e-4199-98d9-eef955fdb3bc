import { <PERSON>, Get, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { HealthCheckService, HealthCheck, TypeOrmHealthIndicator, MemoryHealthIndicator, DiskHealthIndicator } from '@nestjs/terminus';

@ApiTags('health')
@Controller('health')
export class HealthController {
  private readonly logger = new Logger(HealthController.name);

  constructor(
    private readonly health: HealthCheckService,
    private readonly db: TypeOrmHealthIndicator,
    private readonly memory: MemoryHealthIndicator,
    private readonly disk: DiskHealthIndicator,
  ) {}

  @Get()
  @HealthCheck()
  @ApiOperation({ summary: 'Check service health' })
  @ApiResponse({ status: 200, description: 'Service is healthy' })
  @ApiResponse({ status: 503, description: 'Service is unhealthy' })
  async check() {
    this.logger.log('Health check requested');
    
    return this.health.check([
      // Database health check
      async () => this.db.pingCheck('database', { timeout: 3000 }),
      
      // Memory health check
      async () => this.memory.checkHeap('memory_heap', 200 * 1024 * 1024), // 200MB
      
      // Disk health check
      async () => this.disk.checkStorage('disk', { path: '/', thresholdPercent: 0.9 }),
      
      // Service status
      async () => ({ service: { status: 'up' } }),
    ]);
  }

  @Get('readiness')
  @HealthCheck()
  @ApiOperation({ summary: 'Check if service is ready to accept requests' })
  @ApiResponse({ status: 200, description: 'Service is ready' })
  @ApiResponse({ status: 503, description: 'Service is not ready' })
  async checkReadiness() {
    this.logger.log('Readiness check requested');
    
    return this.health.check([
      // Database health check
      async () => this.db.pingCheck('database', { timeout: 3000 }),
    ]);
  }

  @Get('liveness')
  @HealthCheck()
  @ApiOperation({ summary: 'Check if service is alive' })
  @ApiResponse({ status: 200, description: 'Service is alive' })
  @ApiResponse({ status: 503, description: 'Service is not alive' })
  async checkLiveness() {
    this.logger.log('Liveness check requested');
    
    return this.health.check([
      // Memory health check
      async () => this.memory.checkHeap('memory_heap', 200 * 1024 * 1024), // 200MB
      
      // Service status
      async () => ({ service: { status: 'up' } }),
    ]);
  }
}
