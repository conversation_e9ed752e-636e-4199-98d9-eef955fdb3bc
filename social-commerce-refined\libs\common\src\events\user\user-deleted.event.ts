import { BaseEvent } from '../base-event.interface';

/**
 * Event emitted when a user is deleted
 */
export class UserDeletedEvent implements BaseEvent<UserDeletedPayload> {
  id: string;
  type: string = 'user.deleted';
  version: string = '1.0';
  timestamp: string;
  producer: string = 'user-service';
  payload: UserDeletedPayload;

  constructor(payload: UserDeletedPayload) {
    this.id = payload.id;
    this.timestamp = new Date().toISOString();
    this.payload = payload;
  }
}

/**
 * Payload for UserDeletedEvent
 */
export interface UserDeletedPayload {
  /**
   * User ID
   */
  id: string;

  /**
   * User email
   */
  email: string;

  /**
   * Deletion timestamp
   */
  deletedAt: string;
}
