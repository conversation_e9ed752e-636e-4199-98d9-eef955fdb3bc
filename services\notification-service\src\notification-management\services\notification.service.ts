import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Notification, NotificationStatus, NotificationType } from '../entities/notification.entity';
import { CreateNotificationDto } from '../dto/create-notification.dto';
import { EmailService } from './email.service';
import { SmsService } from './sms.service';

@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);

  constructor(
    @InjectRepository(Notification)
    private readonly notificationRepository: Repository<Notification>,
    private readonly emailService: EmailService,
    private readonly smsService: SmsService,
  ) {}

  async create(createNotificationDto: CreateNotificationDto): Promise<Notification> {
    this.logger.log(`Creating notification for user: ${createNotificationDto.userId}`);

    const notification = this.notificationRepository.create(createNotificationDto);
    const savedNotification = await this.notificationRepository.save(notification);

    // Send notification immediately
    await this.sendNotification(savedNotification);

    return savedNotification;
  }

  async findAll(): Promise<Notification[]> {
    return this.notificationRepository.find({
      order: { createdAt: 'DESC' },
    });
  }

  async findById(id: string): Promise<Notification> {
    const notification = await this.notificationRepository.findOne({ where: { id } });
    if (!notification) {
      throw new NotFoundException(`Notification with ID ${id} not found`);
    }
    return notification;
  }

  async findByUserId(userId: string): Promise<Notification[]> {
    return this.notificationRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });
  }

  private async sendNotification(notification: Notification): Promise<void> {
    try {
      this.logger.log(`Sending ${notification.type} notification: ${notification.id}`);

      switch (notification.type) {
        case NotificationType.EMAIL:
          await this.emailService.sendEmail({
            to: notification.recipient,
            subject: notification.subject,
            content: notification.content,
          });
          break;

        case NotificationType.SMS:
          await this.smsService.sendSms({
            to: notification.recipient,
            message: notification.content,
          });
          break;

        case NotificationType.PUSH:
          // TODO: Implement push notification
          this.logger.log('Push notifications not implemented yet');
          break;
      }

      // Update notification status
      notification.status = NotificationStatus.SENT;
      notification.sentAt = new Date();
      await this.notificationRepository.save(notification);

      this.logger.log(`Notification sent successfully: ${notification.id}`);
    } catch (error) {
      this.logger.error(`Failed to send notification ${notification.id}:`, error.message);
      
      notification.status = NotificationStatus.FAILED;
      await this.notificationRepository.save(notification);
    }
  }
}
