#!/bin/bash

# Create store-management subdirectories
mkdir -p services/store-service/src/store-management/controllers
mkdir -p services/store-service/src/store-management/services
mkdir -p services/store-service/src/store-management/repositories
mkdir -p services/store-service/src/store-management/entities
mkdir -p services/store-service/src/store-management/dto

# Create product-management subdirectories
mkdir -p services/store-service/src/product-management/controllers
mkdir -p services/store-service/src/product-management/services
mkdir -p services/store-service/src/product-management/repositories
mkdir -p services/store-service/src/product-management/entities
mkdir -p services/store-service/src/product-management/dto

# Create shared subdirectories
mkdir -p services/store-service/src/shared/guards
mkdir -p services/store-service/src/shared/decorators
mkdir -p services/store-service/src/shared/filters
mkdir -p services/store-service/src/shared/interceptors
mkdir -p services/store-service/src/shared/middleware
mkdir -p services/store-service/src/shared/utils
mkdir -p services/store-service/src/shared/dto
mkdir -p services/store-service/src/shared/entities
mkdir -p services/store-service/src/shared/controllers

# Create test directories
mkdir -p services/store-service/test

echo "Store Service directories created successfully!"
