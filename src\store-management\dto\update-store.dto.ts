import { IsString, IsOptional, IsObject, IsBoolean, IsUrl } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class UpdateStoreDto {
  @ApiPropertyOptional({
    description: 'The name of the store',
    example: 'My Updated Store',
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional({
    description: 'The description of the store',
    example: 'This is an updated store description',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'The URL of the store logo',
    example: 'https://example.com/updated-logo.png',
  })
  @IsUrl()
  @IsOptional()
  logoUrl?: string;

  @ApiPropertyOptional({
    description: 'The URL of the store banner',
    example: 'https://example.com/updated-banner.png',
  })
  @IsUrl()
  @IsOptional()
  bannerUrl?: string;

  @ApiPropertyOptional({
    description: 'Whether the store is active',
    example: false,
  })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'The contact information of the store',
    example: {
      email: '<EMAIL>',
      phone: '+0987654321',
      website: 'https://updated-example.com',
      address: {
        street: '456 New St',
        city: 'Los Angeles',
        state: 'CA',
        zipCode: '90001',
        country: 'USA',
      },
    },
  })
  @IsObject()
  @IsOptional()
  contactInfo?: {
    email?: string;
    phone?: string;
    website?: string;
    address?: {
      street?: string;
      city?: string;
      state?: string;
      zipCode?: string;
      country?: string;
    };
  };

  @ApiPropertyOptional({
    description: 'The social links of the store',
    example: {
      facebook: 'https://facebook.com/updated-store',
      twitter: 'https://twitter.com/updated-store',
      instagram: 'https://instagram.com/updated-store',
    },
  })
  @IsObject()
  @IsOptional()
  socialLinks?: Record<string, string>;

  @ApiPropertyOptional({
    description: 'The business hours of the store',
    example: {
      monday: { open: '10:00', close: '18:00' },
      tuesday: { open: '10:00', close: '18:00' },
      wednesday: { open: '10:00', close: '18:00' },
      thursday: { open: '10:00', close: '18:00' },
      friday: { open: '10:00', close: '18:00' },
      saturday: { open: '11:00', close: '16:00' },
      sunday: { open: '11:00', close: '16:00' },
    },
  })
  @IsObject()
  @IsOptional()
  businessHours?: {
    monday?: { open: string; close: string };
    tuesday?: { open: string; close: string };
    wednesday?: { open: string; close: string };
    thursday?: { open: string; close: string };
    friday?: { open: string; close: string };
    saturday?: { open: string; close: string };
    sunday?: { open: string; close: string };
  };

  @ApiPropertyOptional({
    description: 'The settings of the store',
    example: {
      theme: 'dark',
      currency: 'EUR',
      language: 'fr',
      notifications: {
        email: false,
        sms: true,
        push: false,
      },
    },
  })
  @IsObject()
  @IsOptional()
  settings?: {
    theme?: string;
    currency?: string;
    language?: string;
    notifications?: {
      email?: boolean;
      sms?: boolean;
      push?: boolean;
    };
  };
}
