import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { Order } from './order.entity';

@Entity('order_items')
@Index(['orderId'])
@Index(['productId'])
@Index(['storeId'])
export class OrderItem {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid')
  orderId: string;

  @ManyToOne(() => Order, (order) => order.items, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'orderId' })
  order: Order;

  @Column('uuid')
  productId: string;

  @Column({ nullable: true })
  variantId: string;

  @Column('int')
  quantity: number;

  // Pricing Information (snapshot at time of order)
  @Column('decimal', { precision: 10, scale: 2 })
  price: number;

  @Column('decimal', { precision: 10, scale: 2, default: 0 })
  discount: number;

  @Column('decimal', { precision: 10, scale: 2 })
  total: number;

  // Product Information (cached for order history)
  @Column()
  productName: string;

  @Column('text', { nullable: true })
  productDescription: string;

  @Column({ nullable: true })
  productImage: string;

  @Column('uuid')
  storeId: string;

  @Column({ nullable: true })
  storeName: string;

  // Product Options (size, color, etc.)
  @Column('jsonb', { nullable: true })
  selectedOptions: Record<string, any>;

  // Additional Information
  @Column('jsonb', { nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn({ type: 'timestamp with time zone' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone' })
  updatedAt: Date;

  // Helper methods
  calculateTotal(): void {
    const price = this.price || 0;
    const discount = this.discount || 0;
    const quantity = this.quantity || 0;
    this.total = (price - discount) * quantity;
  }

  // Computed properties
  get unitPrice(): number {
    return this.price - this.discount;
  }

  get totalSavings(): number {
    return this.discount * this.quantity;
  }
}
