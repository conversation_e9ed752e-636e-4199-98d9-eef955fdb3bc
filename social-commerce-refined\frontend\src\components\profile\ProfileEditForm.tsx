import React from 'react';
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  Input,
  Stack,
  Textarea,
  FormErrorMessage,
  useColorModeValue,
  Heading,
  Flex,
  Avatar,
  IconButton,
  Divider,
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import { FiCamera } from 'react-icons/fi';

interface ProfileFormData {
  name: string;
  email: string;
  phone?: string;
  address?: string;
  dateOfBirth?: string;
  website?: string;
  bio?: string;
}

interface ProfileEditFormProps {
  initialData: ProfileFormData;
  onSubmit: (data: ProfileFormData) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

const ProfileEditForm: React.FC<ProfileEditFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
}) => {
  const {
    handleSubmit,
    register,
    formState: { errors },
  } = useForm<ProfileFormData>({
    defaultValues: initialData,
  });

  const bgColor = useColorModeValue('white', 'gray.700');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  return (
    <Box
      as="form"
      onSubmit={handleSubmit(onSubmit)}
      bg={bgColor}
      borderRadius="lg"
      borderWidth="1px"
      borderColor={borderColor}
      p={6}
    >
      <Heading size="md" mb={6}>
        Edit Profile
      </Heading>

      {/* Avatar Upload */}
      <Flex justify="center" mb={6}>
        <Box position="relative">
          <Avatar
            size="xl"
            name={initialData.name || initialData.email}
            src=""
          />
          <IconButton
            aria-label="Change profile picture"
            icon={<FiCamera />}
            size="sm"
            colorScheme="brand"
            rounded="full"
            position="absolute"
            bottom="0"
            right="0"
          />
        </Box>
      </Flex>

      <Stack spacing={4}>
        <FormControl id="name" isInvalid={!!errors.name}>
          <FormLabel>Name</FormLabel>
          <Input
            {...register('name', {
              required: 'Name is required',
            })}
          />
          <FormErrorMessage>{errors.name?.message}</FormErrorMessage>
        </FormControl>

        <FormControl id="email" isInvalid={!!errors.email}>
          <FormLabel>Email</FormLabel>
          <Input
            type="email"
            {...register('email', {
              required: 'Email is required',
              pattern: {
                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                message: 'Invalid email address',
              },
            })}
          />
          <FormErrorMessage>{errors.email?.message}</FormErrorMessage>
        </FormControl>

        <FormControl id="phone" isInvalid={!!errors.phone}>
          <FormLabel>Phone</FormLabel>
          <Input
            {...register('phone', {
              pattern: {
                value: /^[0-9+-]+$/,
                message: 'Invalid phone number',
              },
            })}
          />
          <FormErrorMessage>{errors.phone?.message}</FormErrorMessage>
        </FormControl>

        <FormControl id="address" isInvalid={!!errors.address}>
          <FormLabel>Address</FormLabel>
          <Input {...register('address')} />
          <FormErrorMessage>{errors.address?.message}</FormErrorMessage>
        </FormControl>

        <FormControl id="dateOfBirth" isInvalid={!!errors.dateOfBirth}>
          <FormLabel>Date of Birth</FormLabel>
          <Input
            type="date"
            {...register('dateOfBirth')}
          />
          <FormErrorMessage>{errors.dateOfBirth?.message}</FormErrorMessage>
        </FormControl>

        <FormControl id="website" isInvalid={!!errors.website}>
          <FormLabel>Website</FormLabel>
          <Input
            {...register('website', {
              pattern: {
                value: /^(https?:\/\/)?(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_+.~#?&//=]*)$/,
                message: 'Invalid URL',
              },
            })}
          />
          <FormErrorMessage>{errors.website?.message}</FormErrorMessage>
        </FormControl>

        <FormControl id="bio" isInvalid={!!errors.bio}>
          <FormLabel>Bio</FormLabel>
          <Textarea
            {...register('bio')}
            rows={4}
            resize="vertical"
          />
          <FormErrorMessage>{errors.bio?.message}</FormErrorMessage>
        </FormControl>

        <Divider my={4} />

        <Flex justify="flex-end" gap={4}>
          <Button
            variant="outline"
            onClick={onCancel}
            isDisabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            colorScheme="brand"
            isLoading={isLoading}
          >
            Save Changes
          </Button>
        </Flex>
      </Stack>
    </Box>
  );
};

export default ProfileEditForm;
