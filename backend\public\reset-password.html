<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        .response {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
            white-space: pre-wrap;
        }
        .nav {
            margin-bottom: 20px;
        }
        .nav a {
            margin-right: 10px;
            color: #0066cc;
            text-decoration: none;
        }
        .nav a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="nav">
        <a href="/">Home</a>
        <a href="/reset-password.html">Reset Password</a>
    </div>

    <h1>Reset Password</h1>
    
    <div class="form-group">
        <label for="username">Username:</label>
        <input type="text" id="username" value="simpletestuser">
    </div>
    
    <div class="form-group">
        <label for="password">New Password:</label>
        <input type="password" id="password" value="password123">
    </div>
    
    <button onclick="resetPassword()">Reset Password</button>
    
    <div class="response" id="response"></div>
    
    <script>
        async function resetPassword() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const responseElement = document.getElementById('response');
            
            responseElement.textContent = 'Sending request...';
            
            try {
                // Direct fetch call
                console.log('Making direct fetch call to /debug/reset-password');
                const response = await fetch('/debug/reset-password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username,
                        password,
                    }),
                });
                
                console.log('Response status:', response.status);
                const data = await response.json().catch(() => ({ error: 'Failed to parse JSON response' }));
                console.log('Response data:', data);
                
                responseElement.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                console.error('Error:', error);
                responseElement.textContent = 'Error: ' + error.message;
            }
        }
    </script>
</body>
</html>
