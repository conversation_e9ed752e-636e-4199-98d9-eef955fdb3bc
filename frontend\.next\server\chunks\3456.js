"use strict";exports.id=3456,exports.ids=[3456],exports.modules={93603:(e,r,t)=>{t.d(r,{Z:()=>__WEBPACK_DEFAULT_EXPORT__});var a=t(30784),s=t(9885),l=t(27870),i=t(52451),d=t.n(i),o=t(3902),n=t(59872),c=t(92928),x=t(19923);let __WEBPACK_DEFAULT_EXPORT__=({onSubmit:e,onCancel:r,isLoading:t=!1,className:i=""})=>{let{t:m}=(0,l.$G)("social"),{data:g}=(0,x.Mx)(),[h,y]=(0,s.useState)(""),[p,u]=(0,s.useState)(o.hQ.TEXT),[v,k]=(0,s.useState)([]),[b,f]=(0,s.useState)(o.uk.PUBLIC),[j,w]=(0,s.useState)([]),[N,P]=(0,s.useState)(""),C=(0,s.useRef)(null),handleRemoveTag=e=>{w(j.filter(r=>r!==e))},handleRemoveMedia=e=>{k(v.filter(r=>r!==e)),1===v.length&&u(o.hQ.TEXT)};return a.jsx("div",{className:`${i}`,children:a.jsx("form",{onSubmit:r=>{r.preventDefault(),(h.trim()||0!==v.length)&&(e({type:p,content:h.trim(),mediaUrls:v.length>0?v:void 0,visibility:b,tags:j.length>0?j:void 0}),y(""),u(o.hQ.TEXT),k([]),f(o.uk.PUBLIC),w([]))},children:(0,a.jsxs)("div",{className:"flex items-start mb-4",children:[a.jsx("div",{className:"flex-shrink-0 mr-3",children:a.jsx("div",{className:"w-10 h-10 relative rounded-full overflow-hidden",children:a.jsx(d(),{src:g?.profileImageUrl||"/images/default-avatar.png",alt:g?.displayName||"User",fill:!0,className:"object-cover"})})}),(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx(c.Z,{placeholder:m("createPostPlaceholder","What's on your mind?"),value:h,onChange:e=>{y(e.target.value);let r=e.target.value.match(/#(\w+)/g);if(r){let e=r.map(e=>e.substring(1));w([...new Set(e)])}},className:"mb-3",rows:3}),v.length>0&&a.jsx("div",{className:"grid grid-cols-2 gap-2 mb-3",children:v.map((e,r)=>(0,a.jsxs)("div",{className:"relative rounded-md overflow-hidden h-32",children:[a.jsx(d(),{src:e,alt:`Media ${r+1}`,fill:!0,className:"object-cover"}),a.jsx("button",{type:"button",className:"absolute top-1 right-1 bg-gray-800 bg-opacity-70 text-white rounded-full p-1",onClick:()=>handleRemoveMedia(e),children:a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]},r))}),j.length>0&&a.jsx("div",{className:"flex flex-wrap gap-2 mb-3",children:j.map(e=>(0,a.jsxs)("div",{className:"bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-300 px-2 py-1 rounded-full text-sm flex items-center",children:["#",e,a.jsx("button",{type:"button",className:"ml-1 text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-200",onClick:()=>handleRemoveTag(e),children:a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-3 w-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]},e))}),a.jsx("div",{className:"mb-3",children:a.jsx("input",{type:"text",placeholder:m("addTag","Add a tag..."),value:N,onChange:e=>{P(e.target.value)},onKeyDown:e=>{if("Enter"===e.key&&N.trim()){e.preventDefault();let r=N.trim().replace(/^#/,"");r&&!j.includes(r)&&w([...j,r]),P("")}},className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white text-sm"})}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx("button",{type:"button",className:"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300",onClick:()=>C.current?.click(),children:a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})})}),a.jsx("input",{type:"file",ref:C,className:"hidden",accept:"image/*",multiple:!0,onChange:e=>{if(e.target.files&&e.target.files.length>0){u(o.hQ.IMAGE);let r=Array.from(e.target.files).map(e=>URL.createObjectURL(e));k([...v,...r])}}}),(0,a.jsxs)("select",{value:b,onChange:e=>f(e.target.value),className:"text-sm border-none bg-transparent text-gray-500 dark:text-gray-400 focus:ring-0",children:[a.jsx("option",{value:o.uk.PUBLIC,children:m("public","Public")}),a.jsx("option",{value:o.uk.CONNECTIONS,children:m("connections","Connections")}),a.jsx("option",{value:o.uk.PRIVATE,children:m("private","Private")})]})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx(n.Z,{type:"button",variant:"outline",size:"sm",onClick:r,children:m("cancel","Cancel")}),a.jsx(n.Z,{type:"submit",size:"sm",disabled:!h.trim()&&0===v.length||t,isLoading:t,children:m("post","Post")})]})]})]})]})})})}},19911:(e,r,t)=>{t.d(r,{Z:()=>__WEBPACK_DEFAULT_EXPORT__});var a=t(30784);t(9885);var s=t(27870),l=t(57114),i=t(3902),d=t(59872);let __WEBPACK_DEFAULT_EXPORT__=({filters:e,onFilterChange:r,onCreatePost:t,className:o=""})=>{let{t:n}=(0,s.$G)("social"),c=(0,l.useRouter)(),handlePostTypeFilter=t=>{r({...e,type:t||void 0})};return(0,a.jsxs)("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 ${o}`,children:[a.jsx("div",{className:"mb-6",children:a.jsx(d.Z,{onClick:t,fullWidth:!0,children:n("createPost","Create Post")})}),(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("h3",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-3",children:n("feedFilters","Feed Filters")}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("button",{className:`w-full text-left px-3 py-2 rounded-md ${!e.type&&e.following?"bg-primary-50 text-primary-700 dark:bg-primary-900 dark:text-primary-300":"text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"}`,onClick:()=>r({following:!0}),children:n("following","Following")}),a.jsx("button",{className:`w-full text-left px-3 py-2 rounded-md ${e.type||e.following?"text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700":"bg-primary-50 text-primary-700 dark:bg-primary-900 dark:text-primary-300"}`,onClick:()=>r({following:!1}),children:n("discover","Discover")})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("h3",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-3",children:n("postTypes","Post Types")}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("button",{className:`w-full text-left px-3 py-2 rounded-md ${e.type?"text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700":"bg-primary-50 text-primary-700 dark:bg-primary-900 dark:text-primary-300"}`,onClick:()=>handlePostTypeFilter(null),children:n("allPosts","All Posts")}),a.jsx("button",{className:`w-full text-left px-3 py-2 rounded-md ${e.type===i.hQ.TEXT?"bg-primary-50 text-primary-700 dark:bg-primary-900 dark:text-primary-300":"text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"}`,onClick:()=>handlePostTypeFilter(i.hQ.TEXT),children:n("textPosts","Text Posts")}),a.jsx("button",{className:`w-full text-left px-3 py-2 rounded-md ${e.type===i.hQ.IMAGE?"bg-primary-50 text-primary-700 dark:bg-primary-900 dark:text-primary-300":"text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"}`,onClick:()=>handlePostTypeFilter(i.hQ.IMAGE),children:n("imagePosts","Image Posts")}),a.jsx("button",{className:`w-full text-left px-3 py-2 rounded-md ${e.type===i.hQ.PRODUCT?"bg-primary-50 text-primary-700 dark:bg-primary-900 dark:text-primary-300":"text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"}`,onClick:()=>handlePostTypeFilter(i.hQ.PRODUCT),children:n("productPosts","Product Posts")})]})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-3",children:n("quickLinks","Quick Links")}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("button",{className:"w-full text-left px-3 py-2 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:()=>c.push("/activity/feed"),children:n("activityFeed","Activity Feed")}),a.jsx("button",{className:"w-full text-left px-3 py-2 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:()=>c.push("/profile"),children:n("myProfile","My Profile")}),a.jsx("button",{className:"w-full text-left px-3 py-2 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:()=>c.push("/stores"),children:n("stores","Stores")}),a.jsx("button",{className:"w-full text-left px-3 py-2 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:()=>c.push("/products"),children:n("products","Products")})]})]})]})}}};