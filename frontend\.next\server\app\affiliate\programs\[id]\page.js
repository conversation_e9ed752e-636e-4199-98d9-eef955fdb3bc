(()=>{var e={};e.id=976,e.ids=[976],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},25745:(e,a,r)=>{"use strict";r.r(a),r.d(a,{GlobalError:()=>l.a,__next_app__:()=>x,originalPathname:()=>c,pages:()=>m,routeModule:()=>h,tree:()=>d});var t=r(67096),s=r(16132),i=r(37284),l=r.n(i),o=r(32564),n={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);r.d(a,n);let d=["",{children:["affiliate",{children:["programs",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,55127)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\affiliate\\programs\\[id]\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9291,23)),"next/dist/client/components/not-found-error"]}],m=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\affiliate\\programs\\[id]\\page.tsx"],c="/affiliate/programs/[id]/page",x={require:r,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/affiliate/programs/[id]/page",pathname:"/affiliate/programs/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10037:(e,a,r)=>{Promise.resolve().then(r.bind(r,78226))},78226:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>AffiliateProgramDetailPage});var t=r(30784),s=r(9885),i=r(57114),l=r(27870),o=r(3619),n=r(59872),d=r(63048),m=r(52451),c=r.n(m),x=r(11440),h=r.n(x);let affiliate_AffiliateProgramDetails=({program:e})=>{var a;let{t:r}=(0,l.$G)("affiliate"),formatCurrency=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e);return(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden",children:[(0,t.jsxs)("div",{className:"relative h-40 bg-gradient-to-r from-primary-600 to-primary-400",children:[e.store?.coverImageUrl&&t.jsx(c(),{src:e.store.coverImageUrl,alt:e.store.name,fill:!0,className:"object-cover opacity-50"}),t.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center text-white",children:[t.jsx("h1",{className:"text-3xl font-bold",children:e.name}),t.jsx("p",{className:"mt-2",children:r("byStore","by {{storeName}}",{storeName:e.store?.name})})]})})]}),(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"mb-8",children:[t.jsx("h2",{className:"text-xl font-semibold mb-4",children:r("programOverview","Program Overview")}),t.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-4",children:e.description||r("noDescription","No description provided.")}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mt-6",children:[(0,t.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",children:[t.jsx("h3",{className:"font-medium text-gray-900 dark:text-gray-100 mb-2",children:r("commission","Commission")}),t.jsx("p",{className:"text-2xl font-bold text-primary-600 dark:text-primary-400",children:"PERCENTAGE"===e.commissionType?`${e.commissionValue}%`:formatCurrency(e.commissionValue)}),t.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:"PERCENTAGE"===e.commissionType?r("percentageOfSales","of each sale"):r("fixedPerSale","per sale")})]}),(0,t.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",children:[t.jsx("h3",{className:"font-medium text-gray-900 dark:text-gray-100 mb-2",children:r("cookieDuration","Cookie Duration")}),t.jsx("p",{className:"text-2xl font-bold text-primary-600 dark:text-primary-400",children:e.cookieDuration}),t.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:r("days","days")})]}),(0,t.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",children:[t.jsx("h3",{className:"font-medium text-gray-900 dark:text-gray-100 mb-2",children:r("minimumPayout","Minimum Payout")}),t.jsx("p",{className:"text-2xl font-bold text-primary-600 dark:text-primary-400",children:formatCurrency(e.minimumPayoutAmount)}),t.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:r("payoutFrequency","Paid {{frequency}}",{frequency:r(`frequency.${e.payoutFrequency}`,e.payoutFrequency)})})]})]})]}),e.store&&(0,t.jsxs)("div",{className:"mb-8",children:[t.jsx("h2",{className:"text-xl font-semibold mb-4",children:r("aboutStore","About the Store")}),(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[t.jsx("div",{className:"w-16 h-16 relative rounded-full overflow-hidden mr-4",children:t.jsx(c(),{src:e.store.logoUrl||"/images/default-store-logo.png",alt:e.store.name,fill:!0,className:"object-cover"})}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"text-lg font-medium",children:e.store.name}),t.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:r("memberSince","Member since {{date}}",{date:(a=e.store.createdAt,(0,d.ZP)(new Date(a),"MMM d, yyyy"))})})]})]}),t.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-4",children:e.store.description||r("noStoreDescription","No store description available.")}),t.jsx(h(),{href:`/store/${e.store.id}`,className:"text-primary-600 dark:text-primary-400 hover:underline",children:r("visitStore","Visit Store")})]}),(0,t.jsxs)("div",{className:"mb-8",children:[t.jsx("h2",{className:"text-xl font-semibold mb-4",children:r("paymentMethods","Payment Methods")}),t.jsx("div",{className:"flex flex-wrap gap-2",children:e.availablePaymentMethods.map(e=>t.jsx("span",{className:"px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-full text-sm",children:r(`paymentMethod.${e}`,e)},e))})]}),(0,t.jsxs)("div",{children:[t.jsx("h2",{className:"text-xl font-semibold mb-4",children:r("termsAndConditions","Terms and Conditions")}),t.jsx("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",children:t.jsx("p",{className:"text-gray-600 dark:text-gray-300 whitespace-pre-line",children:e.termsAndConditions||r("noTerms","No specific terms and conditions provided.")})})]})]})]})};var p=r(706),g=r(26352);let affiliate_AffiliateJoinForm=({program:e,onSubmit:a,onCancel:r,isLoading:i=!1})=>{let{t:o}=(0,l.$G)("affiliate"),[d,m]=(0,s.useState)({paymentMethod:e.availablePaymentMethods[0]||g.sI.PAYPAL,paymentDetails:"",website:"",socialMedia:"",marketingPlan:"",agreeToTerms:!1}),[c,x]=(0,s.useState)({}),handleChange=e=>{let{name:a,value:r,type:t}=e.target;if("checkbox"===t){let{checked:r}=e.target;m(e=>({...e,[a]:r}))}else m(e=>({...e,[a]:r}));c[a]&&x(e=>({...e,[a]:""}))},validateForm=()=>{let e={};return d.paymentDetails.trim()||(e.paymentDetails=o("errors.paymentDetailsRequired","Payment details are required")),d.agreeToTerms||(e.agreeToTerms=o("errors.agreeToTermsRequired","You must agree to the terms and conditions")),x(e),0===Object.keys(e).length},handleSubmit=async e=>{if(e.preventDefault(),validateForm())try{await a(d)}catch(e){console.error("Error submitting form:",e),x({form:o("errors.submissionFailed","Failed to join affiliate program. Please try again.")})}};return(0,t.jsxs)("form",{onSubmit:handleSubmit,children:[c.form&&t.jsx("div",{className:"mb-4 p-3 bg-red-100 text-red-700 rounded-md dark:bg-red-900 dark:text-red-100",children:c.form}),(0,t.jsxs)("div",{className:"mb-4",children:[t.jsx("label",{htmlFor:"paymentMethod",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:o("paymentMethod","Payment Method")}),t.jsx("select",{id:"paymentMethod",name:"paymentMethod",value:d.paymentMethod,onChange:handleChange,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500",disabled:i,children:e.availablePaymentMethods.map(e=>t.jsx("option",{value:e,children:o(`paymentMethod.${e}`,e)},e))})]}),(0,t.jsxs)("div",{className:"mb-4",children:[t.jsx("label",{htmlFor:"paymentDetails",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:o("paymentDetails","Payment Details")}),t.jsx("textarea",{id:"paymentDetails",name:"paymentDetails",rows:3,value:d.paymentDetails,onChange:handleChange,className:`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 ${c.paymentDetails?"border-red-500 dark:border-red-500":"border-gray-300 dark:border-gray-700"}`,placeholder:d.paymentMethod===g.sI.PAYPAL?o("paypalEmailPlaceholder","Your PayPal email address"):d.paymentMethod===g.sI.BANK_TRANSFER?o("bankDetailsPlaceholder","Your bank account details"):o("paymentDetailsPlaceholder","Enter your payment details"),disabled:i}),c.paymentDetails&&t.jsx("p",{className:"mt-1 text-sm text-red-600",children:c.paymentDetails})]}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsxs)("label",{htmlFor:"website",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[o("website","Website")," ",(0,t.jsxs)("span",{className:"text-gray-500",children:["(",o("optional","optional"),")"]})]}),t.jsx(p.Z,{id:"website",name:"website",type:"url",value:d.website,onChange:handleChange,placeholder:"https://example.com",disabled:i})]}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsxs)("label",{htmlFor:"socialMedia",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[o("socialMedia","Social Media Accounts")," ",(0,t.jsxs)("span",{className:"text-gray-500",children:["(",o("optional","optional"),")"]})]}),t.jsx("textarea",{id:"socialMedia",name:"socialMedia",rows:2,value:d.socialMedia,onChange:handleChange,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500",placeholder:o("socialMediaPlaceholder","List your social media accounts (Instagram, TikTok, YouTube, etc.)"),disabled:i})]}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsxs)("label",{htmlFor:"marketingPlan",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[o("marketingPlan","How will you promote our products?")," ",(0,t.jsxs)("span",{className:"text-gray-500",children:["(",o("optional","optional"),")"]})]}),t.jsx("textarea",{id:"marketingPlan",name:"marketingPlan",rows:4,value:d.marketingPlan,onChange:handleChange,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500",placeholder:o("marketingPlanPlaceholder","Briefly describe how you plan to promote our products"),disabled:i})]}),t.jsx("div",{className:"mb-6",children:(0,t.jsxs)("div",{className:"flex items-start",children:[t.jsx("div",{className:"flex items-center h-5",children:t.jsx("input",{id:"agreeToTerms",name:"agreeToTerms",type:"checkbox",checked:d.agreeToTerms,onChange:handleChange,className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded",disabled:i})}),(0,t.jsxs)("div",{className:"ml-3 text-sm",children:[t.jsx("label",{htmlFor:"agreeToTerms",className:`font-medium ${c.agreeToTerms?"text-red-700 dark:text-red-500":"text-gray-700 dark:text-gray-300"}`,children:o("agreeToTerms","I agree to the terms and conditions")}),c.agreeToTerms&&t.jsx("p",{className:"mt-1 text-sm text-red-600",children:c.agreeToTerms})]})]})}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3",children:[t.jsx(n.Z,{type:"button",variant:"outline",onClick:r,disabled:i,children:o("cancel","Cancel")}),t.jsx(n.Z,{type:"submit",isLoading:i,disabled:i,children:o("joinProgram","Join Program")})]})]})};var u=r(70661);function AffiliateProgramDetailPage({params:e}){let{id:a}=e,r=(0,i.useRouter)(),{t:d}=(0,l.$G)("affiliate"),[m,c]=(0,s.useState)(!1),{data:x,isLoading:h,error:p}=(0,o.useGetAffiliateProgramByIdQuery)(a),[g,{isLoading:y}]=(0,o.useJoinAffiliateProgramMutation)(),handleJoinProgram=async e=>{try{await g({programId:a,...e}).unwrap(),alert(d("joinSuccess","Successfully joined the affiliate program!")),r.push("/affiliate")}catch(e){console.error("Failed to join affiliate program:",e)}};return h?t.jsx("div",{className:"min-h-screen p-6",children:t.jsx("div",{className:"max-w-4xl mx-auto",children:t.jsx("div",{className:"flex items-center justify-center py-12",children:t.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"})})})}):p||!x?t.jsx("div",{className:"min-h-screen p-6",children:t.jsx("div",{className:"max-w-4xl mx-auto",children:(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center",children:[t.jsx("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:d("programNotFound","Affiliate Program Not Found")}),t.jsx("p",{className:"mb-6",children:d("programNotFoundDesc","The affiliate program you are looking for does not exist or has been removed.")}),t.jsx(n.Z,{onClick:()=>r.push("/affiliate"),children:d("backToAffiliate","Back to Affiliate Dashboard")})]})})}):t.jsx(u.Z,{children:t.jsx("div",{className:"min-h-screen p-6",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto",children:[t.jsx("div",{className:"mb-6",children:(0,t.jsxs)(n.Z,{variant:"outline",onClick:()=>r.push("/affiliate"),className:"mb-4",children:[t.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),d("backToAffiliate","Back to Affiliate Dashboard")]})}),m?(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6",children:[t.jsx("h1",{className:"text-2xl font-bold mb-6",children:d("joinProgram","Join Affiliate Program")}),t.jsx(affiliate_AffiliateJoinForm,{program:x,onSubmit:handleJoinProgram,onCancel:()=>c(!1),isLoading:y})]}):(0,t.jsxs)(t.Fragment,{children:[t.jsx(affiliate_AffiliateProgramDetails,{program:x}),t.jsx("div",{className:"mt-8 flex justify-center",children:t.jsx(n.Z,{onClick:()=>c(!0),size:"lg",children:d("joinNow","Join This Affiliate Program")})})]})]})})})}},55127:(e,a,r)=>{"use strict";r.r(a),r.d(a,{$$typeof:()=>l,__esModule:()=>i,default:()=>n});var t=r(95153);let s=(0,t.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\affiliate\programs\[id]\page.tsx`),{__esModule:i,$$typeof:l}=s,o=s.default,n=o}};var a=require("../../../../webpack-runtime.js");a.C(e);var __webpack_exec__=e=>a(a.s=e),r=a.X(0,[2103,3048,2765,706,3619,661,6352],()=>__webpack_exec__(25745));module.exports=r})();