import { Injectable, NotFoundException, BadRequestException, Inject, Logger } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';
import { CartRepository } from '../repositories/cart.repository';
import { Cart, CartStatus } from '../entities/cart.entity';
import { CartItem } from '../entities/cart-item.entity';
import { CreateCartDto } from '../dto/create-cart.dto';
import { AddToCartDto } from '../dto/add-to-cart.dto';
import { UpdateCartItemDto } from '../dto/update-cart-item.dto';
import { CartResponseDto, CartItemResponseDto } from '../dto/cart-response.dto';

@Injectable()
export class CartService {
  private readonly logger = new Logger(CartService.name);

  constructor(
    private readonly cartRepository: CartRepository,
    @Inject('PRODUCT_SERVICE') private readonly productServiceClient: ClientProxy,
  ) {}

  async createCart(createCartDto: CreateCartDto): Promise<CartResponseDto> {
    this.logger.log('Creating new cart');

    const cart = await this.cartRepository.create({
      userId: createCartDto.userId,
      sessionId: createCartDto.sessionId,
      isGuestCart: createCartDto.isGuestCart || false,
      metadata: createCartDto.metadata,
      lastActivity: new Date(),
    });

    return this.mapToResponseDto(cart);
  }

  async getOrCreateCart(userId?: string, sessionId?: string): Promise<CartResponseDto> {
    let cart: Cart | null = null;

    if (userId) {
      cart = await this.cartRepository.findByUserId(userId);
    } else if (sessionId) {
      cart = await this.cartRepository.findBySessionId(sessionId);
    }

    if (!cart) {
      const createCartDto: CreateCartDto = {
        userId,
        sessionId,
        isGuestCart: !userId,
      };
      return await this.createCart(createCartDto);
    }

    cart.updateLastActivity();
    await this.cartRepository.save(cart);

    return this.mapToResponseDto(cart);
  }

  async getCart(cartId: string): Promise<CartResponseDto> {
    const cart = await this.cartRepository.findById(cartId);
    if (!cart) {
      throw new NotFoundException(`Cart with ID ${cartId} not found`);
    }

    return this.mapToResponseDto(cart);
  }

  async addToCart(cartId: string, addToCartDto: AddToCartDto): Promise<CartResponseDto> {
    this.logger.log(`Adding product ${addToCartDto.productId} to cart ${cartId}`);

    const cart = await this.cartRepository.findById(cartId);
    if (!cart) {
      throw new NotFoundException(`Cart with ID ${cartId} not found`);
    }

    // Verify product exists and get product details
    const product = await this.getProductDetails(addToCartDto.productId);
    if (!product) {
      throw new NotFoundException(`Product with ID ${addToCartDto.productId} not found`);
    }

    // Check if item already exists in cart
    const existingItem = await this.cartRepository.findCartItem(
      cartId,
      addToCartDto.productId,
      addToCartDto.variantId,
    );

    if (existingItem) {
      // Update quantity of existing item
      const newQuantity = existingItem.quantity + addToCartDto.quantity;
      existingItem.updateQuantity(newQuantity);
      existingItem.selectedOptions = addToCartDto.selectedOptions || existingItem.selectedOptions;
      existingItem.metadata = addToCartDto.metadata || existingItem.metadata;

      await this.cartRepository.updateCartItem(existingItem.id, existingItem);
    } else {
      // Add new item to cart
      const cartItem = new CartItem();
      cartItem.productId = addToCartDto.productId;
      cartItem.variantId = addToCartDto.variantId;
      cartItem.quantity = addToCartDto.quantity;
      cartItem.price = product.price;
      cartItem.productName = product.name;
      cartItem.productImage = product.images?.[0];
      cartItem.storeId = product.storeId;
      cartItem.storeName = product.storeName;
      cartItem.selectedOptions = addToCartDto.selectedOptions;
      cartItem.metadata = addToCartDto.metadata;
      cartItem.calculateTotal();

      await this.cartRepository.addItem(cartId, cartItem);
    }

    // Update cart totals and last activity
    const updatedCart = await this.cartRepository.findById(cartId);
    updatedCart.calculateTotals();
    updatedCart.updateLastActivity();
    await this.cartRepository.save(updatedCart);

    return this.mapToResponseDto(updatedCart);
  }

  async updateCartItem(cartId: string, itemId: string, updateDto: UpdateCartItemDto): Promise<CartResponseDto> {
    this.logger.log(`Updating cart item ${itemId} in cart ${cartId}`);

    const cart = await this.cartRepository.findById(cartId);
    if (!cart) {
      throw new NotFoundException(`Cart with ID ${cartId} not found`);
    }

    const cartItem = cart.items.find(item => item.id === itemId);
    if (!cartItem) {
      throw new NotFoundException(`Cart item with ID ${itemId} not found`);
    }

    cartItem.updateQuantity(updateDto.quantity);
    cartItem.selectedOptions = updateDto.selectedOptions || cartItem.selectedOptions;
    cartItem.metadata = updateDto.metadata || cartItem.metadata;

    await this.cartRepository.updateCartItem(itemId, cartItem);

    // Update cart totals
    const updatedCart = await this.cartRepository.findById(cartId);
    updatedCart.calculateTotals();
    updatedCart.updateLastActivity();
    await this.cartRepository.save(updatedCart);

    return this.mapToResponseDto(updatedCart);
  }

  async removeCartItem(cartId: string, itemId: string): Promise<CartResponseDto> {
    this.logger.log(`Removing cart item ${itemId} from cart ${cartId}`);

    const cart = await this.cartRepository.findById(cartId);
    if (!cart) {
      throw new NotFoundException(`Cart with ID ${cartId} not found`);
    }

    const cartItem = cart.items.find(item => item.id === itemId);
    if (!cartItem) {
      throw new NotFoundException(`Cart item with ID ${itemId} not found`);
    }

    await this.cartRepository.removeCartItem(itemId);

    // Update cart totals
    const updatedCart = await this.cartRepository.findById(cartId);
    updatedCart.calculateTotals();
    updatedCart.updateLastActivity();
    await this.cartRepository.save(updatedCart);

    return this.mapToResponseDto(updatedCart);
  }

  async clearCart(cartId: string): Promise<CartResponseDto> {
    this.logger.log(`Clearing cart ${cartId}`);

    const cart = await this.cartRepository.findById(cartId);
    if (!cart) {
      throw new NotFoundException(`Cart with ID ${cartId} not found`);
    }

    await this.cartRepository.clearCart(cartId);

    // Update cart totals
    cart.items = [];
    cart.calculateTotals();
    cart.updateLastActivity();
    await this.cartRepository.save(cart);

    return this.mapToResponseDto(cart);
  }

  async deleteCart(cartId: string): Promise<void> {
    this.logger.log(`Deleting cart ${cartId}`);

    const cart = await this.cartRepository.findById(cartId);
    if (!cart) {
      throw new NotFoundException(`Cart with ID ${cartId} not found`);
    }

    await this.cartRepository.delete(cartId);
  }

  private async getProductDetails(productId: string): Promise<any> {
    try {
      return await firstValueFrom(
        this.productServiceClient.send('find_product_by_id', productId),
      );
    } catch (error) {
      this.logger.error(`Failed to get product details for ${productId}:`, error);
      return null;
    }
  }

  private mapToResponseDto(cart: Cart): CartResponseDto {
    return {
      id: cart.id,
      userId: cart.userId,
      sessionId: cart.sessionId,
      isGuestCart: cart.isGuestCart,
      status: cart.status,
      items: cart.items ? cart.items.map(item => this.mapItemToResponseDto(item)) : [],
      itemCount: cart.itemCount,
      subtotal: Number(cart.subtotal),
      tax: Number(cart.tax),
      shipping: Number(cart.shipping),
      discount: Number(cart.discount),
      total: Number(cart.total),
      couponCode: cart.couponCode,
      shippingMethodId: cart.shippingMethodId,
      shippingAddress: cart.shippingAddress,
      billingAddress: cart.billingAddress,
      metadata: cart.metadata,
      lastActivity: cart.lastActivity,
      createdAt: cart.createdAt,
      updatedAt: cart.updatedAt,
      isEmpty: cart.isEmpty,
    };
  }

  private mapItemToResponseDto(item: CartItem): CartItemResponseDto {
    return {
      id: item.id,
      cartId: item.cartId,
      productId: item.productId,
      variantId: item.variantId,
      quantity: item.quantity,
      price: Number(item.price),
      discount: Number(item.discount),
      total: Number(item.total),
      productName: item.productName,
      productImage: item.productImage,
      storeId: item.storeId,
      storeName: item.storeName,
      selectedOptions: item.selectedOptions,
      metadata: item.metadata,
      createdAt: item.createdAt,
      updatedAt: item.updatedAt,
    };
  }
}
