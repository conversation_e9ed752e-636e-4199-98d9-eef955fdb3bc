import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException } from '@nestjs/common';
import { ProfileService } from './profile.service';
import { ProfileRepository } from '../repositories/profile.repository';
import { UserRepository } from '../../authentication/repositories/user.repository';
import { Profile } from '../entities/profile.entity';
import { User } from '../../authentication/entities/user.entity';
import { CreateProfileDto } from '../dto/create-profile.dto';
import { UpdateProfileDto } from '../dto/update-profile.dto';

describe('ProfileService', () => {
  let service: ProfileService;
  let profileRepository: ProfileRepository;
  let userRepository: UserRepository;

  beforeEach(async () => {
    // Create mock implementations
    const mockProfileRepository = {
      findAll: jest.fn(),
      findOne: jest.fn(),
      findByUserId: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      updateByUserId: jest.fn(),
      remove: jest.fn(),
    };

    const mockUserRepository = {
      findOne: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProfileService,
        {
          provide: ProfileRepository,
          useValue: mockProfileRepository,
        },
        {
          provide: UserRepository,
          useValue: mockUserRepository,
        },
      ],
    }).compile();

    service = module.get<ProfileService>(ProfileService);
    profileRepository = module.get<ProfileRepository>(ProfileRepository);
    userRepository = module.get<UserRepository>(UserRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findAll', () => {
    it('should return all profiles', async () => {
      // Arrange
      const profiles = [
        { id: '1', firstName: 'John', lastName: 'Doe' },
        { id: '2', firstName: 'Jane', lastName: 'Smith' },
      ] as Profile[];

      jest.spyOn(profileRepository, 'findAll').mockResolvedValue(profiles);

      // Act
      const result = await service.findAll();

      // Assert
      expect(result).toEqual(profiles);
      expect(profileRepository.findAll).toHaveBeenCalled();
    });
  });

  describe('findOne', () => {
    it('should return a profile by ID', async () => {
      // Arrange
      const id = '123';
      const profile = {
        id,
        firstName: 'John',
        lastName: 'Doe',
      } as Profile;

      jest.spyOn(profileRepository, 'findOne').mockResolvedValue(profile);

      // Act
      const result = await service.findOne(id);

      // Assert
      expect(result).toEqual(profile);
      expect(profileRepository.findOne).toHaveBeenCalledWith(id);
    });
  });

  describe('findByUserId', () => {
    it('should return a profile by user ID', async () => {
      // Arrange
      const userId = '123';
      const user = { id: userId } as User;
      const profile = {
        id: '456',
        firstName: 'John',
        lastName: 'Doe',
        user,
      } as Profile;

      jest.spyOn(userRepository, 'findOne').mockResolvedValue(user);
      jest.spyOn(profileRepository, 'findByUserId').mockResolvedValue(profile);

      // Act
      const result = await service.findByUserId(userId);

      // Assert
      expect(result).toEqual(profile);
      expect(userRepository.findOne).toHaveBeenCalledWith(userId);
      expect(profileRepository.findByUserId).toHaveBeenCalledWith(userId);
    });

    it('should throw NotFoundException if user not found', async () => {
      // Arrange
      const userId = '123';
      jest.spyOn(userRepository, 'findOne').mockRejectedValue(new NotFoundException());

      // Act & Assert
      await expect(service.findByUserId(userId)).rejects.toThrow(NotFoundException);
      expect(userRepository.findOne).toHaveBeenCalledWith(userId);
    });
  });

  describe('create', () => {
    it('should create a profile for a user', async () => {
      // Arrange
      const userId = '123';
      const user = { id: userId } as User;
      const createProfileDto: CreateProfileDto = {
        firstName: 'John',
        lastName: 'Doe',
      };
      const profile = {
        id: '456',
        ...createProfileDto,
        user,
      } as Profile;

      jest.spyOn(userRepository, 'findOne').mockResolvedValue(user);
      jest.spyOn(profileRepository, 'findByUserId').mockRejectedValue(new NotFoundException());
      jest.spyOn(profileRepository, 'create').mockResolvedValue(profile);

      // Act
      const result = await service.create(userId, createProfileDto);

      // Assert
      expect(result).toEqual(profile);
      expect(userRepository.findOne).toHaveBeenCalledWith(userId);
      expect(profileRepository.findByUserId).toHaveBeenCalledWith(userId);
      expect(profileRepository.create).toHaveBeenCalledWith(userId, createProfileDto);
    });

    it('should throw error if profile already exists', async () => {
      // Arrange
      const userId = '123';
      const user = { id: userId } as User;
      const existingProfile = {
        id: '456',
        firstName: 'John',
        lastName: 'Doe',
        user,
      } as Profile;
      const createProfileDto: CreateProfileDto = {
        firstName: 'Jane',
        lastName: 'Smith',
      };

      jest.spyOn(userRepository, 'findOne').mockResolvedValue(user);
      jest.spyOn(profileRepository, 'findByUserId').mockResolvedValue(existingProfile);

      // Act & Assert
      await expect(service.create(userId, createProfileDto)).rejects.toThrow();
      expect(userRepository.findOne).toHaveBeenCalledWith(userId);
      expect(profileRepository.findByUserId).toHaveBeenCalledWith(userId);
      expect(profileRepository.create).not.toHaveBeenCalled();
    });
  });

  describe('update', () => {
    it('should update a profile', async () => {
      // Arrange
      const id = '123';
      const updateProfileDto: UpdateProfileDto = {
        firstName: 'Jane',
        lastName: 'Smith',
      };
      const updatedProfile = {
        id,
        ...updateProfileDto,
      } as Profile;

      jest.spyOn(profileRepository, 'update').mockResolvedValue(updatedProfile);

      // Act
      const result = await service.update(id, updateProfileDto);

      // Assert
      expect(result).toEqual(updatedProfile);
      expect(profileRepository.update).toHaveBeenCalledWith(id, updateProfileDto);
    });
  });

  describe('updateByUserId', () => {
    it('should update a profile by user ID', async () => {
      // Arrange
      const userId = '123';
      const user = { id: userId } as User;
      const updateProfileDto: UpdateProfileDto = {
        firstName: 'Jane',
        lastName: 'Smith',
      };
      const updatedProfile = {
        id: '456',
        ...updateProfileDto,
        user,
      } as Profile;

      jest.spyOn(userRepository, 'findOne').mockResolvedValue(user);
      jest.spyOn(profileRepository, 'updateByUserId').mockResolvedValue(updatedProfile);

      // Act
      const result = await service.updateByUserId(userId, updateProfileDto);

      // Assert
      expect(result).toEqual(updatedProfile);
      expect(userRepository.findOne).toHaveBeenCalledWith(userId);
      expect(profileRepository.updateByUserId).toHaveBeenCalledWith(userId, updateProfileDto);
    });
  });

  describe('remove', () => {
    it('should remove a profile', async () => {
      // Arrange
      const id = '123';
      jest.spyOn(profileRepository, 'remove').mockResolvedValue(undefined);

      // Act
      await service.remove(id);

      // Assert
      expect(profileRepository.remove).toHaveBeenCalledWith(id);
    });
  });
});
