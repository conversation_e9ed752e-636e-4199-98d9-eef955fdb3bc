(()=>{var e={};e.id=7137,e.ids=[7137],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},81929:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>g,originalPathname:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var a=r(67096),s=r(16132),i=r(37284),n=r.n(i),o=r(32564),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d=["",{children:["affiliate",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,386)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\affiliate\\dashboard\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9291,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\affiliate\\dashboard\\page.tsx"],m="/affiliate/dashboard/page",g={require:r,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/affiliate/dashboard/page",pathname:"/affiliate/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},59244:(e,t,r)=>{Promise.resolve().then(r.bind(r,75752))},75752:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>AffiliateDashboardPage});var a=r(30784);r(9885);var s=r(27870),i=r(14379),n=r(11440),o=r.n(n),l=r(59872),d=r(94820);let affiliate_AffiliateStats=({stats:e,isLoading:t=!1,className:r=""})=>{let{t:n}=(0,s.$G)("affiliate"),{isRtl:o}=(0,i.g)(),formatCurrency=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e);if(t)return a.jsx("div",{className:`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 ${r}`,children:Array.from({length:4}).map((e,t)=>(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 animate-pulse",children:[a.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-2"}),a.jsx("div",{className:"h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-2"}),a.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-2/3"})]},t))});let l=[{title:n("clicks","Clicks"),value:e.clicks||e.totalClicks||0,subtitle:n("totalClicks","Total clicks on your affiliate links"),icon:a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-blue-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122"})})},{title:n("conversions","Conversions"),value:e.conversions||e.totalConversions||0,subtitle:n("conversionRate","Conversion rate: {{rate}}%",{rate:(100*(e.conversionRate||0)).toFixed(2)}),icon:a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-green-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})},{title:n("earnings","Earnings"),value:formatCurrency(e.totalEarnings||e.totalCommissions||0),subtitle:n("totalEarnings","Total earnings from affiliate sales"),icon:a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-yellow-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})},{title:n("pendingCommissions","Pending"),value:formatCurrency(e.pendingCommissions||0),subtitle:n("pendingCommissionsDesc","Commissions waiting for approval"),icon:a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-orange-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}];return a.jsx("div",{className:`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 ${r}`,children:l.map((e,t)=>(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[a.jsx("h3",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:e.title}),e.icon]}),a.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-1",children:e.value}),a.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:e.subtitle})]},t))})};var c=r(3619);function AffiliateDashboardPage(){let{t:e}=(0,s.$G)("affiliate"),{isRtl:t}=(0,i.g)(),{data:r,isLoading:n}=(0,c.nK)({}),m=r?.accounts?.[0],{data:g,isLoading:x}=(0,c.ZE)({accountId:m?.id||""},{skip:!m}),h=!!m;return a.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2",children:e("dashboard.title","Affiliate Dashboard")}),a.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:e("description","Earn money by promoting our products")})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-1",children:[a.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 mb-6",children:a.jsx(d.Z,{})}),h?(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4",children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:e("quickActions","Quick Actions")}),(0,a.jsxs)("div",{className:"space-y-3",children:[a.jsx(o(),{href:"/affiliate/dashboard/links/create",children:a.jsx(l.Z,{variant:"outline",fullWidth:!0,children:e("links.createLink","Create Affiliate Link")})}),a.jsx(o(),{href:"/affiliate/dashboard/payments/request",children:a.jsx(l.Z,{variant:"outline",fullWidth:!0,children:e("payments.requestPayment","Request Payment")})})]})]}):(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4",children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:e("getStarted","Get Started")}),a.jsx("p",{className:"text-gray-500 dark:text-gray-400 mb-4",children:e("joinProgramFirst","Join an affiliate program to get started")}),a.jsx(o(),{href:"/affiliate/programs",children:a.jsx(l.Z,{variant:"primary",fullWidth:!0,children:e("browsePrograms","Browse Programs")})})]})]}),a.jsx("div",{className:"lg:col-span-3",children:h?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("h2",{className:"text-xl font-bold text-gray-900 dark:text-gray-100 mb-4",children:e("stats.title","Performance")}),a.jsx(affiliate_AffiliateStats,{stats:g||{totalClicks:0,totalConversions:0,totalEarnings:0,conversionRate:0,products:[]},isLoading:x})]}),(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6",children:[a.jsx("h2",{className:"text-xl font-bold text-gray-900 dark:text-gray-100 mb-4",children:e("recentActivity","Recent Activity")}),a.jsx("div",{className:"text-center py-8 text-gray-500 dark:text-gray-400",children:e("noRecentActivity","No recent activity to display")})]})]}):a.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6",children:(0,a.jsxs)("div",{className:"text-center py-8",children:[a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-16 w-16 text-gray-400 dark:text-gray-600 mx-auto mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M13 10V3L4 14h7v7l9-11h-7z"})}),a.jsx("h3",{className:"text-xl font-medium text-gray-900 dark:text-gray-100 mb-2",children:e("welcomeToAffiliate","Welcome to the Affiliate Program")}),a.jsx("p",{className:"text-gray-500 dark:text-gray-400 mb-6 max-w-md mx-auto",children:e("welcomeMessage","Join our affiliate program to earn commissions by promoting our products. Get started by browsing available programs.")}),a.jsx(o(),{href:"/affiliate/programs",children:a.jsx(l.Z,{variant:"primary",children:e("browsePrograms","Browse Programs")})})]})})})]})]})})}},386:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>n,__esModule:()=>i,default:()=>l});var a=r(95153);let s=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\affiliate\dashboard\page.tsx`),{__esModule:i,$$typeof:n}=s,o=s.default,l=o}};var t=require("../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),r=t.X(0,[2103,2765,3619,9522],()=>__webpack_exec__(81929));module.exports=r})();