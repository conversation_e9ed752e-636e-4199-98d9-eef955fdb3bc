import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { getRepositoryToken } from '@nestjs/typeorm';
import { User } from '../src/authentication/entities/user.entity';
import { Profile } from '../src/profile-management/entities/profile.entity';
import { Repository } from 'typeorm';

describe('Profile Management (e2e)', () => {
  let app: INestApplication;
  let userRepository: Repository<User>;
  let profileRepository: Repository<Profile>;
  
  // Test user data
  const testUser = {
    email: '<EMAIL>',
    password: 'password123',
  };
  
  // User ID and JWT token for authenticated requests
  let userId: string;
  let jwtToken: string;
  let profileId: string;
  
  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    
    // Set up global pipes
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        transform: true,
        forbidNonWhitelisted: true,
        transformOptions: {
          enableImplicitConversion: true,
        },
      }),
    );
    
    // Get repositories
    userRepository = moduleFixture.get(getRepositoryToken(User));
    profileRepository = moduleFixture.get(getRepositoryToken(Profile));
    
    await app.init();
    
    // Clean up database before tests
    await profileRepository.delete({});
    await userRepository.delete({ email: testUser.email });
    
    // Register a test user
    const registerResponse = await request(app.getHttpServer())
      .post('/auth/register')
      .send(testUser);
    
    userId = registerResponse.body.id;
    
    // Login to get JWT token
    const loginResponse = await request(app.getHttpServer())
      .post('/auth/login')
      .send({
        email: testUser.email,
        password: testUser.password,
      });
    
    jwtToken = loginResponse.body.accessToken;
  });

  afterAll(async () => {
    // Clean up database after tests
    await profileRepository.delete({});
    await userRepository.delete({ email: testUser.email });
    
    await app.close();
  });

  describe('/profiles/user/:userId (POST)', () => {
    it('should create a profile for a user', () => {
      const profileData = {
        firstName: 'John',
        lastName: 'Doe',
        bio: 'Test bio',
        location: 'Test location',
      };
      
      return request(app.getHttpServer())
        .post(`/profiles/user/${userId}`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .send(profileData)
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('id');
          expect(res.body.firstName).toBe(profileData.firstName);
          expect(res.body.lastName).toBe(profileData.lastName);
          expect(res.body.bio).toBe(profileData.bio);
          expect(res.body.location).toBe(profileData.location);
          
          // Save profile ID for later tests
          profileId = res.body.id;
        });
    });

    it('should not create a profile without authentication', () => {
      return request(app.getHttpServer())
        .post(`/profiles/user/${userId}`)
        .send({
          firstName: 'John',
          lastName: 'Doe',
        })
        .expect(401);
    });
  });

  describe('/profiles/user/:userId (GET)', () => {
    it('should get a profile by user ID', () => {
      return request(app.getHttpServer())
        .get(`/profiles/user/${userId}`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('id');
          expect(res.body.id).toBe(profileId);
          expect(res.body.firstName).toBe('John');
          expect(res.body.lastName).toBe('Doe');
        });
    });
  });

  describe('/profiles/:id (GET)', () => {
    it('should get a profile by ID', () => {
      return request(app.getHttpServer())
        .get(`/profiles/${profileId}`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('id');
          expect(res.body.id).toBe(profileId);
          expect(res.body.firstName).toBe('John');
          expect(res.body.lastName).toBe('Doe');
        });
    });
  });

  describe('/profiles/:id (PUT)', () => {
    it('should update a profile', () => {
      const updateData = {
        firstName: 'Jane',
        lastName: 'Smith',
        bio: 'Updated bio',
      };
      
      return request(app.getHttpServer())
        .put(`/profiles/${profileId}`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .send(updateData)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('id');
          expect(res.body.id).toBe(profileId);
          expect(res.body.firstName).toBe(updateData.firstName);
          expect(res.body.lastName).toBe(updateData.lastName);
          expect(res.body.bio).toBe(updateData.bio);
          // Location should remain unchanged
          expect(res.body.location).toBe('Test location');
        });
    });
  });

  describe('/profiles/user/:userId (PUT)', () => {
    it('should update a profile by user ID', () => {
      const updateData = {
        displayName: 'JaneSmith',
        website: 'https://example.com',
      };
      
      return request(app.getHttpServer())
        .put(`/profiles/user/${userId}`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .send(updateData)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('id');
          expect(res.body.id).toBe(profileId);
          expect(res.body.displayName).toBe(updateData.displayName);
          expect(res.body.website).toBe(updateData.website);
          // Other fields should remain unchanged
          expect(res.body.firstName).toBe('Jane');
          expect(res.body.lastName).toBe('Smith');
        });
    });
  });
});
