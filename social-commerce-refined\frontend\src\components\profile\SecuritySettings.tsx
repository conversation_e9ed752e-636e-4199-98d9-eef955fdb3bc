import React, { useState } from 'react';
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  Input,
  Stack,
  FormErrorMessage,
  useColorModeValue,
  Heading,
  Divider,
  useToast,
  Switch,
  Text,
  Flex,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';

interface PasswordFormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

interface SecuritySettingsProps {
  onPasswordChange: (data: PasswordFormData) => Promise<void>;
  onTwoFactorToggle: (enabled: boolean) => Promise<void>;
  twoFactorEnabled: boolean;
}

const SecuritySettings: React.FC<SecuritySettingsProps> = ({
  onPasswordChange,
  onTwoFactorToggle,
  twoFactorEnabled,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isTwoFactorSubmitting, setIsTwoFactorSubmitting] = useState(false);
  const toast = useToast();
  
  const {
    handleSubmit,
    register,
    reset,
    formState: { errors },
    watch,
  } = useForm<PasswordFormData>();
  
  const newPassword = watch('newPassword');
  
  const bgColor = useColorModeValue('white', 'gray.700');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  const handlePasswordSubmit = async (data: PasswordFormData) => {
    setIsSubmitting(true);
    
    try {
      await onPasswordChange(data);
      
      toast({
        title: 'Password updated',
        description: 'Your password has been successfully updated.',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
      
      reset();
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update password. Please try again.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleTwoFactorToggle = async () => {
    setIsTwoFactorSubmitting(true);
    
    try {
      await onTwoFactorToggle(!twoFactorEnabled);
      
      toast({
        title: `Two-factor authentication ${!twoFactorEnabled ? 'enabled' : 'disabled'}`,
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update two-factor authentication settings.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsTwoFactorSubmitting(false);
    }
  };

  return (
    <Box
      bg={bgColor}
      borderRadius="lg"
      borderWidth="1px"
      borderColor={borderColor}
      p={6}
    >
      <Heading size="md" mb={6}>
        Security Settings
      </Heading>

      {/* Password Change Form */}
      <Box as="form" onSubmit={handleSubmit(handlePasswordSubmit)} mb={8}>
        <Heading size="sm" mb={4}>
          Change Password
        </Heading>
        
        <Stack spacing={4}>
          <FormControl id="currentPassword" isInvalid={!!errors.currentPassword}>
            <FormLabel>Current Password</FormLabel>
            <Input
              type="password"
              {...register('currentPassword', {
                required: 'Current password is required',
              })}
            />
            <FormErrorMessage>{errors.currentPassword?.message}</FormErrorMessage>
          </FormControl>

          <FormControl id="newPassword" isInvalid={!!errors.newPassword}>
            <FormLabel>New Password</FormLabel>
            <Input
              type="password"
              {...register('newPassword', {
                required: 'New password is required',
                minLength: {
                  value: 8,
                  message: 'Password must be at least 8 characters',
                },
                pattern: {
                  value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
                  message: 'Password must include uppercase, lowercase, number and special character',
                },
              })}
            />
            <FormErrorMessage>{errors.newPassword?.message}</FormErrorMessage>
          </FormControl>

          <FormControl id="confirmPassword" isInvalid={!!errors.confirmPassword}>
            <FormLabel>Confirm New Password</FormLabel>
            <Input
              type="password"
              {...register('confirmPassword', {
                required: 'Please confirm your password',
                validate: value => value === newPassword || 'Passwords do not match',
              })}
            />
            <FormErrorMessage>{errors.confirmPassword?.message}</FormErrorMessage>
          </FormControl>

          <Button
            type="submit"
            colorScheme="brand"
            isLoading={isSubmitting}
            alignSelf="flex-start"
          >
            Update Password
          </Button>
        </Stack>
      </Box>

      <Divider my={6} />

      {/* Two-Factor Authentication */}
      <Box mb={6}>
        <Heading size="sm" mb={4}>
          Two-Factor Authentication
        </Heading>
        
        <Flex align="center" justify="space-between">
          <Box>
            <Text fontWeight="medium">
              {twoFactorEnabled ? 'Enabled' : 'Disabled'}
            </Text>
            <Text fontSize="sm" color="gray.500">
              {twoFactorEnabled
                ? 'Your account is protected with two-factor authentication'
                : 'Add an extra layer of security to your account'}
            </Text>
          </Box>
          
          <Switch
            isChecked={twoFactorEnabled}
            onChange={handleTwoFactorToggle}
            colorScheme="brand"
            size="lg"
            isDisabled={isTwoFactorSubmitting}
          />
        </Flex>
      </Box>

      {/* Account Deletion */}
      <Box>
        <Heading size="sm" mb={4} color="red.500">
          Delete Account
        </Heading>
        
        <Alert status="error" borderRadius="md">
          <AlertIcon />
          <Box>
            <AlertTitle>Warning!</AlertTitle>
            <AlertDescription>
              Deleting your account is permanent and cannot be undone. All your data will be permanently removed.
            </AlertDescription>
          </Box>
        </Alert>
        
        <Button
          mt={4}
          colorScheme="red"
          variant="outline"
        >
          Delete Account
        </Button>
      </Box>
    </Box>
  );
};

export default SecuritySettings;
