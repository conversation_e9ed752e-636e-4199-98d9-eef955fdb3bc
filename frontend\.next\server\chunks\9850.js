"use strict";exports.id=9850,exports.ids=[9850],exports.modules={29850:(e,a,t)=>{t.d(a,{Z:()=>__WEBPACK_DEFAULT_EXPORT__});var r=t(30784),s=t(9885),l=t(27870),d=t(14379),o=t(59872),i=t(3902);let __WEBPACK_DEFAULT_EXPORT__=({filters:e,onFilterChange:a,className:t=""})=>{let{t:n}=(0,l.$G)("social"),{isRtl:g}=(0,d.g)(),[x,c]=(0,s.useState)(e),handleFilterChange=(e,a)=>{let t={...x,[e]:a};c(t)};return(0,r.jsxs)("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 ${t}`,children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4",children:n("filters","Filters")}),r.jsx("div",{className:"mb-4",children:(0,r.jsxs)("label",{className:"inline-flex items-center",children:[r.jsx("input",{type:"checkbox",className:"form-checkbox h-5 w-5 text-primary-600 dark:text-primary-400",checked:!!x.following,onChange:e=>handleFilterChange("following",e.target.checked)}),r.jsx("span",{className:"ml-2 text-gray-700 dark:text-gray-300",children:n("followingOnly","Following only")})]})}),(0,r.jsxs)("div",{className:"mb-4",children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:n("postType","Post Type")}),(0,r.jsxs)("select",{className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100",value:x.type||"",onChange:e=>handleFilterChange("type",e.target.value||void 0),children:[r.jsx("option",{value:"",children:n("allTypes","All Types")}),r.jsx("option",{value:i.hQ.TEXT,children:n("textPosts","Text")}),r.jsx("option",{value:i.hQ.IMAGE,children:n("imagePosts","Images")}),r.jsx("option",{value:i.hQ.VIDEO,children:n("videoPosts","Videos")}),r.jsx("option",{value:i.hQ.PRODUCT,children:n("productPosts","Products")}),r.jsx("option",{value:i.hQ.STORE,children:n("storePosts","Stores")}),r.jsx("option",{value:i.hQ.POLL,children:n("pollPosts","Polls")})]})]}),(0,r.jsxs)("div",{className:"mb-4",children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:n("dateRange","Date Range")}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-xs text-gray-500 dark:text-gray-400 mb-1",children:n("from","From")}),r.jsx("input",{type:"date",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100",value:x.startDate||"",onChange:e=>handleFilterChange("startDate",e.target.value||void 0)})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-xs text-gray-500 dark:text-gray-400 mb-1",children:n("to","To")}),r.jsx("input",{type:"date",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100",value:x.endDate||"",onChange:e=>handleFilterChange("endDate",e.target.value||void 0),min:x.startDate})]})]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:n("tags","Tags")}),r.jsx("input",{type:"text",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100",placeholder:n("tagsPlaceholder","Enter tags separated by commas"),value:x.tags?.join(", ")||"",onChange:e=>{let a=e.target.value,t=a?a.split(",").map(e=>e.trim()).filter(Boolean):void 0;handleFilterChange("tags",t)}})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[r.jsx(o.Z,{variant:"outline",onClick:()=>{let e={};c(e),a(e)},children:n("reset","Reset")}),r.jsx(o.Z,{variant:"primary",onClick:()=>{a(x)},children:n("apply","Apply")})]})]})}}};