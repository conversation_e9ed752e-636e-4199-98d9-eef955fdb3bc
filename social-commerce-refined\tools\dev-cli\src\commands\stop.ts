import { Command } from 'commander';
import * as chalk from 'chalk';
import * as inquirer from 'inquirer';
import { getAllServices, serviceExists } from '../utils/paths';
import { stopServices } from '../utils/docker';

export function stopCommand(program: Command): void {
  program
    .command('stop [service]')
    .description('Stop services')
    .option('-a, --all', 'Stop all services')
    .option('-i, --infrastructure', 'Stop infrastructure services only')
    .action(async (service: string | undefined, options: { all?: boolean; infrastructure?: boolean }) => {
      try {
        // If no service is specified and --all is not set, prompt for service
        if (!service && !options.all && !options.infrastructure) {
          const services = getAllServices();
          
          if (services.length === 0) {
            console.log(chalk.yellow('No services found'));
            return;
          }
          
          const answers = await inquirer.prompt([
            {
              type: 'list',
              name: 'service',
              message: 'Which service do you want to stop?',
              choices: [...services, 'all', 'infrastructure'],
            },
          ]);
          
          if (answers.service === 'all') {
            options.all = true;
          } else if (answers.service === 'infrastructure') {
            options.infrastructure = true;
          } else {
            service = answers.service;
          }
        }

        // Stop all services
        if (options.all) {
          console.log(chalk.blue('Stopping all services...'));
          await stopServices();
          console.log(chalk.green('All services stopped successfully'));
          return;
        }

        // Stop infrastructure services
        if (options.infrastructure) {
          console.log(chalk.blue('Stopping infrastructure services...'));
          await stopServices();
          console.log(chalk.green('Infrastructure services stopped successfully'));
          return;
        }

        // Stop specific service
        if (service) {
          if (!serviceExists(service)) {
            console.error(chalk.red(`Service ${service}-service does not exist`));
            return;
          }
          
          console.log(chalk.blue(`Stopping ${service}-service...`));
          await stopServices([`${service}-service`]);
          console.log(chalk.green(`${service}-service stopped successfully`));
          return;
        }
      } catch (error) {
        console.error(chalk.red(`Error: ${error.message}`));
        process.exit(1);
      }
    });
}
