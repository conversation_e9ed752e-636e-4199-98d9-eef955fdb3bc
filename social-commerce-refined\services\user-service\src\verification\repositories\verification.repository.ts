import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { VerificationToken, TokenType } from '../entities/verification-token.entity';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class VerificationRepository {
  private readonly logger = new Logger(VerificationRepository.name);

  constructor(
    @InjectRepository(VerificationToken)
    private readonly repository: Repository<VerificationToken>,
  ) {}

  async findAll(): Promise<VerificationToken[]> {
    this.logger.log('Finding all verification tokens');
    return this.repository.find({ relations: ['user'] });
  }

  async findOne(id: string): Promise<VerificationToken> {
    this.logger.log(`Finding verification token with ID: ${id}`);
    const token = await this.repository.findOne({ 
      where: { id },
      relations: ['user'],
    });

    if (!token) {
      throw new NotFoundException(`Verification token with ID ${id} not found`);
    }

    return token;
  }

  async findByToken(token: string): Promise<VerificationToken> {
    this.logger.log(`Finding verification token: ${token}`);
    const verificationToken = await this.repository.findOne({ 
      where: { token },
      relations: ['user'],
    });

    if (!verificationToken) {
      throw new NotFoundException(`Verification token ${token} not found`);
    }

    return verificationToken;
  }

  async findByUserIdAndType(userId: string, type: TokenType): Promise<VerificationToken[]> {
    this.logger.log(`Finding ${type} tokens for user with ID: ${userId}`);
    return this.repository.find({ 
      where: { userId, type },
      relations: ['user'],
    });
  }

  async createToken(userId: string, type: TokenType, expiresInHours: number = 24): Promise<VerificationToken> {
    this.logger.log(`Creating ${type} token for user with ID: ${userId}`);
    
    // Create expiration date
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + expiresInHours);
    
    // Create token
    const verificationToken = this.repository.create({
      token: uuidv4(),
      type,
      expiresAt,
      userId,
    });
    
    return this.repository.save(verificationToken);
  }

  async markAsUsed(id: string): Promise<VerificationToken> {
    this.logger.log(`Marking verification token with ID ${id} as used`);
    
    const token = await this.findOne(id);
    
    token.isUsed = true;
    
    return this.repository.save(token);
  }

  async markTokenAsUsed(token: string): Promise<VerificationToken> {
    this.logger.log(`Marking verification token ${token} as used`);
    
    const verificationToken = await this.findByToken(token);
    
    verificationToken.isUsed = true;
    
    return this.repository.save(verificationToken);
  }

  async invalidateTokensByUserAndType(userId: string, type: TokenType): Promise<void> {
    this.logger.log(`Invalidating all ${type} tokens for user with ID: ${userId}`);
    
    const tokens = await this.findByUserIdAndType(userId, type);
    
    for (const token of tokens) {
      token.isUsed = true;
      await this.repository.save(token);
    }
  }

  async remove(id: string): Promise<void> {
    this.logger.log(`Removing verification token with ID: ${id}`);
    
    const token = await this.findOne(id);
    
    await this.repository.remove(token);
  }
}
