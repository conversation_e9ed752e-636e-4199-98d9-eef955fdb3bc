import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  Index,
} from 'typeorm';
import { CartItem } from './cart-item.entity';

export enum CartStatus {
  ACTIVE = 'active',
  ABANDONED = 'abandoned',
  CONVERTED = 'converted',
}

@Entity('carts')
@Index(['userId'])
@Index(['sessionId'])
@Index(['status'])
export class Cart {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid', { nullable: true })
  userId: string;

  @Column({ nullable: true })
  sessionId: string;

  @Column({ default: false })
  isGuestCart: boolean;

  @Column({
    type: 'enum',
    enum: CartStatus,
    default: CartStatus.ACTIVE,
  })
  status: CartStatus;

  @OneToMany(() => CartItem, (cartItem) => cartItem.cart, {
    cascade: true,
    eager: true,
  })
  items: CartItem[];

  @Column('decimal', { precision: 10, scale: 2, default: 0 })
  subtotal: number;

  @Column('decimal', { precision: 10, scale: 2, default: 0 })
  tax: number;

  @Column('decimal', { precision: 10, scale: 2, default: 0 })
  shipping: number;

  @Column('decimal', { precision: 10, scale: 2, default: 0 })
  discount: number;

  @Column('decimal', { precision: 10, scale: 2, default: 0 })
  total: number;

  @Column({ nullable: true })
  couponCode: string;

  @Column({ nullable: true })
  shippingMethodId: string;

  @Column('jsonb', { nullable: true })
  shippingAddress: Record<string, any>;

  @Column('jsonb', { nullable: true })
  billingAddress: Record<string, any>;

  @Column('jsonb', { nullable: true })
  metadata: Record<string, any>;

  @Column({ type: 'timestamp', nullable: true })
  lastActivity: Date;

  @CreateDateColumn({ type: 'timestamp with time zone' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone' })
  updatedAt: Date;

  // Computed properties
  get itemCount(): number {
    return this.items ? this.items.reduce((sum, item) => sum + item.quantity, 0) : 0;
  }

  get isEmpty(): boolean {
    return this.itemCount === 0;
  }

  // Helper methods
  updateLastActivity(): void {
    this.lastActivity = new Date();
  }

  calculateTotals(): void {
    if (!this.items || this.items.length === 0) {
      this.subtotal = 0;
      this.tax = 0;
      this.shipping = 0;
      this.discount = 0;
      this.total = 0;
      return;
    }

    this.subtotal = this.items.reduce((sum, item) => sum + item.total, 0);

    // Initialize values if they are null/undefined
    this.tax = this.tax || 0;
    this.shipping = this.shipping || 0;
    this.discount = this.discount || 0;

    this.total = this.subtotal + this.tax + this.shipping - this.discount;
  }
}
