#!/usr/bin/env node
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const commander_1 = require("commander");
const figlet = require("figlet");
const chalk = require("chalk");
const path = require("path");
const fs = require("fs-extra");
const start_1 = require("./commands/start");
const stop_1 = require("./commands/stop");
const status_1 = require("./commands/status");
const install_1 = require("./commands/install");
const build_1 = require("./commands/build");
const test_1 = require("./commands/test");
const generate_1 = require("./commands/generate");
const packageJsonPath = path.join(__dirname, '..', 'package.json');
const packageJson = fs.readJsonSync(packageJsonPath);
const program = new commander_1.Command();
console.log(chalk.cyan(figlet.textSync('Social Commerce Dev CLI', {
    horizontalLayout: 'full',
})));
program
    .name('dev')
    .description('Social Commerce Platform Development CLI')
    .version(packageJson.version);
(0, start_1.startCommand)(program);
(0, stop_1.stopCommand)(program);
(0, status_1.statusCommand)(program);
(0, install_1.installCommand)(program);
(0, build_1.buildCommand)(program);
(0, test_1.testCommand)(program);
(0, generate_1.generateCommand)(program);
program
    .command('help')
    .description('Display help information')
    .action(() => {
    program.outputHelp();
});
program.parse(process.argv);
if (!process.argv.slice(2).length) {
    program.outputHelp();
}
//# sourceMappingURL=index.js.map