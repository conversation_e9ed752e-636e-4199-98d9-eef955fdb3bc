import { Controller, Post, Body, Get, Param, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { NotificationService } from '../services/notification.service';
import { CreateNotificationDto } from '../dto/create-notification.dto';
import { SendEmailDto } from '../dto/send-email.dto';
import { SendSmsDto } from '../dto/send-sms.dto';

@ApiTags('Notifications')
@Controller('api/notifications')
export class NotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new notification' })
  @ApiResponse({ status: 201, description: 'Notification created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  async createNotification(@Body() createNotificationDto: CreateNotificationDto) {
    return this.notificationService.createNotification(createNotificationDto);
  }

  @Post('email')
  @ApiOperation({ summary: 'Send email directly' })
  @ApiResponse({ status: 200, description: 'Email sent successfully' })
  @ApiResponse({ status: 400, description: 'Invalid email data' })
  async sendEmail(@Body() sendEmailDto: SendEmailDto) {
    return this.notificationService.sendEmailDirect(sendEmailDto);
  }

  @Post('sms')
  @ApiOperation({ summary: 'Send SMS directly' })
  @ApiResponse({ status: 200, description: 'SMS sent successfully' })
  @ApiResponse({ status: 400, description: 'Invalid SMS data' })
  async sendSms(@Body() sendSmsDto: SendSmsDto) {
    return this.notificationService.sendSmsDirect(sendSmsDto);
  }

  @Get('health')
  @ApiOperation({ summary: 'Check notification service health' })
  @ApiResponse({ status: 200, description: 'Service health status' })
  async getHealth() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'notification-service',
      version: '1.0.0',
      features: {
        email: 'enabled',
        sms: 'enabled (mock)',
        database: 'connected',
        microservices: 'enabled',
      },
    };
  }
}
