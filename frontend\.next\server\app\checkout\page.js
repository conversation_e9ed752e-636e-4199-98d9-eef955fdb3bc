(()=>{var e={};e.id=285,e.ids=[285],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},33358:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>o});var t=a(67096),r=a(16132),n=a(37284),l=a.n(n),i=a(32564),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);a.d(s,c);let o=["",{children:["checkout",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,78855)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\checkout\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,9291,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\checkout\\page.tsx"],u="/checkout/page",m={require:a,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/checkout/page",pathname:"/checkout",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},17956:(e,s,a)=>{Promise.resolve().then(a.bind(a,39632))},39632:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>CheckoutPage});var t=a(30784),r=a(9885),n=a(57114),l=a(87771),i=a(21233),c=a(59872),o=a(706);function CheckoutPage(){let{items:e}=(0,l.v9)(e=>e.cart),s=(0,l.I0)(),a=(0,n.useRouter)(),[d,u]=(0,r.useState)({name:"",email:"",address:"",city:"",postalCode:"",country:""}),[m,h]=(0,r.useState)(!1),handleChange=e=>{let{name:s,value:a}=e.target;u(e=>({...e,[s]:a}))},handleSubmit=async e=>{e.preventDefault(),h(!0),await new Promise(e=>setTimeout(e,1500)),s((0,i.LL)()),a.push("/checkout/success")},formatPrice=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e);return 0===e.length?(a.push("/cart"),null):t.jsx("div",{className:"min-h-screen p-6",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto",children:[t.jsx("h1",{className:"text-3xl font-bold mb-8",children:"Checkout"}),(0,t.jsxs)("div",{className:"md:flex md:gap-8",children:[t.jsx("div",{className:"md:w-2/3",children:(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6",children:[t.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Shipping Information"}),(0,t.jsxs)("form",{onSubmit:handleSubmit,children:[t.jsx(o.Z,{label:"Full Name",name:"name",value:d.name,onChange:handleChange,required:!0}),t.jsx(o.Z,{label:"Email",name:"email",type:"email",value:d.email,onChange:handleChange,required:!0}),t.jsx(o.Z,{label:"Address",name:"address",value:d.address,onChange:handleChange,required:!0}),(0,t.jsxs)("div",{className:"flex gap-4",children:[t.jsx("div",{className:"flex-1",children:t.jsx(o.Z,{label:"City",name:"city",value:d.city,onChange:handleChange,required:!0})}),t.jsx("div",{className:"flex-1",children:t.jsx(o.Z,{label:"Postal Code",name:"postalCode",value:d.postalCode,onChange:handleChange,required:!0})})]}),t.jsx(o.Z,{label:"Country",name:"country",value:d.country,onChange:handleChange,required:!0}),t.jsx("div",{className:"mt-6",children:t.jsx(c.Z,{type:"submit",fullWidth:!0,isLoading:m,children:"Place Order"})})]})]})}),t.jsx("div",{className:"md:w-1/3",children:(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6",children:[t.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Order Summary"}),t.jsx("div",{className:"space-y-4",children:e.map(e=>(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsxs)("div",{children:[t.jsx("p",{className:"font-medium",children:e.title}),(0,t.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Qty: ",e.quantity]})]}),t.jsx("p",{children:formatPrice(e.price*e.quantity)})]},e.id))}),t.jsx("div",{className:"border-t border-gray-200 dark:border-gray-700 mt-4 pt-4",children:(0,t.jsxs)("div",{className:"flex justify-between font-bold",children:[t.jsx("p",{children:"Total"}),t.jsx("p",{children:formatPrice(e.reduce((e,s)=>e+s.price*s.quantity,0))})]})})]})})]})]})})}},78855:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>l,__esModule:()=>n,default:()=>c});var t=a(95153);let r=(0,t.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\checkout\page.tsx`),{__esModule:n,$$typeof:l}=r,i=r.default,c=i}};var s=require("../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),a=s.X(0,[2103,2765,706],()=>__webpack_exec__(33358));module.exports=a})();