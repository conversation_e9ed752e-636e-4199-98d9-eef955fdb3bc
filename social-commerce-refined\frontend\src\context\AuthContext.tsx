import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useRouter } from 'next/router';
import { authApi, userApi } from '@/services/api';
import { jwtDecode } from 'jwt-decode';

interface User {
  id: string;
  email: string;
  name: string;
  role: string;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: any) => Promise<void>;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    // Check if user is logged in on initial load
    const token = localStorage.getItem('token');
    if (token) {
      try {
        // Decode token to get user info
        const decoded: any = jwtDecode(token);

        // Check if token is expired
        const currentTime = Date.now() / 1000;
        if (decoded.exp && decoded.exp < currentTime) {
          // Token is expired
          localStorage.removeItem('token');
          setUser(null);
          setIsLoading(false);
          return;
        }

        // Fetch user profile
        fetchUserProfile();
      } catch (error) {
        console.error('Invalid token:', error);
        localStorage.removeItem('token');
        setUser(null);
        setIsLoading(false);
      }
    } else {
      setIsLoading(false);
    }
  }, []);

  const fetchUserProfile = async () => {
    try {
      const userData = await userApi.getProfile();
      setUser(userData);
    } catch (error) {
      console.error('Error fetching user profile:', error);
      localStorage.removeItem('token');
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    console.log('AuthContext: Login attempt started', { email });
    console.log('AuthContext: Password details', {
      length: password.length,
      firstChar: password.charAt(0),
      lastChar: password.charAt(password.length - 1),
      hasUppercase: /[A-Z]/.test(password),
      hasLowercase: /[a-z]/.test(password),
      hasNumbers: /[0-9]/.test(password),
      hasSpecialChars: /[!@#$%^&*]/.test(password)
    });
    setIsLoading(true);
    try {
      console.log('AuthContext: Calling authApi.login');
      const response: any = await authApi.login(email, password);
      console.log('AuthContext: Login response received', response);

      const token = response.accessToken || response.token; // Handle both formats
      console.log('AuthContext: Token extracted', { hasToken: !!token });

      localStorage.setItem('token', token);

      // Decode token to get user info
      const decoded: any = jwtDecode(token);
      console.log('AuthContext: Token decoded', decoded);

      setUser({
        id: decoded.sub,
        email: decoded.email,
        name: decoded.name || '',
        role: decoded.role || 'user',
      });

      console.log('AuthContext: User set, redirecting to dashboard');
      router.push('/dashboard');
    } catch (error) {
      console.error('AuthContext: Login error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData: any) => {
    setIsLoading(true);
    try {
      await authApi.register(userData);
      // After registration, redirect to login
      router.push('/login?registered=true');
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    localStorage.removeItem('token');
    setUser(null);
    router.push('/login');
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        isAuthenticated: !!user,
        login,
        register,
        logout,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
