import { Controller, Get, Post, Put, Delete, Patch, Param, Body, Query, Headers, Req, Res, HttpStatus, Logger, UseInterceptors } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { RoutingService } from '../services/routing.service';
import { Request, Response } from 'express';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { CacheInterceptor } from '../../shared/interceptors/cache.interceptor';

@ApiTags('stores')
@Controller('stores')
export class StoreController {
  private readonly logger = new Logger(StoreController.name);
  private readonly SERVICE_NAME = 'store';

  constructor(private readonly routingService: RoutingService) {}

  @Get()
  @UseInterceptors(CacheInterceptor)
  @ApiOperation({ summary: 'Get all stores' })
  @ApiResponse({ status: 200, description: 'Return all stores' })
  getAllStores(@Req() req: Request, @Headers() headers: any, @Query() query: any): Observable<any> {
    this.logger.log('Forwarding GET /stores request');
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, '/stores', 'GET', null, headers)
      .pipe(map((response) => response.data));
  }

  @Get(':id')
  @UseInterceptors(CacheInterceptor)
  @ApiOperation({ summary: 'Get a store by ID' })
  @ApiResponse({ status: 200, description: 'Return the store' })
  @ApiResponse({ status: 404, description: 'Store not found' })
  getStoreById(@Param('id') id: string, @Headers() headers: any): Observable<any> {
    this.logger.log(`Forwarding GET /stores/${id} request`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, `/stores/${id}`, 'GET', null, headers)
      .pipe(map((response) => response.data));
  }

  @Post()
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new store' })
  @ApiResponse({ status: 201, description: 'Store created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  createStore(@Body() body: any, @Headers() headers: any): Observable<any> {
    this.logger.log('Forwarding POST /stores request');
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, '/stores', 'POST', body, headers)
      .pipe(map((response) => response.data));
  }

  @Put(':id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update a store' })
  @ApiResponse({ status: 200, description: 'Store updated successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'Store not found' })
  updateStore(@Param('id') id: string, @Body() body: any, @Headers() headers: any): Observable<any> {
    this.logger.log(`Forwarding PUT /stores/${id} request`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, `/stores/${id}`, 'PUT', body, headers)
      .pipe(map((response) => response.data));
  }

  @Delete(':id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete a store' })
  @ApiResponse({ status: 200, description: 'Store deleted successfully' })
  @ApiResponse({ status: 404, description: 'Store not found' })
  deleteStore(@Param('id') id: string, @Headers() headers: any): Observable<any> {
    this.logger.log(`Forwarding DELETE /stores/${id} request`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, `/stores/${id}`, 'DELETE', null, headers)
      .pipe(map((response) => response.data));
  }

  // Products routes
  @Get(':storeId/products')
  @UseInterceptors(CacheInterceptor)
  @ApiOperation({ summary: 'Get all products for a store' })
  @ApiResponse({ status: 200, description: 'Return all products for the store' })
  @ApiResponse({ status: 404, description: 'Store not found' })
  getStoreProducts(@Param('storeId') storeId: string, @Headers() headers: any): Observable<any> {
    this.logger.log(`Forwarding GET /stores/${storeId}/products request`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, `/stores/${storeId}/products`, 'GET', null, headers)
      .pipe(map((response) => response.data));
  }

  @Post(':storeId/products')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new product for a store' })
  @ApiResponse({ status: 201, description: 'Product created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'Store not found' })
  createStoreProduct(@Param('storeId') storeId: string, @Body() body: any, @Headers() headers: any): Observable<any> {
    this.logger.log(`Forwarding POST /stores/${storeId}/products request`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, `/stores/${storeId}/products`, 'POST', body, headers)
      .pipe(map((response) => response.data));
  }

  // Catch-all route for other store service endpoints
  @Get('*')
  @UseInterceptors(CacheInterceptor)
  @ApiOperation({ summary: 'Forward any GET request to the store service' })
  forwardGetRequest(@Req() req: Request, @Headers() headers: any): Observable<any> {
    const path = req.url;
    this.logger.log(`Forwarding GET ${path} request to store service`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, path, 'GET', null, headers)
      .pipe(map((response) => response.data));
  }

  @Post('*')
  @ApiOperation({ summary: 'Forward any POST request to the store service' })
  forwardPostRequest(@Req() req: Request, @Body() body: any, @Headers() headers: any): Observable<any> {
    const path = req.url;
    this.logger.log(`Forwarding POST ${path} request to store service`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, path, 'POST', body, headers)
      .pipe(map((response) => response.data));
  }

  @Put('*')
  @ApiOperation({ summary: 'Forward any PUT request to the store service' })
  forwardPutRequest(@Req() req: Request, @Body() body: any, @Headers() headers: any): Observable<any> {
    const path = req.url;
    this.logger.log(`Forwarding PUT ${path} request to store service`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, path, 'PUT', body, headers)
      .pipe(map((response) => response.data));
  }

  @Delete('*')
  @ApiOperation({ summary: 'Forward any DELETE request to the store service' })
  forwardDeleteRequest(@Req() req: Request, @Headers() headers: any): Observable<any> {
    const path = req.url;
    this.logger.log(`Forwarding DELETE ${path} request to store service`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, path, 'DELETE', null, headers)
      .pipe(map((response) => response.data));
  }

  @Patch('*')
  @ApiOperation({ summary: 'Forward any PATCH request to the store service' })
  forwardPatchRequest(@Req() req: Request, @Body() body: any, @Headers() headers: any): Observable<any> {
    const path = req.url;
    this.logger.log(`Forwarding PATCH ${path} request to store service`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, path, 'PATCH', body, headers)
      .pipe(map((response) => response.data));
  }
}
