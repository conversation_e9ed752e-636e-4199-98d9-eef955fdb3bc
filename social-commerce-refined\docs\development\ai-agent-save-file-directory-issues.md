# AI Agent save-file Tool Directory Issues & Solutions

## Overview

**Date:** May 31, 2025  
**Context:** Notification Service implementation  
**Issue:** save-file tool creating files in wrong directory  
**Status:** ✅ **RESOLVED** - Solution implemented and documented  

## Table of Contents

1. [Issue Description](#issue-description)
2. [Root Cause Analysis](#root-cause-analysis)
3. [Impact Assessment](#impact-assessment)
4. [Solution Implementation](#solution-implementation)
5. [Prevention Guidelines](#prevention-guidelines)
6. [Verification Procedures](#verification-procedures)

## Issue Description

### The Problem
During notification service implementation, the AI agent was creating files in the **parent directory** instead of the **active project directory**.

**Expected Location:** `C:\Users\<USER>\Documents\augment\social-commerce\social-commerce-refined\services\notification-service\src\`  
**Actual Location:** `C:\Users\<USER>\Documents\augment\social-commerce\services\notification-service\src\`

### Symptoms
- Files created with `save-file` tool appeared in wrong directory
- `launch-process` commands with correct working directory had no effect on `save-file`
- Mixed file structure with files in both parent and project directories
- Build failures due to missing files in expected locations

## Root Cause Analysis

### Technical Investigation

#### Working Directory Behavior
```bash
# launch-process correctly sets working directory
cd /c/Users/<USER>/Documents/augment/social-commerce/social-commerce-refined && pwd
# Output: /c/Users/<USER>/Documents/augment/social-commerce/social-commerce-refined

# But save-file ignores this working directory
save-file path="services/notification-service/src/main.ts"
# Creates file in: C:\Users\<USER>\Documents\augment\social-commerce\services\...
# NOT in: C:\Users\<USER>\Documents\augment\social-commerce\social-commerce-refined\services\...
```

#### Root Cause Identified
**The `save-file` tool does NOT respect the working directory set by `launch-process` commands.**

### Why This Happens
1. **Tool Independence:** `save-file` operates independently of terminal working directory
2. **Relative Path Resolution:** Relative paths in `save-file` resolve from a different base directory
3. **Tool Design:** `save-file` likely uses its own working directory context

## Impact Assessment

### Immediate Impact
- ❌ Files created in wrong locations
- ❌ Build processes fail to find expected files
- ❌ Service implementation blocked
- ❌ Confusion about project structure

### Development Impact
- 🔄 Time wasted debugging "missing files"
- 🔄 Manual file movement required
- 🔄 Repeated mistakes if not documented
- 🔄 Inconsistent project structure

### Process Impact
- 📋 Violation of systematic development workflow
- 📋 Breaks template consistency approach
- 📋 Creates technical debt

## Solution Implementation

### Primary Solution: Full Absolute Paths
**RULE:** Always use full absolute paths in `save-file` tool

#### Before (Incorrect)
```typescript
save-file path="services/notification-service/src/main.ts"
```

#### After (Correct)
```typescript
save-file path="C:\Users\<USER>\Documents\augment\social-commerce\social-commerce-refined\services\notification-service\src\main.ts"
```

### Implementation Steps
1. **Identify Incorrect Files:** Find files created in wrong location
2. **Move Files:** Use `launch-process` with `mv` commands to relocate
3. **Update Process:** Use absolute paths for all future `save-file` operations
4. **Verify Location:** Check file placement after each `save-file` operation

### Example Fix Commands
```bash
# Move incorrectly placed files
cd /c/Users/<USER>/Documents/augment/social-commerce/social-commerce-refined && \
mv /c/Users/<USER>/Documents/augment/social-commerce/services/notification-service/src/main.ts \
   services/notification-service/src/

# Verify correct placement
ls -la services/notification-service/src/main.ts
```

## Prevention Guidelines

### For AI Agents
**MANDATORY BEHAVIOR:**
- ✅ Always use full absolute paths in `save-file`
- ✅ Verify file location after creation
- ✅ Use `launch-process` for file operations when possible
- ✅ Document any directory-related issues immediately

**PROHIBITED BEHAVIOR:**
- ❌ Using relative paths in `save-file`
- ❌ Assuming `save-file` respects working directory
- ❌ Proceeding without verifying file placement

### Path Template
```
C:\Users\<USER>\Documents\augment\social-commerce\social-commerce-refined\[relative-path]
```

### Verification Command
```bash
cd /c/Users/<USER>/Documents/augment/social-commerce/social-commerce-refined && \
find . -name "[filename]" -type f
```

## Verification Procedures

### After Each save-file Operation
1. **Verify File Exists in Correct Location**
   ```bash
   cd /c/Users/<USER>/Documents/augment/social-commerce/social-commerce-refined && \
   ls -la [expected-path]
   ```

2. **Check for Files in Wrong Location**
   ```bash
   cd /c/Users/<USER>/Documents/augment/social-commerce && \
   find . -name "[filename]" -type f
   ```

3. **Move Files if Necessary**
   ```bash
   cd /c/Users/<USER>/Documents/augment/social-commerce/social-commerce-refined && \
   mv [wrong-path] [correct-path]
   ```

### Project Structure Verification
```bash
# Verify complete project structure
cd /c/Users/<USER>/Documents/augment/social-commerce/social-commerce-refined && \
tree services/notification-service/src
```

## Related Documentation
- [AI Guidelines](../guidelines/AI-README.md)
- [Systematic Development Workflow](./systematic-development-workflow.md)
- [Development Environment Issues](./development-environment-issues-solutions.md)

---

**Key Lesson:** Tool behavior assumptions must be verified. The `save-file` tool operates independently of terminal working directory.

**Prevention:** Always use full absolute paths in `save-file` operations.

---

**Last Updated:** May 31, 2025  
**Status:** ✅ **COMPLETE** - Issue resolved, prevention guidelines established
