import React, { ReactNode } from 'react';
import { Box, Flex } from '@chakra-ui/react';
import Navbar from './Navbar';
import Sidebar from './Sidebar';
import { useAuth } from '@/context/AuthContext';

interface MainLayoutProps {
  children: ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const { isAuthenticated } = useAuth();

  return (
    <Flex direction="column" minH="100vh">
      <Navbar />
      <Flex flex="1">
        {isAuthenticated && (
          <Box
            as="aside"
            w="250px"
            minW="250px"
            h="calc(100vh - 60px)"
            borderRightWidth="1px"
            display={{ base: 'none', md: 'block' }}
          >
            <Sidebar />
          </Box>
        )}
        <Box as="main" flex="1" p={4} overflowY="auto">
          {children}
        </Box>
      </Flex>
    </Flex>
  );
};

export default MainLayout;
