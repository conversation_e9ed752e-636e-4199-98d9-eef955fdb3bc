import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { VerificationService } from './verification.service';
import { VerificationRepository } from '../repositories/verification.repository';
import { UserRepository } from '../../authentication/repositories/user.repository';
import { VerificationToken, TokenType } from '../entities/verification-token.entity';
import { User } from '../../authentication/entities/user.entity';

describe('VerificationService', () => {
  let service: VerificationService;
  let verificationRepository: VerificationRepository;
  let userRepository: UserRepository;
  let configService: ConfigService;
  let notificationClient: any;

  beforeEach(async () => {
    // Create mock implementations
    const mockVerificationRepository = {
      findByToken: jest.fn(),
      createToken: jest.fn(),
      markTokenAsUsed: jest.fn(),
      invalidateTokensByUserAndType: jest.fn(),
    };

    const mockUserRepository = {
      findOne: jest.fn(),
      findByEmail: jest.fn(),
      verifyEmail: jest.fn(),
      verifyPhone: jest.fn(),
      update: jest.fn(),
    };

    const mockConfigService = {
      get: jest.fn(),
    };

    const mockNotificationClient = {
      emit: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        VerificationService,
        {
          provide: VerificationRepository,
          useValue: mockVerificationRepository,
        },
        {
          provide: UserRepository,
          useValue: mockUserRepository,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: 'NOTIFICATION_SERVICE',
          useValue: mockNotificationClient,
        },
      ],
    }).compile();

    service = module.get<VerificationService>(VerificationService);
    verificationRepository = module.get<VerificationRepository>(VerificationRepository);
    userRepository = module.get<UserRepository>(UserRepository);
    configService = module.get<ConfigService>(ConfigService);
    notificationClient = module.get('NOTIFICATION_SERVICE');
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createEmailVerificationToken', () => {
    it('should create an email verification token', async () => {
      // Arrange
      const userId = '123';
      const user = {
        id: userId,
        email: '<EMAIL>',
      } as User;
      const token = {
        id: '456',
        token: 'verification-token',
        type: TokenType.EMAIL_VERIFICATION,
        userId,
      } as VerificationToken;

      jest.spyOn(userRepository, 'findOne').mockResolvedValue(user);
      jest.spyOn(verificationRepository, 'invalidateTokensByUserAndType').mockResolvedValue(undefined);
      jest.spyOn(verificationRepository, 'createToken').mockResolvedValue(token);
      jest.spyOn(notificationClient, 'emit').mockReturnValue(undefined);
      jest.spyOn(configService, 'get').mockReturnValue('http://localhost:3000');

      // Act
      const result = await service.createEmailVerificationToken(userId);

      // Assert
      expect(result).toEqual(token);
      expect(userRepository.findOne).toHaveBeenCalledWith(userId);
      expect(verificationRepository.invalidateTokensByUserAndType).toHaveBeenCalledWith(userId, TokenType.EMAIL_VERIFICATION);
      expect(verificationRepository.createToken).toHaveBeenCalledWith(userId, TokenType.EMAIL_VERIFICATION);
      expect(notificationClient.emit).toHaveBeenCalled();
    });
  });

  describe('verifyEmail', () => {
    it('should verify email with valid token', async () => {
      // Arrange
      const tokenString = 'verification-token';
      const userId = '123';
      const token = {
        id: '456',
        token: tokenString,
        type: TokenType.EMAIL_VERIFICATION,
        userId,
        isValid: jest.fn().mockReturnValue(true),
      } as unknown as VerificationToken;

      jest.spyOn(verificationRepository, 'findByToken').mockResolvedValue(token);
      jest.spyOn(verificationRepository, 'markTokenAsUsed').mockResolvedValue(token);
      jest.spyOn(userRepository, 'verifyEmail').mockResolvedValue({} as User);

      // Act
      const result = await service.verifyEmail(tokenString);

      // Assert
      expect(result).toBe(true);
      expect(verificationRepository.findByToken).toHaveBeenCalledWith(tokenString);
      expect(token.isValid).toHaveBeenCalled();
      expect(verificationRepository.markTokenAsUsed).toHaveBeenCalledWith(tokenString);
      expect(userRepository.verifyEmail).toHaveBeenCalledWith(userId);
    });

    it('should throw BadRequestException if token is invalid', async () => {
      // Arrange
      const tokenString = 'verification-token';
      const token = {
        id: '456',
        token: tokenString,
        type: TokenType.EMAIL_VERIFICATION,
        isValid: jest.fn().mockReturnValue(false),
      } as unknown as VerificationToken;

      jest.spyOn(verificationRepository, 'findByToken').mockResolvedValue(token);

      // Act & Assert
      await expect(service.verifyEmail(tokenString)).rejects.toThrow(BadRequestException);
      expect(verificationRepository.findByToken).toHaveBeenCalledWith(tokenString);
      expect(token.isValid).toHaveBeenCalled();
      expect(verificationRepository.markTokenAsUsed).not.toHaveBeenCalled();
      expect(userRepository.verifyEmail).not.toHaveBeenCalled();
    });

    it('should throw BadRequestException if token type is not EMAIL_VERIFICATION', async () => {
      // Arrange
      const tokenString = 'verification-token';
      const token = {
        id: '456',
        token: tokenString,
        type: TokenType.PASSWORD_RESET,
        isValid: jest.fn().mockReturnValue(true),
      } as unknown as VerificationToken;

      jest.spyOn(verificationRepository, 'findByToken').mockResolvedValue(token);

      // Act & Assert
      await expect(service.verifyEmail(tokenString)).rejects.toThrow(BadRequestException);
      expect(verificationRepository.findByToken).toHaveBeenCalledWith(tokenString);
      expect(verificationRepository.markTokenAsUsed).not.toHaveBeenCalled();
      expect(userRepository.verifyEmail).not.toHaveBeenCalled();
    });

    it('should throw BadRequestException if token not found', async () => {
      // Arrange
      const tokenString = 'verification-token';
      jest.spyOn(verificationRepository, 'findByToken').mockRejectedValue(new NotFoundException());

      // Act & Assert
      await expect(service.verifyEmail(tokenString)).rejects.toThrow(BadRequestException);
      expect(verificationRepository.findByToken).toHaveBeenCalledWith(tokenString);
      expect(verificationRepository.markTokenAsUsed).not.toHaveBeenCalled();
      expect(userRepository.verifyEmail).not.toHaveBeenCalled();
    });
  });

  describe('resetPassword', () => {
    it('should reset password with valid token', async () => {
      // Arrange
      const tokenString = 'reset-token';
      const userId = '123';
      const newPassword = 'newPassword123';
      const token = {
        id: '456',
        token: tokenString,
        type: TokenType.PASSWORD_RESET,
        userId,
        isValid: jest.fn().mockReturnValue(true),
      } as unknown as VerificationToken;

      jest.spyOn(verificationRepository, 'findByToken').mockResolvedValue(token);
      jest.spyOn(verificationRepository, 'markTokenAsUsed').mockResolvedValue(token);
      jest.spyOn(userRepository, 'update').mockResolvedValue({} as User);

      // Act
      const result = await service.resetPassword(tokenString, newPassword);

      // Assert
      expect(result).toBe(true);
      expect(verificationRepository.findByToken).toHaveBeenCalledWith(tokenString);
      expect(token.isValid).toHaveBeenCalled();
      expect(verificationRepository.markTokenAsUsed).toHaveBeenCalledWith(tokenString);
      expect(userRepository.update).toHaveBeenCalledWith(userId, { password: newPassword });
    });
  });
});
