import { Injectable, Logger, NotFoundException, ForbiddenException } from '@nestjs/common';
import { ProductRepository } from '../repositories/product.repository';
import { StoreRepository } from '../../store-management/repositories/store.repository';
import { CreateProductDto } from '../dto/create-product.dto';
import { UpdateProductDto } from '../dto/update-product.dto';
import { Product } from '../entities/product.entity';

@Injectable()
export class ProductService {
  private readonly logger = new Logger(ProductService.name);

  constructor(
    private readonly productRepository: ProductRepository,
    private readonly storeRepository: StoreRepository,
  ) {}

  async findAll(): Promise<Product[]> {
    this.logger.log('Finding all products');
    return this.productRepository.findAll();
  }

  async findOne(id: string): Promise<Product> {
    this.logger.log(`Finding product with ID: ${id}`);
    return this.productRepository.findOne(id);
  }

  async findByStoreId(storeId: string): Promise<Product[]> {
    this.logger.log(`Finding products for store with ID: ${storeId}`);
    
    // Verify that the store exists
    await this.storeRepository.findOne(storeId);
    
    return this.productRepository.findByStoreId(storeId);
  }

  async create(createProductDto: CreateProductDto): Promise<Product> {
    this.logger.log(`Creating product with name: ${createProductDto.name}`);
    
    // Verify that the store exists
    const store = await this.storeRepository.findOne(createProductDto.storeId);
    
    return this.productRepository.create(createProductDto);
  }

  async update(id: string, updateProductDto: UpdateProductDto, userId: string): Promise<Product> {
    this.logger.log(`Updating product with ID: ${id}`);
    
    // Check if the product exists
    const product = await this.productRepository.findOne(id);
    
    // Check if the user is the owner of the store
    const store = await this.storeRepository.findOne(product.storeId);
    
    if (store.ownerId !== userId) {
      throw new ForbiddenException('You are not authorized to update this product');
    }
    
    return this.productRepository.update(id, updateProductDto);
  }

  async remove(id: string, userId: string): Promise<void> {
    this.logger.log(`Removing product with ID: ${id}`);
    
    // Check if the product exists
    const product = await this.productRepository.findOne(id);
    
    // Check if the user is the owner of the store
    const store = await this.storeRepository.findOne(product.storeId);
    
    if (store.ownerId !== userId) {
      throw new ForbiddenException('You are not authorized to delete this product');
    }
    
    return this.productRepository.remove(id);
  }

  async updateRating(id: string, rating: number, reviewCount: number): Promise<Product> {
    this.logger.log(`Updating rating for product with ID: ${id}`);
    return this.productRepository.updateRating(id, rating, reviewCount);
  }

  async incrementSalesCount(id: string, quantity: number = 1): Promise<Product> {
    this.logger.log(`Incrementing sales count for product with ID: ${id}`);
    return this.productRepository.incrementSalesCount(id, quantity);
  }

  async incrementViewCount(id: string): Promise<Product> {
    this.logger.log(`Incrementing view count for product with ID: ${id}`);
    return this.productRepository.incrementViewCount(id);
  }

  async updateQuantity(id: string, quantity: number): Promise<Product> {
    this.logger.log(`Updating quantity for product with ID: ${id}`);
    return this.productRepository.updateQuantity(id, quantity);
  }
}
