# 🏗️ Service Template Guidelines (Extracted from Working Code)

## Overview
These guidelines are extracted from our working User Service and Profile Analysis Service implementations.

## 📁 Standard Service Structure

```
service-name/
├── src/
│   ├── main.ts                    # Application entry point
│   ├── app.module.ts              # Root module with database & config
│   ├── app.controller.ts          # Health check endpoints
│   ├── app.service.ts             # Basic service info
│   ├── feature-name/              # Business feature (e.g., authentication)
│   │   ├── controllers/
│   │   ├── services/
│   │   ├── dto/
│   │   ├── entities/
│   │   ├── guards/
│   │   └── feature-name.module.ts
│   └── shared/
│       ├── services/
│       └── shared.module.ts
├── package.json                   # Dependencies & scripts
├── tsconfig.json                  # TypeScript config
├── nest-cli.json                  # NestJS CLI config
├── .env.example                   # Environment template
├── Dockerfile                     # Container config
└── README.md                      # Service documentation
```

## 🔧 Required Files Template

### 1. package.json Template
```json
{
  "name": "service-name",
  "version": "1.0.0",
  "description": "Service description for Social NFT Platform",
  "main": "dist/main.js",
  "scripts": {
    "start:dev": "nest start --watch",
    "build": "nest build",
    "start": "nest start",
    "start:prod": "node dist/main"
  },
  "dependencies": {
    "@nestjs/common": "^10.0.0",
    "@nestjs/core": "^10.0.0",
    "@nestjs/platform-express": "^10.0.0",
    "@nestjs/config": "^3.1.1",
    "@nestjs/typeorm": "^10.0.0",
    "@nestjs/swagger": "^7.1.13",
    "typeorm": "^0.3.17",
    "class-validator": "^0.14.0",
    "class-transformer": "^0.5.1",
    "reflect-metadata": "^0.1.13",
    "rxjs": "^7.8.1"
  },
  "devDependencies": {
    "@nestjs/cli": "^10.0.0",
    "typescript": "^5.1.3"
  }
}
```

### 2. main.ts Template
```typescript
import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,
    transform: true,
    forbidNonWhitelisted: true,
  }));
  
  const config = new DocumentBuilder()
    .setTitle('Service Name API')
    .setDescription('Service description')
    .setVersion('1.0')
    .addBearerAuth()
    .build();
  
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);
  
  const port = process.env.PORT || 3001; // Change port per service
  await app.listen(port);
  
  console.log(`🚀 Service running on port ${port}`);
}

bootstrap();
```

### 3. app.module.ts Template
```typescript
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { FeatureModule } from './feature/feature.module';
import { SharedModule } from './shared/shared.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT) || 5432,
      username: process.env.DB_USERNAME || 'postgres',
      password: process.env.DB_PASSWORD || 'postgres',
      database: process.env.DB_DATABASE || 'service_db',
      autoLoadEntities: true,
      synchronize: process.env.NODE_ENV === 'development',
    }),
    SharedModule,
    FeatureModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
```

## 🎯 AI Agent Implementation Rules

### Service Creation Process
1. **Copy user-service directory structure**
2. **Rename service in package.json**
3. **Change port in main.ts**
4. **Update database name in app.module.ts**
5. **Replace feature modules with business-specific features**
6. **Update README.md with service-specific information**

### Port Assignment
- user-service: 3001
- profile-analysis-service: 3002
- nft-generation-service: 3003
- blockchain-service: 3004
- marketplace-service: 3005
- project-service: 3006
- analytics-service: 3007
- notification-service: 3008

### Database Naming
- Service name + "_db" suffix
- Example: user_service_db, profile_analysis_db

### Feature Module Pattern
```typescript
// feature/feature.module.ts
@Module({
  imports: [TypeOrmModule.forFeature([Entity])],
  controllers: [FeatureController],
  providers: [FeatureService],
  exports: [FeatureService],
})
export class FeatureModule {}
```

## ✅ Validation Checklist

Before completing service creation:
- [ ] Service name updated in package.json
- [ ] Unique port assigned in main.ts
- [ ] Database name updated in app.module.ts
- [ ] Feature modules reflect business domain
- [ ] Swagger documentation updated
- [ ] README.md customized for service
- [ ] Environment variables documented
- [ ] Health check endpoints working

## 🚀 Success Metrics

A properly created service should:
- Build without errors
- Start on assigned port
- Show Swagger docs at /api/docs
- Respond to health check at /health
- Follow consistent naming patterns
- Have proper TypeScript types
- Include input validation
