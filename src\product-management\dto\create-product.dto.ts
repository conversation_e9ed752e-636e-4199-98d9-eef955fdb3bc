import { <PERSON>String, IsNotEmpty, IsOptional, IsNumber, IsBoolean, IsArray, IsObject, Min, IsUUID } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class CreateProductDto {
  @ApiProperty({
    description: 'The name of the product',
    example: 'Awesome Product',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiPropertyOptional({
    description: 'The description of the product',
    example: 'This is an awesome product with amazing features',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'The price of the product',
    example: 99.99,
  })
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  price: number;

  @ApiPropertyOptional({
    description: 'The quantity of the product',
    example: 100,
    default: 0,
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  @Type(() => Number)
  quantity?: number;

  @ApiPropertyOptional({
    description: 'Whether the product is active',
    example: true,
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'The images of the product',
    example: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
  })
  @IsArray()
  @IsOptional()
  images?: string[];

  @ApiPropertyOptional({
    description: 'The categories of the product',
    example: ['Electronics', 'Gadgets'],
  })
  @IsArray()
  @IsOptional()
  categories?: string[];

  @ApiPropertyOptional({
    description: 'The tags of the product',
    example: ['new', 'featured', 'sale'],
  })
  @IsArray()
  @IsOptional()
  tags?: string[];

  @ApiPropertyOptional({
    description: 'The attributes of the product',
    example: {
      color: 'Red',
      size: 'Large',
      material: 'Cotton',
    },
  })
  @IsObject()
  @IsOptional()
  attributes?: Record<string, string>;

  @ApiPropertyOptional({
    description: 'The variants of the product',
    example: [
      {
        id: '1',
        name: 'Red Large',
        price: 99.99,
        quantity: 50,
        attributes: {
          color: 'Red',
          size: 'Large',
        },
      },
      {
        id: '2',
        name: 'Blue Medium',
        price: 89.99,
        quantity: 30,
        attributes: {
          color: 'Blue',
          size: 'Medium',
        },
      },
    ],
  })
  @IsArray()
  @IsOptional()
  variants?: {
    id: string;
    name: string;
    price: number;
    quantity: number;
    attributes: Record<string, string>;
  }[];

  @ApiPropertyOptional({
    description: 'The SKU of the product',
    example: 'SKU-123456',
  })
  @IsString()
  @IsOptional()
  sku?: string;

  @ApiPropertyOptional({
    description: 'The barcode of the product',
    example: '123456789012',
  })
  @IsString()
  @IsOptional()
  barcode?: string;

  @ApiPropertyOptional({
    description: 'The weight of the product',
    example: 1.5,
  })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  weight?: number;

  @ApiPropertyOptional({
    description: 'The dimensions of the product',
    example: '10x20x30',
  })
  @IsString()
  @IsOptional()
  dimensions?: string;

  @ApiPropertyOptional({
    description: 'The shipping information of the product',
    example: {
      dimensions: {
        length: 10,
        width: 20,
        height: 30,
        unit: 'cm',
      },
      weight: {
        value: 1.5,
        unit: 'kg',
      },
      freeShipping: true,
      shippingClass: 'standard',
    },
  })
  @IsObject()
  @IsOptional()
  shipping?: {
    dimensions?: {
      length: number;
      width: number;
      height: number;
      unit: string;
    };
    weight?: {
      value: number;
      unit: string;
    };
    freeShipping?: boolean;
    shippingClass?: string;
  };

  @ApiPropertyOptional({
    description: 'The SEO information of the product',
    example: {
      title: 'Awesome Product - Buy Now',
      description: 'This is an awesome product with amazing features',
      keywords: ['awesome', 'product', 'amazing'],
    },
  })
  @IsObject()
  @IsOptional()
  seo?: {
    title?: string;
    description?: string;
    keywords?: string[];
  };

  @ApiProperty({
    description: 'The ID of the store',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  storeId: string;
}
