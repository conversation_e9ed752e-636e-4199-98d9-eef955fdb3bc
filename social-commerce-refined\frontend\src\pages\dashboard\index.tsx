import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  Heading,
  Text,
  SimpleGrid,
  Icon,
  Divider,
} from '@chakra-ui/react';
import { FiShoppingBag, FiShoppingCart, FiDollarSign, FiUsers } from 'react-icons/fi';
import MainLayout from '@/components/layout/MainLayout';
import { useAuth } from '@/context/AuthContext';
import StatCard from '@/components/dashboard/StatCard';
import RecentActivity from '@/components/dashboard/RecentActivity';
import StoreSummary from '@/components/dashboard/StoreSummary';

// Define types for our dashboard data
interface DashboardStats {
  stores: string;
  products: string;
  orders: string;
  revenue: string;
}

interface Store {
  id: string;
  name: string;
  description: string;
  productCount: number;
  orderCount: number;
  isActive: boolean;
  imageUrl?: string;
}

interface Activity {
  id: string;
  type: 'order' | 'store' | 'product' | 'review';
  title: string;
  description: string;
  time: string;
  status?: 'success' | 'pending' | 'error';
}

interface DashboardData {
  stats: DashboardStats;
  stores: Store[];
  activities: Activity[];
}

const Dashboard = () => {
  const { isAuthenticated, isLoading, user } = useAuth();
  const router = useRouter();
  const [dashboardData, setDashboardData] = useState<DashboardData>({
    stats: {
      stores: '0',
      products: '0',
      orders: '0',
      revenue: '$0',
    },
    stores: [],
    activities: [],
  });

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, isLoading, router]);

  // Fetch dashboard data
  useEffect(() => {
    if (isAuthenticated) {
      // This would be replaced with actual API calls
      // For now, we'll use mock data
      setDashboardData({
        stats: {
          stores: '2',
          products: '15',
          orders: '24',
          revenue: '$1,240',
        },
        stores: [
          {
            id: '1',
            name: 'Tech Gadgets',
            description: 'The latest tech gadgets and accessories',
            productCount: 8,
            orderCount: 15,
            isActive: true,
          },
          {
            id: '2',
            name: 'Home Decor',
            description: 'Beautiful items for your home',
            productCount: 7,
            orderCount: 9,
            isActive: true,
          },
        ],
        activities: [
          {
            id: '1',
            type: 'order',
            title: 'New Order #1234',
            description: 'Someone purchased Wireless Headphones',
            time: '10 minutes ago',
            status: 'success',
          },
          {
            id: '2',
            type: 'product',
            title: 'Product Added',
            description: 'You added Smart Watch to your store',
            time: '2 hours ago',
          },
          {
            id: '3',
            type: 'review',
            title: 'New Review',
            description: 'Your product Bluetooth Speaker received a 5-star review',
            time: '1 day ago',
          },
        ],
      });
    }
  }, [isAuthenticated]);

  // Show loading or not found if not authenticated
  if (isLoading || !isAuthenticated) {
    return (
      <Box textAlign="center" py={10}>
        <Text>Loading...</Text>
      </Box>
    );
  }

  return (
    <MainLayout>
      <Box maxW="7xl" mx="auto" px={{ base: 4, md: 8 }}>
        <Heading as="h1" size="xl" mb={2}>
          Dashboard
        </Heading>

        <Text mb={6} color="gray.600">
          Welcome back, {user?.name || 'User'}! Here's an overview of your account.
        </Text>

        {/* Stats Cards */}
        <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6} mb={8}>
          <StatCard
            title="Stores"
            stat={dashboardData.stats.stores}
            icon={<Icon as={FiShoppingBag} w={8} h={8} />}
          />
          <StatCard
            title="Products"
            stat={dashboardData.stats.products}
            icon={<Icon as={FiShoppingCart} w={8} h={8} />}
          />
          <StatCard
            title="Orders"
            stat={dashboardData.stats.orders}
            icon={<Icon as={FiUsers} w={8} h={8} />}
            helpText="Last 30 days"
          />
          <StatCard
            title="Revenue"
            stat={dashboardData.stats.revenue}
            icon={<Icon as={FiDollarSign} w={8} h={8} />}
            helpText="Last 30 days"
            change="12%"
            isUp={true}
          />
        </SimpleGrid>

        <Divider my={8} />

        {/* Stores and Activity */}
        <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={8}>
          <StoreSummary stores={dashboardData.stores} />
          <RecentActivity activities={dashboardData.activities} />
        </SimpleGrid>
      </Box>
    </MainLayout>
  );
};

export default Dashboard;
