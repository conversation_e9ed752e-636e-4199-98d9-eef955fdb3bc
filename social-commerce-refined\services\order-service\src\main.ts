import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import { Transport, MicroserviceOptions } from '@nestjs/microservices';
import { AppModule } from './app.module';

async function bootstrap() {
  const logger = new Logger('OrderService');

  // Create the main HTTP application
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  // Global configuration
  app.setGlobalPrefix('api');
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // CORS configuration
  app.enableCors({
    origin: true,
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    credentials: true,
  });

  // Swagger documentation
  const config = new DocumentBuilder()
    .setTitle('Order Service API')
    .setDescription('Order Service for Social Commerce Platform')
    .setVersion('1.0')
    .addBearerAuth()
    .addTag('orders', 'Order management operations')
    .addTag('health', 'Health check endpoints')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  // Connect microservice for RabbitMQ
  const microservice = app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.RMQ,
    options: {
      urls: [configService.get<string>('RABBITMQ_URL', 'amqp://admin:admin@localhost:5672')],
      queue: configService.get<string>('ORDER_QUEUE', 'order_queue'),
      queueOptions: {
        durable: true,
      },
    },
  });

  // Start both HTTP server and microservice
  await app.startAllMicroservices();
  
  const port = configService.get<number>('PORT', 3006);
  await app.listen(port);

  logger.log(`🚀 Order Service is running on: http://localhost:${port}`);
  logger.log(`📚 API Documentation available at: http://localhost:${port}/api/docs`);
  logger.log(`🔍 Health check available at: http://localhost:${port}/api/health`);
  logger.log(`📨 RabbitMQ microservice connected to queue: ${configService.get<string>('ORDER_QUEUE', 'order_queue')}`);
}

bootstrap().catch((error) => {
  Logger.error('❌ Failed to start Order Service', error);
  process.exit(1);
});
