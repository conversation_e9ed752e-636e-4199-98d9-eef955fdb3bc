"use strict";exports.id=9809,exports.ids=[9809],exports.modules={79809:e=>{e.exports=JSON.parse('{"export":"Export","import":"Import","exportData":"Export Data","importData":"Import Data","selectFormat":"Select Export Format","selectImportFormat":"Select Import Format","selectFields":"Select Fields to Export","additionalOptions":"Additional Options","importOptions":"Import Options","uploadFile":"Upload File","exportButton":"Export","importButton":"Import","validateButton":"Validate","cancel":"Cancel","save":"Save","delete":"Delete","newExport":"New Export","newImport":"New Import","download":"Download","downloadSample":"Download Sample","selectAll":"Select All","deselectAll":"Deselect All","includeRelated":"Include Related Data","includeRelatedDescription":"Export related entities such as categories, attributes, and variants.","includeMedia":"Include Media URLs","includeMediaDescription":"Export media URLs for images and other media files.","updateExisting":"Update Existing Records","updateExistingDescription":"If a record with the same ID exists, update it instead of creating a new one.","skipErrors":"Skip Errors","skipErrorsDescription":"Continue importing even if some records have errors.","validateOnly":"Validate Only","validateOnlyDescription":"Check the file for errors without actually importing the data.","exportStatus":"Export Status","importStatus":"Import Status","exportId":"Export ID","importId":"Import ID","recordsProcessed":"records processed","successCount":"Success","errorCount":"Errors","warningCount":"Warnings","errorOccurred":"An error occurred","validationErrors":"Validation Errors","importErrors":"Import Errors","row":"Row","column":"Column","entityId":"Entity ID","message":"Message","severity":"Severity","exportTemplates":"Export Templates","saveAsTemplate":"Save as Template","selectTemplate":"Select a Template","templateName":"Template Name","templateNamePlaceholder":"Enter template name","templateDescription":"Description (Optional)","templateDescriptionPlaceholder":"Enter template description","confirmDeleteTemplate":"Are you sure you want to delete this template?","noTemplates":"No templates available. Save your current settings as a template.","format":"Format","fields":"Fields","entityType":"Entity Type","createdAt":"Created At","updatedAt":"Updated At","yes":"Yes","no":"No","dragAndDrop":"or drag and drop","csvFileFormat":"CSV file with headers","jsonFileFormat":"JSON file with array of objects","excelFileFormat":"Excel file with data in the first sheet","sampleFileHelp":"Not sure about the file format? Download a sample file to get started.","noFileSelected":"Please select a file to import.","noFieldsSelected":"Please select at least one field to export.","errorLoadingFields":"Error loading export fields. Please try again later.","remove":"Remove","exportDetails":"Export Details","importDetails":"Import Details","formats":{"csv":"CSV","json":"JSON","excel":"Excel","csvDescription":"Comma-separated values file, compatible with most spreadsheet applications.","jsonDescription":"JavaScript Object Notation format, ideal for developers and data processing.","excelDescription":"Microsoft Excel format, with formatting and multiple sheets.","csvImportDescription":"Import from a comma-separated values file. Ideal for spreadsheet exports.","jsonImportDescription":"Import from a JavaScript Object Notation file. Best for preserving complex data structures.","excelImportDescription":"Import from a Microsoft Excel file. Supports multiple sheets and formatting."},"status":{"pending":"Pending","processing":"Processing","validating":"Validating","completed":"Completed","partiallyCompleted":"Partially Completed","failed":"Failed"}}')}};