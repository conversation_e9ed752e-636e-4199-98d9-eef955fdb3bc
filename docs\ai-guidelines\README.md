# AI Assistant Guidelines for Social Commerce Platform

This document provides comprehensive guidelines for AI assistants (including Augment AI Code Assistant) working on the Social Commerce Platform. Following these guidelines is mandatory to ensure consistency, quality, and adherence to the established development workflow.

## Table of Contents

- [Development Workflow](#development-workflow)
- [Code Generation Rules](#code-generation-rules)
- [Service-Specific Guidelines](#service-specific-guidelines)
- [Common Patterns](#common-patterns)
- [Error Handling](#error-handling)
- [Testing Requirements](#testing-requirements)
- [Documentation Requirements](#documentation-requirements)
- [Examples](#examples)

## Development Workflow

AI assistants must strictly adhere to the following development workflow:

### 1. Always Use the Development CLI Tool

The `dev-cli` tool is the primary interface for development tasks. Always use this tool instead of direct npm/Docker commands.

```bash
# CORRECT
dev start user

# INCORRECT
cd backend && npm run start:user
```

### 2. Always Check Service Health Before Troubleshooting

```bash
# REQUIRED BEFORE TROUBLESHOOTING
dev status
```

### 3. Follow the Established Directory Structure

- Place new files in the correct directories
- Follow the naming conventions
- Respect the modular architecture

### 4. Use Docker Compose for Service Management

```bash
# CORRECT
dev start

# INCORRECT
npm run start
```

### 5. Respect Service Boundaries

- Don't bypass the API Gateway
- Use the appropriate message patterns for service communication
- Follow the established microservices architecture

### 6. Implement Proper Error Handling

- Use the established error handling patterns
- Log errors appropriately
- Provide meaningful error messages

### 7. Document All Changes

- Update relevant documentation
- Add comments to complex code
- Follow JSDoc conventions

### 8. Test Thoroughly

- Write tests for all new functionality
- Ensure existing tests pass
- Test error handling and edge cases

## Code Generation Rules

When generating code, AI assistants must:

### 1. Follow Established Patterns

Examine existing code in the same service/module and follow the same patterns. Do not introduce new patterns without explicit approval.

### 2. Use Correct Imports and Dependencies

```typescript
// CORRECT
import { Injectable } from '@nestjs/common';
import { User } from './entities/user.entity';

// INCORRECT
const nestjs = require('@nestjs/common');
```

### 3. Implement Proper Error Handling

```typescript
// CORRECT
try {
  const result = await this.userService.findOne(id);
  if (!result) {
    throw new NotFoundException(`User with ID ${id} not found`);
  }
  return result;
} catch (error) {
  this.logger.error(`Error finding user: ${error.message}`, error.stack);
  throw error;
}

// INCORRECT
const result = await this.userService.findOne(id);
return result;
```

### 4. Add Appropriate Comments and Documentation

```typescript
/**
 * Retrieves a user by their ID
 * 
 * @param id - The user's unique identifier
 * @returns The user entity if found
 * @throws NotFoundException if the user doesn't exist
 */
async findOne(id: string): Promise<User> {
  // Implementation
}
```

### 5. Consider Performance Implications

- Avoid N+1 query problems
- Use pagination for large data sets
- Consider caching where appropriate

### 6. Use TypeScript Best Practices

- Use proper typing
- Avoid `any` type
- Use interfaces and type definitions
- Leverage TypeScript features

## Service-Specific Guidelines

### User Service

- Always validate user input
- Implement proper password hashing
- Follow the authentication flow
- Use the verification service for email/phone verification

### Store Service

- Validate store creation parameters
- Implement proper permission checks
- Follow the established store management patterns

### Product Service

- Validate product data
- Handle product images properly
- Implement inventory management correctly

### Order Service

- Follow the order state machine
- Implement proper transaction handling
- Validate order items

## Common Patterns

### DTOs (Data Transfer Objects)

Always use DTOs for data validation and transformation:

```typescript
export class CreateUserDto {
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @MinLength(8)
  @MaxLength(100)
  password: string;

  @IsString()
  @IsNotEmpty()
  name: string;
}
```

### Repository Pattern

Follow the repository pattern for database operations:

```typescript
@Injectable()
export class UserRepository {
  constructor(
    @InjectRepository(User)
    private readonly repository: Repository<User>,
  ) {}

  async findOne(id: string): Promise<User> {
    return this.repository.findOne({ where: { id } });
  }
}
```

### Service Pattern

Implement business logic in service classes:

```typescript
@Injectable()
export class UserService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly logger: Logger,
  ) {}

  async findOne(id: string): Promise<User> {
    try {
      const user = await this.userRepository.findOne(id);
      if (!user) {
        throw new NotFoundException(`User with ID ${id} not found`);
      }
      return user;
    } catch (error) {
      this.logger.error(`Error finding user: ${error.message}`, error.stack);
      throw error;
    }
  }
}
```

## Error Handling

Follow the established error handling patterns:

1. Use NestJS exception filters
2. Use custom exception classes
3. Log errors appropriately
4. Provide meaningful error messages
5. Include error codes for frontend handling

## Testing Requirements

AI assistants must write tests for all generated code:

1. Unit tests for services and repositories
2. Integration tests for controllers
3. E2E tests for critical flows
4. Test error handling and edge cases

## Documentation Requirements

AI assistants must document all generated code:

1. Add JSDoc comments to functions and classes
2. Update README.md files
3. Update API documentation
4. Add inline comments for complex logic

## Examples

See the following files for examples of correctly implemented patterns:

- [User Controller Example](./examples/user-controller.md)
- [Error Handling Example](./examples/error-handling.md)
- [Testing Example](./examples/testing.md)
- [Documentation Example](./examples/documentation.md)

By following these guidelines, AI assistants will ensure consistency, quality, and adherence to the established development workflow of the Social Commerce Platform.
