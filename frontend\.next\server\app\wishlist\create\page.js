(()=>{var e={};e.id=7282,e.ids=[7282],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},97469:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c});var r=s(67096),a=s(16132),i=s(37284),n=s.n(i),l=s(32564),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c=["",{children:["wishlist",{children:["create",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,75008)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\wishlist\\create\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9291,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\wishlist\\create\\page.tsx"],u="/wishlist/create/page",m={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/wishlist/create/page",pathname:"/wishlist/create",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},11233:(e,t,s)=>{Promise.resolve().then(s.bind(s,97243))},97243:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>CreateWishlistPage});var r=s(30784),a=s(9885),i=s(57114),n=s(86867),l=s(59872),o=s(706),c=s(29874);function CreateWishlistPage(){let e=(0,i.useRouter)(),[t,{isLoading:s}]=(0,n.lh)(),[d,u]=(0,a.useState)(""),[m,x]=(0,a.useState)(!1),[h,p]=(0,a.useState)(""),handleSubmit=async s=>{if(s.preventDefault(),!d.trim()){p("Please enter a wishlist name");return}try{await t({name:d.trim(),isPublic:m}).unwrap(),e.push("/wishlist")}catch(e){console.error("Failed to create wishlist:",e),p("Failed to create wishlist. Please try again.")}};return r.jsx("div",{className:"min-h-screen p-6",children:(0,r.jsxs)("div",{className:"max-w-md mx-auto",children:[r.jsx("div",{className:"flex justify-between items-center mb-6",children:r.jsx("h1",{className:"text-2xl font-bold",children:"Create New Wishlist"})}),r.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6",children:(0,r.jsxs)("form",{onSubmit:handleSubmit,children:[(0,r.jsxs)("div",{className:"mb-4",children:[r.jsx("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Wishlist Name"}),r.jsx(o.Z,{id:"name",type:"text",value:d,onChange:e=>u(e.target.value),placeholder:"Enter wishlist name",required:!0})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("label",{htmlFor:"isPublic",className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Make this wishlist public"}),r.jsx(c.Z,{id:"isPublic",checked:m,onChange:x})]}),r.jsx("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"Public wishlists can be viewed and shared with others."})]}),h&&r.jsx("div",{className:"mb-4 p-2 bg-red-100 text-red-700 rounded-md text-sm",children:h}),(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[r.jsx(l.Z,{type:"button",variant:"outline",onClick:()=>e.push("/wishlist"),children:"Cancel"}),r.jsx(l.Z,{type:"submit",isLoading:s,children:"Create Wishlist"})]})]})})]})})}},29874:(e,t,s)=>{"use strict";s.d(t,{Z:()=>__WEBPACK_DEFAULT_EXPORT__});var r=s(30784);s(9885);let __WEBPACK_DEFAULT_EXPORT__=({id:e,checked:t,onChange:s,disabled:a=!1,className:i=""})=>r.jsx("button",{id:e,type:"button",role:"switch","aria-checked":t,disabled:a,className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 ${t?"bg-primary-600":"bg-gray-200 dark:bg-gray-700"} ${a?"opacity-50 cursor-not-allowed":"cursor-pointer"} ${i}`,onClick:()=>!a&&s(!t),children:r.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${t?"translate-x-6":"translate-x-1"}`})})},75008:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>i,default:()=>o});var r=s(95153);let a=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\wishlist\create\page.tsx`),{__esModule:i,$$typeof:n}=a,l=a.default,o=l}};var t=require("../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),s=t.X(0,[2103,2765,706],()=>__webpack_exec__(97469));module.exports=s})();