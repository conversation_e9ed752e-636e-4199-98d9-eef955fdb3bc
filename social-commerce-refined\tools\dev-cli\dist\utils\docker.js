"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.dockerCompose = dockerCompose;
exports.startServices = startServices;
exports.stopServices = stopServices;
exports.getServicesStatus = getServicesStatus;
const childProcess = require("child_process");
const chalk = require("chalk");
const paths_1 = require("./paths");
function dockerCompose(args) {
    return new Promise((resolve, reject) => {
        var _a, _b;
        const dockerComposePath = (0, paths_1.getDockerComposePath)();
        const command = `docker-compose -f ${dockerComposePath} ${args.join(' ')}`;
        console.log(chalk.blue(`Running: ${command}`));
        const process = childProcess.exec(command);
        (_a = process.stdout) === null || _a === void 0 ? void 0 : _a.on('data', (data) => {
            console.log(data.toString().trim());
        });
        (_b = process.stderr) === null || _b === void 0 ? void 0 : _b.on('data', (data) => {
            console.error(chalk.red(data.toString().trim()));
        });
        process.on('close', (code) => {
            if (code === 0) {
                resolve();
            }
            else {
                reject(new Error(`Docker Compose command failed with code ${code}`));
            }
        });
    });
}
function startServices(services = []) {
    const args = ['up', '-d'];
    if (services.length > 0) {
        args.push(...services);
    }
    return dockerCompose(args);
}
function stopServices(services = []) {
    const args = ['stop'];
    if (services.length > 0) {
        args.push(...services);
    }
    return dockerCompose(args);
}
function getServicesStatus() {
    return new Promise((resolve, reject) => {
        const dockerComposePath = (0, paths_1.getDockerComposePath)();
        const command = `docker-compose -f ${dockerComposePath} ps`;
        childProcess.exec(command, (error, stdout, stderr) => {
            if (error) {
                reject(error);
                return;
            }
            if (stderr) {
                reject(new Error(stderr));
                return;
            }
            resolve(stdout);
        });
    });
}
//# sourceMappingURL=docker.js.map