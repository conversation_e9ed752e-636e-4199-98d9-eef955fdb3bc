{"version": 3, "file": "install.js", "sourceRoot": "", "sources": ["../../src/commands/install.ts"], "names": [], "mappings": ";;AAMA,wCAwEC;AA7ED,+BAA+B;AAC/B,qCAAqC;AACrC,0CAA+D;AAC/D,sCAAmD;AAEnD,SAAgB,cAAc,CAAC,OAAgB;IAC7C,OAAO;SACJ,OAAO,CAAC,mBAAmB,CAAC;SAC5B,WAAW,CAAC,sBAAsB,CAAC;SACnC,MAAM,CAAC,WAAW,EAAE,uCAAuC,CAAC;SAC5D,MAAM,CAAC,KAAK,EAAE,OAA2B,EAAE,OAA0B,EAAE,EAAE;QACxE,IAAI,CAAC;YAEH,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;gBAC7B,MAAM,QAAQ,GAAG,IAAA,sBAAc,GAAE,CAAC;gBAElC,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC1B,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAAC;oBAC/C,OAAO;gBACT,CAAC;gBAED,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,MAAM,CAAC;oBACpC;wBACE,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,SAAS;wBACf,OAAO,EAAE,wDAAwD;wBACjE,OAAO,EAAE,CAAC,GAAG,QAAQ,EAAE,KAAK,CAAC;qBAC9B;iBACF,CAAC,CAAC;gBAEH,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;oBAC9B,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC;gBACrB,CAAC;qBAAM,CAAC;oBACN,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;gBAC5B,CAAC;YACH,CAAC;YAGD,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;gBAChB,MAAM,QAAQ,GAAG,IAAA,sBAAc,GAAE,CAAC;gBAElC,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC1B,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAAC;oBAC/C,OAAO;gBACT,CAAC;gBAED,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,6CAA6C,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;gBAE/F,KAAK,MAAM,GAAG,IAAI,QAAQ,EAAE,CAAC;oBAC3B,IAAI,CAAC;wBACH,MAAM,IAAA,yBAAmB,EAAC,GAAG,CAAC,CAAC;oBACjC,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,qCAAqC,GAAG,aAAa,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;oBACjG,CAAC;gBACH,CAAC;gBAED,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,sDAAsD,CAAC,CAAC,CAAC;gBACjF,OAAO;YACT,CAAC;YAGD,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,IAAA,qBAAa,EAAC,OAAO,CAAC,EAAE,CAAC;oBAC5B,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,OAAO,yBAAyB,CAAC,CAAC,CAAC;oBACtE,OAAO;gBACT,CAAC;gBAED,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,+BAA+B,OAAO,aAAa,CAAC,CAAC,CAAC;gBAC7E,MAAM,IAAA,yBAAmB,EAAC,OAAO,CAAC,CAAC;gBACnC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,2CAA2C,OAAO,UAAU,CAAC,CAAC,CAAC;gBACvF,OAAO;YACT,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YACpD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;IACH,CAAC,CAAC,CAAC;AACP,CAAC"}