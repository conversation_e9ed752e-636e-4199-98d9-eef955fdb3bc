exports.id=2103,exports.ids=[2103],exports.modules={72670:(B,Z,et)=>{"use strict";B.exports=et(91388)},10813:(B,Z,et)=>{"use strict";et.d(Z,{createApi:()=>em});var er,en=et(31011),eo=et(91388),ea=et(9885),ei=et(8250),__spreadArray=function(B,Z){for(var et=0,er=Z.length,en=B.length;et<er;et++,en++)B[en]=Z[et];return B},es=Object.defineProperty,eu=Object.defineProperties,el=Object.getOwnPropertyDescriptors,ec=Object.getOwnPropertySymbols,ed=Object.prototype.hasOwnProperty,ef=Object.prototype.propertyIsEnumerable,__defNormalProp=function(B,Z,et){return Z in B?es(B,Z,{enumerable:!0,configurable:!0,writable:!0,value:et}):B[Z]=et},__spreadValues=function(B,Z){for(var et in Z||(Z={}))ed.call(Z,et)&&__defNormalProp(B,et,Z[et]);if(ec)for(var er=0,en=ec(Z);er<en.length;er++){var et=en[er];ef.call(Z,et)&&__defNormalProp(B,et,Z[et])}return B},__spreadProps=function(B,Z){return eu(B,el(Z))};function useStableQueryArgs(B,Z,et,er){var en=(0,ea.useMemo)(function(){return{queryArgs:B,serialized:"object"==typeof B?Z({queryArgs:B,endpointDefinition:et,endpointName:er}):B}},[B,Z,et,er]),eo=(0,ea.useRef)(en);return(0,ea.useEffect)(function(){eo.current.serialized!==en.serialized&&(eo.current=en)},[en]),eo.current.serialized===en.serialized?eo.current.queryArgs:B}var ep=Symbol();function useShallowStableValue(B){var Z=(0,ea.useRef)(B);return(0,ea.useEffect)(function(){(0,ei.shallowEqual)(Z.current,B)||(Z.current=B)},[B]),(0,ei.shallowEqual)(Z.current,B)?Z.current:B}var eh=WeakMap?new WeakMap:void 0,defaultSerializeQueryArgs=function(B){var Z=B.endpointName,et=B.queryArgs,er="",en=null==eh?void 0:eh.get(et);if("string"==typeof en)er=en;else{var ea=JSON.stringify(et,function(B,Z){return(0,eo.isPlainObject)(Z)?Object.keys(Z).sort().reduce(function(B,et){return B[et]=Z[et],B},{}):Z});(0,eo.isPlainObject)(et)&&(null==eh||eh.set(et,ea)),er=ea}return Z+"("+er+")"},eg="undefined"!=typeof window&&window.document&&window.document.createElement?ea.useLayoutEffect:ea.useEffect,defaultMutationStateSelector=function(B){return B},noPendingQueryStateSelector=function(B){return B.isUninitialized?__spreadProps(__spreadValues({},B),{isUninitialized:!1,isFetching:!0,isLoading:void 0===B.data,status:en.QueryStatus.pending}):B};function buildHooks(B){var Z=B.api,et=B.moduleOptions,er=et.batch,es=et.useDispatch,eu=et.useSelector,el=et.useStore,ec=et.unstable__sideEffectsInRender,ed=B.serializeQueryArgs,ef=B.context,eh=ec?function(B){return B()}:ea.useEffect;return{buildQueryHooks:buildQueryHooks,buildMutationHook:buildMutationHook,usePrefetch:usePrefetch};function queryStatePreSelector(B,Z,et){if((null==Z?void 0:Z.endpointName)&&B.isUninitialized){var er=Z.endpointName,en=ef.endpointDefinitions[er];ed({queryArgs:Z.originalArgs,endpointDefinition:en,endpointName:er})===ed({queryArgs:et,endpointDefinition:en,endpointName:er})&&(Z=void 0)}var eo=B.isSuccess?B.data:null==Z?void 0:Z.data;void 0===eo&&(eo=B.data);var ea=void 0!==eo,ei=B.isLoading,es=B.isSuccess||ei&&ea;return __spreadProps(__spreadValues({},B),{data:eo,currentData:B.data,isFetching:ei,isLoading:!ea&&ei,isSuccess:es})}function usePrefetch(B,et){var er=es(),en=useShallowStableValue(et);return(0,ea.useCallback)(function(et,eo){return er(Z.util.prefetch(B,et,__spreadValues(__spreadValues({},en),eo)))},[B,er,en])}function buildQueryHooks(B){var useQuerySubscription=function(et,er){var eo=void 0===er?{}:er,ei=eo.refetchOnReconnect,eu=eo.refetchOnFocus,el=eo.refetchOnMountOrArgChange,ec=eo.skip,ed=eo.pollingInterval,ep=Z.endpoints[B].initiate,eg=es(),ey=useStableQueryArgs(void 0!==ec&&ec?en.skipToken:et,defaultSerializeQueryArgs,ef.endpointDefinitions[B],B),em=useShallowStableValue({refetchOnReconnect:ei,refetchOnFocus:eu,pollingInterval:void 0===ed?0:ed}),ev=(0,ea.useRef)(!1),eb=(0,ea.useRef)(),eS=eb.current||{},eP=eS.queryCacheKey,eO=eS.requestId,e_=!1;eP&&eO&&(e_=!!eg(Z.internalActions.internal_probeSubscription({queryCacheKey:eP,requestId:eO})));var ex=!e_&&ev.current;return eh(function(){ev.current=e_}),eh(function(){ex&&(eb.current=void 0)},[ex]),eh(function(){var B,Z=eb.current;if(ey===en.skipToken){null==Z||Z.unsubscribe(),eb.current=void 0;return}var et=null==(B=eb.current)?void 0:B.subscriptionOptions;if(Z&&Z.arg===ey)em!==et&&Z.updateSubscriptionOptions(em);else{null==Z||Z.unsubscribe();var er=eg(ep(ey,{subscriptionOptions:em,forceRefetch:el}));eb.current=er}},[eg,ep,el,ey,em,ex]),(0,ea.useEffect)(function(){return function(){var B;null==(B=eb.current)||B.unsubscribe(),eb.current=void 0}},[]),(0,ea.useMemo)(function(){return{refetch:function(){var B;if(!eb.current)throw Error("Cannot refetch a query that has not been started yet.");return null==(B=eb.current)?void 0:B.refetch()}}},[])},useLazyQuerySubscription=function(et){var en=void 0===et?{}:et,eo=en.refetchOnReconnect,ei=en.refetchOnFocus,eu=en.pollingInterval,el=Z.endpoints[B].initiate,ec=es(),ed=(0,ea.useState)(ep),ef=ed[0],eg=ed[1],ey=(0,ea.useRef)(),em=useShallowStableValue({refetchOnReconnect:eo,refetchOnFocus:ei,pollingInterval:void 0===eu?0:eu});eh(function(){var B,Z;em!==(null==(B=ey.current)?void 0:B.subscriptionOptions)&&(null==(Z=ey.current)||Z.updateSubscriptionOptions(em))},[em]);var ev=(0,ea.useRef)(em);eh(function(){ev.current=em},[em]);var eb=(0,ea.useCallback)(function(B,Z){var et;return void 0===Z&&(Z=!1),er(function(){var er;null==(er=ey.current)||er.unsubscribe(),ey.current=et=ec(el(B,{subscriptionOptions:ev.current,forceRefetch:!Z})),eg(B)}),et},[ec,el]);return(0,ea.useEffect)(function(){return function(){var B;null==(B=null==ey?void 0:ey.current)||B.unsubscribe()}},[]),(0,ea.useEffect)(function(){ef===ep||ey.current||eb(ef,!0)},[ef,eb]),(0,ea.useMemo)(function(){return[eb,ef]},[eb,ef])},useQueryState=function(et,er){var es=void 0===er?{}:er,ec=es.skip,ep=es.selectFromResult,eh=Z.endpoints[B].select,ey=useStableQueryArgs(void 0!==ec&&ec?en.skipToken:et,ed,ef.endpointDefinitions[B],B),em=(0,ea.useRef)(),ev=(0,ea.useMemo)(function(){return(0,eo.createSelector)([eh(ey),function(B,Z){return Z},function(B){return ey}],queryStatePreSelector)},[eh,ey]),eb=(0,ea.useMemo)(function(){return ep?(0,eo.createSelector)([ev],ep):ev},[ev,ep]),eS=eu(function(B){return eb(B,em.current)},ei.shallowEqual),eP=ev(el().getState(),em.current);return eg(function(){em.current=eP},[eP]),eS};return{useQueryState:useQueryState,useQuerySubscription:useQuerySubscription,useLazyQuerySubscription:useLazyQuerySubscription,useLazyQuery:function(B){var Z=useLazyQuerySubscription(B),et=Z[0],er=Z[1],en=useQueryState(er,__spreadProps(__spreadValues({},B),{skip:er===ep})),eo=(0,ea.useMemo)(function(){return{lastArg:er}},[er]);return(0,ea.useMemo)(function(){return[et,en,eo]},[et,en,eo])},useQuery:function(B,Z){var et=useQuerySubscription(B,Z),er=useQueryState(B,__spreadValues({selectFromResult:B===en.skipToken||(null==Z?void 0:Z.skip)?void 0:noPendingQueryStateSelector},Z)),eo=er.data,ei=er.status,es=er.isLoading,eu=er.isSuccess,el=er.isError,ec=er.error;return(0,ea.useDebugValue)({data:eo,status:ei,isLoading:es,isSuccess:eu,isError:el,error:ec}),(0,ea.useMemo)(function(){return __spreadValues(__spreadValues({},er),et)},[er,et])}}}function buildMutationHook(B){return function(et){var en=void 0===et?{}:et,el=en.selectFromResult,ec=void 0===el?defaultMutationStateSelector:el,ed=en.fixedCacheKey,ef=Z.endpoints[B],ep=ef.select,eh=ef.initiate,eg=es(),ey=(0,ea.useState)(),em=ey[0],ev=ey[1];(0,ea.useEffect)(function(){return function(){(null==em?void 0:em.arg.fixedCacheKey)||null==em||em.reset()}},[em]);var eb=(0,ea.useCallback)(function(B){var Z=eg(eh(B,{fixedCacheKey:ed}));return ev(Z),Z},[eg,eh,ed]),eS=(em||{}).requestId,eP=eu((0,ea.useMemo)(function(){return(0,eo.createSelector)([ep({fixedCacheKey:ed,requestId:null==em?void 0:em.requestId})],ec)},[ep,em,ec,ed]),ei.shallowEqual),eO=null==ed?null==em?void 0:em.arg.originalArgs:void 0,e_=(0,ea.useCallback)(function(){er(function(){em&&ev(void 0),ed&&eg(Z.internalActions.removeMutationResult({requestId:eS,fixedCacheKey:ed}))})},[eg,ed,em,eS]),ex=eP.endpointName,eR=eP.data,ew=eP.status,eE=eP.isLoading,ej=eP.isSuccess,eC=eP.isError,eM=eP.error;(0,ea.useDebugValue)({endpointName:ex,data:eR,status:ew,isLoading:eE,isSuccess:ej,isError:eC,error:eM});var eA=(0,ea.useMemo)(function(){return __spreadProps(__spreadValues({},eP),{originalArgs:eO,reset:e_})},[eP,eO,e_]);return(0,ea.useMemo)(function(){return[eb,eA]},[eb,eA])}}}function isQueryDefinition(B){return B.type===er.query}function isMutationDefinition(B){return B.type===er.mutation}function capitalize(B){return B.replace(B[0],B[0].toUpperCase())}function safeAssign(B){for(var Z=[],et=1;et<arguments.length;et++)Z[et-1]=arguments[et];Object.assign.apply(Object,__spreadArray([B],Z))}!function(B){B.query="query",B.mutation="mutation"}(er||(er={}));var ey=Symbol(),em=(0,en.buildCreateApi)((0,en.coreModule)(),function(B){var Z=void 0===B?{}:B,et=Z.batch,er=void 0===et?ei.batch:et,en=Z.useDispatch,eo=void 0===en?ei.useDispatch:en,ea=Z.useSelector,es=void 0===ea?ei.useSelector:ea,eu=Z.useStore,el=void 0===eu?ei.useStore:eu,ec=Z.unstable__sideEffectsInRender,ed=void 0!==ec&&ec;return{name:ey,init:function(B,Z,et){var en=buildHooks({api:B,moduleOptions:{batch:er,useDispatch:eo,useSelector:es,useStore:el,unstable__sideEffectsInRender:ed},serializeQueryArgs:Z.serializeQueryArgs,context:et}),ea=en.buildQueryHooks,ei=en.buildMutationHook;return safeAssign(B,{usePrefetch:en.usePrefetch}),safeAssign(et,{batch:er}),{injectEndpoint:function(Z,et){if(isQueryDefinition(et)){var er=ea(Z),en=er.useQuery,eo=er.useLazyQuery,es=er.useLazyQuerySubscription,eu=er.useQueryState,el=er.useQuerySubscription;safeAssign(B.endpoints[Z],{useQuery:en,useLazyQuery:eo,useLazyQuerySubscription:es,useQueryState:eu,useQuerySubscription:el}),B["use"+capitalize(Z)+"Query"]=en,B["useLazy"+capitalize(Z)+"Query"]=eo}else if(isMutationDefinition(et)){var ec=ei(Z);safeAssign(B.endpoints[Z],{useMutation:ec}),B["use"+capitalize(Z)+"Mutation"]=ec}}}}}}())},31011:function(B,Z,et){var er,en,eo=this&&this.__generator||function(B,Z){var et,er,en,eo,ea={label:0,sent:function(){if(1&en[0])throw en[1];return en[1]},trys:[],ops:[]};return eo={next:o(0),throw:o(1),return:o(2)},"function"==typeof Symbol&&(eo[Symbol.iterator]=function(){return this}),eo;function o(eo){return function(ei){return function(eo){if(et)throw TypeError("Generator is already executing.");for(;ea;)try{if(et=1,er&&(en=2&eo[0]?er.return:eo[0]?er.throw||((en=er.return)&&en.call(er),0):er.next)&&!(en=en.call(er,eo[1])).done)return en;switch(er=0,en&&(eo=[2&eo[0],en.value]),eo[0]){case 0:case 1:en=eo;break;case 4:return ea.label++,{value:eo[1],done:!1};case 5:ea.label++,er=eo[1],eo=[0];continue;case 7:eo=ea.ops.pop(),ea.trys.pop();continue;default:if(!((en=(en=ea.trys).length>0&&en[en.length-1])||6!==eo[0]&&2!==eo[0])){ea=0;continue}if(3===eo[0]&&(!en||eo[1]>en[0]&&eo[1]<en[3])){ea.label=eo[1];break}if(6===eo[0]&&ea.label<en[1]){ea.label=en[1],en=eo;break}if(en&&ea.label<en[2]){ea.label=en[2],ea.ops.push(eo);break}en[2]&&ea.ops.pop(),ea.trys.pop();continue}eo=Z.call(B,ea)}catch(B){eo=[6,B],er=0}finally{et=en=0}if(5&eo[0])throw eo[1];return{value:eo[0]?eo[1]:void 0,done:!0}}([eo,ei])}}},ea=this&&this.__spreadArray||function(B,Z){for(var et=0,er=Z.length,en=B.length;et<er;et++,en++)B[en]=Z[et];return B},ei=Object.create,es=Object.defineProperty,eu=Object.defineProperties,el=Object.getOwnPropertyDescriptor,ec=Object.getOwnPropertyDescriptors,ed=Object.getOwnPropertyNames,ef=Object.getOwnPropertySymbols,ep=Object.getPrototypeOf,eh=Object.prototype.hasOwnProperty,eg=Object.prototype.propertyIsEnumerable,h=function(B,Z,et){return Z in B?es(B,Z,{enumerable:!0,configurable:!0,writable:!0,value:et}):B[Z]=et},v=function(B,Z){for(var et in Z||(Z={}))eh.call(Z,et)&&h(B,et,Z[et]);if(ef)for(var er=0,en=ef(Z);er<en.length;er++)eg.call(Z,et=en[er])&&h(B,et,Z[et]);return B},y=function(B,Z){return eu(B,ec(Z))},m=function(B){return es(B,"__esModule",{value:!0})},g=function(B,Z){var et={};for(var er in B)eh.call(B,er)&&0>Z.indexOf(er)&&(et[er]=B[er]);if(null!=B&&ef)for(var en=0,eo=ef(B);en<eo.length;en++)0>Z.indexOf(er=eo[en])&&eg.call(B,er)&&(et[er]=B[er]);return et},b=function(B){return function(B,Z,et){if(Z&&"object"==typeof Z||"function"==typeof Z)for(var r=function(er){eh.call(B,er)||"default"===er||es(B,er,{get:function(){return Z[er]},enumerable:!(et=el(Z,er))||et.enumerable})},er=0,en=ed(Z);er<en.length;er++)r(en[er]);return B}(m(es(null!=B?ei(ep(B)):{},"default",B&&B.__esModule&&"default"in B?{get:function(){return B.default},enumerable:!0}:{value:B,enumerable:!0})),B)},q=function(B,Z,et){return new Promise(function(er,en){var a=function(B){try{o(et.next(B))}catch(B){en(B)}},u=function(B){try{o(et.throw(B))}catch(B){en(B)}},o=function(B){return B.done?er(B.value):Promise.resolve(B.value).then(a,u)};o((et=et.apply(B,Z)).next())})};m(Z),function(B,Z){for(var et in Z)es(B,et,{get:Z[et],enumerable:!0})}(Z,{QueryStatus:function(){return er},buildCreateApi:function(){return ge},copyWithStructuralSharing:function(){return A},coreModule:function(){return Ke},coreModuleName:function(){return e1},createApi:function(){return e2},defaultSerializeQueryArgs:function(){return ve},fakeBaseQuery:function(){return be},fetchBaseQuery:function(){return x},retry:function(){return eb},setupListeners:function(){return F},skipSelector:function(){return eF},skipToken:function(){return eI}}),(en=er||(er={})).uninitialized="uninitialized",en.pending="pending",en.fulfilled="fulfilled",en.rejected="rejected";var S=function(B){return[].concat.apply([],B)},ey=b(et(72670)).isPlainObject;function A(B,Z){if(B===Z||!(ey(B)&&ey(Z)||Array.isArray(B)&&Array.isArray(Z)))return Z;for(var et=Object.keys(Z),er=Object.keys(B),en=et.length===er.length,eo=Array.isArray(Z)?[]:{},ea=0;ea<et.length;ea++){var ei=et[ea];eo[ei]=A(B[ei],Z[ei]),en&&(en=B[ei]===eo[ei])}return en?B:eo}var em=b(et(72670)),R=function(){for(var B=[],Z=0;Z<arguments.length;Z++)B[Z]=arguments[Z];return fetch.apply(void 0,B)},j=function(B){return B.status>=200&&B.status<=299},w=function(B){return/ion\/(vnd\.api\+)?json/.test(B.get("content-type")||"")};function k(B){if(!(0,em.isPlainObject)(B))return B;for(var Z=v({},B),et=0,er=Object.entries(Z);et<er.length;et++){var en=er[et];void 0===en[1]&&delete Z[en[0]]}return Z}function x(B){var Z=this;void 0===B&&(B={});var et=B.baseUrl,er=B.prepareHeaders,en=void 0===er?function(B){return B}:er,ea=B.fetchFn,ei=void 0===ea?R:ea,es=B.paramsSerializer,eu=B.isJsonContentType,el=void 0===eu?w:eu,ec=B.jsonContentType,ed=void 0===ec?"application/json":ec,ef=B.jsonReplacer,ep=B.timeout,eh=B.responseHandler,eg=B.validateStatus,ey=g(B,["baseUrl","prepareHeaders","fetchFn","paramsSerializer","isJsonContentType","jsonContentType","jsonReplacer","timeout","responseHandler","validateStatus"]);return"undefined"==typeof fetch&&ei===R&&console.warn("Warning: `fetch` is not available. Please supply a custom `fetchFn` property to use `fetchBaseQuery` on SSR environments."),function(B,er){return q(Z,null,function(){var Z,ea,eu,ec,ev,eb,eS,eP,eO,e_,ex,eR,ew,eE,ej,eC,eM,eA,ek,eT,eN,eL,eD,eI,eF,eU,eq,e$,ez,eH,eW,eB,eV,eK,eQ;return eo(this,function(eo){switch(eo.label){case 0:return Z=er.signal,ea=er.getState,eu=er.extra,ec=er.endpoint,ev=er.forced,eb=er.type,eO=(eP="string"==typeof B?{url:B}:B).url,ex=void 0===(e_=eP.headers)?new Headers(ey.headers):e_,ew=void 0===(eR=eP.params)?void 0:eR,ej=void 0===(eE=eP.responseHandler)?null!=eh?eh:"json":eE,eM=void 0===(eC=eP.validateStatus)?null!=eg?eg:j:eC,ek=void 0===(eA=eP.timeout)?ep:eA,eT=g(eP,["url","headers","params","responseHandler","validateStatus","timeout"]),eN=v(y(v({},ey),{signal:Z}),eT),ex=new Headers(k(ex)),eL=eN,[4,en(ex,{getState:ea,extra:eu,endpoint:ec,forced:ev,type:eb})];case 1:eL.headers=eo.sent()||ex,eD=function(B){return"object"==typeof B&&((0,em.isPlainObject)(B)||Array.isArray(B)||"function"==typeof B.toJSON)},!eN.headers.has("content-type")&&eD(eN.body)&&eN.headers.set("content-type",ed),eD(eN.body)&&el(eN.headers)&&(eN.body=JSON.stringify(eN.body,ef)),ew&&(eI=~eO.indexOf("?")?"&":"?",eF=es?es(ew):new URLSearchParams(k(ew)),eO+=eI+eF),eO=function(B,Z){if(!B)return Z;if(!Z)return B;if(RegExp("(^|:)//").test(Z))return Z;var et=B.endsWith("/")||!Z.startsWith("?")?"/":"";return""+(B=B.replace(/\/$/,""))+et+Z.replace(/^\//,"")}(et,eO),eU=new Request(eO,eN),eS={request:new Request(eO,eN)},e$=!1,ez=ek&&setTimeout(function(){e$=!0,er.abort()},ek),eo.label=2;case 2:return eo.trys.push([2,4,5,6]),[4,ei(eU)];case 3:return eq=eo.sent(),[3,6];case 4:return eH=eo.sent(),[2,{error:{status:e$?"TIMEOUT_ERROR":"FETCH_ERROR",error:String(eH)},meta:eS}];case 5:return ez&&clearTimeout(ez),[7];case 6:eW=eq.clone(),eS.response=eW,eV="",eo.label=7;case 7:return eo.trys.push([7,9,,10]),[4,Promise.all([O(eq,ej).then(function(B){return eB=B},function(B){return eK=B}),eW.text().then(function(B){return eV=B},function(){})])];case 8:if(eo.sent(),eK)throw eK;return[3,10];case 9:return eQ=eo.sent(),[2,{error:{status:"PARSING_ERROR",originalStatus:eq.status,data:eV,error:String(eQ)},meta:eS}];case 10:return[2,eM(eq,eB)?{data:eB,meta:eS}:{error:{status:eq.status,data:eB},meta:eS}]}})})};function O(B,Z){return q(this,null,function(){var et;return eo(this,function(er){switch(er.label){case 0:return"function"==typeof Z?[2,Z(B)]:("content-type"===Z&&(Z=el(B.headers)?"json":"text"),"json"!==Z?[3,2]:[4,B.text()]);case 1:return[2,(et=er.sent()).length?JSON.parse(et):null];case 2:return[2,B.text()]}})})}}var P=function(B,Z){void 0===Z&&(Z=void 0),this.value=B,this.meta=Z};function Q(B,Z){return void 0===B&&(B=0),void 0===Z&&(Z=5),q(this,null,function(){var et;return eo(this,function(er){switch(er.label){case 0:return et=~~((Math.random()+.4)*(300<<Math.min(B,Z))),[4,new Promise(function(B){return setTimeout(function(Z){return B(Z)},et)})];case 1:return er.sent(),[2]}})})}var ev={},eb=Object.assign(function(B,Z){return function(et,er,en){return q(void 0,null,function(){var ea,ei,es,eu,el,ec;return eo(this,function(eo){switch(eo.label){case 0:ea=[5,(Z||ev).maxRetries,(en||ev).maxRetries].filter(function(B){return void 0!==B}).slice(-1)[0],ei=function(B,Z,et){return et.attempt<=ea},es=v(v({maxRetries:ea,backoff:Q,retryCondition:ei},Z),en),eu=0,eo.label=1;case 1:eo.label=2;case 2:return eo.trys.push([2,4,,6]),[4,B(et,er,en)];case 3:if((el=eo.sent()).error)throw new P(el);return[2,el];case 4:if(ec=eo.sent(),eu++,ec.throwImmediately){if(ec instanceof P)return[2,ec.value];throw ec}return ec instanceof P&&!es.retryCondition(ec.value.error,et,{attempt:eu,baseQueryApi:er,extraOptions:en})?[2,ec.value]:[4,es.backoff(eu,es.maxRetries)];case 5:return eo.sent(),[3,6];case 6:return[3,1];case 7:return[2]}})})}},{fail:function(B){throw Object.assign(new P({error:B}),{throwImmediately:!0})}}),eS=b(et(72670)),eP=(0,eS.createAction)("__rtkq/focused"),eO=(0,eS.createAction)("__rtkq/unfocused"),e_=(0,eS.createAction)("__rtkq/online"),ex=(0,eS.createAction)("__rtkq/offline"),eR=!1;function F(B,Z){var et,er,en,eo;return Z?Z(B,{onFocus:eP,onFocusLost:eO,onOffline:ex,onOnline:e_}):(et=function(){return B(eP())},er=function(){return B(e_())},en=function(){return B(ex())},eo=function(){"visible"===window.document.visibilityState?et():B(eO())},eR||"undefined"!=typeof window&&window.addEventListener&&(window.addEventListener("visibilitychange",eo,!1),window.addEventListener("focus",et,!1),window.addEventListener("online",er,!1),window.addEventListener("offline",en,!1),eR=!0),function(){window.removeEventListener("focus",et),window.removeEventListener("visibilitychange",eo),window.removeEventListener("online",er),window.removeEventListener("offline",en),eR=!1})}var ew,eE,ej=b(et(72670));function L(B){return B.type===ew.query}function W(B,Z,et,er,en,eo){return"function"==typeof B?B(Z,et,er,en).map(H).map(eo):Array.isArray(B)?B.map(H).map(eo):[]}function H(B){return"string"==typeof B?{type:B}:B}(eE=ew||(ew={})).query="query",eE.mutation="mutation";var eC=b(et(72670));function V(B){return null!=B}var eM=Symbol("forceQueryFn"),Y=function(B){return"function"==typeof B[eM]},eA=b(et(72670)),ek=b(et(53241)),eT=b(et(72670));function ee(B){return B}function te(B,Z,et,er){return W(et[B.meta.arg.endpointName][Z],(0,eA.isFulfilled)(B)?B.payload:void 0,(0,eA.isRejectedWithValue)(B)?B.payload:void 0,B.meta.arg.originalArgs,"baseQueryMeta"in B.meta?B.meta.baseQueryMeta:void 0,er)}var eN=b(et(53241)),eL=b(et(53241));function ie(B,Z,et){var er=B[Z];er&&et(er)}function ae(B){var Z;return null!=(Z="arg"in B?B.arg.fixedCacheKey:B.fixedCacheKey)?Z:B.requestId}function ue(B,Z,et){var er=B[ae(Z)];er&&et(er)}var eD={},eI=Symbol.for("RTKQ/skipToken"),eF=eI,eU={status:er.uninitialized},eq=(0,ej.createNextState)(eU,function(){}),e$=(0,ej.createNextState)(eU,function(){}),ez=b(et(72670)),eH=WeakMap?new WeakMap:void 0,ve=function(B){var Z=B.endpointName,et=B.queryArgs,er="",en=null==eH?void 0:eH.get(et);if("string"==typeof en)er=en;else{var eo=JSON.stringify(et,function(B,Z){return(0,ez.isPlainObject)(Z)?Object.keys(Z).sort().reduce(function(B,et){return B[et]=Z[et],B},{}):Z});(0,ez.isPlainObject)(et)&&(null==eH||eH.set(et,eo)),er=eo}return Z+"("+er+")"},eW=b(et(72670)),eB=b(et(49531));function ge(){for(var B=[],Z=0;Z<arguments.length;Z++)B[Z]=arguments[Z];return function(Z){var et=(0,eB.defaultMemoize)(function(B){var et,er;return null==(er=Z.extractRehydrationInfo)?void 0:er.call(Z,B,{reducerPath:null!=(et=Z.reducerPath)?et:"api"})}),er=y(v({reducerPath:"api",keepUnusedDataFor:60,refetchOnMountOrArgChange:!1,refetchOnFocus:!1,refetchOnReconnect:!1},Z),{extractRehydrationInfo:et,serializeQueryArgs:function(B){var et=ve;if("serializeQueryArgs"in B.endpointDefinition){var er=B.endpointDefinition.serializeQueryArgs;et=function(B){var Z=er(B);return"string"==typeof Z?Z:ve(y(v({},B),{queryArgs:Z}))}}else Z.serializeQueryArgs&&(et=Z.serializeQueryArgs);return et(B)},tagTypes:ea([],Z.tagTypes||[])}),en={endpointDefinitions:{},batch:function(B){B()},apiUid:(0,eW.nanoid)(),extractRehydrationInfo:et,hasRehydrationInfo:(0,eB.defaultMemoize)(function(B){return null!=et(B)})},eo={injectEndpoints:function(B){for(var Z=B.endpoints({query:function(B){return y(v({},B),{type:ew.query})},mutation:function(B){return y(v({},B),{type:ew.mutation})}}),et=0,er=Object.entries(Z);et<er.length;et++){var ea=er[et],es=ea[0],eu=ea[1];if(B.overrideExisting||!(es in en.endpointDefinitions)){en.endpointDefinitions[es]=eu;for(var el=0;el<ei.length;el++)ei[el].injectEndpoint(es,eu)}}return eo},enhanceEndpoints:function(B){var Z=B.addTagTypes,et=B.endpoints;if(Z)for(var ea=0;ea<Z.length;ea++){var ei=Z[ea];er.tagTypes.includes(ei)||er.tagTypes.push(ei)}if(et)for(var es=0,eu=Object.entries(et);es<eu.length;es++){var el=eu[es],ec=el[0],ed=el[1];"function"==typeof ed?ed(en.endpointDefinitions[ec]):Object.assign(en.endpointDefinitions[ec]||{},ed)}return eo}},ei=B.map(function(B){return B.init(eo,er,en)});return eo.injectEndpoints({endpoints:Z.endpoints})}}function be(){return function(){throw Error("When using `fakeBaseQuery`, all queries & mutations must use the `queryFn` definition syntax.")}}var eV,eK=b(et(72670)),Oe=function(B){var Z=B.reducerPath,et=B.api,er=B.context,en=B.internalState,eo=et.internalActions,ea=eo.removeQueryResult,ei=eo.unsubscribeQueryResult;function s(B){var Z=en.currentSubscriptions[B];return!!Z&&!function(B){for(var Z in B)return!1;return!0}(Z)}var es={};function l(B,Z,et,en){var eo,ei=er.endpointDefinitions[Z],eu=null!=(eo=null==ei?void 0:ei.keepUnusedDataFor)?eo:en.keepUnusedDataFor;if(1/0!==eu&&!s(B)){var el=es[B];el&&clearTimeout(el),es[B]=setTimeout(function(){s(B)||et.dispatch(ea({queryCacheKey:B})),delete es[B]},1e3*Math.max(0,Math.min(eu,2147482.647)))}}return function(B,en,eo){var ea;if(ei.match(B)){var eu=en.getState()[Z];l(em=B.payload.queryCacheKey,null==(ea=eu.queries[em])?void 0:ea.endpointName,en,eu.config)}if(et.util.resetApiState.match(B))for(var el=0,ec=Object.entries(es);el<ec.length;el++){var ed=ec[el],ef=ed[0],ep=ed[1];ep&&clearTimeout(ep),delete es[ef]}if(er.hasRehydrationInfo(B)){eu=en.getState()[Z];for(var eh=er.extractRehydrationInfo(B).queries,eg=0,ey=Object.entries(eh);eg<ey.length;eg++){var em,ev=ey[eg],eb=ev[1];l(em=ev[0],null==eb?void 0:eb.endpointName,en,eu.config)}}}},eQ=b(et(72670)),Te=function(B){var Z=B.reducerPath,et=B.context,en=B.context.endpointDefinitions,eo=B.mutationThunk,ea=B.api,ei=B.assertTagType,es=B.refetchQuery,eu=ea.internalActions.removeQueryResult,el=(0,eQ.isAnyOf)((0,eQ.isFulfilled)(eo),(0,eQ.isRejectedWithValue)(eo));function d(B,en){var eo=en.getState(),ei=eo[Z],el=ea.util.selectInvalidatedBy(eo,B);et.batch(function(){for(var B,Z=0,et=Array.from(el.values());Z<et.length;Z++){var eo=et[Z].queryCacheKey,ea=ei.queries[eo],ec=null!=(B=ei.subscriptions[eo])?B:{};ea&&(0===Object.keys(ec).length?en.dispatch(eu({queryCacheKey:eo})):ea.status!==er.uninitialized&&en.dispatch(es(ea,eo)))}})}return function(B,Z){el(B)&&d(te(B,"invalidatesTags",en,ei),Z),ea.util.invalidateTags.match(B)&&d(W(B.payload,void 0,void 0,void 0,void 0,ei),Z)}},Re=function(B){var Z=B.reducerPath,et=B.queryThunk,en=B.api,eo=B.refetchQuery,ea=B.internalState,ei={};function s(B,et){var en=B.queryCacheKey,es=et.getState()[Z].queries[en];if(es&&es.status!==er.uninitialized){var eu=d(ea.currentSubscriptions[en]);if(Number.isFinite(eu)){var el=ei[en];(null==el?void 0:el.timeout)&&(clearTimeout(el.timeout),el.timeout=void 0);var ec=Date.now()+eu,ed=ei[en]={nextPollTimestamp:ec,pollingInterval:eu,timeout:setTimeout(function(){ed.timeout=void 0,et.dispatch(eo(es,en))},eu)}}}}function c(B,et){var en=B.queryCacheKey,eo=et.getState()[Z].queries[en];if(eo&&eo.status!==er.uninitialized){var es=d(ea.currentSubscriptions[en]);if(Number.isFinite(es)){var eu=ei[en],el=Date.now()+es;(!eu||el<eu.nextPollTimestamp)&&s({queryCacheKey:en},et)}else l(en)}}function l(B){var Z=ei[B];(null==Z?void 0:Z.timeout)&&clearTimeout(Z.timeout),delete ei[B]}function d(B){void 0===B&&(B={});var Z=Number.POSITIVE_INFINITY;for(var et in B)B[et].pollingInterval&&(Z=Math.min(B[et].pollingInterval,Z));return Z}return function(B,Z){(en.internalActions.updateSubscriptionOptions.match(B)||en.internalActions.unsubscribeQueryResult.match(B))&&c(B.payload,Z),(et.pending.match(B)||et.rejected.match(B)&&B.meta.condition)&&c(B.meta.arg,Z),(et.fulfilled.match(B)||et.rejected.match(B)&&!B.meta.condition)&&s(B.meta.arg,Z),en.util.resetApiState.match(B)&&function(){for(var B=0,Z=Object.keys(ei);B<Z.length;B++)l(Z[B])}()}},eG=b(et(72670)),eY=Error("Promise never resolved before cacheEntryRemoved."),ke=function(B){var Z=B.api,et=B.reducerPath,er=B.context,en=B.queryThunk,eo=B.mutationThunk,ea=(0,eG.isAsyncThunkAction)(en),ei=(0,eG.isAsyncThunkAction)(eo),es=(0,eG.isFulfilled)(en,eo),eu={};function l(B,et,en,eo,ea){var ei=er.endpointDefinitions[B],es=null==ei?void 0:ei.onCacheEntryAdded;if(es){var el={},ec=new Promise(function(B){el.cacheEntryRemoved=B}),ed=Promise.race([new Promise(function(B){el.valueResolved=B}),ec.then(function(){throw eY})]);ed.catch(function(){}),eu[en]=el;var ef=Z.endpoints[B].select(ei.type===ew.query?et:en),ep=eo.dispatch(function(B,Z,et){return et}),eh=y(v({},eo),{getCacheEntry:function(){return ef(eo.getState())},requestId:ea,extra:ep,updateCachedData:ei.type===ew.query?function(er){return eo.dispatch(Z.util.updateQueryData(B,et,er))}:void 0,cacheDataLoaded:ed,cacheEntryRemoved:ec});Promise.resolve(es(et,eh)).catch(function(B){if(B!==eY)throw B})}}return function(B,er,el){var ec=ea(B)?B.meta.arg.queryCacheKey:ei(B)?B.meta.requestId:Z.internalActions.removeQueryResult.match(B)?B.payload.queryCacheKey:Z.internalActions.removeMutationResult.match(B)?ae(B.payload):"";if(en.pending.match(B)){var ed=el[et].queries[ec],ef=er.getState()[et].queries[ec];!ed&&ef&&l(B.meta.arg.endpointName,B.meta.arg.originalArgs,ec,er,B.meta.requestId)}else if(eo.pending.match(B))(ef=er.getState()[et].mutations[ec])&&l(B.meta.arg.endpointName,B.meta.arg.originalArgs,ec,er,B.meta.requestId);else if(es(B))(null==(ey=eu[ec])?void 0:ey.valueResolved)&&(ey.valueResolved({data:B.payload,meta:B.meta.baseQueryMeta}),delete ey.valueResolved);else if(Z.internalActions.removeQueryResult.match(B)||Z.internalActions.removeMutationResult.match(B))(ey=eu[ec])&&(delete eu[ec],ey.cacheEntryRemoved());else if(Z.util.resetApiState.match(B))for(var ep=0,eh=Object.entries(eu);ep<eh.length;ep++){var eg=eh[ep],ey=eg[1];delete eu[eg[0]],ey.cacheEntryRemoved()}}},eX=b(et(72670)),Pe=function(B){var Z=B.api,et=B.context,er=B.queryThunk,en=B.mutationThunk,eo=(0,eX.isPending)(er,en),ea=(0,eX.isRejected)(er,en),ei=(0,eX.isFulfilled)(er,en),es={};return function(B,er){var en,eu,el;if(eo(B)){var ec=B.meta,ed=ec.requestId,ef=ec.arg,ep=ef.endpointName,eh=ef.originalArgs,eg=et.endpointDefinitions[ep],ey=null==eg?void 0:eg.onQueryStarted;if(ey){var em={},ev=new Promise(function(B,Z){em.resolve=B,em.reject=Z});ev.catch(function(){}),es[ed]=em;var eb=Z.endpoints[ep].select(eg.type===ew.query?eh:ed),eS=er.dispatch(function(B,Z,et){return et}),eP=y(v({},er),{getCacheEntry:function(){return eb(er.getState())},requestId:ed,extra:eS,updateCachedData:eg.type===ew.query?function(B){return er.dispatch(Z.util.updateQueryData(ep,eh,B))}:void 0,queryFulfilled:ev});ey(eh,eP)}}else if(ei(B)){var eO=B.meta,e_=eO.baseQueryMeta;null==(en=es[ed=eO.requestId])||en.resolve({data:B.payload,meta:e_}),delete es[ed]}else if(ea(B)){var ex=B.meta;e_=ex.baseQueryMeta,null==(el=es[ed=ex.requestId])||el.reject({error:null!=(eu=B.payload)?eu:B.error,isUnhandledError:!ex.rejectedWithValue,meta:e_}),delete es[ed]}}},Qe=function(B){var Z=B.api,et=B.context.apiUid;return function(B,er){Z.util.resetApiState.match(B)&&er.dispatch(Z.internalActions.middlewareRegistered(et))}},eJ=b(et(53241)),eZ="function"==typeof queueMicrotask?queueMicrotask.bind("undefined"!=typeof window?window:"undefined"!=typeof global?global:globalThis):function(B){return(eV||(eV=Promise.resolve())).then(B).catch(function(B){return setTimeout(function(){throw B},0)})};function Me(B){for(var Z=[],et=1;et<arguments.length;et++)Z[et-1]=arguments[et];Object.assign.apply(Object,ea([B],Z))}var e0=b(et(53241)),e1=Symbol(),Ke=function(){return{name:e1,init:function(B,Z,et){var en,ei,es,eu,el,ec,ed,ef,ep,eh,eg,ey,em,ev,eb,eS,eR,eE,eF,eU,ez,eH,eW,eB,eV,eQ,eG,eY,eX,e2,e8,e3=Z.baseQuery,e4=Z.reducerPath,e5=Z.serializeQueryArgs,e6=Z.keepUnusedDataFor,e9=Z.refetchOnMountOrArgChange,e7=Z.refetchOnFocus,tt=Z.refetchOnReconnect;(0,e0.enablePatches)();var p=function(B){return B};Object.assign(B,{reducerPath:e4,endpoints:{},internalActions:{onOnline:e_,onOffline:ex,onFocus:eP,onFocusLost:eO},util:{}});var tr=function(B){var Z=this,et=B.reducerPath,en=B.baseQuery,ea=B.context.endpointDefinitions,ei=B.serializeQueryArgs,es=B.api,eu=B.assertTagType,l=function(B,et){return q(Z,[B,et],function(B,Z){var et,er,ei,es,eu,el,ec,ed,ef,ep,eh,eg=Z.signal,ey=Z.abort,em=Z.rejectWithValue,ev=Z.fulfillWithValue,eb=Z.dispatch,eS=Z.getState,eP=Z.extra;return eo(this,function(Z){switch(Z.label){case 0:et=ea[B.endpointName],Z.label=1;case 1:return Z.trys.push([1,8,,13]),er=ee,ei=void 0,es={signal:eg,abort:ey,dispatch:eb,getState:eS,extra:eP,endpoint:B.endpointName,type:B.type,forced:"query"===B.type?d(B,eS()):void 0},(eu="query"===B.type?B[eM]:void 0)?(ei=eu(),[3,6]):[3,2];case 2:return et.query?[4,en(et.query(B.originalArgs),es,et.extraOptions)]:[3,4];case 3:return ei=Z.sent(),et.transformResponse&&(er=et.transformResponse),[3,6];case 4:return[4,et.queryFn(B.originalArgs,es,et.extraOptions,function(B){return en(B,es,et.extraOptions)})];case 5:ei=Z.sent(),Z.label=6;case 6:if(ei.error)throw new P(ei.error,ei.meta);return el=ev,[4,er(ei.data,ei.meta,B.originalArgs)];case 7:return[2,el.apply(void 0,[Z.sent(),((ep={fulfilledTimeStamp:Date.now(),baseQueryMeta:ei.meta})[eT.SHOULD_AUTOBATCH]=!0,ep)])];case 8:if(!((ec=Z.sent())instanceof P))return[3,12];ed=ee,et.query&&et.transformErrorResponse&&(ed=et.transformErrorResponse),Z.label=9;case 9:return Z.trys.push([9,11,,12]),ef=em,[4,ed(ec.value,ec.meta,B.originalArgs)];case 10:return[2,ef.apply(void 0,[Z.sent(),((eh={baseQueryMeta:ec.meta})[eT.SHOULD_AUTOBATCH]=!0,eh)])];case 11:return ec=Z.sent(),[3,12];case 12:throw console.error(ec),ec;case 13:return[2]}})})};function d(B,Z){var er,en,eo,ea,ei=null==(en=null==(er=Z[et])?void 0:er.queries)?void 0:en[B.queryCacheKey],es=null==(eo=Z[et])?void 0:eo.config.refetchOnMountOrArgChange,eu=null==ei?void 0:ei.fulfilledTimeStamp,el=null!=(ea=B.forceRefetch)?ea:B.subscribe&&es;return!!el&&(!0===el||(Number(new Date)-Number(eu))/1e3>=el)}function h(B){return function(Z){var et,er;return(null==(er=null==(et=null==Z?void 0:Z.meta)?void 0:et.arg)?void 0:er.endpointName)===B}}return{queryThunk:(0,eT.createAsyncThunk)(et+"/executeQuery",l,{getPendingMeta:function(){var B;return(B={startedTimeStamp:Date.now()})[eT.SHOULD_AUTOBATCH]=!0,B},condition:function(B,Z){var er,en,eo,ei=(0,Z.getState)(),es=null==(en=null==(er=ei[et])?void 0:er.queries)?void 0:en[B.queryCacheKey],eu=null==es?void 0:es.fulfilledTimeStamp,el=B.originalArgs,ec=null==es?void 0:es.originalArgs,ed=ea[B.endpointName];return!(!Y(B)&&("pending"===(null==es?void 0:es.status)||!d(B,ei)&&(!L(ed)||!(null==(eo=null==ed?void 0:ed.forceRefetch)?void 0:eo.call(ed,{currentArg:el,previousArg:ec,endpointState:es,state:ei})))&&eu))},dispatchConditionRejection:!0}),mutationThunk:(0,eT.createAsyncThunk)(et+"/executeMutation",l,{getPendingMeta:function(){var B;return(B={startedTimeStamp:Date.now()})[eT.SHOULD_AUTOBATCH]=!0,B}}),prefetch:function(B,Z,et){return function(er,en){var eo="force"in et&&et.force,ea="ifOlderThan"in et&&et.ifOlderThan,o=function(et){return void 0===et&&(et=!0),es.endpoints[B].initiate(Z,{forceRefetch:et})},ei=es.endpoints[B].select(Z)(en());if(eo)er(o());else if(ea){var eu=null==ei?void 0:ei.fulfilledTimeStamp;if(!eu)return void er(o());(Number(new Date)-Number(new Date(eu)))/1e3>=ea&&er(o())}else er(o(!1))}},updateQueryData:function(B,Z,et,en){return void 0===en&&(en=!0),function(eo,ea){var ei,eu,el,ec=es.endpoints[B].select(Z)(ea()),ed={patches:[],inversePatches:[],undo:function(){return eo(es.util.patchQueryData(B,Z,ed.inversePatches,en))}};if(ec.status===er.uninitialized)return ed;if("data"in ec){if((0,ek.isDraftable)(ec.data)){var ef=(0,ek.produceWithPatches)(ec.data,et),ep=ef[0],eh=ef[2];(ei=ed.patches).push.apply(ei,ef[1]),(eu=ed.inversePatches).push.apply(eu,eh),el=ep}else el=et(ec.data),ed.patches.push({op:"replace",path:[],value:el}),ed.inversePatches.push({op:"replace",path:[],value:ec.data})}return eo(es.util.patchQueryData(B,Z,ed.patches,en)),ed}},upsertQueryData:function(B,Z,et){return function(er){var en;return er(es.endpoints[B].initiate(Z,((en={subscribe:!1,forceRefetch:!0})[eM]=function(){return{data:et}},en)))}},patchQueryData:function(B,Z,et,er){return function(en,eo){var el=ea[B],ec=ei({queryArgs:Z,endpointDefinition:el,endpointName:B});if(en(es.internalActions.queryResultPatched({queryCacheKey:ec,patches:et})),er){var ed=es.endpoints[B].select(Z)(eo()),ef=W(el.providesTags,ed.data,void 0,Z,{},eu);en(es.internalActions.updateProvidedBy({queryCacheKey:ec,providedTags:ef}))}}},buildMatchThunkActions:function(B,Z){return{matchPending:(0,eA.isAllOf)((0,eA.isPending)(B),h(Z)),matchFulfilled:(0,eA.isAllOf)((0,eA.isFulfilled)(B),h(Z)),matchRejected:(0,eA.isAllOf)((0,eA.isRejected)(B),h(Z))}}}}({baseQuery:e3,reducerPath:e4,context:et,api:B,serializeQueryArgs:e5,assertTagType:p}),tn=tr.queryThunk,to=tr.mutationThunk,ta=tr.patchQueryData,ti=tr.updateQueryData,ts=tr.upsertQueryData,tu=tr.prefetch,tl=tr.buildMatchThunkActions,tc=(ei=(en={context:et,queryThunk:tn,mutationThunk:to,reducerPath:e4,assertTagType:p,config:{refetchOnFocus:e7,refetchOnReconnect:tt,refetchOnMountOrArgChange:e9,keepUnusedDataFor:e6,reducerPath:e4}}).reducerPath,es=en.queryThunk,eu=en.mutationThunk,ec=(el=en.context).endpointDefinitions,ed=el.apiUid,ef=el.extractRehydrationInfo,ep=el.hasRehydrationInfo,eh=en.assertTagType,eg=en.config,ey=(0,eC.createAction)(ei+"/resetApiState"),em=(0,eC.createSlice)({name:ei+"/queries",initialState:eD,reducers:{removeQueryResult:{reducer:function(B,Z){delete B[Z.payload.queryCacheKey]},prepare:(0,eC.prepareAutoBatched)()},queryResultPatched:{reducer:function(B,Z){var et=Z.payload,er=et.patches;ie(B,et.queryCacheKey,function(B){B.data=(0,eL.applyPatches)(B.data,er.concat())})},prepare:(0,eC.prepareAutoBatched)()}},extraReducers:function(B){B.addCase(es.pending,function(B,Z){var et,en=Z.meta,eo=Z.meta.arg,ea=Y(eo);(eo.subscribe||ea)&&(null!=B[et=eo.queryCacheKey]||(B[et]={status:er.uninitialized,endpointName:eo.endpointName})),ie(B,eo.queryCacheKey,function(B){B.status=er.pending,B.requestId=ea&&B.requestId?B.requestId:en.requestId,void 0!==eo.originalArgs&&(B.originalArgs=eo.originalArgs),B.startedTimeStamp=en.startedTimeStamp})}).addCase(es.fulfilled,function(B,Z){var et=Z.meta,en=Z.payload;ie(B,et.arg.queryCacheKey,function(B){var Z;if(B.requestId===et.requestId||Y(et.arg)){var eo=ec[et.arg.endpointName].merge;if(B.status=er.fulfilled,eo){if(void 0!==B.data){var ea=et.fulfilledTimeStamp,ei=et.arg,es=et.baseQueryMeta,eu=et.requestId,el=(0,eC.createNextState)(B.data,function(B){return eo(B,en,{arg:ei.originalArgs,baseQueryMeta:es,fulfilledTimeStamp:ea,requestId:eu})});B.data=el}else B.data=en}else B.data=null==(Z=ec[et.arg.endpointName].structuralSharing)||Z?A((0,eN.isDraft)(B.data)?(0,eL.original)(B.data):B.data,en):en;delete B.error,B.fulfilledTimeStamp=et.fulfilledTimeStamp}})}).addCase(es.rejected,function(B,Z){var et=Z.meta,en=et.condition,eo=et.requestId,ea=Z.error,ei=Z.payload;ie(B,et.arg.queryCacheKey,function(B){if(en);else{if(B.requestId!==eo)return;B.status=er.rejected,B.error=null!=ei?ei:ea}})}).addMatcher(ep,function(B,Z){for(var et=ef(Z).queries,en=0,eo=Object.entries(et);en<eo.length;en++){var ea=eo[en],ei=ea[1];(null==ei?void 0:ei.status)!==er.fulfilled&&(null==ei?void 0:ei.status)!==er.rejected||(B[ea[0]]=ei)}})}}),ev=(0,eC.createSlice)({name:ei+"/mutations",initialState:eD,reducers:{removeMutationResult:{reducer:function(B,Z){var et=ae(Z.payload);et in B&&delete B[et]},prepare:(0,eC.prepareAutoBatched)()}},extraReducers:function(B){B.addCase(eu.pending,function(B,Z){var et=Z.meta,en=et.requestId,eo=et.arg,ea=et.startedTimeStamp;eo.track&&(B[ae(Z.meta)]={requestId:en,status:er.pending,endpointName:eo.endpointName,startedTimeStamp:ea})}).addCase(eu.fulfilled,function(B,Z){var et=Z.payload,en=Z.meta;en.arg.track&&ue(B,en,function(B){B.requestId===en.requestId&&(B.status=er.fulfilled,B.data=et,B.fulfilledTimeStamp=en.fulfilledTimeStamp)})}).addCase(eu.rejected,function(B,Z){var et=Z.payload,en=Z.error,eo=Z.meta;eo.arg.track&&ue(B,eo,function(B){B.requestId===eo.requestId&&(B.status=er.rejected,B.error=null!=et?et:en)})}).addMatcher(ep,function(B,Z){for(var et=ef(Z).mutations,en=0,eo=Object.entries(et);en<eo.length;en++){var ea=eo[en],ei=ea[0],es=ea[1];(null==es?void 0:es.status)!==er.fulfilled&&(null==es?void 0:es.status)!==er.rejected||ei===(null==es?void 0:es.requestId)||(B[ei]=es)}})}}),eb=(0,eC.createSlice)({name:ei+"/invalidation",initialState:eD,reducers:{updateProvidedBy:{reducer:function(B,Z){for(var et,er,en,eo,ea=Z.payload,ei=ea.queryCacheKey,es=ea.providedTags,eu=0,el=Object.values(B);eu<el.length;eu++)for(var ec=0,ed=Object.values(el[eu]);ec<ed.length;ec++){var ef=ed[ec],ep=ef.indexOf(ei);-1!==ep&&ef.splice(ep,1)}for(var eh=0;eh<es.length;eh++){var eg=es[eh],ey=eg.type,em=eg.id,ev=null!=(eo=(er=null!=(et=B[ey])?et:B[ey]={})[en=em||"__internal_without_id"])?eo:er[en]=[];ev.includes(ei)||ev.push(ei)}},prepare:(0,eC.prepareAutoBatched)()}},extraReducers:function(B){B.addCase(em.actions.removeQueryResult,function(B,Z){for(var et=Z.payload.queryCacheKey,er=0,en=Object.values(B);er<en.length;er++)for(var eo=0,ea=Object.values(en[er]);eo<ea.length;eo++){var ei=ea[eo],es=ei.indexOf(et);-1!==es&&ei.splice(es,1)}}).addMatcher(ep,function(B,Z){for(var et,er,en,eo,ea=ef(Z).provided,ei=0,es=Object.entries(ea);ei<es.length;ei++)for(var eu=es[ei],el=eu[0],ec=0,ed=Object.entries(eu[1]);ec<ed.length;ec++)for(var ep=ed[ec],eh=ep[0],eg=ep[1],ey=null!=(eo=(er=null!=(et=B[el])?et:B[el]={})[en=eh||"__internal_without_id"])?eo:er[en]=[],em=0;em<eg.length;em++){var ev=eg[em];ey.includes(ev)||ey.push(ev)}}).addMatcher((0,eC.isAnyOf)((0,eC.isFulfilled)(es),(0,eC.isRejectedWithValue)(es)),function(B,Z){var et=te(Z,"providesTags",ec,eh);eb.caseReducers.updateProvidedBy(B,eb.actions.updateProvidedBy({queryCacheKey:Z.meta.arg.queryCacheKey,providedTags:et}))})}}),eS=(0,eC.createSlice)({name:ei+"/subscriptions",initialState:eD,reducers:{updateSubscriptionOptions:function(B,Z){},unsubscribeQueryResult:function(B,Z){},internal_probeSubscription:function(B,Z){}}}),eR=(0,eC.createSlice)({name:ei+"/internalSubscriptions",initialState:eD,reducers:{subscriptionsUpdated:{reducer:function(B,Z){return(0,eL.applyPatches)(B,Z.payload)},prepare:(0,eC.prepareAutoBatched)()}}}),eE=(0,eC.createSlice)({name:ei+"/config",initialState:v({online:"undefined"==typeof navigator||void 0===navigator.onLine||navigator.onLine,focused:"undefined"==typeof document||"hidden"!==document.visibilityState,middlewareRegistered:!1},eg),reducers:{middlewareRegistered:function(B,Z){B.middlewareRegistered="conflict"!==B.middlewareRegistered&&ed===Z.payload||"conflict"}},extraReducers:function(B){B.addCase(e_,function(B){B.online=!0}).addCase(ex,function(B){B.online=!1}).addCase(eP,function(B){B.focused=!0}).addCase(eO,function(B){B.focused=!1}).addMatcher(ep,function(B){return v({},B)})}}),eF=(0,eC.combineReducers)({queries:em.reducer,mutations:ev.reducer,provided:eb.reducer,subscriptions:eR.reducer,config:eE.reducer}),{reducer:function(B,Z){return eF(ey.match(Z)?void 0:B,Z)},actions:y(v(v(v(v(v(v({},eE.actions),em.actions),eS.actions),eR.actions),ev.actions),eb.actions),{unsubscribeMutationResult:ev.actions.removeMutationResult,resetApiState:ey})}),td=tc.reducer,tf=tc.actions;Me(B.util,{patchQueryData:ta,updateQueryData:ti,upsertQueryData:ts,prefetch:tu,resetApiState:tf.resetApiState}),Me(B.internalActions,tf);var tp=function(B){var Z=B.reducerPath,et=B.queryThunk,en=B.api,eo=B.context,ea=eo.apiUid,ei={invalidateTags:(0,eK.createAction)(Z+"/invalidateTags")},es=[Qe,Oe,Te,Re,ke,Pe];return{middleware:function(et){var ei,eu,el,ec,ed,ef,ep,eh,eg,ey=!1,em=y(v({},B),{internalState:{currentSubscriptions:{}},refetchQuery:c}),ev=es.map(function(B){return B(em)}),eb=(ei=em.api,eu=em.queryThunk,el=em.internalState,ec=ei.reducerPath+"/subscriptions",ed=null,ef=!1,eh=(ep=ei.internalActions).updateSubscriptionOptions,eg=ep.unsubscribeQueryResult,function(B,Z){if(ed||(ed=JSON.parse(JSON.stringify(el.currentSubscriptions))),ei.util.resetApiState.match(B))return ed=el.currentSubscriptions={},[!0,!1];if(ei.internalActions.internal_probeSubscription.match(B)){var et,er,en=B.payload;return[!1,!!(null==(et=el.currentSubscriptions[en.queryCacheKey])?void 0:et[en.requestId])]}if(function(B,Z){var et,er,en,eo,ea,es,el,ec,ed;if(eh.match(Z)){var ef=Z.payload,ep=ef.queryCacheKey,ey=ef.requestId;return(null==(et=null==B?void 0:B[ep])?void 0:et[ey])&&(B[ep][ey]=ef.options),!0}if(eg.match(Z)){var em=Z.payload;return ey=em.requestId,B[ep=em.queryCacheKey]&&delete B[ep][ey],!0}if(ei.internalActions.removeQueryResult.match(Z))return delete B[Z.payload.queryCacheKey],!0;if(eu.pending.match(Z)){var ev=Z.meta;if(ey=ev.requestId,(eP=ev.arg).subscribe)return(eb=null!=(en=B[er=eP.queryCacheKey])?en:B[er]={})[ey]=null!=(ea=null!=(eo=eP.subscriptionOptions)?eo:eb[ey])?ea:{},!0}if(eu.rejected.match(Z)){var eb,eS=Z.meta,eP=eS.arg;if(ey=eS.requestId,eS.condition&&eP.subscribe)return(eb=null!=(el=B[es=eP.queryCacheKey])?el:B[es]={})[ey]=null!=(ed=null!=(ec=eP.subscriptionOptions)?ec:eb[ey])?ed:{},!0}return!1}(el.currentSubscriptions,B)){ef||(eZ(function(){var B=JSON.parse(JSON.stringify(el.currentSubscriptions)),et=(0,eJ.produceWithPatches)(ed,function(){return B});Z.next(ei.internalActions.subscriptionsUpdated(et[1])),ed=B,ef=!1}),ef=!0);var eo=!!(null==(er=B.type)?void 0:er.startsWith(ec)),ea=eu.rejected.match(B)&&B.meta.condition&&!!B.meta.arg.subscribe;return[!eo&&!ea,!1]}return[!0,!1]}),eS=function(B){var Z=B.reducerPath,et=B.context,en=B.refetchQuery,eo=B.internalState,ea=B.api.internalActions.removeQueryResult;function o(B,ei){var es=B.getState()[Z],eu=es.queries,el=eo.currentSubscriptions;et.batch(function(){for(var Z=0,et=Object.keys(el);Z<et.length;Z++){var eo=et[Z],ec=eu[eo],ed=el[eo];ed&&ec&&(Object.values(ed).some(function(B){return!0===B[ei]})||Object.values(ed).every(function(B){return void 0===B[ei]})&&es.config[ei])&&(0===Object.keys(ed).length?B.dispatch(ea({queryCacheKey:eo})):ec.status!==er.uninitialized&&B.dispatch(en(ec,eo)))}})}return function(B,Z){eP.match(B)&&o(Z,"refetchOnFocus"),e_.match(B)&&o(Z,"refetchOnReconnect")}}(em);return function(B){return function(er){ey||(ey=!0,et.dispatch(en.internalActions.middlewareRegistered(ea)));var ei,es=y(v({},et),{next:B}),eu=et.getState(),el=eb(er,es,eu),ec=el[1];if(ei=el[0]?B(er):ec,et.getState()[Z]&&(eS(er,es,eu),er&&"string"==typeof er.type&&er.type.startsWith(Z+"/")||eo.hasRehydrationInfo(er)))for(var ed=0;ed<ev.length;ed++)(0,ev[ed])(er,es,eu);return ei}}},actions:ei};function c(B,Z,er){return void 0===er&&(er={}),et(v({type:"query",endpointName:B.endpointName,originalArgs:B.originalArgs,subscribe:!1,forceRefetch:!0,queryCacheKey:Z},er))}}({reducerPath:e4,context:et,queryThunk:tn,mutationThunk:to,api:B,assertTagType:p}),th=tp.middleware;Me(B.util,tp.actions),Me(B,{reducer:td,middleware:th});var tg=function(B){var Z=B.serializeQueryArgs,et=B.reducerPath,i=function(B){return eq},a=function(B){return e$};return{buildQuerySelector:function(B,er){return function(en){var eo=Z({queryArgs:en,endpointDefinition:er,endpointName:B});return(0,ej.createSelector)(en===eI?i:function(B){var Z,er,en;return null!=(en=null==(er=null==(Z=B[et])?void 0:Z.queries)?void 0:er[eo])?en:eq},u)}},buildMutationSelector:function(){return function(B){var Z,er;return er="object"==typeof B?null!=(Z=ae(B))?Z:eI:B,(0,ej.createSelector)(er===eI?a:function(B){var Z,en,eo;return null!=(eo=null==(en=null==(Z=B[et])?void 0:Z.mutations)?void 0:en[er])?eo:e$},u)}},selectInvalidatedBy:function(B,Z){for(var er,en=B[et],eo=new Set,ea=0,ei=Z.map(H);ea<ei.length;ea++){var es=ei[ea],eu=en.provided[es.type];if(eu)for(var el=0,ec=null!=(er=void 0!==es.id?eu[es.id]:S(Object.values(eu)))?er:[];el<ec.length;el++)eo.add(ec[el])}return S(Array.from(eo.values()).map(function(B){var Z=en.queries[B];return Z?[{queryCacheKey:B,endpointName:Z.endpointName,originalArgs:Z.originalArgs}]:[]}))}};function u(B){var Z;return v(v({},B),{status:Z=B.status,isUninitialized:Z===er.uninitialized,isLoading:Z===er.pending,isSuccess:Z===er.fulfilled,isError:Z===er.rejected})}}({serializeQueryArgs:e5,reducerPath:e4}),ty=tg.buildQuerySelector,tm=tg.buildMutationSelector;Me(B.util,{selectInvalidatedBy:tg.selectInvalidatedBy});var tv=(ez=(eU={queryThunk:tn,mutationThunk:to,api:B,serializeQueryArgs:e5,context:et}).serializeQueryArgs,eH=eU.queryThunk,eW=eU.mutationThunk,eB=eU.api,eV=eU.context,eQ=new Map,eG=new Map,eX=(eY=eB.internalActions).unsubscribeQueryResult,e2=eY.removeMutationResult,e8=eY.updateSubscriptionOptions,{buildInitiateQuery:function(B,Z){var a=function(et,er){var en=void 0===er?{}:er,ea=en.subscribe,ei=void 0===ea||ea,es=en.forceRefetch,eu=en.subscriptionOptions,el=en[eM];return function(er,en){var ea,ec,ed=ez({queryArgs:et,endpointDefinition:Z,endpointName:B}),ef=eH(((ea={type:"query",subscribe:ei,forceRefetch:es,subscriptionOptions:eu,endpointName:B,originalArgs:et,queryCacheKey:ed})[eM]=el,ea)),ep=eB.endpoints[B].select(et),eh=er(ef),eg=ep(en()),ey=eh.requestId,em=eh.abort,ev=eg.requestId!==ey,eb=null==(ec=eQ.get(er))?void 0:ec[ed],x=function(){return ep(en())},eS=Object.assign(el?eh.then(x):ev&&!eb?Promise.resolve(eg):Promise.all([eb,eh]).then(x),{arg:et,requestId:ey,subscriptionOptions:eu,queryCacheKey:ed,abort:em,unwrap:function(){return q(this,null,function(){var B;return eo(this,function(Z){switch(Z.label){case 0:return[4,eS];case 1:if((B=Z.sent()).isError)throw B.error;return[2,B.data]}})})},refetch:function(){return er(a(et,{subscribe:!1,forceRefetch:!0}))},unsubscribe:function(){ei&&er(eX({queryCacheKey:ed,requestId:ey}))},updateSubscriptionOptions:function(Z){eS.subscriptionOptions=Z,er(e8({endpointName:B,requestId:ey,queryCacheKey:ed,options:Z}))}});if(!eb&&!ev&&!el){var eP=eQ.get(er)||{};eP[ed]=eS,eQ.set(er,eP),eS.then(function(){delete eP[ed],Object.keys(eP).length||eQ.delete(er)})}return eS}};return a},buildInitiateMutation:function(B){return function(Z,et){var er=void 0===et?{}:et,en=er.track,eo=void 0===en||en,ea=er.fixedCacheKey;return function(et,er){var en=et(eW({type:"mutation",endpointName:B,originalArgs:Z,track:eo,fixedCacheKey:ea})),ei=en.requestId,es=en.abort,eu=en.unwrap,el=en.unwrap().then(function(B){return{data:B}}).catch(function(B){return{error:B}}),v=function(){et(e2({requestId:ei,fixedCacheKey:ea}))},ec=Object.assign(el,{arg:en.arg,requestId:ei,abort:es,unwrap:eu,unsubscribe:v,reset:v}),ed=eG.get(et)||{};return eG.set(et,ed),ed[ei]=ec,ec.then(function(){delete ed[ei],Object.keys(ed).length||eG.delete(et)}),ea&&(ed[ea]=ec,ec.then(function(){ed[ea]===ec&&(delete ed[ea],Object.keys(ed).length||eG.delete(et))})),ec}}},getRunningQueryThunk:function(B,Z){return function(et){var er,en=ez({queryArgs:Z,endpointDefinition:eV.endpointDefinitions[B],endpointName:B});return null==(er=eQ.get(et))?void 0:er[en]}},getRunningMutationThunk:function(B,Z){return function(B){var et;return null==(et=eG.get(B))?void 0:et[Z]}},getRunningQueriesThunk:function(){return function(B){return Object.values(eQ.get(B)||{}).filter(V)}},getRunningMutationsThunk:function(){return function(B){return Object.values(eG.get(B)||{}).filter(V)}},getRunningOperationPromises:function(){var e=function(B){return Array.from(B.values()).flatMap(function(B){return B?Object.values(B):[]})};return ea(ea([],e(eQ)),e(eG)).filter(V)},removalWarning:function(){throw Error("This method had to be removed due to a conceptual bug in RTK.\n       Please see https://github.com/reduxjs/redux-toolkit/pull/2481 for details.\n       See https://redux-toolkit.js.org/rtk-query/usage/server-side-rendering for new guidance on SSR.")}}),tb=tv.buildInitiateQuery,tS=tv.buildInitiateMutation;return Me(B.util,{getRunningOperationPromises:tv.getRunningOperationPromises,getRunningOperationPromise:tv.removalWarning,getRunningMutationThunk:tv.getRunningMutationThunk,getRunningMutationsThunk:tv.getRunningMutationsThunk,getRunningQueryThunk:tv.getRunningQueryThunk,getRunningQueriesThunk:tv.getRunningQueriesThunk}),{name:e1,injectEndpoint:function(Z,et){var er;null!=(er=B.endpoints)[Z]||(er[Z]={}),L(et)?Me(B.endpoints[Z],{name:Z,select:ty(Z,et),initiate:tb(Z,et)},tl(tn,Z)):et.type===ew.mutation&&Me(B.endpoints[Z],{name:Z,select:tm(),initiate:tS(Z)},tl(to,Z))}}}}},e2=ge(Ke())},91388:function(B,Z,et){var er,en=this&&this.__extends||(er=function(B,Z){return(er=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(B,Z){B.__proto__=Z}||function(B,Z){for(var et in Z)Object.prototype.hasOwnProperty.call(Z,et)&&(B[et]=Z[et])})(B,Z)},function(B,Z){if("function"!=typeof Z&&null!==Z)throw TypeError("Class extends value "+String(Z)+" is not a constructor or null");function r(){this.constructor=B}er(B,Z),B.prototype=null===Z?Object.create(Z):(r.prototype=Z.prototype,new r)}),eo=this&&this.__generator||function(B,Z){var et,er,en,eo,ea={label:0,sent:function(){if(1&en[0])throw en[1];return en[1]},trys:[],ops:[]};return eo={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(eo[Symbol.iterator]=function(){return this}),eo;function a(eo){return function(ei){return function(eo){if(et)throw TypeError("Generator is already executing.");for(;ea;)try{if(et=1,er&&(en=2&eo[0]?er.return:eo[0]?er.throw||((en=er.return)&&en.call(er),0):er.next)&&!(en=en.call(er,eo[1])).done)return en;switch(er=0,en&&(eo=[2&eo[0],en.value]),eo[0]){case 0:case 1:en=eo;break;case 4:return ea.label++,{value:eo[1],done:!1};case 5:ea.label++,er=eo[1],eo=[0];continue;case 7:eo=ea.ops.pop(),ea.trys.pop();continue;default:if(!((en=(en=ea.trys).length>0&&en[en.length-1])||6!==eo[0]&&2!==eo[0])){ea=0;continue}if(3===eo[0]&&(!en||eo[1]>en[0]&&eo[1]<en[3])){ea.label=eo[1];break}if(6===eo[0]&&ea.label<en[1]){ea.label=en[1],en=eo;break}if(en&&ea.label<en[2]){ea.label=en[2],ea.ops.push(eo);break}en[2]&&ea.ops.pop(),ea.trys.pop();continue}eo=Z.call(B,ea)}catch(B){eo=[6,B],er=0}finally{et=en=0}if(5&eo[0])throw eo[1];return{value:eo[0]?eo[1]:void 0,done:!0}}([eo,ei])}}},ea=this&&this.__spreadArray||function(B,Z){for(var et=0,er=Z.length,en=B.length;et<er;et++,en++)B[en]=Z[et];return B},ei=Object.create,es=Object.defineProperty,eu=Object.defineProperties,el=Object.getOwnPropertyDescriptor,ec=Object.getOwnPropertyDescriptors,ed=Object.getOwnPropertyNames,ef=Object.getOwnPropertySymbols,ep=Object.getPrototypeOf,eh=Object.prototype.hasOwnProperty,eg=Object.prototype.propertyIsEnumerable,v=function(B,Z,et){return Z in B?es(B,Z,{enumerable:!0,configurable:!0,writable:!0,value:et}):B[Z]=et},y=function(B,Z){for(var et in Z||(Z={}))eh.call(Z,et)&&v(B,et,Z[et]);if(ef)for(var er=0,en=ef(Z);er<en.length;er++)eg.call(Z,et=en[er])&&v(B,et,Z[et]);return B},h=function(B,Z){return eu(B,ec(Z))},g=function(B){return es(B,"__esModule",{value:!0})},b=function(B,Z,et){if(Z&&"object"==typeof Z||"function"==typeof Z)for(var r=function(er){eh.call(B,er)||"default"===er||es(B,er,{get:function(){return Z[er]},enumerable:!(et=el(Z,er))||et.enumerable})},er=0,en=ed(Z);er<en.length;er++)r(en[er]);return B},m=function(B){return b(g(es(null!=B?ei(ep(B)):{},"default",B&&B.__esModule&&"default"in B?{get:function(){return B.default},enumerable:!0}:{value:B,enumerable:!0})),B)},w=function(B,Z,et){return new Promise(function(er,en){var o=function(B){try{a(et.next(B))}catch(B){en(B)}},u=function(B){try{a(et.throw(B))}catch(B){en(B)}},a=function(B){return B.done?er(B.value):Promise.resolve(B.value).then(o,u)};a((et=et.apply(B,Z)).next())})};g(Z),function(B,Z){for(var et in Z)es(B,et,{get:Z[et],enumerable:!0})}(Z,{EnhancerArray:function(){return eE},MiddlewareArray:function(){return ew},SHOULD_AUTOBATCH:function(){return eU},TaskAbortError:function(){return ke},addListener:function(){return eL},autoBatchEnhancer:function(){return $e},clearAllListeners:function(){return eD},configureStore:function(){return Y},createAction:function(){return T},createActionCreatorInvariantMiddleware:function(){return N},createAsyncThunk:function(){return eA},createDraftSafeSelector:function(){return P},createEntityAdapter:function(){return ce},createImmutableStateInvariantMiddleware:function(){return X},createListenerMiddleware:function(){return Ge},createNextState:function(){return em.default},createReducer:function(){return ee},createSelector:function(){return ev.createSelector},createSerializableStateInvariantMiddleware:function(){return K},createSlice:function(){return ne},current:function(){return em.current},findNonSerializableValue:function(){return H},freeze:function(){return em.freeze},getDefaultMiddleware:function(){return Q},getType:function(){return z},isAction:function(){return C},isActionCreator:function(){return D},isAllOf:function(){return be},isAnyOf:function(){return ge},isAsyncThunkAction:function(){return Se},isDraft:function(){return em.isDraft},isFluxStandardAction:function(){return L},isFulfilled:function(){return Ee},isImmutableDefault:function(){return W},isPending:function(){return Oe},isPlain:function(){return G},isPlainObject:function(){return M},isRejected:function(){return je},isRejectedWithValue:function(){return Ae},miniSerializeError:function(){return pe},nanoid:function(){return fe},original:function(){return em.original},prepareAutoBatched:function(){return Ke},removeListener:function(){return eI},unwrapResult:function(){return ye}});var ey=m(et(53241));b(Z,m(et(58131)));var em=m(et(53241)),ev=m(et(49531)),eb=m(et(53241)),eS=m(et(49531)),P=function(){for(var B=[],Z=0;Z<arguments.length;Z++)B[Z]=arguments[Z];var et=eS.createSelector.apply(void 0,B);return function(B){for(var Z=[],er=1;er<arguments.length;er++)Z[er-1]=arguments[er];return et.apply(void 0,ea([(0,eb.isDraft)(B)?(0,eb.current)(B):B],Z))}},eP=m(et(58131)),eO=m(et(58131)),e_="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!=arguments.length)return"object"==typeof arguments[0]?eO.compose:eO.compose.apply(null,arguments)};function M(B){if("object"!=typeof B||null===B)return!1;var Z=Object.getPrototypeOf(B);if(null===Z)return!0;for(var et=Z;null!==Object.getPrototypeOf(et);)et=Object.getPrototypeOf(et);return Z===et}"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window;var ex=m(et(21890)),I=function(B){return B&&"function"==typeof B.match};function T(B,Z){function t(){for(var et=[],er=0;er<arguments.length;er++)et[er]=arguments[er];if(Z){var en=Z.apply(void 0,et);if(!en)throw Error("prepareAction did not return an object");return y(y({type:B,payload:en.payload},"meta"in en&&{meta:en.meta}),"error"in en&&{error:en.error})}return{type:B,payload:et[0]}}return t.toString=function(){return""+B},t.type=B,t.match=function(Z){return Z.type===B},t}function C(B){return M(B)&&"type"in B}function D(B){return"function"==typeof B&&"type"in B&&I(B)}function L(B){return C(B)&&"string"==typeof B.type&&Object.keys(B).every(R)}function R(B){return["type","payload","error","meta"].indexOf(B)>-1}function z(B){return""+B}function N(B){return void 0===B&&(B={}),function(){return function(B){return function(Z){return B(Z)}}}}var eR=m(et(53241)),ew=function(B){function t(){for(var Z=[],et=0;et<arguments.length;et++)Z[et]=arguments[et];var er=B.apply(this,Z)||this;return Object.setPrototypeOf(er,t.prototype),er}return en(t,B),Object.defineProperty(t,Symbol.species,{get:function(){return t},enumerable:!1,configurable:!0}),t.prototype.concat=function(){for(var Z=[],et=0;et<arguments.length;et++)Z[et]=arguments[et];return B.prototype.concat.apply(this,Z)},t.prototype.prepend=function(){for(var B=[],Z=0;Z<arguments.length;Z++)B[Z]=arguments[Z];return 1===B.length&&Array.isArray(B[0])?new(t.bind.apply(t,ea([void 0],B[0].concat(this)))):new(t.bind.apply(t,ea([void 0],B.concat(this))))},t}(Array),eE=function(B){function t(){for(var Z=[],et=0;et<arguments.length;et++)Z[et]=arguments[et];var er=B.apply(this,Z)||this;return Object.setPrototypeOf(er,t.prototype),er}return en(t,B),Object.defineProperty(t,Symbol.species,{get:function(){return t},enumerable:!1,configurable:!0}),t.prototype.concat=function(){for(var Z=[],et=0;et<arguments.length;et++)Z[et]=arguments[et];return B.prototype.concat.apply(this,Z)},t.prototype.prepend=function(){for(var B=[],Z=0;Z<arguments.length;Z++)B[Z]=arguments[Z];return 1===B.length&&Array.isArray(B[0])?new(t.bind.apply(t,ea([void 0],B[0].concat(this)))):new(t.bind.apply(t,ea([void 0],B.concat(this))))},t}(Array);function U(B){return(0,eR.isDraftable)(B)?(0,eR.default)(B,function(){}):B}function W(B){return"object"!=typeof B||null==B||Object.isFrozen(B)}function X(B){return void 0===B&&(B={}),function(){return function(B){return function(Z){return B(Z)}}}}function G(B){var Z=typeof B;return null==B||"string"===Z||"boolean"===Z||"number"===Z||Array.isArray(B)||M(B)}function H(B,Z,et,er,en,eo){var ea;if(void 0===Z&&(Z=""),void 0===et&&(et=G),void 0===en&&(en=[]),!et(B))return{keyPath:Z||"<root>",value:B};if("object"!=typeof B||null===B||(null==eo?void 0:eo.has(B)))return!1;for(var ei=null!=er?er(B):Object.entries(B),es=en.length>0,eu=0;eu<ei.length;eu++){var el=ei[eu],ec=function(B,ei){var eu=Z?Z+"."+B:B;return es&&en.some(function(B){return B instanceof RegExp?B.test(eu):eu===B})?"continue":et(ei)?"object"==typeof ei&&(ea=H(ei,eu,et,er,en,eo))?{value:ea}:void 0:{value:{keyPath:eu,value:ei}}}(el[0],el[1]);if("object"==typeof ec)return ec.value}return eo&&J(B)&&eo.add(B),!1}function J(B){if(!Object.isFrozen(B))return!1;for(var Z=0,et=Object.values(B);Z<et.length;Z++){var er=et[Z];if("object"==typeof er&&null!==er&&!J(er))return!1}return!0}function K(B){return void 0===B&&(B={}),function(){return function(B){return function(Z){return B(Z)}}}}function Q(B){void 0===B&&(B={});var Z=B.thunk,et=void 0===Z||Z,er=new ew;return et&&er.push("boolean"==typeof et?ex.default:ex.default.withExtraArgument(et.extraArgument)),er}function Y(B){var Z,t=function(B){return Q(B)},et=B||{},er=et.reducer,en=void 0===er?void 0:er,eo=et.middleware,ei=void 0===eo?t():eo,es=et.devTools,eu=void 0===es||es,el=et.preloadedState,ec=void 0===el?void 0:el,ed=et.enhancers,ef=void 0===ed?void 0:ed;if("function"==typeof en)Z=en;else{if(!M(en))throw Error('"reducer" is a required argument, and must be a function or an object of functions that can be passed to combineReducers');Z=(0,eP.combineReducers)(en)}var ep=ei;"function"==typeof ep&&(ep=ep(t));var eh=eP.applyMiddleware.apply(void 0,ep),eg=eP.compose;eu&&(eg=e_(y({trace:!1},"object"==typeof eu&&eu)));var ey=new eE(eh),em=ey;Array.isArray(ef)?em=ea([eh],ef):"function"==typeof ef&&(em=ef(ey));var ev=eg.apply(void 0,em);return(0,eP.createStore)(Z,ec,ev)}var ej=m(et(53241));function $(B){var Z,et={},er=[],en={addCase:function(B,Z){var er="string"==typeof B?B:B.type;if(!er)throw Error("`builder.addCase` cannot be called with an empty action type");if(er in et)throw Error("`builder.addCase` cannot be called with two reducers for the same action type");return et[er]=Z,en},addMatcher:function(B,Z){return er.push({matcher:B,reducer:Z}),en},addDefaultCase:function(B){return Z=B,en}};return B(en),[et,er,Z]}function ee(B,Z,et,er){void 0===et&&(et=[]);var en,eo="function"==typeof Z?$(Z):[Z,et,er],ei=eo[0],es=eo[1],eu=eo[2];if("function"==typeof B)en=function(){return U(B())};else{var el=U(B);en=function(){return el}}function s(B,Z){void 0===B&&(B=en());var et=ea([ei[Z.type]],es.filter(function(B){return(0,B.matcher)(Z)}).map(function(B){return B.reducer}));return 0===et.filter(function(B){return!!B}).length&&(et=[eu]),et.reduce(function(B,et){if(et){var er;if((0,ej.isDraft)(B))return void 0===(er=et(B,Z))?B:er;if((0,ej.isDraftable)(B))return(0,ej.default)(B,function(B){return et(B,Z)});if(void 0===(er=et(B,Z))){if(null===B)return B;throw Error("A case reducer on a non-draftable value must not return undefined")}return er}return B},B)}return s.getInitialState=en,s}function ne(B){var Z=B.name;if(!Z)throw Error("`name` is a required option for createSlice");var et,er="function"==typeof B.initialState?B.initialState:U(B.initialState),en=B.reducers||{},eo=Object.keys(en),ea={},ei={},es={};function f(){var Z="function"==typeof B.extraReducers?$(B.extraReducers):[B.extraReducers],et=Z[0],en=Z[1],eo=void 0===en?[]:en,ea=Z[2],es=void 0===ea?void 0:ea,eu=y(y({},void 0===et?{}:et),ei);return ee(er,function(B){for(var Z in eu)B.addCase(Z,eu[Z]);for(var et=0;et<eo.length;et++){var er=eo[et];B.addMatcher(er.matcher,er.reducer)}es&&B.addDefaultCase(es)})}return eo.forEach(function(B){var et,er,eo=en[B],eu=Z+"/"+B;"reducer"in eo?(et=eo.reducer,er=eo.prepare):et=eo,ea[B]=et,ei[eu]=et,es[B]=er?T(eu,er):T(eu)}),{name:Z,reducer:function(B,Z){return et||(et=f()),et(B,Z)},actions:es,caseReducers:ea,getInitialState:function(){return et||(et=f()),et.getInitialState()}}}var eC=m(et(53241));function re(B){return function(Z,et){var r=function(Z){L(et)?B(et.payload,Z):B(et,Z)};return(0,eC.isDraft)(Z)?(r(Z),Z):(0,eC.default)(Z,r)}}function oe(B){return Array.isArray(B)||(B=Object.values(B)),B}function ue(B,Z,et){for(var er=[],en=[],eo=0,ea=B=oe(B);eo<ea.length;eo++){var ei=ea[eo],es=Z(ei);es in et.entities?en.push({id:es,changes:ei}):er.push(ei)}return[er,en]}function ae(B){var Z,et;function n(Z,et){var er=B(Z);er in et.entities||(et.ids.push(er),et.entities[er]=Z)}function t(B,Z){for(var et=0,er=B=oe(B);et<er.length;et++)n(er[et],Z)}function r(Z,et){var er=B(Z);er in et.entities||et.ids.push(er),et.entities[er]=Z}function i(B,Z){var et=!1;B.forEach(function(B){B in Z.entities&&(delete Z.entities[B],et=!0)}),et&&(Z.ids=Z.ids.filter(function(B){return B in Z.entities}))}function o(Z,et){var er={},en={};Z.forEach(function(B){B.id in et.entities&&(en[B.id]={id:B.id,changes:y(y({},en[B.id]?en[B.id].changes:null),B.changes)})}),(Z=Object.values(en)).length>0&&Z.filter(function(Z){var en,eo,ea;return(ea=(eo=B(en=Object.assign({},et.entities[Z.id],Z.changes)))!==Z.id)&&(er[Z.id]=eo,delete et.entities[Z.id]),et.entities[eo]=en,ea}).length>0&&(et.ids=Object.keys(et.entities))}function u(Z,et){var er=ue(Z,B,et),en=er[0];o(er[1],et),t(en,et)}return{removeAll:(Z=function(B){Object.assign(B,{ids:[],entities:{}})},et=re(function(B,et){return Z(et)}),function(B){return et(B,void 0)}),addOne:re(n),addMany:re(t),setOne:re(r),setMany:re(function(B,Z){for(var et=0,er=B=oe(B);et<er.length;et++)r(er[et],Z)}),setAll:re(function(B,Z){B=oe(B),Z.ids=[],Z.entities={},t(B,Z)}),updateOne:re(function(B,Z){return o([B],Z)}),updateMany:re(o),upsertOne:re(function(B,Z){return u([B],Z)}),upsertMany:re(u),removeOne:re(function(B,Z){return i([B],Z)}),removeMany:re(i)}}function ce(B){void 0===B&&(B={});var Z=y({sortComparer:!1,selectId:function(B){return B.id}},B),et=Z.selectId,er=Z.sortComparer,en=er?function(B,Z){var et=ae(B);function r(Z,et){var er=(Z=oe(Z)).filter(function(Z){return!(B(Z) in et.entities)});0!==er.length&&a(er,et)}function i(B,Z){0!==(B=oe(B)).length&&a(B,Z)}function o(Z,et){for(var er=!1,en=0;en<Z.length;en++){var eo=Z[en],ea=et.entities[eo.id];if(ea){er=!0,Object.assign(ea,eo.changes);var ei=B(ea);eo.id!==ei&&(delete et.entities[eo.id],et.entities[ei]=ea)}}er&&c(et)}function u(Z,et){var er=ue(Z,B,et),en=er[0];o(er[1],et),r(en,et)}function a(Z,et){Z.forEach(function(Z){et.entities[B(Z)]=Z}),c(et)}function c(et){var er=Object.values(et.entities);er.sort(Z);var en=er.map(B);(function(B,Z){if(B.length!==Z.length)return!1;for(var et=0;et<B.length&&et<Z.length;et++)if(B[et]!==Z[et])return!1;return!0})(et.ids,en)||(et.ids=en)}return{removeOne:et.removeOne,removeMany:et.removeMany,removeAll:et.removeAll,addOne:re(function(B,Z){return r([B],Z)}),updateOne:re(function(B,Z){return o([B],Z)}),upsertOne:re(function(B,Z){return u([B],Z)}),setOne:re(function(B,Z){return i([B],Z)}),setMany:re(i),setAll:re(function(B,Z){B=oe(B),Z.entities={},Z.ids=[],r(B,Z)}),addMany:re(r),updateMany:re(o),upsertMany:re(u)}}(et,er):ae(et);return y(y(y({selectId:et,sortComparer:er},{getInitialState:function(B){return void 0===B&&(B={}),Object.assign({ids:[],entities:{}},B)}}),{getSelectors:function(B){var n=function(B){return B.ids},t=function(B){return B.entities},Z=P(n,t,function(B,Z){return B.map(function(B){return Z[B]})}),i=function(B,Z){return Z},o=function(B,Z){return B[Z]},et=P(n,function(B){return B.length});if(!B)return{selectIds:n,selectEntities:t,selectAll:Z,selectTotal:et,selectById:P(t,i,o)};var er=P(B,t);return{selectIds:P(B,n),selectEntities:er,selectAll:P(B,Z),selectTotal:P(B,et),selectById:P(er,i,o)}}}),en)}var fe=function(B){void 0===B&&(B=21);for(var Z="",et=B;et--;)Z+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return Z},eM=["name","message","stack","code"],se=function(B,Z){this.payload=B,this.meta=Z},de=function(B,Z){this.payload=B,this.meta=Z},pe=function(B){if("object"==typeof B&&null!==B){for(var Z={},et=0;et<eM.length;et++){var er=eM[et];"string"==typeof B[er]&&(Z[er]=B[er])}return Z}return{message:String(B)}},eA=function(){function e(B,Z,et){var er=T(B+"/fulfilled",function(B,Z,et,er){return{payload:B,meta:h(y({},er||{}),{arg:et,requestId:Z,requestStatus:"fulfilled"})}}),en=T(B+"/pending",function(B,Z,et){return{payload:void 0,meta:h(y({},et||{}),{arg:Z,requestId:B,requestStatus:"pending"})}}),ea=T(B+"/rejected",function(B,Z,er,en,eo){return{payload:en,error:(et&&et.serializeError||pe)(B||"Rejected"),meta:h(y({},eo||{}),{arg:er,requestId:Z,rejectedWithValue:!!en,requestStatus:"rejected",aborted:"AbortError"===(null==B?void 0:B.name),condition:"ConditionError"===(null==B?void 0:B.name)})}}),ei="undefined"!=typeof AbortController?AbortController:function(){function e(){this.signal={aborted:!1,addEventListener:function(){},dispatchEvent:function(){return!1},onabort:function(){},removeEventListener:function(){},reason:void 0,throwIfAborted:function(){}}}return e.prototype.abort=function(){},e}();return Object.assign(function(B){return function(es,eu,el){var ec,ed=(null==et?void 0:et.idGenerator)?et.idGenerator(B):fe(),ef=new ei;function v(B){ec=B,ef.abort()}var ep=function(){return w(this,null,function(){var ei,ep,eh,eg,ey,em;return eo(this,function(eo){var ev;switch(eo.label){case 0:return eo.trys.push([0,4,,5]),null===(ev=eg=null==(ei=null==et?void 0:et.condition)?void 0:ei.call(et,B,{getState:eu,extra:el}))||"object"!=typeof ev||"function"!=typeof ev.then?[3,2]:[4,eg];case 1:eg=eo.sent(),eo.label=2;case 2:if(!1===eg||ef.signal.aborted)throw{name:"ConditionError",message:"Aborted due to condition callback returning false."};return ey=new Promise(function(B,Z){return ef.signal.addEventListener("abort",function(){return Z({name:"AbortError",message:ec||"Aborted"})})}),es(en(ed,B,null==(ep=null==et?void 0:et.getPendingMeta)?void 0:ep.call(et,{requestId:ed,arg:B},{getState:eu,extra:el}))),[4,Promise.race([ey,Promise.resolve(Z(B,{dispatch:es,getState:eu,extra:el,requestId:ed,signal:ef.signal,abort:v,rejectWithValue:function(B,Z){return new se(B,Z)},fulfillWithValue:function(B,Z){return new de(B,Z)}})).then(function(Z){if(Z instanceof se)throw Z;return Z instanceof de?er(Z.payload,ed,B,Z.meta):er(Z,ed,B)})])];case 3:return eh=eo.sent(),[3,5];case 4:return eh=(em=eo.sent())instanceof se?ea(null,ed,B,em.payload,em.meta):ea(em,ed,B),[3,5];case 5:return et&&!et.dispatchConditionRejection&&ea.match(eh)&&eh.meta.condition||es(eh),[2,eh]}})})}();return Object.assign(ep,{abort:v,requestId:ed,arg:B,unwrap:function(){return ep.then(ye)}})}},{pending:en,rejected:ea,fulfilled:er,typePrefix:B})}return e.withTypes=function(){return e},e}();function ye(B){if(B.meta&&B.meta.rejectedWithValue)throw B.payload;if(B.error)throw B.error;return B.payload}var he=function(B,Z){return I(B)?B.match(Z):B(Z)};function ge(){for(var B=[],Z=0;Z<arguments.length;Z++)B[Z]=arguments[Z];return function(Z){return B.some(function(B){return he(B,Z)})}}function be(){for(var B=[],Z=0;Z<arguments.length;Z++)B[Z]=arguments[Z];return function(Z){return B.every(function(B){return he(B,Z)})}}function me(B,Z){if(!B||!B.meta)return!1;var et="string"==typeof B.meta.requestId,er=Z.indexOf(B.meta.requestStatus)>-1;return et&&er}function we(B){return"function"==typeof B[0]&&"pending"in B[0]&&"fulfilled"in B[0]&&"rejected"in B[0]}function Oe(){for(var B=[],Z=0;Z<arguments.length;Z++)B[Z]=arguments[Z];return 0===B.length?function(B){return me(B,["pending"])}:we(B)?function(Z){var et=B.map(function(B){return B.pending});return ge.apply(void 0,et)(Z)}:Oe()(B[0])}function je(){for(var B=[],Z=0;Z<arguments.length;Z++)B[Z]=arguments[Z];return 0===B.length?function(B){return me(B,["rejected"])}:we(B)?function(Z){var et=B.map(function(B){return B.rejected});return ge.apply(void 0,et)(Z)}:je()(B[0])}function Ae(){for(var B=[],Z=0;Z<arguments.length;Z++)B[Z]=arguments[Z];var t=function(B){return B&&B.meta&&B.meta.rejectedWithValue};return 0===B.length||we(B)?function(Z){return be(je.apply(void 0,B),t)(Z)}:Ae()(B[0])}function Ee(){for(var B=[],Z=0;Z<arguments.length;Z++)B[Z]=arguments[Z];return 0===B.length?function(B){return me(B,["fulfilled"])}:we(B)?function(Z){var et=B.map(function(B){return B.fulfilled});return ge.apply(void 0,et)(Z)}:Ee()(B[0])}function Se(){for(var B=[],Z=0;Z<arguments.length;Z++)B[Z]=arguments[Z];return 0===B.length?function(B){return me(B,["pending","fulfilled","rejected"])}:we(B)?function(Z){for(var et=[],er=0;er<B.length;er++){var en=B[er];et.push(en.pending,en.rejected,en.fulfilled)}return ge.apply(void 0,et)(Z)}:Se()(B[0])}var Pe=function(B,Z){if("function"!=typeof B)throw TypeError(Z+" is not a function")},_e=function(){},qe=function(B,Z){return void 0===Z&&(Z=_e),B.catch(Z),B},xe=function(B,Z){return B.addEventListener("abort",Z,{once:!0}),function(){return B.removeEventListener("abort",Z)}},Me=function(B,Z){var et=B.signal;et.aborted||("reason"in et||Object.defineProperty(et,"reason",{enumerable:!0,value:Z,configurable:!0,writable:!0}),B.abort(Z))},ke=function(B){this.code=B,this.name="TaskAbortError",this.message="task cancelled (reason: "+B+")"},Ie=function(B){if(B.aborted)throw new ke(B.reason)};function Te(B,Z){var et=_e;return new Promise(function(er,en){var o=function(){return en(new ke(B.reason))};B.aborted?o():(et=xe(B,o),Z.finally(function(){return et()}).then(er,en))}).finally(function(){et=_e})}var Ce=function(B){return function(Z){return qe(Te(B,Z).then(function(Z){return Ie(B),Z}))}},De=function(B){var Z=Ce(B);return function(B){return Z(new Promise(function(Z){return setTimeout(Z,B)}))}},ek=Object.assign,eT={},eN="listenerMiddleware",Ne=function(B){var Z=B.type,et=B.actionCreator,er=B.matcher,en=B.predicate,eo=B.effect;if(Z)en=T(Z).match;else if(et)Z=et.type,en=et.match;else if(er)en=er;else if(!en)throw Error("Creating or removing a listener requires one of the known fields for matching an action");return Pe(eo,"options.listener"),{predicate:en,type:Z,effect:eo}},Ve=function(B){B.pending.forEach(function(B){Me(B,"listener-cancelled")})},Be=function(B,Z,et){try{B(Z,et)}catch(B){setTimeout(function(){throw B},0)}},eL=T(eN+"/add"),eD=T(eN+"/removeAll"),eI=T(eN+"/remove"),Xe=function(){for(var B=[],Z=0;Z<arguments.length;Z++)B[Z]=arguments[Z];console.error.apply(console,ea([eN+"/error"],B))};function Ge(B){var Z=this;void 0===B&&(B={});var et=new Map,er=B.extra,en=B.onError,ea=void 0===en?Xe:en;Pe(ea,"onError");var a=function(B){for(var Z=0,er=Array.from(et.values());Z<er.length;Z++){var en=er[Z];if(B(en))return en}},c=function(B){var Z,er,en,eo,ea,ei=a(function(Z){return Z.effect===B.effect});return ei||(er=(Z=Ne(B)).type,en=Z.predicate,eo=Z.effect,ei={id:fe(),effect:eo,type:er,predicate:en,pending:new Set,unsubscribe:function(){throw Error("Unsubscribe not initialized")}}),(ea=ei).unsubscribe=function(){return et.delete(ea.id)},et.set(ea.id,ea),function(B){ea.unsubscribe(),(null==B?void 0:B.cancelActive)&&Ve(ea)}},f=function(B){var Z=Ne(B),et=Z.type,er=Z.effect,en=Z.predicate,eo=a(function(B){return("string"==typeof et?B.type===et:B.predicate===en)&&B.effect===er});return eo&&(eo.unsubscribe(),B.cancelActive&&Ve(eo)),!!eo},l=function(B,en,ei,es){return w(Z,null,function(){var Z,eu,el,ec;return eo(this,function(ed){var ef,ep,eh;switch(ed.label){case 0:eh=(Z=new AbortController).signal,eu=function(B,Z){return qe(w(void 0,null,function(){var et,er,en;return eo(this,function(eo){switch(eo.label){case 0:Ie(eh),et=function(){},er=[new Promise(function(Z,er){var en=c({predicate:B,effect:function(B,et){et.unsubscribe(),Z([B,et.getState(),et.getOriginalState()])}});et=function(){en(),er()}})],null!=Z&&er.push(new Promise(function(B){return setTimeout(B,Z,null)})),eo.label=1;case 1:return eo.trys.push([1,,3,4]),[4,Te(eh,Promise.race(er))];case 2:return en=eo.sent(),Ie(eh),[2,en];case 3:return et(),[7];case 4:return[2]}})}))},el=[],ed.label=1;case 1:return ed.trys.push([1,3,4,6]),B.pending.add(Z),[4,Promise.resolve(B.effect(en,ek({},ei,{getOriginalState:es,condition:function(B,Z){return eu(B,Z).then(Boolean)},take:eu,delay:De(Z.signal),pause:Ce(Z.signal),extra:er,signal:Z.signal,fork:(ef=Z.signal,ep=el,function(B,Z){Pe(B,"taskExecutor");var et=new AbortController;xe(ef,function(){return Me(et,ef.reason)});var er,en=(er=function(){return Me(et,"task-completed")},w(void 0,null,function(){var Z;return eo(this,function(en){switch(en.label){case 0:return en.trys.push([0,3,4,5]),[4,Promise.resolve()];case 1:return en.sent(),[4,w(void 0,null,function(){var Z;return eo(this,function(er){switch(er.label){case 0:return Ie(ef),Ie(et.signal),[4,B({pause:Ce(et.signal),delay:De(et.signal),signal:et.signal})];case 1:return Z=er.sent(),Ie(et.signal),[2,Z]}})})];case 2:return[2,{status:"ok",value:en.sent()}];case 3:return[2,{status:(Z=en.sent())instanceof ke?"cancelled":"rejected",error:Z}];case 4:return null==er||er(),[7];case 5:return[2]}})}));return(null==Z?void 0:Z.autoJoin)&&ep.push(en),{result:Ce(ef)(en),cancel:function(){Me(et,"task-cancelled")}}}),unsubscribe:B.unsubscribe,subscribe:function(){et.set(B.id,B)},cancelActiveListeners:function(){B.pending.forEach(function(B,et,er){B!==Z&&(Me(B,"listener-cancelled"),er.delete(B))})}})))];case 2:return ed.sent(),[3,6];case 3:return(ec=ed.sent())instanceof ke||Be(ea,ec,{raisedBy:"effect"}),[3,6];case 4:return[4,Promise.allSettled(el)];case 5:return ed.sent(),Me(Z,"listener-completed"),B.pending.delete(Z),[7];case 6:return[2]}})})},s=function(){et.forEach(Ve),et.clear()};return{middleware:function(B){return function(Z){return function(er){if(!C(er))return Z(er);if(eL.match(er))return c(er.payload);if(!eD.match(er)){if(eI.match(er))return f(er.payload);var en,eo=B.getState(),a=function(){if(eo===eT)throw Error(eN+": getOriginalState can only be called synchronously");return eo};try{if(en=Z(er),et.size>0)for(var ei=B.getState(),es=Array.from(et.values()),eu=0;eu<es.length;eu++){var el=es[eu],ec=!1;try{ec=el.predicate(er,ei,eo)}catch(B){ec=!1,Be(ea,B,{raisedBy:"predicate"})}ec&&l(el,er,B,a)}}finally{eo=eT}return en}s()}}},startListening:c,stopListening:f,clearListeners:s}}var eF,eU="RTK_autoBatch",Ke=function(){return function(B){var Z;return{payload:B,meta:((Z={})[eU]=!0,Z)}}},eq="function"==typeof queueMicrotask?queueMicrotask.bind("undefined"!=typeof window?window:"undefined"!=typeof global?global:globalThis):function(B){return(eF||(eF=Promise.resolve())).then(B).catch(function(B){return setTimeout(function(){throw B},0)})},Ye=function(B){return function(Z){setTimeout(Z,B)}},e$="undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:Ye(10),$e=function(B){return void 0===B&&(B={type:"raf"}),function(Z){return function(){for(var et=[],er=0;er<arguments.length;er++)et[er]=arguments[er];var en=Z.apply(void 0,et),eo=!0,ea=!1,ei=!1,es=new Set,eu="tick"===B.type?eq:"raf"===B.type?e$:"callback"===B.type?B.queueNotification:Ye(B.timeout),l=function(){ei=!1,ea&&(ea=!1,es.forEach(function(B){return B()}))};return Object.assign({},en,{subscribe:function(B){var Z=en.subscribe(function(){return eo&&B()});return es.add(B),function(){Z(),es.delete(B)}},dispatch:function(B){var Z;try{return(ea=!(eo=!(null==(Z=null==B?void 0:B.meta)?void 0:Z[eU])))&&(ei||(ei=!0,eu(l))),en.dispatch(B)}finally{eo=!0}}})}}};(0,ey.enableES5)()},17914:(B,Z,et)=>{"use strict";function n(B){for(var Z=arguments.length,et=Array(Z>1?Z-1:0),er=1;er<Z;er++)et[er-1]=arguments[er];throw Error("[Immer] minified error nr: "+B+(et.length?" "+et.map(function(B){return"'"+B+"'"}).join(","):"")+". Find the full error at: https://bit.ly/3cXEKWf")}function r(B){return!!B&&!!B[ec]}function t(B){var Z;return!!B&&(function(B){if(!B||"object"!=typeof B)return!1;var Z=Object.getPrototypeOf(B);if(null===Z)return!0;var et=Object.hasOwnProperty.call(Z,"constructor")&&Z.constructor;return et===Object||"function"==typeof et&&Function.toString.call(et)===ed}(B)||Array.isArray(B)||!!B[el]||!!(null===(Z=B.constructor)||void 0===Z?void 0:Z[el])||s(B)||v(B))}function i(B,Z,et){void 0===et&&(et=!1),0===o(B)?(et?Object.keys:ef)(B).forEach(function(er){et&&"symbol"==typeof er||Z(er,B[er],B)}):B.forEach(function(et,er){return Z(er,et,B)})}function o(B){var Z=B[ec];return Z?Z.i>3?Z.i-4:Z.i:Array.isArray(B)?1:s(B)?2:v(B)?3:0}function u(B,Z){return 2===o(B)?B.has(Z):Object.prototype.hasOwnProperty.call(B,Z)}function a(B,Z){return 2===o(B)?B.get(Z):B[Z]}function f(B,Z,et){var er=o(B);2===er?B.set(Z,et):3===er?B.add(et):B[Z]=et}function c(B,Z){return B===Z?0!==B||1/B==1/Z:B!=B&&Z!=Z}function s(B){return ea&&B instanceof Map}function v(B){return ei&&B instanceof Set}function p(B){return B.o||B.t}function l(B){if(Array.isArray(B))return Array.prototype.slice.call(B);var Z=ep(B);delete Z[ec];for(var et=ef(Z),er=0;er<et.length;er++){var en=et[er],eo=Z[en];!1===eo.writable&&(eo.writable=!0,eo.configurable=!0),(eo.get||eo.set)&&(Z[en]={configurable:!0,writable:!0,enumerable:eo.enumerable,value:B[en]})}return Object.create(Object.getPrototypeOf(B),Z)}function d(B,Z){return void 0===Z&&(Z=!1),y(B)||r(B)||!t(B)||(o(B)>1&&(B.set=B.add=B.clear=B.delete=h),Object.freeze(B),Z&&i(B,function(B,Z){return d(Z,!0)},!0)),B}function h(){n(2)}function y(B){return null==B||"object"!=typeof B||Object.isFrozen(B)}function b(B){var Z=eh[B];return Z||n(18,B),Z}function m(B,Z){eh[B]||(eh[B]=Z)}function j(B,Z){Z&&(b("Patches"),B.u=[],B.s=[],B.v=Z)}function g(B){O(B),B.p.forEach(S),B.p=null}function O(B){B===en&&(en=B.l)}function w(B){return en={p:[],l:en,h:B,m:!0,_:0}}function S(B){var Z=B[ec];0===Z.i||1===Z.i?Z.j():Z.g=!0}function P(B,Z){Z._=Z.p.length;var et=Z.p[0],er=void 0!==B&&B!==et;return Z.h.O||b("ES5").S(Z,B,er),er?(et[ec].P&&(g(Z),n(4)),t(B)&&(B=M(Z,B),Z.l||x(Z,B)),Z.u&&b("Patches").M(et[ec].t,B,Z.u,Z.s)):B=M(Z,et,[]),g(Z),Z.u&&Z.v(Z.u,Z.s),B!==eu?B:void 0}function M(B,Z,et){if(y(Z))return Z;var er=Z[ec];if(!er)return i(Z,function(en,eo){return A(B,er,Z,en,eo,et)},!0),Z;if(er.A!==B)return Z;if(!er.P)return x(B,er.t,!0),er.t;if(!er.I){er.I=!0,er.A._--;var en=4===er.i||5===er.i?er.o=l(er.k):er.o,eo=en,ea=!1;3===er.i&&(eo=new Set(en),en.clear(),ea=!0),i(eo,function(Z,eo){return A(B,er,en,Z,eo,et,ea)}),x(B,en,!1),et&&B.u&&b("Patches").N(er,et,B.u,B.s)}return er.o}function A(B,Z,et,er,en,eo,ea){if(r(en)){var ei=M(B,en,eo&&Z&&3!==Z.i&&!u(Z.R,er)?eo.concat(er):void 0);if(f(et,er,ei),!r(ei))return;B.m=!1}else ea&&et.add(en);if(t(en)&&!y(en)){if(!B.h.D&&B._<1)return;M(B,en),Z&&Z.A.l||x(B,en)}}function x(B,Z,et){void 0===et&&(et=!1),!B.l&&B.h.D&&B.m&&d(Z,et)}function z(B,Z){var et=B[ec];return(et?p(et):B)[Z]}function I(B,Z){if(Z in B)for(var et=Object.getPrototypeOf(B);et;){var er=Object.getOwnPropertyDescriptor(et,Z);if(er)return er;et=Object.getPrototypeOf(et)}}function k(B){B.P||(B.P=!0,B.l&&k(B.l))}function E(B){B.o||(B.o=l(B.t))}function N(B,Z,et){var er,eo,ea,ei,es,eu,el,ec=s(Z)?b("MapSet").F(Z,et):v(Z)?b("MapSet").T(Z,et):B.O?(ea=eo={i:(er=Array.isArray(Z))?1:0,A:et?et.A:en,P:!1,I:!1,R:{},l:et,t:Z,k:null,o:null,j:null,C:!1},ei=eg,er&&(ea=[eo],ei=ey),eu=(es=Proxy.revocable(ea,ei)).revoke,el=es.proxy,eo.k=el,eo.j=eu,el):b("ES5").J(Z,et);return(et?et.A:en).p.push(ec),ec}function R(B){return r(B)||n(22,B),function n(B){if(!t(B))return B;var Z,et=B[ec],er=o(B);if(et){if(!et.P&&(et.i<4||!b("ES5").K(et)))return et.t;et.I=!0,Z=D(B,er),et.I=!1}else Z=D(B,er);return i(Z,function(B,er){et&&a(et.t,B)===er||f(Z,B,n(er))}),3===er?new Set(Z):Z}(B)}function D(B,Z){switch(Z){case 2:return new Map(B);case 3:return Array.from(B)}return l(B)}function F(){function t(Z,et){var er=B[Z];return er?er.enumerable=et:B[Z]=er={configurable:!0,enumerable:et,get:function(){var B=this[ec];return eg.get(B,Z)},set:function(B){var et=this[ec];eg.set(et,Z,B)}},er}function e(B){for(var Z=B.length-1;Z>=0;Z--){var et=B[Z][ec];if(!et.P)switch(et.i){case 5:a(et)&&k(et);break;case 4:o(et)&&k(et)}}}function o(B){for(var Z=B.t,et=B.k,er=ef(et),en=er.length-1;en>=0;en--){var eo=er[en];if(eo!==ec){var ea=Z[eo];if(void 0===ea&&!u(Z,eo))return!0;var ei=et[eo],es=ei&&ei[ec];if(es?es.t!==ea:!c(ei,ea))return!0}}var eu=!!Z[ec];return er.length!==ef(Z).length+(eu?0:1)}function a(B){var Z=B.k;if(Z.length!==B.t.length)return!0;var et=Object.getOwnPropertyDescriptor(Z,Z.length-1);if(et&&!et.get)return!0;for(var er=0;er<Z.length;er++)if(!Z.hasOwnProperty(er))return!0;return!1}var B={};m("ES5",{J:function(B,Z){var et=Array.isArray(B),er=function(B,Z){if(B){for(var et=Array(Z.length),er=0;er<Z.length;er++)Object.defineProperty(et,""+er,t(er,!0));return et}var en=ep(Z);delete en[ec];for(var eo=ef(en),ea=0;ea<eo.length;ea++){var ei=eo[ea];en[ei]=t(ei,B||!!en[ei].enumerable)}return Object.create(Object.getPrototypeOf(Z),en)}(et,B),eo={i:et?5:4,A:Z?Z.A:en,P:!1,I:!1,R:{},l:Z,t:B,k:er,o:null,g:!1,C:!1};return Object.defineProperty(er,ec,{value:eo,writable:!0}),er},S:function(B,Z,et){et?r(Z)&&Z[ec].A===B&&e(B.p):(B.u&&function n(B){if(B&&"object"==typeof B){var Z=B[ec];if(Z){var et=Z.t,er=Z.k,en=Z.R,eo=Z.i;if(4===eo)i(er,function(B){B!==ec&&(void 0!==et[B]||u(et,B)?en[B]||n(er[B]):(en[B]=!0,k(Z)))}),i(et,function(B){void 0!==er[B]||u(er,B)||(en[B]=!1,k(Z))});else if(5===eo){if(a(Z)&&(k(Z),en.length=!0),er.length<et.length)for(var ea=er.length;ea<et.length;ea++)en[ea]=!1;else for(var ei=et.length;ei<er.length;ei++)en[ei]=!0;for(var es=Math.min(er.length,et.length),eu=0;eu<es;eu++)er.hasOwnProperty(eu)||(en[eu]=!0),void 0===en[eu]&&n(er[eu])}}}}(B.p[0]),e(B.p))},K:function(B){return 4===B.i?o(B):a(B)}})}et.d(Z,{xC:()=>configureStore,oM:()=>createSlice});var er,en,eo="undefined"!=typeof Symbol&&"symbol"==typeof Symbol("x"),ea="undefined"!=typeof Map,ei="undefined"!=typeof Set,es="undefined"!=typeof Proxy&&void 0!==Proxy.revocable&&"undefined"!=typeof Reflect,eu=eo?Symbol.for("immer-nothing"):((er={})["immer-nothing"]=!0,er),el=eo?Symbol.for("immer-draftable"):"__$immer_draftable",ec=eo?Symbol.for("immer-state"):"__$immer_state",ed=("undefined"!=typeof Symbol&&Symbol.iterator,""+Object.prototype.constructor),ef="undefined"!=typeof Reflect&&Reflect.ownKeys?Reflect.ownKeys:void 0!==Object.getOwnPropertySymbols?function(B){return Object.getOwnPropertyNames(B).concat(Object.getOwnPropertySymbols(B))}:Object.getOwnPropertyNames,ep=Object.getOwnPropertyDescriptors||function(B){var Z={};return ef(B).forEach(function(et){Z[et]=Object.getOwnPropertyDescriptor(B,et)}),Z},eh={},eg={get:function(B,Z){if(Z===ec)return B;var et,er,en=p(B);if(!u(en,Z))return(er=I(en,Z))?"value"in er?er.value:null===(et=er.get)||void 0===et?void 0:et.call(B.k):void 0;var eo=en[Z];return B.I||!t(eo)?eo:eo===z(B.t,Z)?(E(B),B.o[Z]=N(B.A.h,eo,B)):eo},has:function(B,Z){return Z in p(B)},ownKeys:function(B){return Reflect.ownKeys(p(B))},set:function(B,Z,et){var er=I(p(B),Z);if(null==er?void 0:er.set)return er.set.call(B.k,et),!0;if(!B.P){var en=z(p(B),Z),eo=null==en?void 0:en[ec];if(eo&&eo.t===et)return B.o[Z]=et,B.R[Z]=!1,!0;if(c(et,en)&&(void 0!==et||u(B.t,Z)))return!0;E(B),k(B)}return B.o[Z]===et&&(void 0!==et||Z in B.o)||Number.isNaN(et)&&Number.isNaN(B.o[Z])||(B.o[Z]=et,B.R[Z]=!0),!0},deleteProperty:function(B,Z){return void 0!==z(B.t,Z)||Z in B.t?(B.R[Z]=!1,E(B),k(B)):delete B.R[Z],B.o&&delete B.o[Z],!0},getOwnPropertyDescriptor:function(B,Z){var et=p(B),er=Reflect.getOwnPropertyDescriptor(et,Z);return er?{writable:!0,configurable:1!==B.i||"length"!==Z,enumerable:er.enumerable,value:et[Z]}:er},defineProperty:function(){n(11)},getPrototypeOf:function(B){return Object.getPrototypeOf(B.t)},setPrototypeOf:function(){n(12)}},ey={};i(eg,function(B,Z){ey[B]=function(){return arguments[0]=arguments[0][0],Z.apply(this,arguments)}}),ey.deleteProperty=function(B,Z){return ey.set.call(this,B,Z,void 0)},ey.set=function(B,Z,et){return eg.set.call(this,B[0],Z,et,B[0])};var em=new(function(){function e(B){var Z=this;this.O=es,this.D=!0,this.produce=function(B,et,er){if("function"==typeof B&&"function"!=typeof et){var en,eo=et;return et=B,function(B){var er=this;void 0===B&&(B=eo);for(var en=arguments.length,ea=Array(en>1?en-1:0),ei=1;ei<en;ei++)ea[ei-1]=arguments[ei];return Z.produce(B,function(B){var Z;return(Z=et).call.apply(Z,[er,B].concat(ea))})}}if("function"!=typeof et&&n(6),void 0!==er&&"function"!=typeof er&&n(7),t(B)){var ea=w(Z),ei=N(Z,B,void 0),es=!0;try{en=et(ei),es=!1}finally{es?g(ea):O(ea)}return"undefined"!=typeof Promise&&en instanceof Promise?en.then(function(B){return j(ea,er),P(B,ea)},function(B){throw g(ea),B}):(j(ea,er),P(en,ea))}if(!B||"object"!=typeof B){if(void 0===(en=et(B))&&(en=B),en===eu&&(en=void 0),Z.D&&d(en,!0),er){var el=[],ec=[];b("Patches").M(B,en,el,ec),er(el,ec)}return en}n(21,B)},this.produceWithPatches=function(B,et){if("function"==typeof B)return function(et){for(var er=arguments.length,en=Array(er>1?er-1:0),eo=1;eo<er;eo++)en[eo-1]=arguments[eo];return Z.produceWithPatches(et,function(Z){return B.apply(void 0,[Z].concat(en))})};var er,en,eo=Z.produce(B,et,function(B,Z){er=B,en=Z});return"undefined"!=typeof Promise&&eo instanceof Promise?eo.then(function(B){return[B,er,en]}):[eo,er,en]},"boolean"==typeof(null==B?void 0:B.useProxies)&&this.setUseProxies(B.useProxies),"boolean"==typeof(null==B?void 0:B.autoFreeze)&&this.setAutoFreeze(B.autoFreeze)}var B=e.prototype;return B.createDraft=function(B){t(B)||n(8),r(B)&&(B=R(B));var Z=w(this),et=N(this,B,void 0);return et[ec].C=!0,O(Z),et},B.finishDraft=function(B,Z){var et=(B&&B[ec]).A;return j(et,Z),P(void 0,et)},B.setAutoFreeze=function(B){this.D=B},B.setUseProxies=function(B){B&&!es&&n(20),this.O=B},B.applyPatches=function(B,Z){for(et=Z.length-1;et>=0;et--){var et,er=Z[et];if(0===er.path.length&&"replace"===er.op){B=er.value;break}}et>-1&&(Z=Z.slice(et+1));var en=b("Patches").$;return r(B)?en(B,Z):this.produce(B,function(B){return en(B,Z)})},e}()),ev=em.produce;em.produceWithPatches.bind(em),em.setAutoFreeze.bind(em),em.setUseProxies.bind(em),em.applyPatches.bind(em),em.createDraft.bind(em),em.finishDraft.bind(em);var eb=et(58131),eS=et(21890),eP=function(){var extendStatics=function(B,Z){return(extendStatics=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(B,Z){B.__proto__=Z}||function(B,Z){for(var et in Z)Object.prototype.hasOwnProperty.call(Z,et)&&(B[et]=Z[et])})(B,Z)};return function(B,Z){if("function"!=typeof Z&&null!==Z)throw TypeError("Class extends value "+String(Z)+" is not a constructor or null");function __(){this.constructor=B}extendStatics(B,Z),B.prototype=null===Z?Object.create(Z):(__.prototype=Z.prototype,new __)}}(),__generator=function(B,Z){var et,er,en,eo,ea={label:0,sent:function(){if(1&en[0])throw en[1];return en[1]},trys:[],ops:[]};return eo={next:verb(0),throw:verb(1),return:verb(2)},"function"==typeof Symbol&&(eo[Symbol.iterator]=function(){return this}),eo;function verb(B){return function(Z){return step([B,Z])}}function step(eo){if(et)throw TypeError("Generator is already executing.");for(;ea;)try{if(et=1,er&&(en=2&eo[0]?er.return:eo[0]?er.throw||((en=er.return)&&en.call(er),0):er.next)&&!(en=en.call(er,eo[1])).done)return en;switch(er=0,en&&(eo=[2&eo[0],en.value]),eo[0]){case 0:case 1:en=eo;break;case 4:return ea.label++,{value:eo[1],done:!1};case 5:ea.label++,er=eo[1],eo=[0];continue;case 7:eo=ea.ops.pop(),ea.trys.pop();continue;default:if(!(en=(en=ea.trys).length>0&&en[en.length-1])&&(6===eo[0]||2===eo[0])){ea=0;continue}if(3===eo[0]&&(!en||eo[1]>en[0]&&eo[1]<en[3])){ea.label=eo[1];break}if(6===eo[0]&&ea.label<en[1]){ea.label=en[1],en=eo;break}if(en&&ea.label<en[2]){ea.label=en[2],ea.ops.push(eo);break}en[2]&&ea.ops.pop(),ea.trys.pop();continue}eo=Z.call(B,ea)}catch(B){eo=[6,B],er=0}finally{et=en=0}if(5&eo[0])throw eo[1];return{value:eo[0]?eo[1]:void 0,done:!0}}},__spreadArray=function(B,Z){for(var et=0,er=Z.length,en=B.length;et<er;et++,en++)B[en]=Z[et];return B},eO=Object.defineProperty,e_=Object.defineProperties,ex=Object.getOwnPropertyDescriptors,eR=Object.getOwnPropertySymbols,ew=Object.prototype.hasOwnProperty,eE=Object.prototype.propertyIsEnumerable,__defNormalProp=function(B,Z,et){return Z in B?eO(B,Z,{enumerable:!0,configurable:!0,writable:!0,value:et}):B[Z]=et},__spreadValues=function(B,Z){for(var et in Z||(Z={}))ew.call(Z,et)&&__defNormalProp(B,et,Z[et]);if(eR)for(var er=0,en=eR(Z);er<en.length;er++){var et=en[er];eE.call(Z,et)&&__defNormalProp(B,et,Z[et])}return B},__spreadProps=function(B,Z){return e_(B,ex(Z))},ej="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!=arguments.length)return"object"==typeof arguments[0]?eb.compose:eb.compose.apply(null,arguments)};function isPlainObject(B){if("object"!=typeof B||null===B)return!1;var Z=Object.getPrototypeOf(B);if(null===Z)return!0;for(var et=Z;null!==Object.getPrototypeOf(et);)et=Object.getPrototypeOf(et);return Z===et}function createAction(B,Z){function actionCreator(){for(var et=[],er=0;er<arguments.length;er++)et[er]=arguments[er];if(Z){var en=Z.apply(void 0,et);if(!en)throw Error("prepareAction did not return an object");return __spreadValues(__spreadValues({type:B,payload:en.payload},"meta"in en&&{meta:en.meta}),"error"in en&&{error:en.error})}return{type:B,payload:et[0]}}return actionCreator.toString=function(){return""+B},actionCreator.type=B,actionCreator.match=function(Z){return Z.type===B},actionCreator}function isAction(B){return isPlainObject(B)&&"type"in B}function isFSA(B){return isAction(B)&&"string"==typeof B.type&&Object.keys(B).every(isValidKey)}function isValidKey(B){return["type","payload","error","meta"].indexOf(B)>-1}"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;var eC=function(B){function MiddlewareArray(){for(var Z=[],et=0;et<arguments.length;et++)Z[et]=arguments[et];var er=B.apply(this,Z)||this;return Object.setPrototypeOf(er,MiddlewareArray.prototype),er}return eP(MiddlewareArray,B),Object.defineProperty(MiddlewareArray,Symbol.species,{get:function(){return MiddlewareArray},enumerable:!1,configurable:!0}),MiddlewareArray.prototype.concat=function(){for(var Z=[],et=0;et<arguments.length;et++)Z[et]=arguments[et];return B.prototype.concat.apply(this,Z)},MiddlewareArray.prototype.prepend=function(){for(var B=[],Z=0;Z<arguments.length;Z++)B[Z]=arguments[Z];return 1===B.length&&Array.isArray(B[0])?new(MiddlewareArray.bind.apply(MiddlewareArray,__spreadArray([void 0],B[0].concat(this)))):new(MiddlewareArray.bind.apply(MiddlewareArray,__spreadArray([void 0],B.concat(this))))},MiddlewareArray}(Array),eM=function(B){function EnhancerArray(){for(var Z=[],et=0;et<arguments.length;et++)Z[et]=arguments[et];var er=B.apply(this,Z)||this;return Object.setPrototypeOf(er,EnhancerArray.prototype),er}return eP(EnhancerArray,B),Object.defineProperty(EnhancerArray,Symbol.species,{get:function(){return EnhancerArray},enumerable:!1,configurable:!0}),EnhancerArray.prototype.concat=function(){for(var Z=[],et=0;et<arguments.length;et++)Z[et]=arguments[et];return B.prototype.concat.apply(this,Z)},EnhancerArray.prototype.prepend=function(){for(var B=[],Z=0;Z<arguments.length;Z++)B[Z]=arguments[Z];return 1===B.length&&Array.isArray(B[0])?new(EnhancerArray.bind.apply(EnhancerArray,__spreadArray([void 0],B[0].concat(this)))):new(EnhancerArray.bind.apply(EnhancerArray,__spreadArray([void 0],B.concat(this))))},EnhancerArray}(Array);function freezeDraftable(B){return t(B)?ev(B,function(){}):B}function isBoolean(B){return"boolean"==typeof B}function curryGetDefaultMiddleware(){return function(B){return getDefaultMiddleware(B)}}function getDefaultMiddleware(B){void 0===B&&(B={});var Z=B.thunk,et=void 0===Z||Z;B.immutableCheck,B.serializableCheck,B.actionCreatorCheck;var er=new eC;return et&&(isBoolean(et)?er.push(eS.default):er.push(eS.default.withExtraArgument(et.extraArgument))),er}function configureStore(B){var Z,et=curryGetDefaultMiddleware(),er=B||{},en=er.reducer,eo=void 0===en?void 0:en,ea=er.middleware,ei=void 0===ea?et():ea,es=er.devTools,eu=void 0===es||es,el=er.preloadedState,ec=void 0===el?void 0:el,ed=er.enhancers,ef=void 0===ed?void 0:ed;if("function"==typeof eo)Z=eo;else if(isPlainObject(eo))Z=(0,eb.combineReducers)(eo);else throw Error('"reducer" is a required argument, and must be a function or an object of functions that can be passed to combineReducers');var ep=ei;"function"==typeof ep&&(ep=ep(et));var eh=eb.applyMiddleware.apply(void 0,ep),eg=eb.compose;eu&&(eg=ej(__spreadValues({trace:!1},"object"==typeof eu&&eu)));var ey=new eM(eh),em=ey;Array.isArray(ef)?em=__spreadArray([eh],ef):"function"==typeof ef&&(em=ef(ey));var ev=eg.apply(void 0,em);return(0,eb.createStore)(Z,ec,ev)}function executeReducerBuilderCallback(B){var Z,et={},er=[],en={addCase:function(B,Z){var er="string"==typeof B?B:B.type;if(!er)throw Error("`builder.addCase` cannot be called with an empty action type");if(er in et)throw Error("`builder.addCase` cannot be called with two reducers for the same action type");return et[er]=Z,en},addMatcher:function(B,Z){return er.push({matcher:B,reducer:Z}),en},addDefaultCase:function(B){return Z=B,en}};return B(en),[et,er,Z]}function isStateFunction(B){return"function"==typeof B}function createReducer(B,Z,et,er){void 0===et&&(et=[]);var en,eo="function"==typeof Z?executeReducerBuilderCallback(Z):[Z,et,er],ea=eo[0],ei=eo[1],es=eo[2];if(isStateFunction(B))en=function(){return freezeDraftable(B())};else{var eu=freezeDraftable(B);en=function(){return eu}}function reducer(B,Z){void 0===B&&(B=en());var et=__spreadArray([ea[Z.type]],ei.filter(function(B){return(0,B.matcher)(Z)}).map(function(B){return B.reducer}));return 0===et.filter(function(B){return!!B}).length&&(et=[es]),et.reduce(function(B,et){if(et){if(r(B)){var er=et(B,Z);return void 0===er?B:er}if(t(B))return ev(B,function(B){return et(B,Z)});var er=et(B,Z);if(void 0===er){if(null===B)return B;throw Error("A case reducer on a non-draftable value must not return undefined")}return er}return B},B)}return reducer.getInitialState=en,reducer}function getType2(B,Z){return B+"/"+Z}function createSlice(B){var Z,et=B.name;if(!et)throw Error("`name` is a required option for createSlice");var er="function"==typeof B.initialState?B.initialState:freezeDraftable(B.initialState),en=B.reducers||{},eo=Object.keys(en),ea={},ei={},es={};function buildReducer(){var Z="function"==typeof B.extraReducers?executeReducerBuilderCallback(B.extraReducers):[B.extraReducers],et=Z[0],en=Z[1],eo=void 0===en?[]:en,ea=Z[2],es=void 0===ea?void 0:ea,eu=__spreadValues(__spreadValues({},void 0===et?{}:et),ei);return createReducer(er,function(B){for(var Z in eu)B.addCase(Z,eu[Z]);for(var et=0;et<eo.length;et++){var er=eo[et];B.addMatcher(er.matcher,er.reducer)}es&&B.addDefaultCase(es)})}return eo.forEach(function(B){var Z,er,eo=en[B],eu=getType2(et,B);"reducer"in eo?(Z=eo.reducer,er=eo.prepare):Z=eo,ea[B]=Z,ei[eu]=Z,es[B]=er?createAction(eu,er):createAction(eu)}),{name:et,reducer:function(B,et){return Z||(Z=buildReducer()),Z(B,et)},actions:es,caseReducers:ea,getInitialState:function(){return Z||(Z=buildReducer()),Z.getInitialState()}}}function createSingleArgumentStateOperator(B){var Z=createStateOperator(function(Z,et){return B(et)});return function(B){return Z(B,void 0)}}function createStateOperator(B){return function(Z,et){function isPayloadActionArgument(B){return isFSA(B)}var runMutator=function(Z){isPayloadActionArgument(et)?B(et.payload,Z):B(et,Z)};return isDraft3(Z)?(runMutator(Z),Z):createNextState3(Z,runMutator)}}function ensureEntitiesArray(B){return Array.isArray(B)||(B=Object.values(B)),B}function splitAddedUpdatedEntities(B,Z,et){B=ensureEntitiesArray(B);for(var er=[],en=[],eo=0,ea=B;eo<ea.length;eo++){var ei=ea[eo],es=Z(ei);es in et.entities?en.push({id:es,changes:ei}):er.push(ei)}return[er,en]}var nanoid=function(B){void 0===B&&(B=21);for(var Z="",et=B;et--;)Z+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return Z},eA=["name","message","stack","code"],RejectWithValue=function(B,Z){this.payload=B,this.meta=Z},FulfillWithMeta=function(B,Z){this.payload=B,this.meta=Z},miniSerializeError=function(B){if("object"==typeof B&&null!==B){for(var Z={},et=0;et<eA.length;et++){var er=eA[et];"string"==typeof B[er]&&(Z[er]=B[er])}return Z}return{message:String(B)}};function unwrapResult(B){if(B.meta&&B.meta.rejectedWithValue)throw B.payload;if(B.error)throw B.error;return B.payload}function isThenable(B){return null!==B&&"object"==typeof B&&"function"==typeof B.then}(function(){function createAsyncThunk2(B,Z,et){var er=createAction(B+"/fulfilled",function(B,Z,et,er){return{payload:B,meta:__spreadProps(__spreadValues({},er||{}),{arg:et,requestId:Z,requestStatus:"fulfilled"})}}),en=createAction(B+"/pending",function(B,Z,et){return{payload:void 0,meta:__spreadProps(__spreadValues({},et||{}),{arg:Z,requestId:B,requestStatus:"pending"})}}),eo=createAction(B+"/rejected",function(B,Z,er,en,eo){return{payload:en,error:(et&&et.serializeError||miniSerializeError)(B||"Rejected"),meta:__spreadProps(__spreadValues({},eo||{}),{arg:er,requestId:Z,rejectedWithValue:!!en,requestStatus:"rejected",aborted:(null==B?void 0:B.name)==="AbortError",condition:(null==B?void 0:B.name)==="ConditionError"})}}),ea="undefined"!=typeof AbortController?AbortController:function(){function class_1(){this.signal={aborted:!1,addEventListener:function(){},dispatchEvent:function(){return!1},onabort:function(){},removeEventListener:function(){},reason:void 0,throwIfAborted:function(){}}}return class_1.prototype.abort=function(){},class_1}();return Object.assign(function(B){return function(ei,es,eu){var el,ec=(null==et?void 0:et.idGenerator)?et.idGenerator(B):nanoid(),ed=new ea;function abort(B){el=B,ed.abort()}var ef=function(){var ea,ef;return ea=this,ef=function(){var ea,ef,ep,eh,eg,ey;return __generator(this,function(em){switch(em.label){case 0:if(em.trys.push([0,4,,5]),!isThenable(eh=null==(ea=null==et?void 0:et.condition)?void 0:ea.call(et,B,{getState:es,extra:eu})))return[3,2];return[4,eh];case 1:eh=em.sent(),em.label=2;case 2:if(!1===eh||ed.signal.aborted)throw{name:"ConditionError",message:"Aborted due to condition callback returning false."};return eg=new Promise(function(B,Z){return ed.signal.addEventListener("abort",function(){return Z({name:"AbortError",message:el||"Aborted"})})}),ei(en(ec,B,null==(ef=null==et?void 0:et.getPendingMeta)?void 0:ef.call(et,{requestId:ec,arg:B},{getState:es,extra:eu}))),[4,Promise.race([eg,Promise.resolve(Z(B,{dispatch:ei,getState:es,extra:eu,requestId:ec,signal:ed.signal,abort:abort,rejectWithValue:function(B,Z){return new RejectWithValue(B,Z)},fulfillWithValue:function(B,Z){return new FulfillWithMeta(B,Z)}})).then(function(Z){if(Z instanceof RejectWithValue)throw Z;return Z instanceof FulfillWithMeta?er(Z.payload,ec,B,Z.meta):er(Z,ec,B)})])];case 3:return ep=em.sent(),[3,5];case 4:return ep=(ey=em.sent())instanceof RejectWithValue?eo(null,ec,B,ey.payload,ey.meta):eo(ey,ec,B),[3,5];case 5:return et&&!et.dispatchConditionRejection&&eo.match(ep)&&ep.meta.condition||ei(ep),[2,ep]}})},new Promise(function(B,Z){var fulfilled=function(B){try{step(ef.next(B))}catch(B){Z(B)}},rejected=function(B){try{step(ef.throw(B))}catch(B){Z(B)}},step=function(Z){return Z.done?B(Z.value):Promise.resolve(Z.value).then(fulfilled,rejected)};step((ef=ef.apply(ea,null)).next())})}();return Object.assign(ef,{abort:abort,requestId:ec,arg:B,unwrap:function(){return ef.then(unwrapResult)}})}},{pending:en,rejected:eo,fulfilled:er,typePrefix:B})}createAsyncThunk2.withTypes=function(){return createAsyncThunk2}})();var matches=function(B,Z){return B&&"function"==typeof B.match?B.match(Z):B(Z)},assertFunction=function(B,Z){if("function"!=typeof B)throw TypeError(Z+" is not a function")},noop=function(){},ek="cancelled",TaskAbortError=function(B){this.code=B,this.name="TaskAbortError",this.message="task "+ek+" (reason: "+B+")"};Object.assign;var eT="listenerMiddleware";createAction(eT+"/add"),createAction(eT+"/removeAll"),createAction(eT+"/remove"),"function"==typeof queueMicrotask&&queueMicrotask.bind("undefined"!=typeof window?window:"undefined"!=typeof global?global:globalThis),"undefined"!=typeof window&&window.requestAnimationFrame&&window.requestAnimationFrame,F()},86753:(B,Z,et)=>{"use strict";var er=et(23314),en={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},eo={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},ea={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},ei={};function getStatics(B){return er.isMemo(B)?ea:ei[B.$$typeof]||en}ei[er.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},ei[er.Memo]=ea;var es=Object.defineProperty,eu=Object.getOwnPropertyNames,el=Object.getOwnPropertySymbols,ec=Object.getOwnPropertyDescriptor,ed=Object.getPrototypeOf,ef=Object.prototype;function hoistNonReactStatics(B,Z,et){if("string"!=typeof Z){if(ef){var er=ed(Z);er&&er!==ef&&hoistNonReactStatics(B,er,et)}var en=eu(Z);el&&(en=en.concat(el(Z)));for(var ea=getStatics(B),ei=getStatics(Z),ep=0;ep<en.length;++ep){var eh=en[ep];if(!eo[eh]&&!(et&&et[eh])&&!(ei&&ei[eh])&&!(ea&&ea[eh])){var eg=ec(Z,eh);try{es(B,eh,eg)}catch(B){}}}}return B}B.exports=hoistNonReactStatics},37460:(B,Z,et)=>{var er,en=(er=et(83889))&&"object"==typeof er&&"default"in er?er.default:er,eo=/\s([^'"/\s><]+?)[\s/>]|([^\s=]+)=\s?(".*?"|'.*?')/g;function r(B){var Z={type:"tag",name:"",voidElement:!1,attrs:{},children:[]},et=B.match(/<\/?([^\s]+?)[/\s>]/);if(et&&(Z.name=et[1],(en[et[1]]||"/"===B.charAt(B.length-2))&&(Z.voidElement=!0),Z.name.startsWith("!--"))){var er=B.indexOf("-->");return{type:"comment",comment:-1!==er?B.slice(4,er):""}}for(var ea=new RegExp(eo),ei=null;null!==(ei=ea.exec(B));)if(ei[0].trim()){if(ei[1]){var es=ei[1].trim(),eu=[es,""];es.indexOf("=")>-1&&(eu=es.split("=")),Z.attrs[eu[0]]=eu[1],ea.lastIndex--}else ei[2]&&(Z.attrs[ei[2]]=ei[3].trim().substring(1,ei[3].length-1))}return Z}var ea=/<[a-zA-Z0-9\-\!\/](?:"[^"]*"|'[^']*'|[^'">])*>/g,ei=/^\s*$/,es=Object.create(null);function a(B,Z){switch(Z.type){case"text":return B+Z.content;case"tag":return B+="<"+Z.name+(Z.attrs?function(B){var Z=[];for(var et in B)Z.push(et+'="'+B[et]+'"');return Z.length?" "+Z.join(" "):""}(Z.attrs):"")+(Z.voidElement?"/>":">"),Z.voidElement?B:B+Z.children.reduce(a,"")+"</"+Z.name+">";case"comment":return B+"<!--"+Z.comment+"-->"}}B.exports={parse:function(B,Z){Z||(Z={}),Z.components||(Z.components=es);var et,er=[],en=[],eo=-1,eu=!1;if(0!==B.indexOf("<")){var el=B.indexOf("<");er.push({type:"text",content:-1===el?B:B.substring(0,el)})}return B.replace(ea,function(ea,es){if(eu){if(ea!=="</"+et.name+">")return;eu=!1}var el,ec="/"!==ea.charAt(1),ed=ea.startsWith("<!--"),ef=es+ea.length,ep=B.charAt(ef);if(ed){var eh=r(ea);return eo<0?er.push(eh):(el=en[eo]).children.push(eh),er}if(ec&&(eo++,"tag"===(et=r(ea)).type&&Z.components[et.name]&&(et.type="component",eu=!0),et.voidElement||eu||!ep||"<"===ep||et.children.push({type:"text",content:B.slice(ef,B.indexOf("<",ef))}),0===eo&&er.push(et),(el=en[eo-1])&&el.children.push(et),en[eo]=et),(!ec||et.voidElement)&&(eo>-1&&(et.voidElement||et.name===ea.slice(2,-1))&&(et=-1==--eo?er:en[eo]),!eu&&"<"!==ep&&ep)){el=-1===eo?er:en[eo].children;var eg=B.indexOf("<",ef),ey=B.slice(ef,-1===eg?void 0:eg);ei.test(ey)&&(ey=" "),(eg>-1&&eo+el.length>=0||" "!==ey)&&el.push({type:"text",content:ey})}}),er},stringify:function(B){return B.reduce(function(B,Z){return B+a("",Z)},"")}}},63550:(B,Z)=>{function n(B){for(var Z=arguments.length,et=Array(Z>1?Z-1:0),er=1;er<Z;er++)et[er-1]=arguments[er];throw Error("[Immer] minified error nr: "+B+(et.length?" "+et.map(function(B){return"'"+B+"'"}).join(","):"")+". Find the full error at: https://bit.ly/3cXEKWf")}function r(B){return!!B&&!!B[el]}function t(B){var Z;return!!B&&(function(B){if(!B||"object"!=typeof B)return!1;var Z=Object.getPrototypeOf(B);if(null===Z)return!0;var et=Object.hasOwnProperty.call(Z,"constructor")&&Z.constructor;return et===Object||"function"==typeof et&&Function.toString.call(et)===ed}(B)||Array.isArray(B)||!!B[eu]||!!(null===(Z=B.constructor)||void 0===Z?void 0:Z[eu])||c(B)||v(B))}function e(B,Z,et){void 0===et&&(et=!1),0===i(B)?(et?Object.keys:ef)(B).forEach(function(er){et&&"symbol"==typeof er||Z(er,B[er],B)}):B.forEach(function(et,er){return Z(er,et,B)})}function i(B){var Z=B[el];return Z?Z.t>3?Z.t-4:Z.t:Array.isArray(B)?1:c(B)?2:v(B)?3:0}function u(B,Z){return 2===i(B)?B.has(Z):Object.prototype.hasOwnProperty.call(B,Z)}function o(B,Z){return 2===i(B)?B.get(Z):B[Z]}function f(B,Z,et){var er=i(B);2===er?B.set(Z,et):3===er?B.add(et):B[Z]=et}function a(B,Z){return B===Z?0!==B||1/B==1/Z:B!=B&&Z!=Z}function c(B){return eo&&B instanceof Map}function v(B){return ea&&B instanceof Set}function s(B){return B.i||B.u}function p(B){if(Array.isArray(B))return Array.prototype.slice.call(B);var Z=ep(B);delete Z[el];for(var et=ef(Z),er=0;er<et.length;er++){var en=et[er],eo=Z[en];!1===eo.writable&&(eo.writable=!0,eo.configurable=!0),(eo.get||eo.set)&&(Z[en]={configurable:!0,writable:!0,enumerable:eo.enumerable,value:B[en]})}return Object.create(Object.getPrototypeOf(B),Z)}function l(B,Z){return void 0===Z&&(Z=!1),h(B)||r(B)||!t(B)||(i(B)>1&&(B.set=B.add=B.clear=B.delete=d),Object.freeze(B),Z&&e(B,function(B,Z){return l(Z,!0)},!0)),B}function d(){n(2)}function h(B){return null==B||"object"!=typeof B||Object.isFrozen(B)}function y(B){var Z=eh[B];return Z||n(18,B),Z}function _(B,Z){eh[B]||(eh[B]=Z)}function m(B,Z){Z&&(y("Patches"),B.o=[],B.v=[],B.s=Z)}function j(B){O(B),B.p.forEach(w),B.p=null}function O(B){B===er&&(er=B.l)}function x(B){return er={p:[],l:er,h:B,_:!0,m:0}}function w(B){var Z=B[el];0===Z.t||1===Z.t?Z.j():Z.O=!0}function S(B,Z){Z.m=Z.p.length;var et=Z.p[0],er=void 0!==B&&B!==et;return Z.h.S||y("ES5").P(Z,B,er),er?(et[el].g&&(j(Z),n(4)),t(B)&&(B=P(Z,B),Z.l||M(Z,B)),Z.o&&y("Patches").M(et[el].u,B,Z.o,Z.v)):B=P(Z,et,[]),j(Z),Z.o&&Z.s(Z.o,Z.v),B!==es?B:void 0}function P(B,Z,et){if(h(Z))return Z;var er=Z[el];if(!er)return e(Z,function(en,eo){return g(B,er,Z,en,eo,et)},!0),Z;if(er.A!==B)return Z;if(!er.g)return M(B,er.u,!0),er.u;if(!er.R){er.R=!0,er.A.m--;var en=4===er.t||5===er.t?er.i=p(er.k):er.i,eo=en,ea=!1;3===er.t&&(eo=new Set(en),en.clear(),ea=!0),e(eo,function(Z,eo){return g(B,er,en,Z,eo,et,ea)}),M(B,en,!1),et&&B.o&&y("Patches").F(er,et,B.o,B.v)}return er.i}function g(B,Z,et,er,en,eo,ea){if(r(en)){var ei=P(B,en,eo&&Z&&3!==Z.t&&!u(Z.N,er)?eo.concat(er):void 0);if(f(et,er,ei),!r(ei))return;B._=!1}else ea&&et.add(en);if(t(en)&&!h(en)){if(!B.h.D&&B.m<1)return;P(B,en),Z&&Z.A.l||M(B,en)}}function M(B,Z,et){void 0===et&&(et=!1),!B.l&&B.h.D&&B._&&l(Z,et)}function A(B,Z){var et=B[el];return(et?s(et):B)[Z]}function z(B,Z){if(Z in B)for(var et=Object.getPrototypeOf(B);et;){var er=Object.getOwnPropertyDescriptor(et,Z);if(er)return er;et=Object.getPrototypeOf(et)}}function E(B){B.g||(B.g=!0,B.l&&E(B.l))}function R(B){B.i||(B.i=p(B.u))}function k(B,Z,et){var en,eo,ea,ei,es,eu,el,ec=c(Z)?y("MapSet").K(Z,et):v(Z)?y("MapSet").$(Z,et):B.S?(ea=eo={t:(en=Array.isArray(Z))?1:0,A:et?et.A:er,g:!1,R:!1,N:{},l:et,u:Z,k:null,i:null,j:null,C:!1},ei=eg,en&&(ea=[eo],ei=ey),eu=(es=Proxy.revocable(ea,ei)).revoke,el=es.proxy,eo.k=el,eo.j=eu,el):y("ES5").I(Z,et);return(et?et.A:er).p.push(ec),ec}function F(B){return r(B)||n(22,B),function n(B){if(!t(B))return B;var Z,et=B[el],er=i(B);if(et){if(!et.g&&(et.t<4||!y("ES5").J(et)))return et.u;et.R=!0,Z=N(B,er),et.R=!1}else Z=N(B,er);return e(Z,function(B,er){et&&o(et.u,B)===er||f(Z,B,n(er))}),3===er?new Set(Z):Z}(B)}function N(B,Z){switch(Z){case 2:return new Map(B);case 3:return Array.from(B)}return p(B)}function D(){function n(Z,et){var er=B[Z];return er?er.enumerable=et:B[Z]=er={configurable:!0,enumerable:et,get:function(){return eg.get(this[el],Z)},set:function(B){eg.set(this[el],Z,B)}},er}function t(B){for(var Z=B.length-1;Z>=0;Z--){var et=B[Z][el];if(!et.g)switch(et.t){case 5:o(et)&&E(et);break;case 4:i(et)&&E(et)}}}function i(B){for(var Z=B.u,et=B.k,er=ef(et),en=er.length-1;en>=0;en--){var eo=er[en];if(eo!==el){var ea=Z[eo];if(void 0===ea&&!u(Z,eo))return!0;var ei=et[eo],es=ei&&ei[el];if(es?es.u!==ea:!a(ei,ea))return!0}}var eu=!!Z[el];return er.length!==ef(Z).length+(eu?0:1)}function o(B){var Z=B.k;if(Z.length!==B.u.length)return!0;var et=Object.getOwnPropertyDescriptor(Z,Z.length-1);if(et&&!et.get)return!0;for(var er=0;er<Z.length;er++)if(!Z.hasOwnProperty(er))return!0;return!1}var B={};_("ES5",{I:function(B,Z){var et=Array.isArray(B),en=function(B,Z){if(B){for(var et=Array(Z.length),er=0;er<Z.length;er++)Object.defineProperty(et,""+er,n(er,!0));return et}var en=ep(Z);delete en[el];for(var eo=ef(en),ea=0;ea<eo.length;ea++){var ei=eo[ea];en[ei]=n(ei,B||!!en[ei].enumerable)}return Object.create(Object.getPrototypeOf(Z),en)}(et,B),eo={t:et?5:4,A:Z?Z.A:er,g:!1,R:!1,N:{},l:Z,u:B,k:en,i:null,O:!1,C:!1};return Object.defineProperty(en,el,{value:eo,writable:!0}),en},P:function(B,Z,et){et?r(Z)&&Z[el].A===B&&t(B.p):(B.o&&function n(B){if(B&&"object"==typeof B){var Z=B[el];if(Z){var et=Z.u,er=Z.k,en=Z.N,eo=Z.t;if(4===eo)e(er,function(B){B!==el&&(void 0!==et[B]||u(et,B)?en[B]||n(er[B]):(en[B]=!0,E(Z)))}),e(et,function(B){void 0!==er[B]||u(er,B)||(en[B]=!1,E(Z))});else if(5===eo){if(o(Z)&&(E(Z),en.length=!0),er.length<et.length)for(var ea=er.length;ea<et.length;ea++)en[ea]=!1;else for(var ei=et.length;ei<er.length;ei++)en[ei]=!0;for(var es=Math.min(er.length,et.length),eu=0;eu<es;eu++)er.hasOwnProperty(eu)||(en[eu]=!0),void 0===en[eu]&&n(er[eu])}}}}(B.p[0]),t(B.p))},J:function(B){return 4===B.t?i(B):o(B)}})}function K(){function f(B){if(!t(B))return B;if(Array.isArray(B))return B.map(f);if(c(B))return new Map(Array.from(B.entries()).map(function(B){return[B[0],f(B[1])]}));if(v(B))return new Set(Array.from(B).map(f));var Z=Object.create(Object.getPrototypeOf(B));for(var et in B)Z[et]=f(B[et]);return u(B,eu)&&(Z[eu]=B[eu]),Z}function a(B){return r(B)?f(B):B}_("Patches",{W:function(B,Z){return Z.forEach(function(Z){for(var et=Z.path,er=Z.op,en=B,eo=0;eo<et.length-1;eo++){var ea=i(en),ei=et[eo];"string"!=typeof ei&&"number"!=typeof ei&&(ei=""+ei),0!==ea&&1!==ea||"__proto__"!==ei&&"constructor"!==ei||n(24),"function"==typeof en&&"prototype"===ei&&n(24),"object"!=typeof(en=o(en,ei))&&n(15,et.join("/"))}var es=i(en),eu=f(Z.value),el=et[et.length-1];switch(er){case"replace":switch(es){case 2:return en.set(el,eu);case 3:n(16);default:return en[el]=eu}case"add":switch(es){case 1:return"-"===el?en.push(eu):en.splice(el,0,eu);case 2:return en.set(el,eu);case 3:return en.add(eu);default:return en[el]=eu}case"remove":switch(es){case 1:return en.splice(el,1);case 2:return en.delete(el);case 3:return en.delete(Z.value);default:return delete en[el]}default:n(17,er)}}),B},F:function(B,Z,et,er){var en,eo,ea,ei,es;switch(B.t){case 0:case 4:case 2:return en=B.u,eo=B.i,void e(B.N,function(B,ea){var ei=o(en,B),es=o(eo,B),eu=ea?u(en,B)?"replace":"add":"remove";if(ei!==es||"replace"!==eu){var el=Z.concat(B);et.push("remove"===eu?{op:eu,path:el}:{op:eu,path:el,value:es}),er.push("add"===eu?{op:"remove",path:el}:"remove"===eu?{op:"add",path:el,value:a(ei)}:{op:"replace",path:el,value:a(ei)})}});case 5:case 1:return function(B,Z,et,er){var en=B.u,eo=B.N,ea=B.i;if(ea.length<en.length){var ei=[ea,en];en=ei[0],ea=ei[1];var es=[er,et];et=es[0],er=es[1]}for(var eu=0;eu<en.length;eu++)if(eo[eu]&&ea[eu]!==en[eu]){var el=Z.concat([eu]);et.push({op:"replace",path:el,value:a(ea[eu])}),er.push({op:"replace",path:el,value:a(en[eu])})}for(var ec=en.length;ec<ea.length;ec++){var ed=Z.concat([ec]);et.push({op:"add",path:ed,value:a(ea[ec])})}en.length<ea.length&&er.push({op:"replace",path:Z.concat(["length"]),value:en.length})}(B,Z,et,er);case 3:return ea=B.u,ei=B.i,es=0,void(ea.forEach(function(B){if(!ei.has(B)){var en=Z.concat([es]);et.push({op:"remove",path:en,value:B}),er.unshift({op:"add",path:en,value:B})}es++}),es=0,ei.forEach(function(B){if(!ea.has(B)){var en=Z.concat([es]);et.push({op:"add",path:en,value:B}),er.unshift({op:"remove",path:en,value:B})}es++}))}},M:function(B,Z,et,er){et.push({op:"replace",path:[],value:Z===es?void 0:Z}),er.push({op:"replace",path:[],value:B})}})}function $(){function r(B,Z){function t(){this.constructor=B}f(B,Z),B.prototype=(t.prototype=Z.prototype,new t)}function i(B){B.i||(B.N=new Map,B.i=new Map(B.u))}function u(B){B.i||(B.i=new Set,B.u.forEach(function(Z){if(t(Z)){var et=k(B.A.h,Z,B);B.p.set(Z,et),B.i.add(et)}else B.i.add(Z)}))}function o(B){B.O&&n(3,JSON.stringify(s(B)))}var f=function(B,Z){return(f=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(B,Z){B.__proto__=Z}||function(B,Z){for(var et in Z)Z.hasOwnProperty(et)&&(B[et]=Z[et])})(B,Z)},B=function(){function n(B,Z){return this[el]={t:2,l:Z,A:Z?Z.A:er,g:!1,R:!1,i:void 0,N:void 0,u:B,k:this,C:!1,O:!1},this}r(n,Map);var B=n.prototype;return Object.defineProperty(B,"size",{get:function(){return s(this[el]).size}}),B.has=function(B){return s(this[el]).has(B)},B.set=function(B,Z){var et=this[el];return o(et),s(et).has(B)&&s(et).get(B)===Z||(i(et),E(et),et.N.set(B,!0),et.i.set(B,Z),et.N.set(B,!0)),this},B.delete=function(B){if(!this.has(B))return!1;var Z=this[el];return o(Z),i(Z),E(Z),Z.u.has(B)?Z.N.set(B,!1):Z.N.delete(B),Z.i.delete(B),!0},B.clear=function(){var B=this[el];o(B),s(B).size&&(i(B),E(B),B.N=new Map,e(B.u,function(Z){B.N.set(Z,!1)}),B.i.clear())},B.forEach=function(B,Z){var et=this;s(this[el]).forEach(function(er,en){B.call(Z,et.get(en),en,et)})},B.get=function(B){var Z=this[el];o(Z);var et=s(Z).get(B);if(Z.R||!t(et)||et!==Z.u.get(B))return et;var er=k(Z.A.h,et,Z);return i(Z),Z.i.set(B,er),er},B.keys=function(){return s(this[el]).keys()},B.values=function(){var B,Z=this,et=this.keys();return(B={})[ec]=function(){return Z.values()},B.next=function(){var B=et.next();return B.done?B:{done:!1,value:Z.get(B.value)}},B},B.entries=function(){var B,Z=this,et=this.keys();return(B={})[ec]=function(){return Z.entries()},B.next=function(){var B=et.next();if(B.done)return B;var er=Z.get(B.value);return{done:!1,value:[B.value,er]}},B},B[ec]=function(){return this.entries()},n}(),Z=function(){function n(B,Z){return this[el]={t:3,l:Z,A:Z?Z.A:er,g:!1,R:!1,i:void 0,u:B,k:this,p:new Map,O:!1,C:!1},this}r(n,Set);var B=n.prototype;return Object.defineProperty(B,"size",{get:function(){return s(this[el]).size}}),B.has=function(B){var Z=this[el];return o(Z),Z.i?!!Z.i.has(B)||!(!Z.p.has(B)||!Z.i.has(Z.p.get(B))):Z.u.has(B)},B.add=function(B){var Z=this[el];return o(Z),this.has(B)||(u(Z),E(Z),Z.i.add(B)),this},B.delete=function(B){if(!this.has(B))return!1;var Z=this[el];return o(Z),u(Z),E(Z),Z.i.delete(B)||!!Z.p.has(B)&&Z.i.delete(Z.p.get(B))},B.clear=function(){var B=this[el];o(B),s(B).size&&(u(B),E(B),B.i.clear())},B.values=function(){var B=this[el];return o(B),u(B),B.i.values()},B.entries=function(){var B=this[el];return o(B),u(B),B.i.entries()},B.keys=function(){return this.values()},B[ec]=function(){return this.values()},B.forEach=function(B,Z){for(var et=this.values(),er=et.next();!er.done;)B.call(Z,er.value,er.value,this),er=et.next()},n}();_("MapSet",{K:function(Z,et){return new B(Z,et)},$:function(B,et){return new Z(B,et)}})}Object.defineProperty(Z,"__esModule",{value:!0});var et,er,en="undefined"!=typeof Symbol&&"symbol"==typeof Symbol("x"),eo="undefined"!=typeof Map,ea="undefined"!=typeof Set,ei="undefined"!=typeof Proxy&&void 0!==Proxy.revocable&&"undefined"!=typeof Reflect,es=en?Symbol.for("immer-nothing"):((et={})["immer-nothing"]=!0,et),eu=en?Symbol.for("immer-draftable"):"__$immer_draftable",el=en?Symbol.for("immer-state"):"__$immer_state",ec="undefined"!=typeof Symbol&&Symbol.iterator||"@@iterator",ed=""+Object.prototype.constructor,ef="undefined"!=typeof Reflect&&Reflect.ownKeys?Reflect.ownKeys:void 0!==Object.getOwnPropertySymbols?function(B){return Object.getOwnPropertyNames(B).concat(Object.getOwnPropertySymbols(B))}:Object.getOwnPropertyNames,ep=Object.getOwnPropertyDescriptors||function(B){var Z={};return ef(B).forEach(function(et){Z[et]=Object.getOwnPropertyDescriptor(B,et)}),Z},eh={},eg={get:function(B,Z){if(Z===el)return B;var et,er,en=s(B);if(!u(en,Z))return(er=z(en,Z))?"value"in er?er.value:null===(et=er.get)||void 0===et?void 0:et.call(B.k):void 0;var eo=en[Z];return B.R||!t(eo)?eo:eo===A(B.u,Z)?(R(B),B.i[Z]=k(B.A.h,eo,B)):eo},has:function(B,Z){return Z in s(B)},ownKeys:function(B){return Reflect.ownKeys(s(B))},set:function(B,Z,et){var er=z(s(B),Z);if(null==er?void 0:er.set)return er.set.call(B.k,et),!0;if(!B.g){var en=A(s(B),Z),eo=null==en?void 0:en[el];if(eo&&eo.u===et)return B.i[Z]=et,B.N[Z]=!1,!0;if(a(et,en)&&(void 0!==et||u(B.u,Z)))return!0;R(B),E(B)}return B.i[Z]===et&&(void 0!==et||Z in B.i)||Number.isNaN(et)&&Number.isNaN(B.i[Z])||(B.i[Z]=et,B.N[Z]=!0),!0},deleteProperty:function(B,Z){return void 0!==A(B.u,Z)||Z in B.u?(B.N[Z]=!1,R(B),E(B)):delete B.N[Z],B.i&&delete B.i[Z],!0},getOwnPropertyDescriptor:function(B,Z){var et=s(B),er=Reflect.getOwnPropertyDescriptor(et,Z);return er?{writable:!0,configurable:1!==B.t||"length"!==Z,enumerable:er.enumerable,value:et[Z]}:er},defineProperty:function(){n(11)},getPrototypeOf:function(B){return Object.getPrototypeOf(B.u)},setPrototypeOf:function(){n(12)}},ey={};e(eg,function(B,Z){ey[B]=function(){return arguments[0]=arguments[0][0],Z.apply(this,arguments)}}),ey.deleteProperty=function(B,Z){return ey.set.call(this,B,Z,void 0)},ey.set=function(B,Z,et){return eg.set.call(this,B[0],Z,et,B[0])};var em=function(){function e(B){var Z=this;this.S=ei,this.D=!0,this.produce=function(B,et,er){if("function"==typeof B&&"function"!=typeof et){var en,eo=et;return et=B,function(B){var er=this;void 0===B&&(B=eo);for(var en=arguments.length,ea=Array(en>1?en-1:0),ei=1;ei<en;ei++)ea[ei-1]=arguments[ei];return Z.produce(B,function(B){var Z;return(Z=et).call.apply(Z,[er,B].concat(ea))})}}if("function"!=typeof et&&n(6),void 0!==er&&"function"!=typeof er&&n(7),t(B)){var ea=x(Z),ei=k(Z,B,void 0),eu=!0;try{en=et(ei),eu=!1}finally{eu?j(ea):O(ea)}return"undefined"!=typeof Promise&&en instanceof Promise?en.then(function(B){return m(ea,er),S(B,ea)},function(B){throw j(ea),B}):(m(ea,er),S(en,ea))}if(!B||"object"!=typeof B){if(void 0===(en=et(B))&&(en=B),en===es&&(en=void 0),Z.D&&l(en,!0),er){var el=[],ec=[];y("Patches").M(B,en,el,ec),er(el,ec)}return en}n(21,B)},this.produceWithPatches=function(B,et){if("function"==typeof B)return function(et){for(var er=arguments.length,en=Array(er>1?er-1:0),eo=1;eo<er;eo++)en[eo-1]=arguments[eo];return Z.produceWithPatches(et,function(Z){return B.apply(void 0,[Z].concat(en))})};var er,en,eo=Z.produce(B,et,function(B,Z){er=B,en=Z});return"undefined"!=typeof Promise&&eo instanceof Promise?eo.then(function(B){return[B,er,en]}):[eo,er,en]},"boolean"==typeof(null==B?void 0:B.useProxies)&&this.setUseProxies(B.useProxies),"boolean"==typeof(null==B?void 0:B.autoFreeze)&&this.setAutoFreeze(B.autoFreeze)}var B=e.prototype;return B.createDraft=function(B){t(B)||n(8),r(B)&&(B=F(B));var Z=x(this),et=k(this,B,void 0);return et[el].C=!0,O(Z),et},B.finishDraft=function(B,Z){var et=(B&&B[el]).A;return m(et,Z),S(void 0,et)},B.setAutoFreeze=function(B){this.D=B},B.setUseProxies=function(B){B&&!ei&&n(20),this.S=B},B.applyPatches=function(B,Z){for(et=Z.length-1;et>=0;et--){var et,er=Z[et];if(0===er.path.length&&"replace"===er.op){B=er.value;break}}et>-1&&(Z=Z.slice(et+1));var en=y("Patches").W;return r(B)?en(B,Z):this.produce(B,function(B){return en(B,Z)})},e}(),ev=new em,eb=ev.produce,eS=ev.produceWithPatches.bind(ev),eP=ev.setAutoFreeze.bind(ev),eO=ev.setUseProxies.bind(ev),e_=ev.applyPatches.bind(ev),ex=ev.createDraft.bind(ev),eR=ev.finishDraft.bind(ev);Z.Immer=em,Z.applyPatches=e_,Z.castDraft=function(B){return B},Z.castImmutable=function(B){return B},Z.createDraft=ex,Z.current=F,Z.default=eb,Z.enableAllPlugins=function(){D(),$(),K()},Z.enableES5=D,Z.enableMapSet=$,Z.enablePatches=K,Z.finishDraft=eR,Z.freeze=l,Z.immerable=eu,Z.isDraft=r,Z.isDraftable=t,Z.nothing=es,Z.original=function(B){return r(B)||n(23,B),B[el].u},Z.produce=eb,Z.produceWithPatches=eS,Z.setAutoFreeze=eP,Z.setUseProxies=eO},53241:(B,Z,et)=>{"use strict";B.exports=et(63550)},40632:B=>{B.exports={style:{fontFamily:"'__Inter_d65c78', '__Inter_Fallback_d65c78'",fontStyle:"normal"},className:"__className_d65c78",variable:"__variable_d65c78"}},28769:B=>{B.exports={style:{fontFamily:"'__Vazirmatn_54dde5', '__Vazirmatn_Fallback_54dde5'",fontStyle:"normal"},className:"__className_54dde5",variable:"__variable_54dde5"}},16879:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"addBasePath",{enumerable:!0,get:function(){return addBasePath}});let er=et(8549),en=et(76945);function addBasePath(B,Z){return(0,en.normalizePathTrailingSlash)((0,er.addPathPrefix)(B,""))}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},54401:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"addLocale",{enumerable:!0,get:function(){return addLocale}});let er=et(76945),addLocale=function(B){for(var Z=arguments.length,en=Array(Z>1?Z-1:0),eo=1;eo<Z;eo++)en[eo-1]=arguments[eo];return(0,er.normalizePathTrailingSlash)(et(56850).b(B,...en))};("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},95422:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"callServer",{enumerable:!0,get:function(){return callServer}});let er=et(13724);async function callServer(B,Z){let et=(0,er.getServerActionDispatcher)();if(!et)throw Error("Invariant: missing action dispatcher.");return new Promise((er,en)=>{et({actionId:B,actionArgs:Z,resolve:er,reject:en})})}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},43204:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"AppRouterAnnouncer",{enumerable:!0,get:function(){return AppRouterAnnouncer}});let er=et(9885),en=et(88908),eo="next-route-announcer";function getAnnouncerNode(){var B;let Z=document.getElementsByName(eo)[0];if(null==Z?void 0:null==(B=Z.shadowRoot)?void 0:B.childNodes[0])return Z.shadowRoot.childNodes[0];{let B=document.createElement(eo);B.style.cssText="position:absolute";let Z=document.createElement("div");Z.ariaLive="assertive",Z.id="__next-route-announcer__",Z.role="alert",Z.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal";let et=B.attachShadow({mode:"open"});return et.appendChild(Z),document.body.appendChild(B),Z}}function AppRouterAnnouncer(B){let{tree:Z}=B,[et,ea]=(0,er.useState)(null);(0,er.useEffect)(()=>{let B=getAnnouncerNode();return ea(B),()=>{let B=document.getElementsByTagName(eo)[0];(null==B?void 0:B.isConnected)&&document.body.removeChild(B)}},[]);let[ei,es]=(0,er.useState)(""),eu=(0,er.useRef)();return(0,er.useEffect)(()=>{let B="";if(document.title)B=document.title;else{let Z=document.querySelector("h1");Z&&(B=Z.innerText||Z.textContent||"")}void 0!==eu.current&&eu.current!==B&&es(B),eu.current=B},[Z]),et?(0,en.createPortal)(ei,et):null}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},54361:(B,Z)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{RSC:function(){return et},ACTION:function(){return er},NEXT_ROUTER_STATE_TREE:function(){return en},NEXT_ROUTER_PREFETCH:function(){return eo},NEXT_URL:function(){return ea},RSC_CONTENT_TYPE_HEADER:function(){return ei},RSC_VARY_HEADER:function(){return es},FLIGHT_PARAMETERS:function(){return eu},NEXT_RSC_UNION_QUERY:function(){return el}});let et="RSC",er="Next-Action",en="Next-Router-State-Tree",eo="Next-Router-Prefetch",ea="Next-Url",ei="text/x-component",es=et+", "+en+", "+eo+", "+ea,eu=[[et],[en],[eo]],el="_rsc";("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},13724:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{getServerActionDispatcher:function(){return getServerActionDispatcher},urlToUrlWithoutFlightMarker:function(){return urlToUrlWithoutFlightMarker},default:function(){return AppRouter}});let er=et(8425),en=er._(et(9885)),eo=et(82428),ea=et(77986),ei=et(3678),es=et(11706),eu=et(11736),el=et(79236),ec=et(35365),ed=et(29624),ef=et(34692),ep=et(16879),eh=et(43204),eg=et(87502),ey=et(52226),em=et(59880),ev=et(54361),eb=et(64978),eS=et(99760),eP=null,eO=null;function getServerActionDispatcher(){return eO}let e_={refresh:()=>{}};function urlToUrlWithoutFlightMarker(B){let Z=new URL(B,location.origin);return Z.searchParams.delete(ev.NEXT_RSC_UNION_QUERY),Z}function isExternalURL(B){return B.origin!==window.location.origin}function HistoryUpdater(B){let{tree:Z,pushRef:et,canonicalUrl:er,sync:eo}=B;return(0,en.useInsertionEffect)(()=>{let B={__NA:!0,tree:Z};et.pendingPush&&(0,es.createHrefFromUrl)(new URL(window.location.href))!==er?(et.pendingPush=!1,window.history.pushState(B,"",er)):window.history.replaceState(B,"",er),eo()},[Z,et,er,eo]),null}let createEmptyCacheNode=()=>({status:eo.CacheStates.LAZY_INITIALIZED,data:null,subTreeData:null,parallelRoutes:new Map});function useServerActionDispatcher(B){let Z=(0,en.useCallback)(Z=>{(0,en.startTransition)(()=>{B({...Z,type:ei.ACTION_SERVER_ACTION,mutable:{globalMutable:e_},cache:createEmptyCacheNode()})})},[B]);eO=Z}function useChangeByServerResponse(B){return(0,en.useCallback)((Z,et,er)=>{(0,en.startTransition)(()=>{B({type:ei.ACTION_SERVER_PATCH,flightData:et,previousTree:Z,overrideCanonicalUrl:er,cache:createEmptyCacheNode(),mutable:{globalMutable:e_}})})},[B])}function useNavigate(B){return(0,en.useCallback)((Z,et,er,en)=>{let eo=new URL((0,ep.addBasePath)(Z),location.href);return e_.pendingNavigatePath=(0,es.createHrefFromUrl)(eo),B({type:ei.ACTION_NAVIGATE,url:eo,isExternalUrl:isExternalURL(eo),locationSearch:location.search,forceOptimisticNavigation:er,shouldScroll:null==en||en,navigateType:et,cache:createEmptyCacheNode(),mutable:{globalMutable:e_}})},[B])}function Router(B){let{buildId:Z,initialHead:et,initialTree:er,initialCanonicalUrl:es,children:ec,assetPrefix:ev}=B,eO=(0,en.useMemo)(()=>(0,ed.createInitialRouterState)({buildId:Z,children:ec,initialCanonicalUrl:es,initialTree:er,initialParallelRoutes:eP,isServer:!0,location:null,initialHead:et}),[Z,ec,es,er,et]),[{tree:ex,cache:eR,prefetchCache:ew,pushRef:eE,focusAndScrollRef:ej,canonicalUrl:eC,nextUrl:eM},eA,ek]=(0,el.useReducerWithReduxDevtools)(ea.reducer,eO);(0,en.useEffect)(()=>{eP=null},[]);let{searchParams:eT,pathname:eN}=(0,en.useMemo)(()=>{let B=new URL(eC,"http://n");return{searchParams:B.searchParams,pathname:(0,eS.hasBasePath)(B.pathname)?(0,eb.removeBasePath)(B.pathname):B.pathname}},[eC]),eL=useChangeByServerResponse(eA),eD=useNavigate(eA);useServerActionDispatcher(eA);let eI=(0,en.useMemo)(()=>{let B={back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(B,Z)=>{if((0,ef.isBot)(window.navigator.userAgent))return;let et=new URL((0,ep.addBasePath)(B),location.href);isExternalURL(et)||(0,en.startTransition)(()=>{var B;eA({type:ei.ACTION_PREFETCH,url:et,kind:null!=(B=null==Z?void 0:Z.kind)?B:ei.PrefetchKind.FULL})})},replace:(B,Z)=>{void 0===Z&&(Z={}),(0,en.startTransition)(()=>{var et;eD(B,"replace",!!Z.forceOptimisticNavigation,null==(et=Z.scroll)||et)})},push:(B,Z)=>{void 0===Z&&(Z={}),(0,en.startTransition)(()=>{var et;eD(B,"push",!!Z.forceOptimisticNavigation,null==(et=Z.scroll)||et)})},refresh:()=>{(0,en.startTransition)(()=>{eA({type:ei.ACTION_REFRESH,cache:createEmptyCacheNode(),mutable:{globalMutable:e_},origin:window.location.origin})})},fastRefresh:()=>{throw Error("fastRefresh can only be used in development mode. Please use refresh instead.")}};return B},[eA,eD]);if((0,en.useEffect)(()=>{window.next&&(window.next.router=eI)},[eI]),(0,en.useEffect)(()=>{e_.refresh=eI.refresh},[eI.refresh]),(0,en.useEffect)(()=>{function handlePageShow(B){var Z;B.persisted&&(null==(Z=window.history.state)?void 0:Z.tree)&&eA({type:ei.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.tree})}return window.addEventListener("pageshow",handlePageShow),()=>{window.removeEventListener("pageshow",handlePageShow)}},[eA]),eE.mpaNavigation){if(e_.pendingMpaPath!==eC){let B=window.location;eE.pendingPush?B.assign(eC):B.replace(eC),e_.pendingMpaPath=eC}(0,en.use)((0,em.createInfinitePromise)())}let eF=(0,en.useCallback)(B=>{let{state:Z}=B;if(Z){if(!Z.__NA){window.location.reload();return}(0,en.startTransition)(()=>{eA({type:ei.ACTION_RESTORE,url:new URL(window.location.href),tree:Z.tree})})}},[eA]);(0,en.useEffect)(()=>(window.addEventListener("popstate",eF),()=>{window.removeEventListener("popstate",eF)}),[eF]);let eU=(0,en.useMemo)(()=>(0,ey.findHeadInCache)(eR,ex[1]),[eR,ex]),eq=en.default.createElement(eg.RedirectBoundary,null,eU,eR.subTreeData,en.default.createElement(eh.AppRouterAnnouncer,{tree:ex}));return en.default.createElement(en.default.Fragment,null,en.default.createElement(HistoryUpdater,{tree:ex,pushRef:eE,canonicalUrl:eC,sync:ek}),en.default.createElement(eu.PathnameContext.Provider,{value:eN},en.default.createElement(eu.SearchParamsContext.Provider,{value:eT},en.default.createElement(eo.GlobalLayoutRouterContext.Provider,{value:{buildId:Z,changeByServerResponse:eL,tree:ex,focusAndScrollRef:ej,nextUrl:eM}},en.default.createElement(eo.AppRouterContext.Provider,{value:eI},en.default.createElement(eo.LayoutRouterContext.Provider,{value:{childNodes:eR.parallelRoutes,tree:ex,url:eC}},eq))))))}function AppRouter(B){let{globalErrorComponent:Z,...et}=B;return en.default.createElement(ec.ErrorBoundary,{errorComponent:Z},en.default.createElement(Router,et))}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},64954:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"bailoutToClientRendering",{enumerable:!0,get:function(){return bailoutToClientRendering}});let er=et(61118),en=et(94749);function bailoutToClientRendering(){let B=en.staticGenerationAsyncStorage.getStore();return null!=B&&!!B.forceStatic||((null==B?void 0:B.isStaticGeneration)&&(0,er.throwWithNoSSR)(),!1)}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},53402:(B,Z,et)=>{"use strict";function clientHookInServerComponentError(B){}Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"clientHookInServerComponentError",{enumerable:!0,get:function(){return clientHookInServerComponentError}}),et(80085),et(9885),("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},35365:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{ErrorBoundaryHandler:function(){return ErrorBoundaryHandler},GlobalError:function(){return GlobalError},default:function(){return ei},ErrorBoundary:function(){return ErrorBoundary}});let er=et(80085),en=er._(et(9885)),eo=et(4979),ea={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},text:{fontSize:"14px",fontWeight:400,lineHeight:"28px",margin:"0 8px"}};function HandleISRError(B){let{error:Z}=B;if("function"==typeof fetch.__nextGetStaticStore){var et;let B=null==(et=fetch.__nextGetStaticStore())?void 0:et.getStore();if((null==B?void 0:B.isRevalidate)||(null==B?void 0:B.isStaticGeneration))throw console.error(Z),Z}return null}let ErrorBoundaryHandler=class ErrorBoundaryHandler extends en.default.Component{static getDerivedStateFromError(B){return{error:B}}static getDerivedStateFromProps(B,Z){return B.pathname!==Z.previousPathname&&Z.error?{error:null,previousPathname:B.pathname}:{error:Z.error,previousPathname:B.pathname}}render(){return this.state.error?en.default.createElement(en.default.Fragment,null,en.default.createElement(HandleISRError,{error:this.state.error}),this.props.errorStyles,this.props.errorScripts,en.default.createElement(this.props.errorComponent,{error:this.state.error,reset:this.reset})):this.props.children}constructor(B){super(B),this.reset=()=>{this.setState({error:null})},this.state={error:null,previousPathname:this.props.pathname}}};function GlobalError(B){let{error:Z}=B,et=null==Z?void 0:Z.digest;return en.default.createElement("html",{id:"__next_error__"},en.default.createElement("head",null),en.default.createElement("body",null,en.default.createElement(HandleISRError,{error:Z}),en.default.createElement("div",{style:ea.error},en.default.createElement("div",null,en.default.createElement("h2",{style:ea.text},"Application error: a "+(et?"server":"client")+"-side exception has occurred (see the "+(et?"server logs":"browser console")+" for more information)."),et?en.default.createElement("p",{style:ea.text},"Digest: "+et):null))))}let ei=GlobalError;function ErrorBoundary(B){let{errorComponent:Z,errorStyles:et,errorScripts:er,children:ea}=B,ei=(0,eo.usePathname)();return Z?en.default.createElement(ErrorBoundaryHandler,{pathname:ei,errorComponent:Z,errorStyles:et,errorScripts:er},ea):en.default.createElement(en.default.Fragment,null,ea)}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},45171:(B,Z)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{DYNAMIC_ERROR_CODE:function(){return et},DynamicServerError:function(){return DynamicServerError}});let et="DYNAMIC_SERVER_USAGE";let DynamicServerError=class DynamicServerError extends Error{constructor(B){super("Dynamic server usage: "+B),this.digest=et}};("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},59880:(B,Z)=>{"use strict";let et;function createInfinitePromise(){return et||(et=new Promise(()=>{})),et}Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"createInfinitePromise",{enumerable:!0,get:function(){return createInfinitePromise}}),("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},44900:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"default",{enumerable:!0,get:function(){return OuterLayoutRouter}}),et(80085);let er=et(8425),en=er._(et(9885));et(88908);let eo=et(82428),ea=et(79102),ei=et(59880),es=et(35365),eu=et(4538),el=et(94361),ec=et(87502),ed=et(44714),ef=et(31275),ep=et(84701),eh=et(8026);function walkAddRefetch(B,Z){if(B){let[et,er]=B,en=2===B.length;if((0,eu.matchSegment)(Z[0],et)&&Z[1].hasOwnProperty(er)){if(en){let B=walkAddRefetch(void 0,Z[1][er]);return[Z[0],{...Z[1],[er]:[B[0],B[1],B[2],"refetch"]}]}return[Z[0],{...Z[1],[er]:walkAddRefetch(B.slice(2),Z[1][er])}]}}return Z}function findDOMNode(B){return null}let eg=["bottom","height","left","right","top","width","x","y"];function shouldSkipElement(B){if(["sticky","fixed"].includes(getComputedStyle(B).position))return!0;let Z=B.getBoundingClientRect();return eg.every(B=>0===Z[B])}function topOfElementInViewport(B,Z){let et=B.getBoundingClientRect();return et.top>=0&&et.top<=Z}function getHashFragmentDomNode(B){var Z;return"top"===B?document.body:null!=(Z=document.getElementById(B))?Z:document.getElementsByName(B)[0]}let InnerScrollAndFocusHandler=class InnerScrollAndFocusHandler extends en.default.Component{componentDidMount(){this.handlePotentialScroll()}componentDidUpdate(){this.props.focusAndScrollRef.apply&&this.handlePotentialScroll()}render(){return this.props.children}constructor(...B){super(...B),this.handlePotentialScroll=()=>{let{focusAndScrollRef:B,segmentPath:Z}=this.props;if(B.apply){if(0!==B.segmentPaths.length&&!B.segmentPaths.some(B=>Z.every((Z,et)=>(0,eu.matchSegment)(Z,B[et]))))return;let et=null,er=B.hashFragment;if(er&&(et=getHashFragmentDomNode(er)),et||(et=findDOMNode(this)),!(et instanceof Element))return;for(;!(et instanceof HTMLElement)||shouldSkipElement(et);){if(null===et.nextElementSibling)return;et=et.nextElementSibling}B.apply=!1,B.hashFragment=null,B.segmentPaths=[],(0,el.handleSmoothScroll)(()=>{if(er){et.scrollIntoView();return}let B=document.documentElement,Z=B.clientHeight;!topOfElementInViewport(et,Z)&&(B.scrollTop=0,topOfElementInViewport(et,Z)||et.scrollIntoView())},{dontForceLayout:!0,onlyHashChange:B.onlyHashChange}),B.onlyHashChange=!1,et.focus()}}}};function ScrollAndFocusHandler(B){let{segmentPath:Z,children:et}=B,er=(0,en.useContext)(eo.GlobalLayoutRouterContext);if(!er)throw Error("invariant global layout router not mounted");return en.default.createElement(InnerScrollAndFocusHandler,{segmentPath:Z,focusAndScrollRef:er.focusAndScrollRef},et)}function InnerLayoutRouter(B){let{parallelRouterKey:Z,url:et,childNodes:er,childProp:es,segmentPath:eu,tree:el,cacheKey:ec}=B,ed=(0,en.useContext)(eo.GlobalLayoutRouterContext);if(!ed)throw Error("invariant global layout router not mounted");let{buildId:ef,changeByServerResponse:ep,tree:eg}=ed,ey=er.get(ec);if(es&&null!==es.current&&(ey?ey.status===eo.CacheStates.LAZY_INITIALIZED&&(ey.status=eo.CacheStates.READY,ey.subTreeData=es.current):(ey={status:eo.CacheStates.READY,data:null,subTreeData:es.current,parallelRoutes:new Map},er.set(ec,ey))),!ey||ey.status===eo.CacheStates.LAZY_INITIALIZED){let B=walkAddRefetch(["",...eu],eg);ey={status:eo.CacheStates.DATA_FETCH,data:(0,eh.createRecordFromThenable)((0,ea.fetchServerResponse)(new URL(et,location.origin),B,ed.nextUrl,ef)),subTreeData:null,head:ey&&ey.status===eo.CacheStates.LAZY_INITIALIZED?ey.head:void 0,parallelRoutes:ey&&ey.status===eo.CacheStates.LAZY_INITIALIZED?ey.parallelRoutes:new Map},er.set(ec,ey)}if(!ey)throw Error("Child node should always exist");if(ey.subTreeData&&ey.data)throw Error("Child node should not have both subTreeData and data");if(ey.data){let[B,Z]=(0,en.use)(ey.data);ey.data=null,setTimeout(()=>{(0,en.startTransition)(()=>{ep(eg,B,Z)})}),(0,en.use)((0,ei.createInfinitePromise)())}ey.subTreeData||(0,en.use)((0,ei.createInfinitePromise)());let em=en.default.createElement(eo.LayoutRouterContext.Provider,{value:{tree:el[1][Z],childNodes:ey.parallelRoutes,url:et}},ey.subTreeData);return em}function LoadingBoundary(B){let{children:Z,loading:et,loadingStyles:er,loadingScripts:eo,hasLoading:ea}=B;return ea?en.default.createElement(en.Suspense,{fallback:en.default.createElement(en.default.Fragment,null,er,eo,et)},Z):en.default.createElement(en.default.Fragment,null,Z)}function OuterLayoutRouter(B){let{parallelRouterKey:Z,segmentPath:et,childProp:er,error:ea,errorStyles:ei,errorScripts:el,templateStyles:eh,templateScripts:eg,loading:ey,loadingStyles:em,loadingScripts:ev,hasLoading:eb,template:eS,notFound:eP,notFoundStyles:eO,styles:e_}=B,ex=(0,en.useContext)(eo.LayoutRouterContext);if(!ex)throw Error("invariant expected layout router to be mounted");let{childNodes:eR,tree:ew,url:eE}=ex,ej=eR.get(Z);ej||(ej=new Map,eR.set(Z,ej));let eC=ew[1][Z][0],eM=er.segment,eA=(0,ef.getSegmentValue)(eC),ek=[eC];return en.default.createElement(en.default.Fragment,null,e_,ek.map(B=>{let e_=(0,eu.matchSegment)(B,eM),ex=(0,ef.getSegmentValue)(B),eR=(0,ep.createRouterCacheKey)(B);return en.default.createElement(eo.TemplateContext.Provider,{key:(0,ep.createRouterCacheKey)(B,!0),value:en.default.createElement(ScrollAndFocusHandler,{segmentPath:et},en.default.createElement(es.ErrorBoundary,{errorComponent:ea,errorStyles:ei,errorScripts:el},en.default.createElement(LoadingBoundary,{hasLoading:eb,loading:ey,loadingStyles:em,loadingScripts:ev},en.default.createElement(ed.NotFoundBoundary,{notFound:eP,notFoundStyles:eO},en.default.createElement(ec.RedirectBoundary,null,en.default.createElement(InnerLayoutRouter,{parallelRouterKey:Z,url:eE,tree:ew,childNodes:ej,childProp:e_?er:null,segmentPath:et,cacheKey:eR,isActive:eA===ex}))))))},eh,eg,eS)}))}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},4538:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{matchSegment:function(){return matchSegment},canSegmentBeOverridden:function(){return canSegmentBeOverridden}});let er=et(42290),matchSegment=(B,Z)=>"string"==typeof B?"string"==typeof Z&&B===Z:"string"!=typeof Z&&B[0]===Z[0]&&B[1]===Z[1],canSegmentBeOverridden=(B,Z)=>{var et;return!Array.isArray(B)&&!!Array.isArray(Z)&&(null==(et=(0,er.getSegmentParam)(B))?void 0:et.param)===Z[0]};("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},23094:(B,Z,et)=>{"use strict";function maybePostpone(B,Z){if(!B.isStaticGeneration||!B.experimental.ppr)return;let er=et(9885);"function"==typeof er.unstable_postpone&&er.unstable_postpone(Z)}Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"maybePostpone",{enumerable:!0,get:function(){return maybePostpone}}),("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},4979:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{ReadonlyURLSearchParams:function(){return ReadonlyURLSearchParams},useSearchParams:function(){return useSearchParams},usePathname:function(){return usePathname},ServerInsertedHTMLContext:function(){return es.ServerInsertedHTMLContext},useServerInsertedHTML:function(){return es.useServerInsertedHTML},useRouter:function(){return useRouter},useParams:function(){return useParams},useSelectedLayoutSegments:function(){return useSelectedLayoutSegments},useSelectedLayoutSegment:function(){return useSelectedLayoutSegment},redirect:function(){return eu.redirect},permanentRedirect:function(){return eu.permanentRedirect},RedirectType:function(){return eu.RedirectType},notFound:function(){return el.notFound}});let er=et(9885),en=et(82428),eo=et(11736),ea=et(53402),ei=et(31275),es=et(75753),eu=et(1612),el=et(71103),ec=Symbol("internal for urlsearchparams readonly");function readonlyURLSearchParamsError(){return Error("ReadonlyURLSearchParams cannot be modified")}let ReadonlyURLSearchParams=class ReadonlyURLSearchParams{[Symbol.iterator](){return this[ec][Symbol.iterator]()}append(){throw readonlyURLSearchParamsError()}delete(){throw readonlyURLSearchParamsError()}set(){throw readonlyURLSearchParamsError()}sort(){throw readonlyURLSearchParamsError()}constructor(B){this[ec]=B,this.entries=B.entries.bind(B),this.forEach=B.forEach.bind(B),this.get=B.get.bind(B),this.getAll=B.getAll.bind(B),this.has=B.has.bind(B),this.keys=B.keys.bind(B),this.values=B.values.bind(B),this.toString=B.toString.bind(B),this.size=B.size}};function useSearchParams(){(0,ea.clientHookInServerComponentError)("useSearchParams");let B=(0,er.useContext)(eo.SearchParamsContext),Z=(0,er.useMemo)(()=>B?new ReadonlyURLSearchParams(B):null,[B]);{let{bailoutToClientRendering:B}=et(64954);B()}return Z}function usePathname(){return(0,ea.clientHookInServerComponentError)("usePathname"),(0,er.useContext)(eo.PathnameContext)}function useRouter(){(0,ea.clientHookInServerComponentError)("useRouter");let B=(0,er.useContext)(en.AppRouterContext);if(null===B)throw Error("invariant expected app router to be mounted");return B}function getSelectedParams(B,Z){void 0===Z&&(Z={});let et=B[1];for(let B of Object.values(et)){let et=B[0],er=Array.isArray(et),en=er?et[1]:et;if(!en||en.startsWith("__PAGE__"))continue;let eo=er&&("c"===et[2]||"oc"===et[2]);eo?Z[et[0]]=et[1].split("/"):er&&(Z[et[0]]=et[1]),Z=getSelectedParams(B,Z)}return Z}function useParams(){(0,ea.clientHookInServerComponentError)("useParams");let B=(0,er.useContext)(en.GlobalLayoutRouterContext),Z=(0,er.useContext)(eo.PathParamsContext);return(0,er.useMemo)(()=>(null==B?void 0:B.tree)?getSelectedParams(B.tree):Z,[null==B?void 0:B.tree,Z])}function getSelectedLayoutSegmentPath(B,Z,et,er){let en;if(void 0===et&&(et=!0),void 0===er&&(er=[]),et)en=B[1][Z];else{var eo;let Z=B[1];en=null!=(eo=Z.children)?eo:Object.values(Z)[0]}if(!en)return er;let ea=en[0],es=(0,ei.getSegmentValue)(ea);return!es||es.startsWith("__PAGE__")?er:(er.push(es),getSelectedLayoutSegmentPath(en,Z,!1,er))}function useSelectedLayoutSegments(B){void 0===B&&(B="children"),(0,ea.clientHookInServerComponentError)("useSelectedLayoutSegments");let{tree:Z}=(0,er.useContext)(en.LayoutRouterContext);return getSelectedLayoutSegmentPath(Z,B)}function useSelectedLayoutSegment(B){void 0===B&&(B="children"),(0,ea.clientHookInServerComponentError)("useSelectedLayoutSegment");let Z=useSelectedLayoutSegments(B);return 0===Z.length?null:Z[0]}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},44714:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"NotFoundBoundary",{enumerable:!0,get:function(){return NotFoundBoundary}});let er=et(80085),en=er._(et(9885)),eo=et(4979);let NotFoundErrorBoundary=class NotFoundErrorBoundary extends en.default.Component{static getDerivedStateFromError(B){if((null==B?void 0:B.digest)==="NEXT_NOT_FOUND")return{notFoundTriggered:!0};throw B}static getDerivedStateFromProps(B,Z){return B.pathname!==Z.previousPathname&&Z.notFoundTriggered?{notFoundTriggered:!1,previousPathname:B.pathname}:{notFoundTriggered:Z.notFoundTriggered,previousPathname:B.pathname}}render(){return this.state.notFoundTriggered?en.default.createElement(en.default.Fragment,null,en.default.createElement("meta",{name:"robots",content:"noindex"}),!1,this.props.notFoundStyles,this.props.notFound):this.props.children}constructor(B){super(B),this.state={notFoundTriggered:!!B.asNotFound,previousPathname:B.pathname}}};function NotFoundBoundary(B){let{notFound:Z,notFoundStyles:et,asNotFound:er,children:ea}=B,ei=(0,eo.usePathname)();return Z?en.default.createElement(NotFoundErrorBoundary,{pathname:ei,notFound:Z,notFoundStyles:et,asNotFound:er},ea):en.default.createElement(en.default.Fragment,null,ea)}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},71103:(B,Z)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{notFound:function(){return notFound},isNotFoundError:function(){return isNotFoundError}});let et="NEXT_NOT_FOUND";function notFound(){let B=Error(et);throw B.digest=et,B}function isNotFoundError(B){return(null==B?void 0:B.digest)===et}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},8862:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"PromiseQueue",{enumerable:!0,get:function(){return PromiseQueue}});let er=et(13592),en=et(94941);var eo=en._("_maxConcurrency"),ea=en._("_runningCount"),ei=en._("_queue"),es=en._("_processNext");let PromiseQueue=class PromiseQueue{enqueue(B){let Z,et;let en=new Promise((B,er)=>{Z=B,et=er}),task=async()=>{try{er._(this,ea)[ea]++;let et=await B();Z(et)}catch(B){et(B)}finally{er._(this,ea)[ea]--,er._(this,es)[es]()}};return er._(this,ei)[ei].push({promiseFn:en,task}),er._(this,es)[es](),en}bump(B){let Z=er._(this,ei)[ei].findIndex(Z=>Z.promiseFn===B);if(Z>-1){let B=er._(this,ei)[ei].splice(Z,1)[0];er._(this,ei)[ei].unshift(B),er._(this,es)[es](!0)}}constructor(B=5){Object.defineProperty(this,es,{value:processNext}),Object.defineProperty(this,eo,{writable:!0,value:void 0}),Object.defineProperty(this,ea,{writable:!0,value:void 0}),Object.defineProperty(this,ei,{writable:!0,value:void 0}),er._(this,eo)[eo]=B,er._(this,ea)[ea]=0,er._(this,ei)[ei]=[]}};function processNext(B){if(void 0===B&&(B=!1),(er._(this,ea)[ea]<er._(this,eo)[eo]||B)&&er._(this,ei)[ei].length>0){var Z;null==(Z=er._(this,ei)[ei].shift())||Z.task()}}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},87502:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{RedirectErrorBoundary:function(){return RedirectErrorBoundary},RedirectBoundary:function(){return RedirectBoundary}});let er=et(8425),en=er._(et(9885)),eo=et(4979),ea=et(1612);function HandleRedirect(B){let{redirect:Z,reset:et,redirectType:er}=B,ei=(0,eo.useRouter)();return(0,en.useEffect)(()=>{en.default.startTransition(()=>{er===ea.RedirectType.push?ei.push(Z,{}):ei.replace(Z,{}),et()})},[Z,er,et,ei]),null}let RedirectErrorBoundary=class RedirectErrorBoundary extends en.default.Component{static getDerivedStateFromError(B){if((0,ea.isRedirectError)(B)){let Z=(0,ea.getURLFromRedirectError)(B),et=(0,ea.getRedirectTypeFromError)(B);return{redirect:Z,redirectType:et}}throw B}render(){let{redirect:B,redirectType:Z}=this.state;return null!==B&&null!==Z?en.default.createElement(HandleRedirect,{redirect:B,redirectType:Z,reset:()=>this.setState({redirect:null})}):this.props.children}constructor(B){super(B),this.state={redirect:null,redirectType:null}}};function RedirectBoundary(B){let{children:Z}=B,et=(0,eo.useRouter)();return en.default.createElement(RedirectErrorBoundary,{router:et},Z)}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},1612:(B,Z,et)=>{"use strict";var er;Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{RedirectType:function(){return er},getRedirectError:function(){return getRedirectError},redirect:function(){return redirect},permanentRedirect:function(){return permanentRedirect},isRedirectError:function(){return isRedirectError},getURLFromRedirectError:function(){return getURLFromRedirectError},getRedirectTypeFromError:function(){return getRedirectTypeFromError}});let en=et(55403),eo="NEXT_REDIRECT";function getRedirectError(B,Z,et){void 0===et&&(et=!1);let er=Error(eo);er.digest=eo+";"+Z+";"+B+";"+et;let ea=en.requestAsyncStorage.getStore();return ea&&(er.mutableCookies=ea.mutableCookies),er}function redirect(B,Z){throw void 0===Z&&(Z="replace"),getRedirectError(B,Z,!1)}function permanentRedirect(B,Z){throw void 0===Z&&(Z="replace"),getRedirectError(B,Z,!0)}function isRedirectError(B){if("string"!=typeof(null==B?void 0:B.digest))return!1;let[Z,et,er,en]=B.digest.split(";",4);return Z===eo&&("replace"===et||"push"===et)&&"string"==typeof er&&("true"===en||"false"===en)}function getURLFromRedirectError(B){return isRedirectError(B)?B.digest.split(";",3)[2]:null}function getRedirectTypeFromError(B){if(!isRedirectError(B))throw Error("Not a redirect error");return B.digest.split(";",2)[1]}(function(B){B.push="push",B.replace="replace"})(er||(er={})),("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},45392:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"default",{enumerable:!0,get:function(){return RenderFromTemplateContext}});let er=et(8425),en=er._(et(9885)),eo=et(82428);function RenderFromTemplateContext(){let B=(0,en.useContext)(eo.TemplateContext);return en.default.createElement(en.default.Fragment,null,B)}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},51847:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"applyFlightData",{enumerable:!0,get:function(){return applyFlightData}});let er=et(82428),en=et(65929),eo=et(94059);function applyFlightData(B,Z,et,ea){void 0===ea&&(ea=!1);let[ei,es,eu]=et.slice(-3);return null!==es&&(3===et.length?(Z.status=er.CacheStates.READY,Z.subTreeData=es,(0,en.fillLazyItemsTillLeafWithHead)(Z,B,ei,eu,ea)):(Z.status=er.CacheStates.READY,Z.subTreeData=B.subTreeData,Z.parallelRoutes=new Map(B.parallelRoutes),(0,eo.fillCacheWithNewSubTreeData)(Z,B,et,ea)),!0)}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},69605:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return applyRouterStatePatchToTree}});let er=et(4538);function applyPatch(B,Z){let[et,en]=B,[eo,ea]=Z;if("__DEFAULT__"===eo&&"__DEFAULT__"!==et)return B;if((0,er.matchSegment)(et,eo)){let Z={};for(let B in en){let et=void 0!==ea[B];et?Z[B]=applyPatch(en[B],ea[B]):Z[B]=en[B]}for(let B in ea)Z[B]||(Z[B]=ea[B]);let er=[et,Z];return B[2]&&(er[2]=B[2]),B[3]&&(er[3]=B[3]),B[4]&&(er[4]=B[4]),er}return Z}function applyRouterStatePatchToTree(B,Z,et){let en;let[eo,ea,,,ei]=Z;if(1===B.length){let B=applyPatch(Z,et);return B}let[es,eu]=B;if(!(0,er.matchSegment)(es,eo))return null;let el=2===B.length;if(el)en=applyPatch(ea[eu],et);else if(null===(en=applyRouterStatePatchToTree(B.slice(2),ea[eu],et)))return null;let ec=[B[0],{...ea,[eu]:en}];return ei&&(ec[4]=!0),ec}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},6663:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{extractPathFromFlightRouterState:function(){return extractPathFromFlightRouterState},computeChangedPath:function(){return computeChangedPath}});let er=et(84265),en=et(392),eo=et(4538),removeLeadingSlash=B=>"/"===B[0]?B.slice(1):B,segmentToPathname=B=>"string"==typeof B?B:B[1];function normalizeSegments(B){return B.reduce((B,Z)=>""===(Z=removeLeadingSlash(Z))||(0,en.isGroupSegment)(Z)?B:B+"/"+Z,"")||"/"}function extractPathFromFlightRouterState(B){var Z;let et=Array.isArray(B[0])?B[0][1]:B[0];if("__DEFAULT__"===et||er.INTERCEPTION_ROUTE_MARKERS.some(B=>et.startsWith(B)))return;if(et.startsWith("__PAGE__"))return"";let en=[et],eo=null!=(Z=B[1])?Z:{},ea=eo.children?extractPathFromFlightRouterState(eo.children):void 0;if(void 0!==ea)en.push(ea);else for(let[B,Z]of Object.entries(eo)){if("children"===B)continue;let et=extractPathFromFlightRouterState(Z);void 0!==et&&en.push(et)}return normalizeSegments(en)}function computeChangedPathImpl(B,Z){let[et,en]=B,[ea,ei]=Z,es=segmentToPathname(et),eu=segmentToPathname(ea);if(er.INTERCEPTION_ROUTE_MARKERS.some(B=>es.startsWith(B)||eu.startsWith(B)))return"";if(!(0,eo.matchSegment)(et,ea)){var el;return null!=(el=extractPathFromFlightRouterState(Z))?el:""}for(let B in en)if(ei[B]){let Z=computeChangedPathImpl(en[B],ei[B]);if(null!==Z)return segmentToPathname(ea)+"/"+Z}return null}function computeChangedPath(B,Z){let et=computeChangedPathImpl(B,Z);return null==et||"/"===et?et:normalizeSegments(et.split("/"))}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},11706:(B,Z)=>{"use strict";function createHrefFromUrl(B,Z){return void 0===Z&&(Z=!0),B.pathname+B.search+(Z?B.hash:"")}Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"createHrefFromUrl",{enumerable:!0,get:function(){return createHrefFromUrl}}),("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},29624:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"createInitialRouterState",{enumerable:!0,get:function(){return createInitialRouterState}});let er=et(82428),en=et(11706),eo=et(65929),ea=et(6663);function createInitialRouterState(B){var Z;let{buildId:et,initialTree:ei,children:es,initialCanonicalUrl:eu,initialParallelRoutes:el,isServer:ec,location:ed,initialHead:ef}=B,ep={status:er.CacheStates.READY,data:null,subTreeData:es,parallelRoutes:ec?new Map:el};return(null===el||0===el.size)&&(0,eo.fillLazyItemsTillLeafWithHead)(ep,void 0,ei,ef),{buildId:et,tree:ei,cache:ep,prefetchCache:new Map,pushRef:{pendingPush:!1,mpaNavigation:!1},focusAndScrollRef:{apply:!1,onlyHashChange:!1,hashFragment:null,segmentPaths:[]},canonicalUrl:ed?(0,en.createHrefFromUrl)(ed):eu,nextUrl:null!=(Z=(0,ea.extractPathFromFlightRouterState)(ei)||(null==ed?void 0:ed.pathname))?Z:null}}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},78775:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"createOptimisticTree",{enumerable:!0,get:function(){return createOptimisticTree}});let er=et(4538);function createOptimisticTree(B,Z,et){let en;let[eo,ea,ei,es,eu]=Z||[null,{}],el=B[0],ec=1===B.length,ed=null!==eo&&(0,er.matchSegment)(eo,el),ef=Object.keys(ea).length>1,ep=!Z||!ed||ef,eh={};if(null!==eo&&ed&&(eh=ea),!ec&&!ef){let Z=createOptimisticTree(B.slice(1),eh?eh.children:null,et||ep);en=Z}let eg=[el,{...eh,...en?{children:en}:{}}];return ei&&(eg[2]=ei),!et&&ep?eg[3]="refetch":ed&&es&&(eg[3]=es),ed&&eu&&(eg[4]=eu),eg}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},8026:(B,Z)=>{"use strict";function createRecordFromThenable(B){return B.status="pending",B.then(Z=>{"pending"===B.status&&(B.status="fulfilled",B.value=Z)},Z=>{"pending"===B.status&&(B.status="rejected",B.reason=Z)}),B}Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"createRecordFromThenable",{enumerable:!0,get:function(){return createRecordFromThenable}}),("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},84701:(B,Z)=>{"use strict";function createRouterCacheKey(B,Z){return void 0===Z&&(Z=!1),Array.isArray(B)?(B[0]+"|"+B[1]+"|"+B[2]).toLowerCase():Z&&B.startsWith("__PAGE__")?"__PAGE__":B}Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"createRouterCacheKey",{enumerable:!0,get:function(){return createRouterCacheKey}}),("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},79102:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"fetchServerResponse",{enumerable:!0,get:function(){return fetchServerResponse}});let er=et(54361),en=et(13724),eo=et(95422),ea=et(3678),ei=et(30755),es=et(45082),{createFromFetch:eu}=et(12623);function doMpaNavigation(B){return[(0,en.urlToUrlWithoutFlightMarker)(B).toString(),void 0]}async function fetchServerResponse(B,Z,et,el,ec){let ed={[er.RSC]:"1",[er.NEXT_ROUTER_STATE_TREE]:encodeURIComponent(JSON.stringify(Z))};ec===ea.PrefetchKind.AUTO&&(ed[er.NEXT_ROUTER_PREFETCH]="1"),et&&(ed[er.NEXT_URL]=et);let ef=(0,ei.hexHash)([ed[er.NEXT_ROUTER_PREFETCH]||"0",ed[er.NEXT_ROUTER_STATE_TREE],ed[er.NEXT_URL]].join(","));try{let Z=new URL(B);Z.searchParams.set(er.NEXT_RSC_UNION_QUERY,ef);let et=await fetch(Z,{credentials:"same-origin",headers:ed}),ea=(0,en.urlToUrlWithoutFlightMarker)(et.url),ei=et.redirected?ea:void 0,ec=et.headers.get("content-type")||"",ep=!!et.headers.get(es.NEXT_DID_POSTPONE_HEADER);if(ec!==er.RSC_CONTENT_TYPE_HEADER||!et.ok)return B.hash&&(ea.hash=B.hash),doMpaNavigation(ea.toString());let[eh,eg]=await eu(Promise.resolve(et),{callServer:eo.callServer});if(el!==eh)return doMpaNavigation(et.url);return[eg,ei,ep]}catch(Z){return console.error("Failed to fetch RSC payload for "+B+". Falling back to browser navigation.",Z),[B.toString(),void 0]}}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},81924:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"fillCacheWithDataProperty",{enumerable:!0,get:function(){return fillCacheWithDataProperty}});let er=et(82428),en=et(84701);function fillCacheWithDataProperty(B,Z,et,eo,ea){void 0===ea&&(ea=!1);let ei=et.length<=2,[es,eu]=et,el=(0,en.createRouterCacheKey)(eu),ec=Z.parallelRoutes.get(es);if(!ec||ea&&Z.parallelRoutes.size>1)return{bailOptimistic:!0};let ed=B.parallelRoutes.get(es);ed&&ed!==ec||(ed=new Map(ec),B.parallelRoutes.set(es,ed));let ef=ec.get(el),ep=ed.get(el);if(ei){ep&&ep.data&&ep!==ef||ed.set(el,{status:er.CacheStates.DATA_FETCH,data:eo(),subTreeData:null,parallelRoutes:new Map});return}if(!ep||!ef){ep||ed.set(el,{status:er.CacheStates.DATA_FETCH,data:eo(),subTreeData:null,parallelRoutes:new Map});return}return ep===ef&&(ep={status:ep.status,data:ep.data,subTreeData:ep.subTreeData,parallelRoutes:new Map(ep.parallelRoutes)},ed.set(el,ep)),fillCacheWithDataProperty(ep,ef,et.slice(2),eo)}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},94059:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"fillCacheWithNewSubTreeData",{enumerable:!0,get:function(){return fillCacheWithNewSubTreeData}});let er=et(82428),en=et(32582),eo=et(65929),ea=et(84701);function fillCacheWithNewSubTreeData(B,Z,et,ei){let es=et.length<=5,[eu,el]=et,ec=(0,ea.createRouterCacheKey)(el),ed=Z.parallelRoutes.get(eu);if(!ed)return;let ef=B.parallelRoutes.get(eu);ef&&ef!==ed||(ef=new Map(ed),B.parallelRoutes.set(eu,ef));let ep=ed.get(ec),eh=ef.get(ec);if(es){eh&&eh.data&&eh!==ep||(eh={status:er.CacheStates.READY,data:null,subTreeData:et[3],parallelRoutes:ep?new Map(ep.parallelRoutes):new Map},ep&&(0,en.invalidateCacheByRouterState)(eh,ep,et[2]),(0,eo.fillLazyItemsTillLeafWithHead)(eh,ep,et[2],et[4],ei),ef.set(ec,eh));return}eh&&ep&&(eh===ep&&(eh={status:eh.status,data:eh.data,subTreeData:eh.subTreeData,parallelRoutes:new Map(eh.parallelRoutes)},ef.set(ec,eh)),fillCacheWithNewSubTreeData(eh,ep,et.slice(2),ei))}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},65929:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return fillLazyItemsTillLeafWithHead}});let er=et(82428),en=et(84701);function fillLazyItemsTillLeafWithHead(B,Z,et,eo,ea){let ei=0===Object.keys(et[1]).length;if(ei){B.head=eo;return}for(let ei in et[1]){let es=et[1][ei],eu=es[0],el=(0,en.createRouterCacheKey)(eu);if(Z){let et=Z.parallelRoutes.get(ei);if(et){let Z=new Map(et),en=Z.get(el),eu=ea&&en?{status:en.status,data:en.data,subTreeData:en.subTreeData,parallelRoutes:new Map(en.parallelRoutes)}:{status:er.CacheStates.LAZY_INITIALIZED,data:null,subTreeData:null,parallelRoutes:new Map(null==en?void 0:en.parallelRoutes)};Z.set(el,eu),fillLazyItemsTillLeafWithHead(eu,en,es,eo,ea),B.parallelRoutes.set(ei,Z);continue}}let ec={status:er.CacheStates.LAZY_INITIALIZED,data:null,subTreeData:null,parallelRoutes:new Map},ed=B.parallelRoutes.get(ei);ed?ed.set(el,ec):B.parallelRoutes.set(ei,new Map([[el,ec]])),fillLazyItemsTillLeafWithHead(ec,void 0,es,eo,ea)}}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},16699:(B,Z)=>{"use strict";var et;function getPrefetchEntryCacheStatus(B){let{kind:Z,prefetchTime:et,lastUsedTime:er}=B;return Date.now()<(null!=er?er:et)+3e4?er?"reusable":"fresh":"auto"===Z&&Date.now()<et+3e5?"stale":"full"===Z&&Date.now()<et+3e5?"reusable":"expired"}Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{PrefetchCacheEntryStatus:function(){return et},getPrefetchEntryCacheStatus:function(){return getPrefetchEntryCacheStatus}}),function(B){B.fresh="fresh",B.reusable="reusable",B.expired="expired",B.stale="stale"}(et||(et={})),("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},43466:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"handleMutable",{enumerable:!0,get:function(){return handleMutable}});let er=et(6663);function handleMutable(B,Z){var et,en,eo,ea;let ei=null==(en=Z.shouldScroll)||en;return{buildId:B.buildId,canonicalUrl:null!=Z.canonicalUrl?Z.canonicalUrl===B.canonicalUrl?B.canonicalUrl:Z.canonicalUrl:B.canonicalUrl,pushRef:{pendingPush:null!=Z.pendingPush?Z.pendingPush:B.pushRef.pendingPush,mpaNavigation:null!=Z.mpaNavigation?Z.mpaNavigation:B.pushRef.mpaNavigation},focusAndScrollRef:{apply:!!ei&&((null==Z?void 0:Z.scrollableSegments)!==void 0||B.focusAndScrollRef.apply),onlyHashChange:!!Z.hashFragment&&B.canonicalUrl.split("#",1)[0]===(null==(et=Z.canonicalUrl)?void 0:et.split("#",1)[0]),hashFragment:ei?Z.hashFragment&&""!==Z.hashFragment?decodeURIComponent(Z.hashFragment.slice(1)):B.focusAndScrollRef.hashFragment:null,segmentPaths:ei?null!=(eo=null==Z?void 0:Z.scrollableSegments)?eo:B.focusAndScrollRef.segmentPaths:[]},cache:Z.cache?Z.cache:B.cache,prefetchCache:Z.prefetchCache?Z.prefetchCache:B.prefetchCache,tree:void 0!==Z.patchedTree?Z.patchedTree:B.tree,nextUrl:void 0!==Z.patchedTree?null!=(ea=(0,er.computeChangedPath)(B.tree,Z.patchedTree))?ea:B.canonicalUrl:B.nextUrl}}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},21986:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return invalidateCacheBelowFlightSegmentPath}});let er=et(84701);function invalidateCacheBelowFlightSegmentPath(B,Z,et){let en=et.length<=2,[eo,ea]=et,ei=(0,er.createRouterCacheKey)(ea),es=Z.parallelRoutes.get(eo);if(!es)return;let eu=B.parallelRoutes.get(eo);if(eu&&eu!==es||(eu=new Map(es),B.parallelRoutes.set(eo,eu)),en){eu.delete(ei);return}let el=es.get(ei),ec=eu.get(ei);ec&&el&&(ec===el&&(ec={status:ec.status,data:ec.data,subTreeData:ec.subTreeData,parallelRoutes:new Map(ec.parallelRoutes)},eu.set(ei,ec)),invalidateCacheBelowFlightSegmentPath(ec,el,et.slice(2)))}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},32582:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return invalidateCacheByRouterState}});let er=et(84701);function invalidateCacheByRouterState(B,Z,et){for(let en in et[1]){let eo=et[1][en][0],ea=(0,er.createRouterCacheKey)(eo),ei=Z.parallelRoutes.get(en);if(ei){let Z=new Map(ei);Z.delete(ea),B.parallelRoutes.set(en,Z)}}}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},90145:(B,Z)=>{"use strict";function isNavigatingToNewRootLayout(B,Z){let et=B[0],er=Z[0];if(Array.isArray(et)&&Array.isArray(er)){if(et[0]!==er[0]||et[2]!==er[2])return!0}else if(et!==er)return!0;if(B[4])return!Z[4];if(Z[4])return!0;let en=Object.values(B[1])[0],eo=Object.values(Z[1])[0];return!en||!eo||isNavigatingToNewRootLayout(en,eo)}Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return isNavigatingToNewRootLayout}}),("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},84879:(B,Z)=>{"use strict";function readRecordValue(B){if("fulfilled"===B.status)return B.value;throw B}Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"readRecordValue",{enumerable:!0,get:function(){return readRecordValue}}),("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},92755:(B,Z,et)=>{"use strict";function fastRefreshReducerNoop(B,Z){return B}Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"fastRefreshReducer",{enumerable:!0,get:function(){return er}}),et(79102),et(8026),et(84879),et(11706),et(69605),et(90145),et(28237),et(43466),et(51847);let er=fastRefreshReducerNoop;("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},52226:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"findHeadInCache",{enumerable:!0,get:function(){return findHeadInCache}});let er=et(84701);function findHeadInCache(B,Z){let et=0===Object.keys(Z).length;if(et)return B.head;for(let et in Z){let[en,eo]=Z[et],ea=B.parallelRoutes.get(et);if(!ea)continue;let ei=(0,er.createRouterCacheKey)(en),es=ea.get(ei);if(!es)continue;let eu=findHeadInCache(es,eo);if(eu)return eu}}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},31275:(B,Z)=>{"use strict";function getSegmentValue(B){return Array.isArray(B)?B[1]:B}Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"getSegmentValue",{enumerable:!0,get:function(){return getSegmentValue}}),("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},28237:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{handleExternalUrl:function(){return handleExternalUrl},navigateReducer:function(){return navigateReducer}});let er=et(82428),en=et(79102),eo=et(8026),ea=et(84879),ei=et(11706),es=et(21986),eu=et(81924),el=et(78775),ec=et(69605),ed=et(84320),ef=et(90145),ep=et(3678),eh=et(43466),eg=et(51847),ey=et(16699),em=et(18155),ev=et(31196);function handleExternalUrl(B,Z,et,er){return Z.previousTree=B.tree,Z.mpaNavigation=!0,Z.canonicalUrl=et,Z.pendingPush=er,Z.scrollableSegments=void 0,(0,eh.handleMutable)(B,Z)}function generateSegmentsFromPatch(B){let Z=[],[et,er]=B;if(0===Object.keys(er).length)return[[et]];for(let[B,en]of Object.entries(er))for(let er of generateSegmentsFromPatch(en))""===et?Z.push([B,...er]):Z.push([et,B,...er]);return Z}function addRefetchToLeafSegments(B,Z,et,en,eo){let ea=!1;B.status=er.CacheStates.READY,B.subTreeData=Z.subTreeData,B.parallelRoutes=new Map(Z.parallelRoutes);let ei=generateSegmentsFromPatch(en).map(B=>[...et,...B]);for(let et of ei){let er=(0,eu.fillCacheWithDataProperty)(B,Z,et,eo);(null==er?void 0:er.bailOptimistic)||(ea=!0)}return ea}function navigateReducer(B,Z){let{url:et,isExternalUrl:eb,navigateType:eS,cache:eP,mutable:eO,forceOptimisticNavigation:e_,shouldScroll:ex}=Z,{pathname:eR,hash:ew}=et,eE=(0,ei.createHrefFromUrl)(et),ej="push"===eS;(0,em.prunePrefetchCache)(B.prefetchCache);let eC=JSON.stringify(eO.previousTree)===JSON.stringify(B.tree);if(eC)return(0,eh.handleMutable)(B,eO);if(eb)return handleExternalUrl(B,eO,et.toString(),ej);let eM=B.prefetchCache.get((0,ei.createHrefFromUrl)(et,!1));if(e_&&(null==eM?void 0:eM.kind)!==ep.PrefetchKind.TEMPORARY){let Z=eR.split("/");Z.push("__PAGE__");let ea=(0,el.createOptimisticTree)(Z,B.tree,!1),es={...eP};es.status=er.CacheStates.READY,es.subTreeData=B.cache.subTreeData,es.parallelRoutes=new Map(B.cache.parallelRoutes);let ec=null,ed=Z.slice(1).map(B=>["children",B]).flat(),ef=(0,eu.fillCacheWithDataProperty)(es,B.cache,ed,()=>(ec||(ec=(0,eo.createRecordFromThenable)((0,en.fetchServerResponse)(et,ea,B.nextUrl,B.buildId))),ec),!0);if(!(null==ef?void 0:ef.bailOptimistic))return eO.previousTree=B.tree,eO.patchedTree=ea,eO.pendingPush=ej,eO.hashFragment=ew,eO.shouldScroll=ex,eO.scrollableSegments=[],eO.cache=es,eO.canonicalUrl=eE,B.prefetchCache.set((0,ei.createHrefFromUrl)(et,!1),{data:ec?(0,eo.createRecordFromThenable)(Promise.resolve(ec)):null,kind:ep.PrefetchKind.TEMPORARY,prefetchTime:Date.now(),treeAtTimeOfPrefetch:B.tree,lastUsedTime:Date.now()}),(0,eh.handleMutable)(B,eO)}if(!eM){let Z=(0,eo.createRecordFromThenable)((0,en.fetchServerResponse)(et,B.tree,B.nextUrl,B.buildId,void 0)),er={data:(0,eo.createRecordFromThenable)(Promise.resolve(Z)),kind:ep.PrefetchKind.TEMPORARY,prefetchTime:Date.now(),treeAtTimeOfPrefetch:B.tree,lastUsedTime:null};B.prefetchCache.set((0,ei.createHrefFromUrl)(et,!1),er),eM=er}let eA=(0,ey.getPrefetchEntryCacheStatus)(eM),{treeAtTimeOfPrefetch:ek,data:eT}=eM;ev.prefetchQueue.bump(eT);let[eN,eL,eD]=(0,ea.readRecordValue)(eT);if(eM.lastUsedTime||(eM.lastUsedTime=Date.now()),"string"==typeof eN)return handleExternalUrl(B,eO,eN,ej);let eI=B.tree,eF=B.cache,eU=[];for(let Z of eN){let ea=Z.slice(0,-4),ei=Z.slice(-3)[0],eu=["",...ea],el=(0,ec.applyRouterStatePatchToTree)(eu,eI,ei);if(null===el&&(el=(0,ec.applyRouterStatePatchToTree)(eu,ek,ei)),null!==el){if((0,ef.isNavigatingToNewRootLayout)(eI,el))return handleExternalUrl(B,eO,eE,ej);let ec=!eD&&(0,eg.applyFlightData)(eF,eP,Z,"auto"===eM.kind&&eA===ey.PrefetchCacheEntryStatus.reusable);ec||eA!==ey.PrefetchCacheEntryStatus.stale||(ec=addRefetchToLeafSegments(eP,eF,ea,ei,()=>(0,eo.createRecordFromThenable)((0,en.fetchServerResponse)(et,eI,B.nextUrl,B.buildId))));let ep=(0,ed.shouldHardNavigate)(eu,eI);for(let B of(ep?(eP.status=er.CacheStates.READY,eP.subTreeData=eF.subTreeData,(0,es.invalidateCacheBelowFlightSegmentPath)(eP,eF,ea),eO.cache=eP):ec&&(eO.cache=eP),eF=eP,eI=el,generateSegmentsFromPatch(ei))){let Z=[...ea,...B];"__DEFAULT__"!==Z[Z.length-1]&&eU.push(Z)}}}return eO.previousTree=B.tree,eO.patchedTree=eI,eO.canonicalUrl=eL?(0,ei.createHrefFromUrl)(eL):eE,eO.pendingPush=ej,eO.scrollableSegments=eU,eO.hashFragment=ew,eO.shouldScroll=ex,(0,eh.handleMutable)(B,eO)}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},31196:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{prefetchQueue:function(){return el},prefetchReducer:function(){return prefetchReducer}});let er=et(11706),en=et(79102),eo=et(3678),ea=et(8026),ei=et(18155),es=et(54361),eu=et(8862),el=new eu.PromiseQueue(5);function prefetchReducer(B,Z){(0,ei.prunePrefetchCache)(B.prefetchCache);let{url:et}=Z;et.searchParams.delete(es.NEXT_RSC_UNION_QUERY);let eu=(0,er.createHrefFromUrl)(et,!1),ec=B.prefetchCache.get(eu);if(ec&&(ec.kind===eo.PrefetchKind.TEMPORARY&&B.prefetchCache.set(eu,{...ec,kind:Z.kind}),!(ec.kind===eo.PrefetchKind.AUTO&&Z.kind===eo.PrefetchKind.FULL)))return B;let ed=(0,ea.createRecordFromThenable)(el.enqueue(()=>(0,en.fetchServerResponse)(et,B.tree,B.nextUrl,B.buildId,Z.kind)));return B.prefetchCache.set(eu,{treeAtTimeOfPrefetch:B.tree,data:ed,kind:Z.kind,prefetchTime:Date.now(),lastUsedTime:null}),B}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},18155:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"prunePrefetchCache",{enumerable:!0,get:function(){return prunePrefetchCache}});let er=et(16699);function prunePrefetchCache(B){for(let[Z,et]of B)(0,er.getPrefetchEntryCacheStatus)(et)===er.PrefetchCacheEntryStatus.expired&&B.delete(Z)}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},58038:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"refreshReducer",{enumerable:!0,get:function(){return refreshReducer}});let er=et(79102),en=et(8026),eo=et(84879),ea=et(11706),ei=et(69605),es=et(90145),eu=et(28237),el=et(43466),ec=et(82428),ed=et(65929);function refreshReducer(B,Z){let{cache:et,mutable:ef,origin:ep}=Z,eh=B.canonicalUrl,eg=B.tree,ey=JSON.stringify(ef.previousTree)===JSON.stringify(eg);if(ey)return(0,el.handleMutable)(B,ef);et.data||(et.data=(0,en.createRecordFromThenable)((0,er.fetchServerResponse)(new URL(eh,ep),[eg[0],eg[1],eg[2],"refetch"],B.nextUrl,B.buildId)));let[em,ev]=(0,eo.readRecordValue)(et.data);if("string"==typeof em)return(0,eu.handleExternalUrl)(B,ef,em,B.pushRef.pendingPush);for(let Z of(et.data=null,em)){if(3!==Z.length)return console.log("REFRESH FAILED"),B;let[er]=Z,en=(0,ei.applyRouterStatePatchToTree)([""],eg,er);if(null===en)throw Error("SEGMENT MISMATCH");if((0,es.isNavigatingToNewRootLayout)(eg,en))return(0,eu.handleExternalUrl)(B,ef,eh,B.pushRef.pendingPush);let eo=ev?(0,ea.createHrefFromUrl)(ev):void 0;ev&&(ef.canonicalUrl=eo);let[el,ep]=Z.slice(-2);null!==el&&(et.status=ec.CacheStates.READY,et.subTreeData=el,(0,ed.fillLazyItemsTillLeafWithHead)(et,void 0,er,ep),ef.cache=et,ef.prefetchCache=new Map),ef.previousTree=eg,ef.patchedTree=en,ef.canonicalUrl=eh,eg=en}return(0,el.handleMutable)(B,ef)}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},72910:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"restoreReducer",{enumerable:!0,get:function(){return restoreReducer}});let er=et(11706);function restoreReducer(B,Z){let{url:et,tree:en}=Z,eo=(0,er.createHrefFromUrl)(et);return{buildId:B.buildId,canonicalUrl:eo,pushRef:B.pushRef,focusAndScrollRef:B.focusAndScrollRef,cache:B.cache,prefetchCache:B.prefetchCache,tree:en,nextUrl:et.pathname}}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},39747:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"serverActionReducer",{enumerable:!0,get:function(){return serverActionReducer}});let er=et(95422),en=et(54361),eo=et(8026),ea=et(84879),ei=et(16879),es=et(11706),eu=et(28237),el=et(69605),ec=et(90145),ed=et(82428),ef=et(43466),ep=et(65929),{createFromFetch:eh,encodeReply:eg}=et(12623);async function fetchServerAction(B,Z){let et,{actionId:eo,actionArgs:ea}=Z,es=await eg(ea),eu=await fetch("",{method:"POST",headers:{Accept:en.RSC_CONTENT_TYPE_HEADER,[en.ACTION]:eo,[en.NEXT_ROUTER_STATE_TREE]:encodeURIComponent(JSON.stringify(B.tree)),...B.nextUrl?{[en.NEXT_URL]:B.nextUrl}:{}},body:es}),el=eu.headers.get("x-action-redirect");try{let B=JSON.parse(eu.headers.get("x-action-revalidated")||"[[],0,0]");et={paths:B[0]||[],tag:!!B[1],cookie:B[2]}}catch(B){et={paths:[],tag:!1,cookie:!1}}let ec=el?new URL((0,ei.addBasePath)(el),new URL(B.canonicalUrl,window.location.href)):void 0;if(eu.headers.get("content-type")===en.RSC_CONTENT_TYPE_HEADER){let B=await eh(Promise.resolve(eu),{callServer:er.callServer});if(el){let[,Z]=null!=B?B:[];return{actionFlightData:Z,redirectLocation:ec,revalidatedParts:et}}let[Z,[,en]]=null!=B?B:[];return{actionResult:Z,actionFlightData:en,redirectLocation:ec,revalidatedParts:et}}return{redirectLocation:ec,revalidatedParts:et}}function serverActionReducer(B,Z){let{mutable:et,cache:er,resolve:en,reject:ei}=Z,eh=B.canonicalUrl,eg=B.tree,ey=JSON.stringify(et.previousTree)===JSON.stringify(eg);if(ey)return(0,ef.handleMutable)(B,et);if(et.inFlightServerAction){if("fulfilled"!==et.inFlightServerAction.status&&et.globalMutable.pendingNavigatePath&&et.globalMutable.pendingNavigatePath!==eh)return et.inFlightServerAction.then(()=>{et.actionResultResolved||(et.inFlightServerAction=null,et.globalMutable.pendingNavigatePath=void 0,et.globalMutable.refresh(),et.actionResultResolved=!0)},()=>{}),B}else et.inFlightServerAction=(0,eo.createRecordFromThenable)(fetchServerAction(B,Z));try{let{actionResult:Z,actionFlightData:eo,redirectLocation:ei}=(0,ea.readRecordValue)(et.inFlightServerAction);if(ei&&(B.pushRef.pendingPush=!0,et.pendingPush=!0),et.previousTree=B.tree,!eo){if(et.actionResultResolved||(en(Z),et.actionResultResolved=!0),ei)return(0,eu.handleExternalUrl)(B,et,ei.href,B.pushRef.pendingPush);return B}if("string"==typeof eo)return(0,eu.handleExternalUrl)(B,et,eo,B.pushRef.pendingPush);for(let Z of(et.inFlightServerAction=null,eo)){if(3!==Z.length)return console.log("SERVER ACTION APPLY FAILED"),B;let[en]=Z,eo=(0,el.applyRouterStatePatchToTree)([""],eg,en);if(null===eo)throw Error("SEGMENT MISMATCH");if((0,ec.isNavigatingToNewRootLayout)(eg,eo))return(0,eu.handleExternalUrl)(B,et,eh,B.pushRef.pendingPush);let[ea,ei]=Z.slice(-2);null!==ea&&(er.status=ed.CacheStates.READY,er.subTreeData=ea,(0,ep.fillLazyItemsTillLeafWithHead)(er,void 0,en,ei),et.cache=er,et.prefetchCache=new Map),et.previousTree=eg,et.patchedTree=eo,et.canonicalUrl=eh,eg=eo}if(ei){let B=(0,es.createHrefFromUrl)(ei,!1);et.canonicalUrl=B}return et.actionResultResolved||(en(Z),et.actionResultResolved=!0),(0,ef.handleMutable)(B,et)}catch(Z){if("rejected"===Z.status)return et.actionResultResolved||(ei(Z.reason),et.actionResultResolved=!0),B;throw Z}}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},89794:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"serverPatchReducer",{enumerable:!0,get:function(){return serverPatchReducer}});let er=et(11706),en=et(69605),eo=et(90145),ea=et(28237),ei=et(51847),es=et(43466);function serverPatchReducer(B,Z){let{flightData:et,previousTree:eu,overrideCanonicalUrl:el,cache:ec,mutable:ed}=Z,ef=JSON.stringify(eu)===JSON.stringify(B.tree);if(!ef)return console.log("TREE MISMATCH"),B;if(ed.previousTree)return(0,es.handleMutable)(B,ed);if("string"==typeof et)return(0,ea.handleExternalUrl)(B,ed,et,B.pushRef.pendingPush);let ep=B.tree,eh=B.cache;for(let Z of et){let et=Z.slice(0,-4),[es]=Z.slice(-3,-2),eu=(0,en.applyRouterStatePatchToTree)(["",...et],ep,es);if(null===eu)throw Error("SEGMENT MISMATCH");if((0,eo.isNavigatingToNewRootLayout)(ep,eu))return(0,ea.handleExternalUrl)(B,ed,B.canonicalUrl,B.pushRef.pendingPush);let ef=el?(0,er.createHrefFromUrl)(el):void 0;ef&&(ed.canonicalUrl=ef),(0,ei.applyFlightData)(eh,ec,Z),ed.previousTree=ep,ed.patchedTree=eu,ed.cache=ec,eh=ec,ep=eu}return(0,es.handleMutable)(B,ed)}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},3678:(B,Z)=>{"use strict";var et;Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{PrefetchKind:function(){return et},ACTION_REFRESH:function(){return er},ACTION_NAVIGATE:function(){return en},ACTION_RESTORE:function(){return eo},ACTION_SERVER_PATCH:function(){return ea},ACTION_PREFETCH:function(){return ei},ACTION_FAST_REFRESH:function(){return es},ACTION_SERVER_ACTION:function(){return eu}});let er="refresh",en="navigate",eo="restore",ea="server-patch",ei="prefetch",es="fast-refresh",eu="server-action";(function(B){B.AUTO="auto",B.FULL="full",B.TEMPORARY="temporary"})(et||(et={})),("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},77986:(B,Z,et)=>{"use strict";function serverReducer(B,Z){return B}Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"reducer",{enumerable:!0,get:function(){return er}}),et(3678),et(28237),et(89794),et(72910),et(58038),et(31196),et(92755),et(39747);let er=serverReducer;("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},84320:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"shouldHardNavigate",{enumerable:!0,get:function(){return shouldHardNavigate}});let er=et(4538);function shouldHardNavigate(B,Z){let[et,en]=Z,[eo,ea]=B;if(!(0,er.matchSegment)(eo,et))return!!Array.isArray(eo);let ei=B.length<=2;return!ei&&shouldHardNavigate(B.slice(2),en[ea])}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},93032:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"createSearchParamsBailoutProxy",{enumerable:!0,get:function(){return createSearchParamsBailoutProxy}});let er=et(31492);function createSearchParamsBailoutProxy(){return new Proxy({},{get(B,Z){"string"==typeof Z&&(0,er.staticGenerationBailout)("searchParams."+Z)}})}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},31492:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"staticGenerationBailout",{enumerable:!0,get:function(){return staticGenerationBailout}});let er=et(45171),en=et(23094),eo=et(94749);let StaticGenBailoutError=class StaticGenBailoutError extends Error{constructor(...B){super(...B),this.code="NEXT_STATIC_GEN_BAILOUT"}};function formatErrorMessage(B,Z){let{dynamic:et,link:er}=Z||{};return"Page"+(et?' with `dynamic = "'+et+'"`':"")+" couldn't be rendered statically because it used `"+B+"`."+(er?" See more info here: "+er:"")}let staticGenerationBailout=(B,Z)=>{let et=eo.staticGenerationAsyncStorage.getStore();if(!et)return!1;if(et.forceStatic)return!0;if(et.dynamicShouldError){var ea;throw new StaticGenBailoutError(formatErrorMessage(B,{...Z,dynamic:null!=(ea=null==Z?void 0:Z.dynamic)?ea:"error"}))}let ei=formatErrorMessage(B,{...Z,link:"https://nextjs.org/docs/messages/dynamic-server-error"});if((0,en.maybePostpone)(et,ei),et.revalidate=0,(null==Z?void 0:Z.dynamic)||(et.staticPrefetchBailout=!0),et.isStaticGeneration){let Z=new er.DynamicServerError(ei);throw et.dynamicUsageDescription=B,et.dynamicUsageStack=Z.stack,Z}return!1};("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},8898:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"default",{enumerable:!0,get:function(){return StaticGenerationSearchParamsBailoutProvider}});let er=et(80085),en=er._(et(9885)),eo=et(93032);function StaticGenerationSearchParamsBailoutProvider(B){let{Component:Z,propsForComponent:et,isStaticGeneration:er}=B;if(er){let B=(0,eo.createSearchParamsBailoutProxy)();return en.default.createElement(Z,{searchParams:B,...et})}return en.default.createElement(Z,et)}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},79236:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"useReducerWithReduxDevtools",{enumerable:!0,get:function(){return en}});let er=et(9885);function useReducerWithReduxDevtoolsNoop(B,Z){let[et,en]=(0,er.useReducer)(B,Z);return[et,en,()=>{}]}let en=useReducerWithReduxDevtoolsNoop;("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},25587:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"detectDomainLocale",{enumerable:!0,get:function(){return detectDomainLocale}});let detectDomainLocale=function(){for(var B=arguments.length,Z=Array(B),er=0;er<B;er++)Z[er]=arguments[er];return et(56e3).D(...Z)};("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},66670:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"getDomainLocale",{enumerable:!0,get:function(){return getDomainLocale}});let er=et(76945);function getDomainLocale(B,Z,en,eo){{let ea=et(5725).normalizeLocalePath,ei=et(25587).detectDomainLocale,es=Z||ea(B,en).detectedLocale,eu=ei(eo,void 0,es);if(eu){let Z="http"+(eu.http?"":"s")+"://",et=es===eu.defaultLocale?"":"/"+es;return""+Z+eu.domain+(0,er.normalizePathTrailingSlash)(""+et+B)}return!1}}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},99760:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"hasBasePath",{enumerable:!0,get:function(){return hasBasePath}});let er=et(16364);function hasBasePath(B){return(0,er.pathHasPrefix)(B,"")}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},46686:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"Image",{enumerable:!0,get:function(){return eh}});let er=et(80085),en=et(8425),eo=en._(et(9885)),ea=er._(et(88908)),ei=er._(et(56420)),es=et(55161),eu=et(21412),el=et(87927);et(8601);let ec=et(10713),ed=er._(et(68320)),ef={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function handleLoading(B,Z,et,er,en,eo){let ea=null==B?void 0:B.src;if(!B||B["data-loaded-src"]===ea)return;B["data-loaded-src"]=ea;let ei="decode"in B?B.decode():Promise.resolve();ei.catch(()=>{}).then(()=>{if(B.parentElement&&B.isConnected){if("empty"!==Z&&en(!0),null==et?void 0:et.current){let Z=new Event("load");Object.defineProperty(Z,"target",{writable:!1,value:B});let er=!1,en=!1;et.current({...Z,nativeEvent:Z,currentTarget:B,target:B,isDefaultPrevented:()=>er,isPropagationStopped:()=>en,persist:()=>{},preventDefault:()=>{er=!0,Z.preventDefault()},stopPropagation:()=>{en=!0,Z.stopPropagation()}})}(null==er?void 0:er.current)&&er.current(B)}})}function getDynamicProps(B){let[Z,et]=eo.version.split(".",2),er=parseInt(Z,10),en=parseInt(et,10);return er>18||18===er&&en>=3?{fetchPriority:B}:{fetchpriority:B}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let ep=(0,eo.forwardRef)((B,Z)=>{let{src:et,srcSet:er,sizes:en,height:ea,width:ei,decoding:es,className:eu,style:el,fetchPriority:ec,placeholder:ed,loading:ef,unoptimized:ep,fill:eh,onLoadRef:eg,onLoadingCompleteRef:ey,setBlurComplete:em,setShowAltText:ev,onLoad:eb,onError:eS,...eP}=B;return eo.default.createElement("img",{...eP,...getDynamicProps(ec),loading:ef,width:ei,height:ea,decoding:es,"data-nimg":eh?"fill":"1",className:eu,style:el,sizes:en,srcSet:er,src:et,ref:(0,eo.useCallback)(B=>{Z&&("function"==typeof Z?Z(B):"object"==typeof Z&&(Z.current=B)),B&&(eS&&(B.src=B.src),B.complete&&handleLoading(B,ed,eg,ey,em,ep))},[et,ed,eg,ey,em,eS,ep,Z]),onLoad:B=>{let Z=B.currentTarget;handleLoading(Z,ed,eg,ey,em,ep)},onError:B=>{ev(!0),"empty"!==ed&&em(!0),eS&&eS(B)}})});function ImagePreload(B){let{isAppRouter:Z,imgAttributes:et}=B,er={as:"image",imageSrcSet:et.srcSet,imageSizes:et.sizes,crossOrigin:et.crossOrigin,referrerPolicy:et.referrerPolicy,...getDynamicProps(et.fetchPriority)};return Z&&ea.default.preload?(ea.default.preload(et.src,er),null):eo.default.createElement(ei.default,null,eo.default.createElement("link",{key:"__nimg-"+et.src+et.srcSet+et.sizes,rel:"preload",href:et.srcSet?void 0:et.src,...er}))}let eh=(0,eo.forwardRef)((B,Z)=>{let et=(0,eo.useContext)(ec.RouterContext),er=(0,eo.useContext)(el.ImageConfigContext),en=(0,eo.useMemo)(()=>{let B=ef||er||eu.imageConfigDefault,Z=[...B.deviceSizes,...B.imageSizes].sort((B,Z)=>B-Z),et=B.deviceSizes.sort((B,Z)=>B-Z);return{...B,allSizes:Z,deviceSizes:et}},[er]),{onLoad:ea,onLoadingComplete:ei}=B,eh=(0,eo.useRef)(ea);(0,eo.useEffect)(()=>{eh.current=ea},[ea]);let eg=(0,eo.useRef)(ei);(0,eo.useEffect)(()=>{eg.current=ei},[ei]);let[ey,em]=(0,eo.useState)(!1),[ev,eb]=(0,eo.useState)(!1),{props:eS,meta:eP}=(0,es.getImgProps)(B,{defaultLoader:ed.default,imgConf:en,blurComplete:ey,showAltText:ev});return eo.default.createElement(eo.default.Fragment,null,eo.default.createElement(ep,{...eS,unoptimized:eP.unoptimized,placeholder:eP.placeholder,fill:eP.fill,onLoadRef:eh,onLoadingCompleteRef:eg,setBlurComplete:em,setShowAltText:eb,ref:Z}),eP.priority?eo.default.createElement(ImagePreload,{isAppRouter:!et,imgAttributes:eS}):null)});("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},30614:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"default",{enumerable:!0,get:function(){return ey}});let er=et(80085),en=er._(et(9885)),eo=et(62861),ea=et(20058),ei=et(30602),es=et(84679),eu=et(54401),el=et(10713),ec=et(82428),ed=et(32229),ef=et(66670),ep=et(16879),eh=et(3678);function isModifiedEvent(B){let Z=B.currentTarget,et=Z.getAttribute("target");return et&&"_self"!==et||B.metaKey||B.ctrlKey||B.shiftKey||B.altKey||B.nativeEvent&&2===B.nativeEvent.which}function linkClicked(B,Z,et,er,eo,ei,es,eu,el,ec){let{nodeName:ed}=B.currentTarget,ef="A"===ed.toUpperCase();if(ef&&(isModifiedEvent(B)||!el&&!(0,ea.isLocalURL)(et)))return;B.preventDefault();let navigate=()=>{let B=null==es||es;"beforePopState"in Z?Z[eo?"replace":"push"](et,er,{shallow:ei,locale:eu,scroll:B}):Z[eo?"replace":"push"](er||et,{forceOptimisticNavigation:!ec,scroll:B})};el?en.default.startTransition(navigate):navigate()}function formatStringOrUrl(B){return"string"==typeof B?B:(0,ei.formatUrl)(B)}let eg=en.default.forwardRef(function(B,Z){let et,er;let{href:ea,as:ei,children:eg,prefetch:ey=null,passHref:em,replace:ev,shallow:eb,scroll:eS,locale:eP,onClick:eO,onMouseEnter:e_,onTouchStart:ex,legacyBehavior:eR=!1,...ew}=B;et=eg,eR&&("string"==typeof et||"number"==typeof et)&&(et=en.default.createElement("a",null,et));let eE=en.default.useContext(el.RouterContext),ej=en.default.useContext(ec.AppRouterContext),eC=null!=eE?eE:ej,eM=!eE,eA=!1!==ey,ek=null===ey?eh.PrefetchKind.AUTO:eh.PrefetchKind.FULL,{href:eT,as:eN}=en.default.useMemo(()=>{if(!eE){let B=formatStringOrUrl(ea);return{href:B,as:ei?formatStringOrUrl(ei):B}}let[B,Z]=(0,eo.resolveHref)(eE,ea,!0);return{href:B,as:ei?(0,eo.resolveHref)(eE,ei):Z||B}},[eE,ea,ei]),eL=en.default.useRef(eT),eD=en.default.useRef(eN);eR&&(er=en.default.Children.only(et));let eI=eR?er&&"object"==typeof er&&er.ref:Z,[eF,eU,eq]=(0,ed.useIntersection)({rootMargin:"200px"}),e$=en.default.useCallback(B=>{(eD.current!==eN||eL.current!==eT)&&(eq(),eD.current=eN,eL.current=eT),eF(B),eI&&("function"==typeof eI?eI(B):"object"==typeof eI&&(eI.current=B))},[eN,eI,eT,eq,eF]);en.default.useEffect(()=>{},[eN,eT,eU,eP,eA,null==eE?void 0:eE.locale,eC,eM,ek]);let ez={ref:e$,onClick(B){eR||"function"!=typeof eO||eO(B),eR&&er.props&&"function"==typeof er.props.onClick&&er.props.onClick(B),eC&&!B.defaultPrevented&&linkClicked(B,eC,eT,eN,ev,eb,eS,eP,eM,eA)},onMouseEnter(B){eR||"function"!=typeof e_||e_(B),eR&&er.props&&"function"==typeof er.props.onMouseEnter&&er.props.onMouseEnter(B)},onTouchStart(B){eR||"function"!=typeof ex||ex(B),eR&&er.props&&"function"==typeof er.props.onTouchStart&&er.props.onTouchStart(B)}};if((0,es.isAbsoluteUrl)(eN))ez.href=eN;else if(!eR||em||"a"===er.type&&!("href"in er.props)){let B=void 0!==eP?eP:null==eE?void 0:eE.locale,Z=(null==eE?void 0:eE.isLocaleDomain)&&(0,ef.getDomainLocale)(eN,B,null==eE?void 0:eE.locales,null==eE?void 0:eE.domainLocales);ez.href=Z||(0,ep.addBasePath)((0,eu.addLocale)(eN,B,null==eE?void 0:eE.defaultLocale))}return eR?en.default.cloneElement(er,ez):en.default.createElement("a",{...ew,...ez},et)}),ey=eg;("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},5725:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"normalizeLocalePath",{enumerable:!0,get:function(){return normalizeLocalePath}});let normalizeLocalePath=(B,Z)=>et(54162).h(B,Z);("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},76945:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return normalizePathTrailingSlash}});let er=et(96923),en=et(65525),normalizePathTrailingSlash=B=>{if(!B.startsWith("/"))return B;let{pathname:Z,query:et,hash:eo}=(0,en.parsePath)(B);return""+(0,er.removeTrailingSlash)(Z)+et+eo};("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},64978:(B,Z,et)=>{"use strict";function removeBasePath(B){return B}Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"removeBasePath",{enumerable:!0,get:function(){return removeBasePath}}),et(99760),("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},14149:(B,Z)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{requestIdleCallback:function(){return et},cancelIdleCallback:function(){return er}});let et="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(B){let Z=Date.now();return self.setTimeout(function(){B({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-Z))}})},1)},er="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(B){return clearTimeout(B)};("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},62861:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"resolveHref",{enumerable:!0,get:function(){return resolveHref}});let er=et(56141),en=et(30602),eo=et(37739),ea=et(84679),ei=et(76945),es=et(20058),eu=et(33751),el=et(44006);function resolveHref(B,Z,et){let ec;let ed="string"==typeof Z?Z:(0,en.formatWithValidation)(Z),ef=ed.match(/^[a-zA-Z]{1,}:\/\//),ep=ef?ed.slice(ef[0].length):ed,eh=ep.split("?",1);if((eh[0]||"").match(/(\/\/|\\)/)){console.error("Invalid href '"+ed+"' passed to next/router in page: '"+B.pathname+"'. Repeated forward-slashes (//) or backslashes \\ are not valid in the href.");let Z=(0,ea.normalizeRepeatedSlashes)(ep);ed=(ef?ef[0]:"")+Z}if(!(0,es.isLocalURL)(ed))return et?[ed]:ed;try{ec=new URL(ed.startsWith("#")?B.asPath:B.pathname,"http://n")}catch(B){ec=new URL("/","http://n")}try{let B=new URL(ed,ec);B.pathname=(0,ei.normalizePathTrailingSlash)(B.pathname);let Z="";if((0,eu.isDynamicRoute)(B.pathname)&&B.searchParams&&et){let et=(0,er.searchParamsToUrlQuery)(B.searchParams),{result:ea,params:ei}=(0,el.interpolateAs)(B.pathname,B.pathname,et);ea&&(Z=(0,en.formatWithValidation)({pathname:ea,hash:B.hash,query:(0,eo.omit)(et,ei)}))}let ea=B.origin===ec.origin?B.href.slice(B.origin.length):B.href;return et?[ea,Z||ea]:ea}catch(B){return et?[ed]:ed}}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},32229:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"useIntersection",{enumerable:!0,get:function(){return useIntersection}});let er=et(9885),en=et(14149),eo="function"==typeof IntersectionObserver,ea=new Map,ei=[];function createObserver(B){let Z;let et={root:B.root||null,margin:B.rootMargin||""},er=ei.find(B=>B.root===et.root&&B.margin===et.margin);if(er&&(Z=ea.get(er)))return Z;let en=new Map,eo=new IntersectionObserver(B=>{B.forEach(B=>{let Z=en.get(B.target),et=B.isIntersecting||B.intersectionRatio>0;Z&&et&&Z(et)})},B);return Z={id:et,observer:eo,elements:en},ei.push(et),ea.set(et,Z),Z}function observe(B,Z,et){let{id:er,observer:en,elements:eo}=createObserver(et);return eo.set(B,Z),en.observe(B),function(){if(eo.delete(B),en.unobserve(B),0===eo.size){en.disconnect(),ea.delete(er);let B=ei.findIndex(B=>B.root===er.root&&B.margin===er.margin);B>-1&&ei.splice(B,1)}}}function useIntersection(B){let{rootRef:Z,rootMargin:et,disabled:ea}=B,ei=ea||!eo,[es,eu]=(0,er.useState)(!1),el=(0,er.useRef)(null),ec=(0,er.useCallback)(B=>{el.current=B},[]);(0,er.useEffect)(()=>{if(eo){if(ei||es)return;let B=el.current;if(B&&B.tagName){let er=observe(B,B=>B&&eu(B),{root:null==Z?void 0:Z.current,rootMargin:et});return er}}else if(!es){let B=(0,en.requestIdleCallback)(()=>eu(!0));return()=>(0,en.cancelIdleCallback)(B)}},[ei,et,Z,es,el.current]);let ed=(0,er.useCallback)(()=>{eu(!1)},[]);return[ec,es,ed]}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},81281:(B,Z)=>{"use strict";function isInAmpMode(B){let{ampFirst:Z=!1,hybrid:et=!1,hasQuery:er=!1}=void 0===B?{}:B;return Z||et&&er}Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"isInAmpMode",{enumerable:!0,get:function(){return isInAmpMode}})},50821:(B,Z)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"escapeStringRegexp",{enumerable:!0,get:function(){return escapeStringRegexp}});let et=/[|\\{}()[\]^$+*?.-]/,er=/[|\\{}()[\]^$+*?.-]/g;function escapeStringRegexp(B){return et.test(B)?B.replace(er,"\\$&"):B}},55161:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"getImgProps",{enumerable:!0,get:function(){return getImgProps}}),et(8601);let er=et(52490),en=et(21412);function isStaticRequire(B){return void 0!==B.default}function isStaticImageData(B){return void 0!==B.src}function isStaticImport(B){return"object"==typeof B&&(isStaticRequire(B)||isStaticImageData(B))}function getInt(B){return void 0===B?B:"number"==typeof B?Number.isFinite(B)?B:NaN:"string"==typeof B&&/^[0-9]+$/.test(B)?parseInt(B,10):NaN}function getWidths(B,Z,et){let{deviceSizes:er,allSizes:en}=B;if(et){let B=/(^|\s)(1?\d?\d)vw/g,Z=[];for(let er;er=B.exec(et);er)Z.push(parseInt(er[2]));if(Z.length){let B=.01*Math.min(...Z);return{widths:en.filter(Z=>Z>=er[0]*B),kind:"w"}}return{widths:en,kind:"w"}}if("number"!=typeof Z)return{widths:er,kind:"w"};let eo=[...new Set([Z,2*Z].map(B=>en.find(Z=>Z>=B)||en[en.length-1]))];return{widths:eo,kind:"x"}}function generateImgAttrs(B){let{config:Z,src:et,unoptimized:er,width:en,quality:eo,sizes:ea,loader:ei}=B;if(er)return{src:et,srcSet:void 0,sizes:void 0};let{widths:es,kind:eu}=getWidths(Z,en,ea),el=es.length-1;return{sizes:ea||"w"!==eu?ea:"100vw",srcSet:es.map((B,er)=>ei({config:Z,src:et,quality:eo,width:B})+" "+("w"===eu?B:er+1)+eu).join(", "),src:ei({config:Z,src:et,quality:eo,width:es[el]})}}function getImgProps(B,Z){let et,eo,ea,{src:ei,sizes:es,unoptimized:eu=!1,priority:el=!1,loading:ec,className:ed,quality:ef,width:ep,height:eh,fill:eg=!1,style:ey,onLoad:em,onLoadingComplete:ev,placeholder:eb="empty",blurDataURL:eS,fetchPriority:eP,layout:eO,objectFit:e_,objectPosition:ex,lazyBoundary:eR,lazyRoot:ew,...eE}=B,{imgConf:ej,showAltText:eC,blurComplete:eM,defaultLoader:eA}=Z,ek=ej||en.imageConfigDefault;if("allSizes"in ek)et=ek;else{let B=[...ek.deviceSizes,...ek.imageSizes].sort((B,Z)=>B-Z),Z=ek.deviceSizes.sort((B,Z)=>B-Z);et={...ek,allSizes:B,deviceSizes:Z}}let eT=eE.loader||eA;delete eE.loader,delete eE.srcSet;let eN="__next_img_default"in eT;if(eN){if("custom"===et.loader)throw Error('Image with src "'+ei+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader')}else{let B=eT;eT=Z=>{let{config:et,...er}=Z;return B(er)}}if(eO){"fill"===eO&&(eg=!0);let B={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[eO];B&&(ey={...ey,...B});let Z={responsive:"100vw",fill:"100vw"}[eO];Z&&!es&&(es=Z)}let eL="",eD=getInt(ep),eI=getInt(eh);if(isStaticImport(ei)){let B=isStaticRequire(ei)?ei.default:ei;if(!B.src)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(B));if(!B.height||!B.width)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(B));if(eo=B.blurWidth,ea=B.blurHeight,eS=eS||B.blurDataURL,eL=B.src,!eg){if(eD||eI){if(eD&&!eI){let Z=eD/B.width;eI=Math.round(B.height*Z)}else if(!eD&&eI){let Z=eI/B.height;eD=Math.round(B.width*Z)}}else eD=B.width,eI=B.height}}let eF=!el&&("lazy"===ec||void 0===ec);(!(ei="string"==typeof ei?ei:eL)||ei.startsWith("data:")||ei.startsWith("blob:"))&&(eu=!0,eF=!1),et.unoptimized&&(eu=!0),eN&&ei.endsWith(".svg")&&!et.dangerouslyAllowSVG&&(eu=!0),el&&(eP="high");let eU=getInt(ef),eq=Object.assign(eg?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:e_,objectPosition:ex}:{},eC?{}:{color:"transparent"},ey),e$=eM||"empty"===eb?null:"blur"===eb?'url("data:image/svg+xml;charset=utf-8,'+(0,er.getImageBlurSvg)({widthInt:eD,heightInt:eI,blurWidth:eo,blurHeight:ea,blurDataURL:eS||"",objectFit:eq.objectFit})+'")':'url("'+eb+'")',ez=e$?{backgroundSize:eq.objectFit||"cover",backgroundPosition:eq.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:e$}:{},eH=generateImgAttrs({config:et,src:ei,unoptimized:eu,width:eD,quality:eU,sizes:es,loader:eT}),eW={...eE,loading:eF?"lazy":ec,fetchPriority:eP,width:eD,height:eI,decoding:"async",className:ed,style:{...eq,...ez},sizes:eH.sizes,srcSet:eH.srcSet,src:eH.src},eB={unoptimized:eu,priority:el,placeholder:eb,fill:eg};return{props:eW,meta:eB}}},30755:(B,Z)=>{"use strict";function djb2Hash(B){let Z=5381;for(let et=0;et<B.length;et++){let er=B.charCodeAt(et);Z=(Z<<5)+Z+er}return Math.abs(Z)}function hexHash(B){return djb2Hash(B).toString(36).slice(0,5)}Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{djb2Hash:function(){return djb2Hash},hexHash:function(){return hexHash}})},56420:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{defaultHead:function(){return defaultHead},default:function(){return ec}});let er=et(80085),en=et(8425),eo=en._(et(9885)),ea=er._(et(94005)),ei=et(12999),es=et(75851),eu=et(81281);function defaultHead(B){void 0===B&&(B=!1);let Z=[eo.default.createElement("meta",{charSet:"utf-8"})];return B||Z.push(eo.default.createElement("meta",{name:"viewport",content:"width=device-width"})),Z}function onlyReactElement(B,Z){return"string"==typeof Z||"number"==typeof Z?B:Z.type===eo.default.Fragment?B.concat(eo.default.Children.toArray(Z.props.children).reduce((B,Z)=>"string"==typeof Z||"number"==typeof Z?B:B.concat(Z),[])):B.concat(Z)}et(8601);let el=["name","httpEquiv","charSet","itemProp"];function unique(){let B=new Set,Z=new Set,et=new Set,er={};return en=>{let eo=!0,ea=!1;if(en.key&&"number"!=typeof en.key&&en.key.indexOf("$")>0){ea=!0;let Z=en.key.slice(en.key.indexOf("$")+1);B.has(Z)?eo=!1:B.add(Z)}switch(en.type){case"title":case"base":Z.has(en.type)?eo=!1:Z.add(en.type);break;case"meta":for(let B=0,Z=el.length;B<Z;B++){let Z=el[B];if(en.props.hasOwnProperty(Z)){if("charSet"===Z)et.has(Z)?eo=!1:et.add(Z);else{let B=en.props[Z],et=er[Z]||new Set;("name"!==Z||!ea)&&et.has(B)?eo=!1:(et.add(B),er[Z]=et)}}}}return eo}}function reduceComponents(B,Z){let{inAmpMode:et}=Z;return B.reduce(onlyReactElement,[]).reverse().concat(defaultHead(et).reverse()).filter(unique()).reverse().map((B,Z)=>{let er=B.key||Z;if(!et&&"link"===B.type&&B.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(Z=>B.props.href.startsWith(Z))){let Z={...B.props||{}};return Z["data-href"]=Z.href,Z.href=void 0,Z["data-optimized-fonts"]=!0,eo.default.cloneElement(B,Z)}return eo.default.cloneElement(B,{key:er})})}function Head(B){let{children:Z}=B,et=(0,eo.useContext)(ei.AmpStateContext),er=(0,eo.useContext)(es.HeadManagerContext);return eo.default.createElement(ea.default,{reduceComponentsToState:reduceComponents,headManager:er,inAmpMode:(0,eu.isInAmpMode)(et)},Z)}let ec=Head;("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},56e3:(B,Z)=>{"use strict";function detectDomainLocale(B,Z,et){if(B)for(let eo of(et&&(et=et.toLowerCase()),B)){var er,en;let B=null==(er=eo.domain)?void 0:er.split(":",1)[0].toLowerCase();if(Z===B||et===eo.defaultLocale.toLowerCase()||(null==(en=eo.locales)?void 0:en.some(B=>B.toLowerCase()===et)))return eo}}Object.defineProperty(Z,"D",{enumerable:!0,get:function(){return detectDomainLocale}})},54162:(B,Z)=>{"use strict";function normalizeLocalePath(B,Z){let et;let er=B.split("/");return(Z||[]).some(Z=>!!er[1]&&er[1].toLowerCase()===Z.toLowerCase()&&(et=Z,er.splice(1,1),B=er.join("/")||"/",!0)),{pathname:B,detectedLocale:et}}Object.defineProperty(Z,"h",{enumerable:!0,get:function(){return normalizeLocalePath}})},52490:(B,Z)=>{"use strict";function getImageBlurSvg(B){let{widthInt:Z,heightInt:et,blurWidth:er,blurHeight:en,blurDataURL:eo,objectFit:ea}=B,ei=er?40*er:Z,es=en?40*en:et,eu=ei&&es?"viewBox='0 0 "+ei+" "+es+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+eu+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(eu?"none":"contain"===ea?"xMidYMid":"cover"===ea?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+eo+"'/%3E%3C/svg%3E"}Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"getImageBlurSvg",{enumerable:!0,get:function(){return getImageBlurSvg}})},21412:(B,Z)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{VALID_LOADERS:function(){return et},imageConfigDefault:function(){return er}});let et=["default","imgix","cloudinary","akamai","custom"],er={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[],unoptimized:!1}},57990:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{unstable_getImgProps:function(){return unstable_getImgProps},default:function(){return es}});let er=et(80085),en=et(55161),eo=et(8601),ea=et(46686),ei=er._(et(68320)),unstable_getImgProps=B=>{(0,eo.warnOnce)("Warning: unstable_getImgProps() is experimental and may change or be removed at any time. Use at your own risk.");let{props:Z}=(0,en.getImgProps)(B,{defaultLoader:ei.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[B,et]of Object.entries(Z))void 0===et&&delete Z[B];return{props:Z}},es=ea.Image},68320:(B,Z)=>{"use strict";function defaultLoader(B){let{config:Z,src:et,width:er,quality:en}=B;return Z.path+"?url="+encodeURIComponent(et)+"&w="+er+"&q="+(en||75)}Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"default",{enumerable:!0,get:function(){return et}}),defaultLoader.__next_img_default=!0;let et=defaultLoader},61118:(B,Z)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{NEXT_DYNAMIC_NO_SSR_CODE:function(){return et},throwWithNoSSR:function(){return throwWithNoSSR}});let et="NEXT_DYNAMIC_NO_SSR_CODE";function throwWithNoSSR(){let B=Error(et);throw B.digest=et,B}},61518:(B,Z)=>{"use strict";function ensureLeadingSlash(B){return B.startsWith("/")?B:"/"+B}Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"ensureLeadingSlash",{enumerable:!0,get:function(){return ensureLeadingSlash}})},56850:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"b",{enumerable:!0,get:function(){return addLocale}});let er=et(8549),en=et(16364);function addLocale(B,Z,et,eo){if(!Z||Z===et)return B;let ea=B.toLowerCase();return!eo&&((0,en.pathHasPrefix)(ea,"/api")||(0,en.pathHasPrefix)(ea,"/"+Z.toLowerCase()))?B:(0,er.addPathPrefix)(B,"/"+Z)}},8549:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"addPathPrefix",{enumerable:!0,get:function(){return addPathPrefix}});let er=et(65525);function addPathPrefix(B,Z){if(!B.startsWith("/")||!Z)return B;let{pathname:et,query:en,hash:eo}=(0,er.parsePath)(B);return""+Z+et+en+eo}},48321:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{normalizeAppPath:function(){return normalizeAppPath},normalizeRscURL:function(){return normalizeRscURL},normalizePostponedURL:function(){return normalizePostponedURL}});let er=et(61518),en=et(392),eo=et(57310);function normalizeAppPath(B){return(0,er.ensureLeadingSlash)(B.split("/").reduce((B,Z,et,er)=>!Z||(0,en.isGroupSegment)(Z)||"@"===Z[0]||("page"===Z||"route"===Z)&&et===er.length-1?B:B+"/"+Z,""))}function normalizeRscURL(B){return B.replace(/\.rsc($|\?)/,"$1")}function normalizePostponedURL(B){let Z=(0,eo.parse)(B),{pathname:et}=Z;return et&&et.startsWith("/_next/postponed")?(et=et.substring(16)||"/",(0,eo.format)({...Z,pathname:et})):B}},30602:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{formatUrl:function(){return formatUrl},urlObjectKeys:function(){return ea},formatWithValidation:function(){return formatWithValidation}});let er=et(8425),en=er._(et(56141)),eo=/https?|ftp|gopher|file/;function formatUrl(B){let{auth:Z,hostname:et}=B,er=B.protocol||"",ea=B.pathname||"",ei=B.hash||"",es=B.query||"",eu=!1;Z=Z?encodeURIComponent(Z).replace(/%3A/i,":")+"@":"",B.host?eu=Z+B.host:et&&(eu=Z+(~et.indexOf(":")?"["+et+"]":et),B.port&&(eu+=":"+B.port)),es&&"object"==typeof es&&(es=String(en.urlQueryToSearchParams(es)));let el=B.search||es&&"?"+es||"";return er&&!er.endsWith(":")&&(er+=":"),B.slashes||(!er||eo.test(er))&&!1!==eu?(eu="//"+(eu||""),ea&&"/"!==ea[0]&&(ea="/"+ea)):eu||(eu=""),ei&&"#"!==ei[0]&&(ei="#"+ei),el&&"?"!==el[0]&&(el="?"+el),""+er+eu+(ea=ea.replace(/[?#]/g,encodeURIComponent))+(el=el.replace("#","%23"))+ei}let ea=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function formatWithValidation(B){return formatUrl(B)}},94361:(B,Z)=>{"use strict";function handleSmoothScroll(B,Z){if(void 0===Z&&(Z={}),Z.onlyHashChange){B();return}let et=document.documentElement,er=et.style.scrollBehavior;et.style.scrollBehavior="auto",Z.dontForceLayout||et.getClientRects(),B(),et.style.scrollBehavior=er}Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"handleSmoothScroll",{enumerable:!0,get:function(){return handleSmoothScroll}})},33751:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{getSortedRoutes:function(){return er.getSortedRoutes},isDynamicRoute:function(){return en.isDynamicRoute}});let er=et(72861),en=et(21534)},44006:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"interpolateAs",{enumerable:!0,get:function(){return interpolateAs}});let er=et(43630),en=et(80680);function interpolateAs(B,Z,et){let eo="",ea=(0,en.getRouteRegex)(B),ei=ea.groups,es=(Z!==B?(0,er.getRouteMatcher)(ea)(Z):"")||et;eo=B;let eu=Object.keys(ei);return eu.every(B=>{let Z=es[B]||"",{repeat:et,optional:er}=ei[B],en="["+(et?"...":"")+B+"]";return er&&(en=(Z?"":"/")+"["+en+"]"),et&&!Array.isArray(Z)&&(Z=[Z]),(er||B in es)&&(eo=eo.replace(en,et?Z.map(B=>encodeURIComponent(B)).join("/"):encodeURIComponent(Z))||"/")})||(eo=""),{params:eu,result:eo}}},34692:(B,Z)=>{"use strict";function isBot(B){return/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(B)}Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"isBot",{enumerable:!0,get:function(){return isBot}})},21534:(B,Z)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"isDynamicRoute",{enumerable:!0,get:function(){return isDynamicRoute}});let et=/\/\[[^/]+?\](?=\/|$)/;function isDynamicRoute(B){return et.test(B)}},20058:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"isLocalURL",{enumerable:!0,get:function(){return isLocalURL}});let er=et(84679),en=et(99760);function isLocalURL(B){if(!(0,er.isAbsoluteUrl)(B))return!0;try{let Z=(0,er.getLocationOrigin)(),et=new URL(B,Z);return et.origin===Z&&(0,en.hasBasePath)(et.pathname)}catch(B){return!1}}},37739:(B,Z)=>{"use strict";function omit(B,Z){let et={};return Object.keys(B).forEach(er=>{Z.includes(er)||(et[er]=B[er])}),et}Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"omit",{enumerable:!0,get:function(){return omit}})},65525:(B,Z)=>{"use strict";function parsePath(B){let Z=B.indexOf("#"),et=B.indexOf("?"),er=et>-1&&(Z<0||et<Z);return er||Z>-1?{pathname:B.substring(0,er?et:Z),query:er?B.substring(et,Z>-1?Z:void 0):"",hash:Z>-1?B.slice(Z):""}:{pathname:B,query:"",hash:""}}Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"parsePath",{enumerable:!0,get:function(){return parsePath}})},16364:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"pathHasPrefix",{enumerable:!0,get:function(){return pathHasPrefix}});let er=et(65525);function pathHasPrefix(B,Z){if("string"!=typeof B)return!1;let{pathname:et}=(0,er.parsePath)(B);return et===Z||et.startsWith(Z+"/")}},56141:(B,Z)=>{"use strict";function searchParamsToUrlQuery(B){let Z={};return B.forEach((B,et)=>{void 0===Z[et]?Z[et]=B:Array.isArray(Z[et])?Z[et].push(B):Z[et]=[Z[et],B]}),Z}function stringifyUrlQueryParam(B){return"string"!=typeof B&&("number"!=typeof B||isNaN(B))&&"boolean"!=typeof B?"":String(B)}function urlQueryToSearchParams(B){let Z=new URLSearchParams;return Object.entries(B).forEach(B=>{let[et,er]=B;Array.isArray(er)?er.forEach(B=>Z.append(et,stringifyUrlQueryParam(B))):Z.set(et,stringifyUrlQueryParam(er))}),Z}function assign(B){for(var Z=arguments.length,et=Array(Z>1?Z-1:0),er=1;er<Z;er++)et[er-1]=arguments[er];return et.forEach(Z=>{Array.from(Z.keys()).forEach(Z=>B.delete(Z)),Z.forEach((Z,et)=>B.append(et,Z))}),B}Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{searchParamsToUrlQuery:function(){return searchParamsToUrlQuery},urlQueryToSearchParams:function(){return urlQueryToSearchParams},assign:function(){return assign}})},96923:(B,Z)=>{"use strict";function removeTrailingSlash(B){return B.replace(/\/$/,"")||"/"}Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"removeTrailingSlash",{enumerable:!0,get:function(){return removeTrailingSlash}})},43630:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"getRouteMatcher",{enumerable:!0,get:function(){return getRouteMatcher}});let er=et(84679);function getRouteMatcher(B){let{re:Z,groups:et}=B;return B=>{let en=Z.exec(B);if(!en)return!1;let decode=B=>{try{return decodeURIComponent(B)}catch(B){throw new er.DecodeError("failed to decode param")}},eo={};return Object.keys(et).forEach(B=>{let Z=et[B],er=en[Z.pos];void 0!==er&&(eo[B]=~er.indexOf("/")?er.split("/").map(B=>decode(B)):Z.repeat?[decode(er)]:decode(er))}),eo}}},80680:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{getRouteRegex:function(){return getRouteRegex},getNamedRouteRegex:function(){return getNamedRouteRegex},getNamedMiddlewareRegex:function(){return getNamedMiddlewareRegex}});let er=et(84265),en=et(50821),eo=et(96923);function parseParameter(B){let Z=B.startsWith("[")&&B.endsWith("]");Z&&(B=B.slice(1,-1));let et=B.startsWith("...");return et&&(B=B.slice(3)),{key:B,repeat:et,optional:Z}}function getParametrizedRoute(B){let Z=(0,eo.removeTrailingSlash)(B).slice(1).split("/"),et={},ea=1;return{parameterizedRoute:Z.map(B=>{let Z=er.INTERCEPTION_ROUTE_MARKERS.find(Z=>B.startsWith(Z)),eo=B.match(/\[((?:\[.*\])|.+)\]/);if(Z&&eo){let{key:B,optional:er,repeat:ei}=parseParameter(eo[1]);return et[B]={pos:ea++,repeat:ei,optional:er},"/"+(0,en.escapeStringRegexp)(Z)+"([^/]+?)"}if(!eo)return"/"+(0,en.escapeStringRegexp)(B);{let{key:B,repeat:Z,optional:er}=parseParameter(eo[1]);return et[B]={pos:ea++,repeat:Z,optional:er},Z?er?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)"}}).join(""),groups:et}}function getRouteRegex(B){let{parameterizedRoute:Z,groups:et}=getParametrizedRoute(B);return{re:RegExp("^"+Z+"(?:/)?$"),groups:et}}function buildGetSafeRouteKey(){let B=0;return()=>{let Z="",et=++B;for(;et>0;)Z+=String.fromCharCode(97+(et-1)%26),et=Math.floor((et-1)/26);return Z}}function getSafeKeyFromSegment(B){let{getSafeRouteKey:Z,segment:et,routeKeys:er,keyPrefix:en}=B,{key:eo,optional:ea,repeat:ei}=parseParameter(et),es=eo.replace(/\W/g,"");en&&(es=""+en+es);let eu=!1;return(0===es.length||es.length>30)&&(eu=!0),isNaN(parseInt(es.slice(0,1)))||(eu=!0),eu&&(es=Z()),en?er[es]=""+en+eo:er[es]=""+eo,ei?ea?"(?:/(?<"+es+">.+?))?":"/(?<"+es+">.+?)":"/(?<"+es+">[^/]+?)"}function getNamedParametrizedRoute(B,Z){let et=(0,eo.removeTrailingSlash)(B).slice(1).split("/"),ea=buildGetSafeRouteKey(),ei={};return{namedParameterizedRoute:et.map(B=>{let et=er.INTERCEPTION_ROUTE_MARKERS.some(Z=>B.startsWith(Z)),eo=B.match(/\[((?:\[.*\])|.+)\]/);return et&&eo?getSafeKeyFromSegment({getSafeRouteKey:ea,segment:eo[1],routeKeys:ei,keyPrefix:Z?"nxtI":void 0}):eo?getSafeKeyFromSegment({getSafeRouteKey:ea,segment:eo[1],routeKeys:ei,keyPrefix:Z?"nxtP":void 0}):"/"+(0,en.escapeStringRegexp)(B)}).join(""),routeKeys:ei}}function getNamedRouteRegex(B,Z){let et=getNamedParametrizedRoute(B,Z);return{...getRouteRegex(B),namedRegex:"^"+et.namedParameterizedRoute+"(?:/)?$",routeKeys:et.routeKeys}}function getNamedMiddlewareRegex(B,Z){let{parameterizedRoute:et}=getParametrizedRoute(B),{catchAll:er=!0}=Z;if("/"===et)return{namedRegex:"^/"+(er?".*":"")+"$"};let{namedParameterizedRoute:en}=getNamedParametrizedRoute(B,!1);return{namedRegex:"^"+en+(er?"(?:(/.*)?)":"")+"$"}}},72861:(B,Z)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"getSortedRoutes",{enumerable:!0,get:function(){return getSortedRoutes}});let UrlNode=class UrlNode{insert(B){this._insert(B.split("/").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(B){void 0===B&&(B="/");let Z=[...this.children.keys()].sort();null!==this.slugName&&Z.splice(Z.indexOf("[]"),1),null!==this.restSlugName&&Z.splice(Z.indexOf("[...]"),1),null!==this.optionalRestSlugName&&Z.splice(Z.indexOf("[[...]]"),1);let et=Z.map(Z=>this.children.get(Z)._smoosh(""+B+Z+"/")).reduce((B,Z)=>[...B,...Z],[]);if(null!==this.slugName&&et.push(...this.children.get("[]")._smoosh(B+"["+this.slugName+"]/")),!this.placeholder){let Z="/"===B?"/":B.slice(0,-1);if(null!=this.optionalRestSlugName)throw Error('You cannot define a route with the same specificity as a optional catch-all route ("'+Z+'" and "'+Z+"[[..."+this.optionalRestSlugName+']]").');et.unshift(Z)}return null!==this.restSlugName&&et.push(...this.children.get("[...]")._smoosh(B+"[..."+this.restSlugName+"]/")),null!==this.optionalRestSlugName&&et.push(...this.children.get("[[...]]")._smoosh(B+"[[..."+this.optionalRestSlugName+"]]/")),et}_insert(B,Z,et){if(0===B.length){this.placeholder=!1;return}if(et)throw Error("Catch-all must be the last part of the URL.");let er=B[0];if(er.startsWith("[")&&er.endsWith("]")){let en=er.slice(1,-1),eo=!1;if(en.startsWith("[")&&en.endsWith("]")&&(en=en.slice(1,-1),eo=!0),en.startsWith("...")&&(en=en.substring(3),et=!0),en.startsWith("[")||en.endsWith("]"))throw Error("Segment names may not start or end with extra brackets ('"+en+"').");if(en.startsWith("."))throw Error("Segment names may not start with erroneous periods ('"+en+"').");function handleSlug(B,et){if(null!==B&&B!==et)throw Error("You cannot use different slug names for the same dynamic path ('"+B+"' !== '"+et+"').");Z.forEach(B=>{if(B===et)throw Error('You cannot have the same slug name "'+et+'" repeat within a single dynamic path');if(B.replace(/\W/g,"")===er.replace(/\W/g,""))throw Error('You cannot have the slug names "'+B+'" and "'+et+'" differ only by non-word symbols within a single dynamic path')}),Z.push(et)}if(et){if(eo){if(null!=this.restSlugName)throw Error('You cannot use both an required and optional catch-all route at the same level ("[...'+this.restSlugName+']" and "'+B[0]+'" ).');handleSlug(this.optionalRestSlugName,en),this.optionalRestSlugName=en,er="[[...]]"}else{if(null!=this.optionalRestSlugName)throw Error('You cannot use both an optional and required catch-all route at the same level ("[[...'+this.optionalRestSlugName+']]" and "'+B[0]+'").');handleSlug(this.restSlugName,en),this.restSlugName=en,er="[...]"}}else{if(eo)throw Error('Optional route parameters are not yet supported ("'+B[0]+'").');handleSlug(this.slugName,en),this.slugName=en,er="[]"}}this.children.has(er)||this.children.set(er,new UrlNode),this.children.get(er)._insert(B.slice(1),Z,et)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}};function getSortedRoutes(B){let Z=new UrlNode;return B.forEach(B=>Z.insert(B)),Z.smoosh()}},392:(B,Z)=>{"use strict";function isGroupSegment(B){return"("===B[0]&&B.endsWith(")")}Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"isGroupSegment",{enumerable:!0,get:function(){return isGroupSegment}})},94005:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"default",{enumerable:!0,get:function(){return SideEffect}});let er=et(9885),useClientOnlyLayoutEffect=()=>{},useClientOnlyEffect=()=>{};function SideEffect(B){var Z;let{headManager:et,reduceComponentsToState:en}=B;function emitChange(){if(et&&et.mountedInstances){let Z=er.Children.toArray(Array.from(et.mountedInstances).filter(Boolean));et.updateHead(en(Z,B))}}return null==et||null==(Z=et.mountedInstances)||Z.add(B.children),emitChange(),useClientOnlyLayoutEffect(()=>{var Z;return null==et||null==(Z=et.mountedInstances)||Z.add(B.children),()=>{var Z;null==et||null==(Z=et.mountedInstances)||Z.delete(B.children)}}),useClientOnlyLayoutEffect(()=>(et&&(et._pendingUpdate=emitChange),()=>{et&&(et._pendingUpdate=emitChange)})),useClientOnlyEffect(()=>(et&&et._pendingUpdate&&(et._pendingUpdate(),et._pendingUpdate=null),()=>{et&&et._pendingUpdate&&(et._pendingUpdate(),et._pendingUpdate=null)})),null}},84679:(B,Z)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{WEB_VITALS:function(){return et},execOnce:function(){return execOnce},isAbsoluteUrl:function(){return isAbsoluteUrl},getLocationOrigin:function(){return getLocationOrigin},getURL:function(){return getURL},getDisplayName:function(){return getDisplayName},isResSent:function(){return isResSent},normalizeRepeatedSlashes:function(){return normalizeRepeatedSlashes},loadGetInitialProps:function(){return loadGetInitialProps},SP:function(){return en},ST:function(){return eo},DecodeError:function(){return DecodeError},NormalizeError:function(){return NormalizeError},PageNotFoundError:function(){return PageNotFoundError},MissingStaticPage:function(){return MissingStaticPage},MiddlewareNotFoundError:function(){return MiddlewareNotFoundError},stringifyError:function(){return stringifyError}});let et=["CLS","FCP","FID","INP","LCP","TTFB"];function execOnce(B){let Z,et=!1;return function(){for(var er=arguments.length,en=Array(er),eo=0;eo<er;eo++)en[eo]=arguments[eo];return et||(et=!0,Z=B(...en)),Z}}let er=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,isAbsoluteUrl=B=>er.test(B);function getLocationOrigin(){let{protocol:B,hostname:Z,port:et}=window.location;return B+"//"+Z+(et?":"+et:"")}function getURL(){let{href:B}=window.location,Z=getLocationOrigin();return B.substring(Z.length)}function getDisplayName(B){return"string"==typeof B?B:B.displayName||B.name||"Unknown"}function isResSent(B){return B.finished||B.headersSent}function normalizeRepeatedSlashes(B){let Z=B.split("?"),et=Z[0];return et.replace(/\\/g,"/").replace(/\/\/+/g,"/")+(Z[1]?"?"+Z.slice(1).join("?"):"")}async function loadGetInitialProps(B,Z){let et=Z.res||Z.ctx&&Z.ctx.res;if(!B.getInitialProps)return Z.ctx&&Z.Component?{pageProps:await loadGetInitialProps(Z.Component,Z.ctx)}:{};let er=await B.getInitialProps(Z);if(et&&isResSent(et))return er;if(!er){let Z='"'+getDisplayName(B)+'.getInitialProps()" should resolve to an object. But found "'+er+'" instead.';throw Error(Z)}return er}let en="undefined"!=typeof performance,eo=en&&["mark","measure","getEntriesByName"].every(B=>"function"==typeof performance[B]);let DecodeError=class DecodeError extends Error{};let NormalizeError=class NormalizeError extends Error{};let PageNotFoundError=class PageNotFoundError extends Error{constructor(B){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+B}};let MissingStaticPage=class MissingStaticPage extends Error{constructor(B,Z){super(),this.message="Failed to load static file for page: "+B+" "+Z}};let MiddlewareNotFoundError=class MiddlewareNotFoundError extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}};function stringifyError(B){return JSON.stringify({message:B.message,stack:B.stack})}},8601:(B,Z)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"warnOnce",{enumerable:!0,get:function(){return warnOnce}});let warnOnce=B=>{}},95153:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"createProxy",{enumerable:!0,get:function(){return en}});let er=et(55951),en=er.createClientModuleProxy},28730:(B,Z,et)=>{"use strict";let{createProxy:er}=et(95153);B.exports=er("C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\node_modules\\next\\dist\\client\\components\\app-router.js")},1099:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"DraftMode",{enumerable:!0,get:function(){return DraftMode}});let er=et(3657);let DraftMode=class DraftMode{get isEnabled(){return this._provider.isEnabled}enable(){if(!(0,er.staticGenerationBailout)("draftMode().enable()"))return this._provider.enable()}disable(){if(!(0,er.staticGenerationBailout)("draftMode().disable()"))return this._provider.disable()}constructor(B){this._provider=B}};("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},37284:(B,Z,et)=>{"use strict";let{createProxy:er}=et(95153);B.exports=er("C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js")},92491:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{headers:function(){return headers},cookies:function(){return cookies},draftMode:function(){return draftMode}});let er=et(96888),en=et(21306),eo=et(76449),ea=et(91877),ei=et(25528),es=et(3657),eu=et(1099);function headers(){if((0,es.staticGenerationBailout)("headers",{link:"https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering"}))return en.HeadersAdapter.seal(new Headers({}));let B=ea.requestAsyncStorage.getStore();if(!B)throw Error("Invariant: headers() expects to have requestAsyncStorage, none available.");return B.headers}function cookies(){if((0,es.staticGenerationBailout)("cookies",{link:"https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering"}))return er.RequestCookiesAdapter.seal(new eo.RequestCookies(new Headers({})));let B=ea.requestAsyncStorage.getStore();if(!B)throw Error("Invariant: cookies() expects to have requestAsyncStorage, none available.");let Z=ei.actionAsyncStorage.getStore();return Z&&(Z.isAction||Z.isAppRoute)?B.mutableCookies:B.cookies}function draftMode(){let B=ea.requestAsyncStorage.getStore();if(!B)throw Error("Invariant: draftMode() expects to have requestAsyncStorage, none available.");return new eu.DraftMode(B.draftMode)}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},79195:(B,Z)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{DYNAMIC_ERROR_CODE:function(){return et},DynamicServerError:function(){return DynamicServerError}});let et="DYNAMIC_SERVER_USAGE";let DynamicServerError=class DynamicServerError extends Error{constructor(B){super("Dynamic server usage: "+B),this.digest=et}};("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},68165:(B,Z,et)=>{"use strict";let{createProxy:er}=et(95153);B.exports=er("C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js")},22236:(B,Z,et)=>{"use strict";function maybePostpone(B,Z){if(!B.isStaticGeneration||!B.experimental.ppr)return;let er=et(3542);"function"==typeof er.unstable_postpone&&er.unstable_postpone(Z)}Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"maybePostpone",{enumerable:!0,get:function(){return maybePostpone}}),("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},24009:(B,Z,et)=>{"use strict";let{createProxy:er}=et(95153);B.exports=er("C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js")},9291:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"default",{enumerable:!0,get:function(){return NotFound}});let er=et(95196),en=er._(et(3542)),eo={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},desc:{display:"inline-block"},h1:{display:"inline-block",margin:"0 20px 0 0",padding:"0 23px 0 0",fontSize:24,fontWeight:500,verticalAlign:"top",lineHeight:"49px"},h2:{fontSize:14,fontWeight:400,lineHeight:"49px",margin:0}};function NotFound(){return en.default.createElement(en.default.Fragment,null,en.default.createElement("title",null,"404: This page could not be found."),en.default.createElement("div",{style:eo.error},en.default.createElement("div",null,en.default.createElement("style",{dangerouslySetInnerHTML:{__html:"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}),en.default.createElement("h1",{className:"next-error-h1",style:eo.h1},"404"),en.default.createElement("div",{style:eo.desc},en.default.createElement("h2",{style:eo.h2},"This page could not be found.")))))}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},85676:(B,Z,et)=>{"use strict";let{createProxy:er}=et(95153);B.exports=er("C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js")},31263:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"createSearchParamsBailoutProxy",{enumerable:!0,get:function(){return createSearchParamsBailoutProxy}});let er=et(3657);function createSearchParamsBailoutProxy(){return new Proxy({},{get(B,Z){"string"==typeof Z&&(0,er.staticGenerationBailout)("searchParams."+Z)}})}("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},3657:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"staticGenerationBailout",{enumerable:!0,get:function(){return staticGenerationBailout}});let er=et(79195),en=et(22236),eo=et(25319);let StaticGenBailoutError=class StaticGenBailoutError extends Error{constructor(...B){super(...B),this.code="NEXT_STATIC_GEN_BAILOUT"}};function formatErrorMessage(B,Z){let{dynamic:et,link:er}=Z||{};return"Page"+(et?' with `dynamic = "'+et+'"`':"")+" couldn't be rendered statically because it used `"+B+"`."+(er?" See more info here: "+er:"")}let staticGenerationBailout=(B,Z)=>{let et=eo.staticGenerationAsyncStorage.getStore();if(!et)return!1;if(et.forceStatic)return!0;if(et.dynamicShouldError){var ea;throw new StaticGenBailoutError(formatErrorMessage(B,{...Z,dynamic:null!=(ea=null==Z?void 0:Z.dynamic)?ea:"error"}))}let ei=formatErrorMessage(B,{...Z,link:"https://nextjs.org/docs/messages/dynamic-server-error"});if((0,en.maybePostpone)(et,ei),et.revalidate=0,(null==Z?void 0:Z.dynamic)||(et.staticPrefetchBailout=!0),et.isStaticGeneration){let Z=new er.DynamicServerError(ei);throw et.dynamicUsageDescription=B,et.dynamicUsageStack=Z.stack,Z}return!1};("function"==typeof Z.default||"object"==typeof Z.default&&null!==Z.default)&&void 0===Z.default.__esModule&&(Object.defineProperty(Z.default,"__esModule",{value:!0}),Object.assign(Z.default,Z),B.exports=Z.default)},37701:(B,Z,et)=>{"use strict";let{createProxy:er}=et(95153);B.exports=er("C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\node_modules\\next\\dist\\client\\components\\static-generation-searchparams-bailout-provider.js")},30989:B=>{"use strict";var Z=Object.defineProperty,et=Object.getOwnPropertyDescriptor,er=Object.getOwnPropertyNames,en=Object.prototype.hasOwnProperty,eo={};function stringifyCookie(B){var Z;let et=["path"in B&&B.path&&`Path=${B.path}`,"expires"in B&&(B.expires||0===B.expires)&&`Expires=${("number"==typeof B.expires?new Date(B.expires):B.expires).toUTCString()}`,"maxAge"in B&&"number"==typeof B.maxAge&&`Max-Age=${B.maxAge}`,"domain"in B&&B.domain&&`Domain=${B.domain}`,"secure"in B&&B.secure&&"Secure","httpOnly"in B&&B.httpOnly&&"HttpOnly","sameSite"in B&&B.sameSite&&`SameSite=${B.sameSite}`,"priority"in B&&B.priority&&`Priority=${B.priority}`].filter(Boolean);return`${B.name}=${encodeURIComponent(null!=(Z=B.value)?Z:"")}; ${et.join("; ")}`}function parseCookie(B){let Z=new Map;for(let et of B.split(/; */)){if(!et)continue;let B=et.indexOf("=");if(-1===B){Z.set(et,"true");continue}let[er,en]=[et.slice(0,B),et.slice(B+1)];try{Z.set(er,decodeURIComponent(null!=en?en:"true"))}catch{}}return Z}function parseSetCookie(B){if(!B)return;let[[Z,et],...er]=parseCookie(B),{domain:en,expires:eo,httponly:ea,maxage:ei,path:es,samesite:eu,secure:el,priority:ec}=Object.fromEntries(er.map(([B,Z])=>[B.toLowerCase(),Z])),ed={name:Z,value:decodeURIComponent(et),domain:en,...eo&&{expires:new Date(eo)},...ea&&{httpOnly:!0},..."string"==typeof ei&&{maxAge:Number(ei)},path:es,...eu&&{sameSite:parseSameSite(eu)},...el&&{secure:!0},...ec&&{priority:parsePriority(ec)}};return compact(ed)}function compact(B){let Z={};for(let et in B)B[et]&&(Z[et]=B[et]);return Z}((B,et)=>{for(var er in et)Z(B,er,{get:et[er],enumerable:!0})})(eo,{RequestCookies:()=>es,ResponseCookies:()=>eu,parseCookie:()=>parseCookie,parseSetCookie:()=>parseSetCookie,stringifyCookie:()=>stringifyCookie}),B.exports=((B,eo,ea,ei)=>{if(eo&&"object"==typeof eo||"function"==typeof eo)for(let ea of er(eo))en.call(B,ea)||void 0===ea||Z(B,ea,{get:()=>eo[ea],enumerable:!(ei=et(eo,ea))||ei.enumerable});return B})(Z({},"__esModule",{value:!0}),eo);var ea=["strict","lax","none"];function parseSameSite(B){return B=B.toLowerCase(),ea.includes(B)?B:void 0}var ei=["low","medium","high"];function parsePriority(B){return B=B.toLowerCase(),ei.includes(B)?B:void 0}function splitCookiesString(B){if(!B)return[];var Z,et,er,en,eo,ea=[],ei=0;function skipWhitespace(){for(;ei<B.length&&/\s/.test(B.charAt(ei));)ei+=1;return ei<B.length}function notSpecialChar(){return"="!==(et=B.charAt(ei))&&";"!==et&&","!==et}for(;ei<B.length;){for(Z=ei,eo=!1;skipWhitespace();)if(","===(et=B.charAt(ei))){for(er=ei,ei+=1,skipWhitespace(),en=ei;ei<B.length&&notSpecialChar();)ei+=1;ei<B.length&&"="===B.charAt(ei)?(eo=!0,ei=en,ea.push(B.substring(Z,er)),Z=ei):ei=er+1}else ei+=1;(!eo||ei>=B.length)&&ea.push(B.substring(Z,B.length))}return ea}var es=class{constructor(B){this._parsed=new Map,this._headers=B;let Z=B.get("cookie");if(Z){let B=parseCookie(Z);for(let[Z,et]of B)this._parsed.set(Z,{name:Z,value:et})}}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...B){let Z="string"==typeof B[0]?B[0]:B[0].name;return this._parsed.get(Z)}getAll(...B){var Z;let et=Array.from(this._parsed);if(!B.length)return et.map(([B,Z])=>Z);let er="string"==typeof B[0]?B[0]:null==(Z=B[0])?void 0:Z.name;return et.filter(([B])=>B===er).map(([B,Z])=>Z)}has(B){return this._parsed.has(B)}set(...B){let[Z,et]=1===B.length?[B[0].name,B[0].value]:B,er=this._parsed;return er.set(Z,{name:Z,value:et}),this._headers.set("cookie",Array.from(er).map(([B,Z])=>stringifyCookie(Z)).join("; ")),this}delete(B){let Z=this._parsed,et=Array.isArray(B)?B.map(B=>Z.delete(B)):Z.delete(B);return this._headers.set("cookie",Array.from(Z).map(([B,Z])=>stringifyCookie(Z)).join("; ")),et}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(B=>`${B.name}=${encodeURIComponent(B.value)}`).join("; ")}},eu=class{constructor(B){var Z,et,er;this._parsed=new Map,this._headers=B;let en=null!=(er=null!=(et=null==(Z=B.getSetCookie)?void 0:Z.call(B))?et:B.get("set-cookie"))?er:[],eo=Array.isArray(en)?en:splitCookiesString(en);for(let B of eo){let Z=parseSetCookie(B);Z&&this._parsed.set(Z.name,Z)}}get(...B){let Z="string"==typeof B[0]?B[0]:B[0].name;return this._parsed.get(Z)}getAll(...B){var Z;let et=Array.from(this._parsed.values());if(!B.length)return et;let er="string"==typeof B[0]?B[0]:null==(Z=B[0])?void 0:Z.name;return et.filter(B=>B.name===er)}has(B){return this._parsed.has(B)}set(...B){let[Z,et,er]=1===B.length?[B[0].name,B[0].value,B[0]]:B,en=this._parsed;return en.set(Z,normalizeCookie({name:Z,value:et,...er})),replace(en,this._headers),this}delete(...B){let[Z,et,er]="string"==typeof B[0]?[B[0]]:[B[0].name,B[0].path,B[0].domain];return this.set({name:Z,path:et,domain:er,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(stringifyCookie).join("; ")}};function replace(B,Z){for(let[,et]of(Z.delete("set-cookie"),B)){let B=stringifyCookie(et);Z.append("set-cookie",B)}}function normalizeCookie(B={name:"",value:""}){return"number"==typeof B.expires&&(B.expires=new Date(B.expires)),B.maxAge&&(B.expires=new Date(Date.now()+1e3*B.maxAge)),(null===B.path||void 0===B.path)&&(B.path="/"),B}},32564:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{renderToReadableStream:function(){return er.renderToReadableStream},decodeReply:function(){return er.decodeReply},decodeAction:function(){return er.decodeAction},decodeFormState:function(){return er.decodeFormState},AppRouter:function(){return en.default},LayoutRouter:function(){return eo.default},RenderFromTemplateContext:function(){return ea.default},staticGenerationAsyncStorage:function(){return ei.staticGenerationAsyncStorage},requestAsyncStorage:function(){return es.requestAsyncStorage},actionAsyncStorage:function(){return eu.actionAsyncStorage},staticGenerationBailout:function(){return el.staticGenerationBailout},createSearchParamsBailoutProxy:function(){return ed.createSearchParamsBailoutProxy},serverHooks:function(){return ef},preloadStyle:function(){return ep.preloadStyle},preloadFont:function(){return ep.preloadFont},preconnect:function(){return ep.preconnect},taintObjectReference:function(){return eh.taintObjectReference},StaticGenerationSearchParamsBailoutProvider:function(){return ec.default},NotFoundBoundary:function(){return eg}});let er=et(55951),en=_interop_require_default(et(28730)),eo=_interop_require_default(et(68165)),ea=_interop_require_default(et(85676)),ei=et(25319),es=et(91877),eu=et(25528),el=et(3657),ec=_interop_require_default(et(37701)),ed=et(31263),ef=_interop_require_wildcard(et(79195)),ep=et(48483),eh=et(13369);function _interop_require_default(B){return B&&B.__esModule?B:{default:B}}function _getRequireWildcardCache(B){if("function"!=typeof WeakMap)return null;var Z=new WeakMap,et=new WeakMap;return(_getRequireWildcardCache=function(B){return B?et:Z})(B)}function _interop_require_wildcard(B,Z){if(!Z&&B&&B.__esModule)return B;if(null===B||"object"!=typeof B&&"function"!=typeof B)return{default:B};var et=_getRequireWildcardCache(Z);if(et&&et.has(B))return et.get(B);var er={},en=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var eo in B)if("default"!==eo&&Object.prototype.hasOwnProperty.call(B,eo)){var ea=en?Object.getOwnPropertyDescriptor(B,eo):null;ea&&(ea.get||ea.set)?Object.defineProperty(er,eo,ea):er[eo]=B[eo]}return er.default=B,et&&et.set(B,er),er}let{NotFoundBoundary:eg}=et(24009)},48483:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{preloadStyle:function(){return preloadStyle},preloadFont:function(){return preloadFont},preconnect:function(){return preconnect}});let er=_interop_require_default(et(58337));function _interop_require_default(B){return B&&B.__esModule?B:{default:B}}function preloadStyle(B,Z){let et={as:"style"};"string"==typeof Z&&(et.crossOrigin=Z),er.default.preload(B,et)}function preloadFont(B,Z,et){let en={as:"font",type:Z};"string"==typeof et&&(en.crossOrigin=et),er.default.preload(B,en)}function preconnect(B,Z){er.default.preconnect(B,"string"==typeof Z?{crossOrigin:Z}:void 0)}},13369:(B,Z,et)=>{"use strict";function notImplemented(){throw Error("Taint can only be used with the taint flag.")}Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{taintObjectReference:function(){return er},taintUniqueValue:function(){return en}}),et(3542);let er=notImplemented,en=notImplemented},16132:(B,Z)=>{"use strict";var et;Object.defineProperty(Z,"x",{enumerable:!0,get:function(){return et}}),function(B){B.PAGES="PAGES",B.PAGES_API="PAGES_API",B.APP_PAGE="APP_PAGE",B.APP_ROUTE="APP_ROUTE"}(et||(et={}))},67096:(B,Z,et)=>{"use strict";B.exports=et(20399)},58337:(B,Z,et)=>{"use strict";B.exports=et(67096).vendored["react-rsc"].ReactDOM},4656:(B,Z,et)=>{"use strict";B.exports=et(67096).vendored["react-rsc"].ReactJsxRuntime},55951:(B,Z,et)=>{"use strict";B.exports=et(67096).vendored["react-rsc"].ReactServerDOMWebpackServerEdge},3542:(B,Z,et)=>{"use strict";B.exports=et(67096).vendored["react-rsc"].React},21306:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{ReadonlyHeadersError:function(){return ReadonlyHeadersError},HeadersAdapter:function(){return HeadersAdapter}});let er=et(49165);let ReadonlyHeadersError=class ReadonlyHeadersError extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new ReadonlyHeadersError}};let HeadersAdapter=class HeadersAdapter extends Headers{constructor(B){super(),this.headers=new Proxy(B,{get(Z,et,en){if("symbol"==typeof et)return er.ReflectAdapter.get(Z,et,en);let eo=et.toLowerCase(),ea=Object.keys(B).find(B=>B.toLowerCase()===eo);if(void 0!==ea)return er.ReflectAdapter.get(Z,ea,en)},set(Z,et,en,eo){if("symbol"==typeof et)return er.ReflectAdapter.set(Z,et,en,eo);let ea=et.toLowerCase(),ei=Object.keys(B).find(B=>B.toLowerCase()===ea);return er.ReflectAdapter.set(Z,ei??et,en,eo)},has(Z,et){if("symbol"==typeof et)return er.ReflectAdapter.has(Z,et);let en=et.toLowerCase(),eo=Object.keys(B).find(B=>B.toLowerCase()===en);return void 0!==eo&&er.ReflectAdapter.has(Z,eo)},deleteProperty(Z,et){if("symbol"==typeof et)return er.ReflectAdapter.deleteProperty(Z,et);let en=et.toLowerCase(),eo=Object.keys(B).find(B=>B.toLowerCase()===en);return void 0===eo||er.ReflectAdapter.deleteProperty(Z,eo)}})}static seal(B){return new Proxy(B,{get(B,Z,et){switch(Z){case"append":case"delete":case"set":return ReadonlyHeadersError.callable;default:return er.ReflectAdapter.get(B,Z,et)}}})}merge(B){return Array.isArray(B)?B.join(", "):B}static from(B){return B instanceof Headers?B:new HeadersAdapter(B)}append(B,Z){let et=this.headers[B];"string"==typeof et?this.headers[B]=[et,Z]:Array.isArray(et)?et.push(Z):this.headers[B]=Z}delete(B){delete this.headers[B]}get(B){let Z=this.headers[B];return void 0!==Z?this.merge(Z):null}has(B){return void 0!==this.headers[B]}set(B,Z){this.headers[B]=Z}forEach(B,Z){for(let[et,er]of this.entries())B.call(Z,er,et,this)}*entries(){for(let B of Object.keys(this.headers)){let Z=B.toLowerCase(),et=this.get(Z);yield[Z,et]}}*keys(){for(let B of Object.keys(this.headers)){let Z=B.toLowerCase();yield Z}}*values(){for(let B of Object.keys(this.headers)){let Z=this.get(B);yield Z}}[Symbol.iterator](){return this.entries()}}},49165:(B,Z)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"ReflectAdapter",{enumerable:!0,get:function(){return ReflectAdapter}});let ReflectAdapter=class ReflectAdapter{static get(B,Z,et){let er=Reflect.get(B,Z,et);return"function"==typeof er?er.bind(B):er}static set(B,Z,et,er){return Reflect.set(B,Z,et,er)}static has(B,Z){return Reflect.has(B,Z)}static deleteProperty(B,Z){return Reflect.deleteProperty(B,Z)}}},96888:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{ReadonlyRequestCookiesError:function(){return ReadonlyRequestCookiesError},RequestCookiesAdapter:function(){return RequestCookiesAdapter},getModifiedCookieValues:function(){return getModifiedCookieValues},appendMutableCookies:function(){return appendMutableCookies},MutableRequestCookiesAdapter:function(){return MutableRequestCookiesAdapter}});let er=et(76449),en=et(49165);let ReadonlyRequestCookiesError=class ReadonlyRequestCookiesError extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#cookiessetname-value-options")}static callable(){throw new ReadonlyRequestCookiesError}};let RequestCookiesAdapter=class RequestCookiesAdapter{static seal(B){return new Proxy(B,{get(B,Z,et){switch(Z){case"clear":case"delete":case"set":return ReadonlyRequestCookiesError.callable;default:return en.ReflectAdapter.get(B,Z,et)}}})}};let eo=Symbol.for("next.mutated.cookies");function getModifiedCookieValues(B){let Z=B[eo];return Z&&Array.isArray(Z)&&0!==Z.length?Z:[]}function appendMutableCookies(B,Z){let et=getModifiedCookieValues(Z);if(0===et.length)return!1;let en=new er.ResponseCookies(B),eo=en.getAll();for(let B of et)en.set(B);for(let B of eo)en.set(B);return!0}let MutableRequestCookiesAdapter=class MutableRequestCookiesAdapter{static wrap(B,Z){let et=new er.ResponseCookies(new Headers);for(let Z of B.getAll())et.set(Z);let ea=[],ei=new Set,updateResponseCookies=()=>{var B;let en=null==fetch.__nextGetStaticStore?void 0:null==(B=fetch.__nextGetStaticStore.call(fetch))?void 0:B.getStore();en&&(en.pathWasRevalidated=!0);let eo=et.getAll();if(ea=eo.filter(B=>ei.has(B.name)),Z){let B=[];for(let Z of ea){let et=new er.ResponseCookies(new Headers);et.set(Z),B.push(et.toString())}Z(B)}};return new Proxy(et,{get(B,Z,et){switch(Z){case eo:return ea;case"delete":return function(...Z){ei.add("string"==typeof Z[0]?Z[0]:Z[0].name);try{B.delete(...Z)}finally{updateResponseCookies()}};case"set":return function(...Z){ei.add("string"==typeof Z[0]?Z[0]:Z[0].name);try{return B.set(...Z)}finally{updateResponseCookies()}};default:return en.ReflectAdapter.get(B,Z,et)}}})}}},76449:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{RequestCookies:function(){return er.RequestCookies},ResponseCookies:function(){return er.ResponseCookies}});let er=et(30989)},24596:(B,Z,et)=>{"use strict";B.exports=et(92491)},45082:(B,Z)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{NEXT_QUERY_PARAM_PREFIX:function(){return et},PRERENDER_REVALIDATE_HEADER:function(){return er},PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER:function(){return en},NEXT_DID_POSTPONE_HEADER:function(){return eo},NEXT_CACHE_TAGS_HEADER:function(){return ea},NEXT_CACHE_SOFT_TAGS_HEADER:function(){return ei},NEXT_CACHE_REVALIDATED_TAGS_HEADER:function(){return es},NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER:function(){return eu},NEXT_CACHE_TAG_MAX_LENGTH:function(){return el},NEXT_CACHE_SOFT_TAG_MAX_LENGTH:function(){return ec},NEXT_CACHE_IMPLICIT_TAG_ID:function(){return ed},CACHE_ONE_YEAR:function(){return ef},MIDDLEWARE_FILENAME:function(){return ep},MIDDLEWARE_LOCATION_REGEXP:function(){return eh},INSTRUMENTATION_HOOK_FILENAME:function(){return eg},PAGES_DIR_ALIAS:function(){return ey},DOT_NEXT_ALIAS:function(){return em},ROOT_DIR_ALIAS:function(){return ev},APP_DIR_ALIAS:function(){return eb},RSC_MOD_REF_PROXY_ALIAS:function(){return eS},RSC_ACTION_VALIDATE_ALIAS:function(){return eP},RSC_ACTION_PROXY_ALIAS:function(){return eO},RSC_ACTION_ENCRYPTION_ALIAS:function(){return e_},RSC_ACTION_CLIENT_WRAPPER_ALIAS:function(){return ex},PUBLIC_DIR_MIDDLEWARE_CONFLICT:function(){return eR},SSG_GET_INITIAL_PROPS_CONFLICT:function(){return ew},SERVER_PROPS_GET_INIT_PROPS_CONFLICT:function(){return eE},SERVER_PROPS_SSG_CONFLICT:function(){return ej},STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR:function(){return eC},SERVER_PROPS_EXPORT_ERROR:function(){return eM},GSP_NO_RETURNED_VALUE:function(){return eA},GSSP_NO_RETURNED_VALUE:function(){return ek},UNSTABLE_REVALIDATE_RENAME_ERROR:function(){return eT},GSSP_COMPONENT_MEMBER_ERROR:function(){return eN},NON_STANDARD_NODE_ENV:function(){return eL},SSG_FALLBACK_EXPORT_ERROR:function(){return eD},ESLINT_DEFAULT_DIRS:function(){return eI},ESLINT_PROMPT_VALUES:function(){return eF},SERVER_RUNTIME:function(){return eU},WEBPACK_LAYERS:function(){return e$},WEBPACK_RESOURCE_QUERIES:function(){return ez}});let et="nxtP",er="x-prerender-revalidate",en="x-prerender-revalidate-if-generated",eo="x-nextjs-postponed",ea="x-next-cache-tags",ei="x-next-cache-soft-tags",es="x-next-revalidated-tags",eu="x-next-revalidate-tag-token",el=256,ec=1024,ed="_N_T_",ef=31536e3,ep="middleware",eh=`(?:src/)?${ep}`,eg="instrumentation",ey="private-next-pages",em="private-dot-next",ev="private-next-root-dir",eb="private-next-app-dir",eS="private-next-rsc-mod-ref-proxy",eP="private-next-rsc-action-validate",eO="private-next-rsc-action-proxy",e_="private-next-rsc-action-encryption",ex="private-next-rsc-action-client-wrapper",eR="You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict",ew="You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",eE="You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",ej="You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",eC="can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",eM="pages with `getServerSideProps` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export",eA="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",ek="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",eT="The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",eN="can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member",eL='You are using a non-standard "NODE_ENV" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env',eD="Pages with `fallback` enabled in `getStaticPaths` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export",eI=["app","pages","components","lib","src"],eF=[{title:"Strict",recommended:!0,config:{extends:"next/core-web-vitals"}},{title:"Base",config:{extends:"next"}},{title:"Cancel",config:null}],eU={edge:"edge",experimentalEdge:"experimental-edge",nodejs:"nodejs"},eq={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",api:"api",middleware:"middleware",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",appMetadataRoute:"app-metadata-route",appRouteHandler:"app-route-handler"},e$={...eq,GROUP:{server:[eq.reactServerComponents,eq.actionBrowser,eq.appMetadataRoute,eq.appRouteHandler],nonClientServerTarget:[eq.middleware,eq.api],app:[eq.reactServerComponents,eq.actionBrowser,eq.appMetadataRoute,eq.appRouteHandler,eq.serverSideRendering,eq.appPagesBrowser]}},ez={edgeSSREntry:"__next_edge_ssr_entry__",metadata:"__next_metadata__",metadataRoute:"__next_metadata_route__",metadataImageMeta:"__next_metadata_image_meta__"}},42290:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Object.defineProperty(Z,"getSegmentParam",{enumerable:!0,get:function(){return getSegmentParam}});let er=et(84265);function getSegmentParam(B){let Z=er.INTERCEPTION_ROUTE_MARKERS.find(Z=>B.startsWith(Z));return(Z&&(B=B.slice(Z.length)),B.startsWith("[[...")&&B.endsWith("]]"))?{type:"optional-catchall",param:B.slice(5,-2)}:B.startsWith("[...")&&B.endsWith("]")?{type:"catchall",param:B.slice(4,-1)}:B.startsWith("[")&&B.endsWith("]")?{type:"dynamic",param:B.slice(1,-1)}:null}},84265:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),function(B,Z){for(var et in Z)Object.defineProperty(B,et,{enumerable:!0,get:Z[et]})}(Z,{INTERCEPTION_ROUTE_MARKERS:function(){return en},isInterceptionRouteAppPath:function(){return isInterceptionRouteAppPath},extractInterceptionRouteInformation:function(){return extractInterceptionRouteInformation}});let er=et(48321),en=["(..)(..)","(.)","(..)","(...)"];function isInterceptionRouteAppPath(B){return void 0!==B.split("/").find(B=>en.find(Z=>B.startsWith(Z)))}function extractInterceptionRouteInformation(B){let Z,et,eo;for(let er of B.split("/"))if(et=en.find(B=>er.startsWith(B))){[Z,eo]=B.split(et,2);break}if(!Z||!et||!eo)throw Error(`Invalid interception route: ${B}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`);switch(Z=(0,er.normalizeAppPath)(Z),et){case"(.)":eo="/"===Z?`/${eo}`:Z+"/"+eo;break;case"(..)":if("/"===Z)throw Error(`Invalid interception route: ${B}. Cannot use (..) marker at the root level, use (.) instead.`);eo=Z.split("/").slice(0,-1).concat(eo).join("/");break;case"(...)":eo="/"+eo;break;case"(..)(..)":let ea=Z.split("/");if(ea.length<=2)throw Error(`Invalid interception route: ${B}. Cannot use (..)(..) marker at the root level or one level up.`);eo=ea.slice(0,-2).concat(eo).join("/");break;default:throw Error("Invariant: unexpected marker")}return{interceptingRoute:Z,interceptedRoute:eo}}},10316:(B,Z,et)=>{"use strict";B.exports=et(20399)},12999:(B,Z,et)=>{"use strict";B.exports=et(10316).vendored.contexts.AmpContext},82428:(B,Z,et)=>{"use strict";B.exports=et(10316).vendored.contexts.AppRouterContext},75851:(B,Z,et)=>{"use strict";B.exports=et(10316).vendored.contexts.HeadManagerContext},11736:(B,Z,et)=>{"use strict";B.exports=et(10316).vendored.contexts.HooksClientContext},87927:(B,Z,et)=>{"use strict";B.exports=et(10316).vendored.contexts.ImageConfigContext},10713:(B,Z,et)=>{"use strict";B.exports=et(10316).vendored.contexts.RouterContext},75753:(B,Z,et)=>{"use strict";B.exports=et(10316).vendored.contexts.ServerInsertedHtml},88908:(B,Z,et)=>{"use strict";B.exports=et(10316).vendored["react-ssr"].ReactDOM},30784:(B,Z,et)=>{"use strict";B.exports=et(10316).vendored["react-ssr"].ReactJsxRuntime},12623:(B,Z,et)=>{"use strict";B.exports=et(10316).vendored["react-ssr"].ReactServerDOMWebpackClientEdge},9885:(B,Z,et)=>{"use strict";B.exports=et(10316).vendored["react-ssr"].React},52451:(B,Z,et)=>{B.exports=et(57990)},11440:(B,Z,et)=>{B.exports=et(30614)},57114:(B,Z,et)=>{B.exports=et(4979)},26795:(B,Z)=>{"use strict";/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var et="function"==typeof Symbol&&Symbol.for,er=et?Symbol.for("react.element"):60103,en=et?Symbol.for("react.portal"):60106,eo=et?Symbol.for("react.fragment"):60107,ea=et?Symbol.for("react.strict_mode"):60108,ei=et?Symbol.for("react.profiler"):60114,es=et?Symbol.for("react.provider"):60109,eu=et?Symbol.for("react.context"):60110,el=et?Symbol.for("react.async_mode"):60111,ec=et?Symbol.for("react.concurrent_mode"):60111,ed=et?Symbol.for("react.forward_ref"):60112,ef=et?Symbol.for("react.suspense"):60113,ep=et?Symbol.for("react.suspense_list"):60120,eh=et?Symbol.for("react.memo"):60115,eg=et?Symbol.for("react.lazy"):60116,ey=et?Symbol.for("react.block"):60121,em=et?Symbol.for("react.fundamental"):60117,ev=et?Symbol.for("react.responder"):60118,eb=et?Symbol.for("react.scope"):60119;function z(B){if("object"==typeof B&&null!==B){var Z=B.$$typeof;switch(Z){case er:switch(B=B.type){case el:case ec:case eo:case ei:case ea:case ef:return B;default:switch(B=B&&B.$$typeof){case eu:case ed:case eg:case eh:case es:return B;default:return Z}}case en:return Z}}}function A(B){return z(B)===ec}Z.AsyncMode=el,Z.ConcurrentMode=ec,Z.ContextConsumer=eu,Z.ContextProvider=es,Z.Element=er,Z.ForwardRef=ed,Z.Fragment=eo,Z.Lazy=eg,Z.Memo=eh,Z.Portal=en,Z.Profiler=ei,Z.StrictMode=ea,Z.Suspense=ef,Z.isAsyncMode=function(B){return A(B)||z(B)===el},Z.isConcurrentMode=A,Z.isContextConsumer=function(B){return z(B)===eu},Z.isContextProvider=function(B){return z(B)===es},Z.isElement=function(B){return"object"==typeof B&&null!==B&&B.$$typeof===er},Z.isForwardRef=function(B){return z(B)===ed},Z.isFragment=function(B){return z(B)===eo},Z.isLazy=function(B){return z(B)===eg},Z.isMemo=function(B){return z(B)===eh},Z.isPortal=function(B){return z(B)===en},Z.isProfiler=function(B){return z(B)===ei},Z.isStrictMode=function(B){return z(B)===ea},Z.isSuspense=function(B){return z(B)===ef},Z.isValidElementType=function(B){return"string"==typeof B||"function"==typeof B||B===eo||B===ec||B===ei||B===ea||B===ef||B===ep||"object"==typeof B&&null!==B&&(B.$$typeof===eg||B.$$typeof===eh||B.$$typeof===es||B.$$typeof===eu||B.$$typeof===ed||B.$$typeof===em||B.$$typeof===ev||B.$$typeof===eb||B.$$typeof===ey)},Z.typeOf=z},23314:(B,Z,et)=>{"use strict";B.exports=et(26795)},87771:(B,Z,et)=>{"use strict";et.d(Z,{zt:()=>eh,I0:()=>ey,v9:()=>ec});var er=et(61928),en=et(96122),eo=et(88908);let batch=function(B){B()},getBatch=()=>batch;var ea=et(9885);let ei=Symbol.for("react-redux-context"),es="undefined"!=typeof globalThis?globalThis:{};function getContext(){var B;if(!ea.createContext)return{};let Z=null!=(B=es[ei])?B:es[ei]=new Map,et=Z.get(ea.createContext);return et||(et=ea.createContext(null),Z.set(ea.createContext,et)),et}let eu=getContext();function createReduxContextHook(B=eu){return function(){let Z=(0,ea.useContext)(B);return Z}}let el=createReduxContextHook(),useSyncExternalStoreWithSelector=()=>{throw Error("uSES not initialized!")},refEquality=(B,Z)=>B===Z;function createSelectorHook(B=eu){let Z=B===eu?el:createReduxContextHook(B);return function(B,et={}){let{equalityFn:er=refEquality,stabilityCheck:en,noopCheck:eo}="function"==typeof et?{equalityFn:et}:et,{store:ei,subscription:es,getServerState:eu,stabilityCheck:el,noopCheck:ec}=Z();(0,ea.useRef)(!0);let ed=(0,ea.useCallback)({[B.name](Z){let et=B(Z);return et}}[B.name],[B,el,en]),ef=useSyncExternalStoreWithSelector(es.addNestedSub,ei.getState,eu||ei.getState,ed,er);return(0,ea.useDebugValue)(ef),ef}}let ec=createSelectorHook();function createListenerCollection(){let B=getBatch(),Z=null,et=null;return{clear(){Z=null,et=null},notify(){B(()=>{let B=Z;for(;B;)B.callback(),B=B.next})},get(){let B=[],et=Z;for(;et;)B.push(et),et=et.next;return B},subscribe(B){let er=!0,en=et={callback:B,next:null,prev:et};return en.prev?en.prev.next=en:Z=en,function(){er&&null!==Z&&(er=!1,en.next?en.next.prev=en.prev:et=en.prev,en.prev?en.prev.next=en.next:Z=en.next)}}}}et(86753),et(40181);let ed={notify(){},get:()=>[]};function Subscription_createSubscription(B,Z){let et;let er=ed,en=0,eo=!1;function addNestedSub(B){trySubscribe();let Z=er.subscribe(B),et=!1;return()=>{et||(et=!0,Z(),tryUnsubscribe())}}function notifyNestedSubs(){er.notify()}function handleChangeWrapper(){ea.onStateChange&&ea.onStateChange()}function isSubscribed(){return eo}function trySubscribe(){en++,et||(et=Z?Z.addNestedSub(handleChangeWrapper):B.subscribe(handleChangeWrapper),er=createListenerCollection())}function tryUnsubscribe(){en--,et&&0===en&&(et(),et=void 0,er.clear(),er=ed)}function trySubscribeSelf(){eo||(eo=!0,trySubscribe())}function tryUnsubscribeSelf(){eo&&(eo=!1,tryUnsubscribe())}let ea={addNestedSub,notifyNestedSubs,handleChangeWrapper,isSubscribed,trySubscribe:trySubscribeSelf,tryUnsubscribe:tryUnsubscribeSelf,getListeners:()=>er};return ea}let ef=!!("undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement),ep=ef?ea.useLayoutEffect:ea.useEffect;function Provider({store:B,context:Z,children:et,serverState:er,stabilityCheck:en="once",noopCheck:eo="once"}){let ei=ea.useMemo(()=>{let Z=Subscription_createSubscription(B);return{store:B,subscription:Z,getServerState:er?()=>er:void 0,stabilityCheck:en,noopCheck:eo}},[B,er,en,eo]),es=ea.useMemo(()=>B.getState(),[B]);return ep(()=>{let{subscription:Z}=ei;return Z.onStateChange=Z.notifyNestedSubs,Z.trySubscribe(),es!==B.getState()&&Z.notifyNestedSubs(),()=>{Z.tryUnsubscribe(),Z.onStateChange=void 0}},[ei,es]),ea.createElement((Z||eu).Provider,{value:ei},et)}let eh=Provider;function createStoreHook(B=eu){let Z=B===eu?el:createReduxContextHook(B);return function(){let{store:B}=Z();return B}}let eg=createStoreHook();function createDispatchHook(B=eu){let Z=B===eu?eg:createStoreHook(B);return function(){let B=Z();return B.dispatch}}let ey=createDispatchHook();useSyncExternalStoreWithSelector=en.useSyncExternalStoreWithSelector,er.useSyncExternalStore,batch=eo.unstable_batchedUpdates},37778:(B,Z,et)=>{"use strict";Z.__esModule=!0,Z.default=Z.ReactReduxContext=void 0;var er=_interopRequireWildcard(et(9885));function _getRequireWildcardCache(B){if("function"!=typeof WeakMap)return null;var Z=new WeakMap,et=new WeakMap;return(_getRequireWildcardCache=function(B){return B?et:Z})(B)}function _interopRequireWildcard(B,Z){if(!Z&&B&&B.__esModule)return B;if(null===B||"object"!=typeof B&&"function"!=typeof B)return{default:B};var et=_getRequireWildcardCache(Z);if(et&&et.has(B))return et.get(B);var er={},en=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var eo in B)if("default"!==eo&&Object.prototype.hasOwnProperty.call(B,eo)){var ea=en?Object.getOwnPropertyDescriptor(B,eo):null;ea&&(ea.get||ea.set)?Object.defineProperty(er,eo,ea):er[eo]=B[eo]}return er.default=B,et&&et.set(B,er),er}let en=Symbol.for("react-redux-context"),eo="undefined"!=typeof globalThis?globalThis:{};function getContext(){var B;if(!er.createContext)return{};let Z=null!=(B=eo[en])?B:eo[en]=new Map,et=Z.get(er.createContext);return et||(et=er.createContext(null),Z.set(er.createContext,et)),et}let ea=getContext();Z.ReactReduxContext=ea,Z.default=ea},1111:(B,Z,et)=>{"use strict";Z.__esModule=!0,Z.default=void 0;var er=_interopRequireWildcard(et(9885)),en=et(37778),eo=et(65462),ea=et(46275);function _getRequireWildcardCache(B){if("function"!=typeof WeakMap)return null;var Z=new WeakMap,et=new WeakMap;return(_getRequireWildcardCache=function(B){return B?et:Z})(B)}function _interopRequireWildcard(B,Z){if(!Z&&B&&B.__esModule)return B;if(null===B||"object"!=typeof B&&"function"!=typeof B)return{default:B};var et=_getRequireWildcardCache(Z);if(et&&et.has(B))return et.get(B);var er={},en=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var eo in B)if("default"!==eo&&Object.prototype.hasOwnProperty.call(B,eo)){var ea=en?Object.getOwnPropertyDescriptor(B,eo):null;ea&&(ea.get||ea.set)?Object.defineProperty(er,eo,ea):er[eo]=B[eo]}return er.default=B,et&&et.set(B,er),er}Z.default=function({store:B,context:Z,children:et,serverState:ei,stabilityCheck:es="once",noopCheck:eu="once"}){let el=er.useMemo(()=>{let Z=(0,eo.createSubscription)(B);return{store:B,subscription:Z,getServerState:ei?()=>ei:void 0,stabilityCheck:es,noopCheck:eu}},[B,ei,es,eu]),ec=er.useMemo(()=>B.getState(),[B]);(0,ea.useIsomorphicLayoutEffect)(()=>{let{subscription:Z}=el;return Z.onStateChange=Z.notifyNestedSubs,Z.trySubscribe(),ec!==B.getState()&&Z.notifyNestedSubs(),()=>{Z.tryUnsubscribe(),Z.onStateChange=void 0}},[el,ec]);let ed=Z||en.ReactReduxContext;return er.createElement(ed.Provider,{value:el},et)}},53305:(B,Z,et)=>{"use strict";var er=et(92439);Z.__esModule=!0,Z.default=Z.initializeConnect=void 0;var en=er(et(43259)),eo=er(et(54845)),ea=er(et(86753)),ei=_interopRequireWildcard(et(9885)),es=et(40181),eu=er(et(46734)),el=et(56047),ec=et(99630),ed=et(91722),ef=et(65462),ep=et(46275),eh=er(et(77373));er(et(69951));var eg=et(37778),ey=et(96466);let em=["reactReduxForwardedRef"];function _getRequireWildcardCache(B){if("function"!=typeof WeakMap)return null;var Z=new WeakMap,et=new WeakMap;return(_getRequireWildcardCache=function(B){return B?et:Z})(B)}function _interopRequireWildcard(B,Z){if(!Z&&B&&B.__esModule)return B;if(null===B||"object"!=typeof B&&"function"!=typeof B)return{default:B};var et=_getRequireWildcardCache(Z);if(et&&et.has(B))return et.get(B);var er={},en=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var eo in B)if("default"!==eo&&Object.prototype.hasOwnProperty.call(B,eo)){var ea=en?Object.getOwnPropertyDescriptor(B,eo):null;ea&&(ea.get||ea.set)?Object.defineProperty(er,eo,ea):er[eo]=B[eo]}return er.default=B,et&&et.set(B,er),er}let ev=ey.notInitialized;Z.initializeConnect=B=>{ev=B};let eb=[null,null];function useIsomorphicLayoutEffectWithArgs(B,Z,et){(0,ep.useIsomorphicLayoutEffect)(()=>B(...Z),et)}function captureWrapperProps(B,Z,et,er,en,eo){B.current=er,et.current=!1,en.current&&(en.current=null,eo())}function subscribeUpdates(B,Z,et,er,en,eo,ea,ei,es,eu,el){if(!B)return()=>{};let ec=!1,ed=null,checkForUpdates=()=>{let B,et;if(ec||!ei.current)return;let ef=Z.getState();try{B=er(ef,en.current)}catch(B){et=B,ed=B}et||(ed=null),B===eo.current?ea.current||eu():(eo.current=B,es.current=B,ea.current=!0,el())};return et.onStateChange=checkForUpdates,et.trySubscribe(),checkForUpdates(),()=>{if(ec=!0,et.tryUnsubscribe(),et.onStateChange=null,ed)throw ed}}function strictEqual(B,Z){return B===Z}Z.default=function(B,Z,et,{pure:er,areStatesEqual:ey=strictEqual,areOwnPropsEqual:eS=eh.default,areStatePropsEqual:eP=eh.default,areMergedPropsEqual:eO=eh.default,forwardRef:e_=!1,context:ex=eg.ReactReduxContext}={}){let eR=(0,ec.mapStateToPropsFactory)(B),ew=(0,el.mapDispatchToPropsFactory)(Z),eE=(0,ed.mergePropsFactory)(et),ej=!!B;return B=>{let Z=B.displayName||B.name||"Component",et=`Connect(${Z})`,er={shouldHandleStateChanges:ej,displayName:et,wrappedComponentName:Z,WrappedComponent:B,initMapStateToProps:eR,initMapDispatchToProps:ew,initMergeProps:eE,areStatesEqual:ey,areStatePropsEqual:eP,areOwnPropsEqual:eS,areMergedPropsEqual:eO};function ConnectFunction(Z){let et;let[ea,el,ec]=ei.useMemo(()=>{let{reactReduxForwardedRef:B}=Z,et=(0,eo.default)(Z,em);return[Z.context,B,et]},[Z]),ed=ei.useMemo(()=>ea&&ea.Consumer&&(0,es.isContextConsumer)(ei.createElement(ea.Consumer,null))?ea:ex,[ea,ex]),eh=ei.useContext(ed),eg=!!Z.store&&!!Z.store.getState&&!!Z.store.dispatch,ey=!!eh&&!!eh.store,eS=eg?Z.store:eh.store,eP=ey?eh.getServerState:eS.getState,eO=ei.useMemo(()=>(0,eu.default)(eS.dispatch,er),[eS]),[e_,eR]=ei.useMemo(()=>{if(!ej)return eb;let B=(0,ef.createSubscription)(eS,eg?void 0:eh.subscription),Z=B.notifyNestedSubs.bind(B);return[B,Z]},[eS,eg,eh]),ew=ei.useMemo(()=>eg?eh:(0,en.default)({},eh,{subscription:e_}),[eg,eh,e_]),eE=ei.useRef(),eC=ei.useRef(ec),eM=ei.useRef(),eA=ei.useRef(!1);ei.useRef(!1);let ek=ei.useRef(!1),eT=ei.useRef();(0,ep.useIsomorphicLayoutEffect)(()=>(ek.current=!0,()=>{ek.current=!1}),[]);let eN=ei.useMemo(()=>()=>eM.current&&ec===eC.current?eM.current:eO(eS.getState(),ec),[eS,ec]),eL=ei.useMemo(()=>B=>e_?subscribeUpdates(ej,eS,e_,eO,eC,eE,eA,ek,eM,eR,B):()=>{},[e_]);useIsomorphicLayoutEffectWithArgs(captureWrapperProps,[eC,eE,eA,ec,eM,eR]);try{et=ev(eL,eN,eP?()=>eO(eP(),ec):eN)}catch(B){throw eT.current&&(B.message+=`
The error may be correlated with this previous error:
${eT.current.stack}

`),B}(0,ep.useIsomorphicLayoutEffect)(()=>{eT.current=void 0,eM.current=void 0,eE.current=et});let eD=ei.useMemo(()=>ei.createElement(B,(0,en.default)({},et,{ref:el})),[el,B,et]),eI=ei.useMemo(()=>ej?ei.createElement(ed.Provider,{value:ew},eD):eD,[ed,eD,ew]);return eI}let el=ei.memo(ConnectFunction);if(el.WrappedComponent=B,el.displayName=ConnectFunction.displayName=et,e_){let Z=ei.forwardRef(function(B,Z){return ei.createElement(el,(0,en.default)({},B,{reactReduxForwardedRef:Z}))});return Z.displayName=et,Z.WrappedComponent=B,(0,ea.default)(Z,B)}return(0,ea.default)(el,B)}}},4654:(B,Z)=>{"use strict";function createInvalidArgFactory(B,Z){return(et,er)=>{throw Error(`Invalid value of type ${typeof B} for ${Z} argument when connecting component ${er.wrappedComponentName}.`)}}Z.__esModule=!0,Z.createInvalidArgFactory=createInvalidArgFactory},56047:(B,Z,et)=>{"use strict";var er=et(92439);Z.__esModule=!0,Z.mapDispatchToPropsFactory=mapDispatchToPropsFactory;var en=er(et(79198)),eo=et(88859),ea=et(4654);function mapDispatchToPropsFactory(B){return B&&"object"==typeof B?(0,eo.wrapMapToPropsConstant)(Z=>(0,en.default)(B,Z)):B?"function"==typeof B?(0,eo.wrapMapToPropsFunc)(B,"mapDispatchToProps"):(0,ea.createInvalidArgFactory)(B,"mapDispatchToProps"):(0,eo.wrapMapToPropsConstant)(B=>({dispatch:B}))}},99630:(B,Z,et)=>{"use strict";Z.__esModule=!0,Z.mapStateToPropsFactory=mapStateToPropsFactory;var er=et(88859),en=et(4654);function mapStateToPropsFactory(B){return B?"function"==typeof B?(0,er.wrapMapToPropsFunc)(B,"mapStateToProps"):(0,en.createInvalidArgFactory)(B,"mapStateToProps"):(0,er.wrapMapToPropsConstant)(()=>({}))}},91722:(B,Z,et)=>{"use strict";var er=et(92439);Z.__esModule=!0,Z.defaultMergeProps=defaultMergeProps,Z.wrapMergePropsFunc=wrapMergePropsFunc,Z.mergePropsFactory=mergePropsFactory;var en=er(et(43259));er(et(67926));var eo=et(4654);function defaultMergeProps(B,Z,et){return(0,en.default)({},et,B,Z)}function wrapMergePropsFunc(B){return function(Z,{displayName:et,areMergedPropsEqual:er}){let en,eo=!1;return function(Z,et,ea){let ei=B(Z,et,ea);return eo?er(ei,en)||(en=ei):(eo=!0,en=ei),en}}}function mergePropsFactory(B){return B?"function"==typeof B?wrapMergePropsFunc(B):(0,eo.createInvalidArgFactory)(B,"mergeProps"):()=>defaultMergeProps}},46734:(B,Z,et)=>{"use strict";var er=et(92439);Z.__esModule=!0,Z.pureFinalPropsSelectorFactory=pureFinalPropsSelectorFactory,Z.default=finalPropsSelectorFactory;var en=er(et(54845));er(et(5379));let eo=["initMapStateToProps","initMapDispatchToProps","initMergeProps"];function pureFinalPropsSelectorFactory(B,Z,et,er,{areStatesEqual:en,areOwnPropsEqual:eo,areStatePropsEqual:ea}){let ei,es,eu,el,ec,ed=!1;function handleFirstCall(en,eo){return eu=B(ei=en,es=eo),el=Z(er,es),ec=et(eu,el,es),ed=!0,ec}function handleNewPropsAndNewState(){return eu=B(ei,es),Z.dependsOnOwnProps&&(el=Z(er,es)),ec=et(eu,el,es)}function handleNewProps(){return B.dependsOnOwnProps&&(eu=B(ei,es)),Z.dependsOnOwnProps&&(el=Z(er,es)),ec=et(eu,el,es)}function handleNewState(){let Z=B(ei,es),er=!ea(Z,eu);return eu=Z,er&&(ec=et(eu,el,es)),ec}function handleSubsequentCalls(B,Z){let et=!eo(Z,es),er=!en(B,ei,Z,es);return(ei=B,es=Z,et&&er)?handleNewPropsAndNewState():et?handleNewProps():er?handleNewState():ec}return function(B,Z){return ed?handleSubsequentCalls(B,Z):handleFirstCall(B,Z)}}function finalPropsSelectorFactory(B,Z){let{initMapStateToProps:et,initMapDispatchToProps:er,initMergeProps:ea}=Z,ei=(0,en.default)(Z,eo),es=et(B,ei),eu=er(B,ei),el=ea(B,ei);return pureFinalPropsSelectorFactory(es,eu,el,B,ei)}},5379:(B,Z,et)=>{"use strict";var er=et(92439);Z.__esModule=!0,Z.default=verifySubselectors;var en=er(et(69951));function verify(B,Z){if(B)"mapStateToProps"!==Z&&"mapDispatchToProps"!==Z||Object.prototype.hasOwnProperty.call(B,"dependsOnOwnProps")||(0,en.default)(`The selector for ${Z} of connect did not specify a value for dependsOnOwnProps.`);else throw Error(`Unexpected value for ${Z} in connect.`)}function verifySubselectors(B,Z,et){verify(B,"mapStateToProps"),verify(Z,"mapDispatchToProps"),verify(et,"mergeProps")}},88859:(B,Z,et)=>{"use strict";var er=et(92439);function wrapMapToPropsConstant(B){return function(Z){let et=B(Z);function constantSelector(){return et}return constantSelector.dependsOnOwnProps=!1,constantSelector}}function getDependsOnOwnProps(B){return B.dependsOnOwnProps?!!B.dependsOnOwnProps:1!==B.length}function wrapMapToPropsFunc(B,Z){return function(Z,{displayName:et}){let proxy=function(B,Z){return proxy.dependsOnOwnProps?proxy.mapToProps(B,Z):proxy.mapToProps(B,void 0)};return proxy.dependsOnOwnProps=!0,proxy.mapToProps=function(Z,et){proxy.mapToProps=B,proxy.dependsOnOwnProps=getDependsOnOwnProps(B);let er=proxy(Z,et);return"function"==typeof er&&(proxy.mapToProps=er,proxy.dependsOnOwnProps=getDependsOnOwnProps(er),er=proxy(Z,et)),er},proxy}}Z.__esModule=!0,Z.wrapMapToPropsConstant=wrapMapToPropsConstant,Z.getDependsOnOwnProps=getDependsOnOwnProps,Z.wrapMapToPropsFunc=wrapMapToPropsFunc,er(et(67926))},87643:(B,Z,et)=>{"use strict";var er=et(92439);Z.__esModule=!0;var en={Provider:!0,connect:!0,ReactReduxContext:!0,useDispatch:!0,createDispatchHook:!0,useSelector:!0,createSelectorHook:!0,useStore:!0,createStoreHook:!0,shallowEqual:!0};Object.defineProperty(Z,"Provider",{enumerable:!0,get:function(){return eo.default}}),Object.defineProperty(Z,"connect",{enumerable:!0,get:function(){return ea.default}}),Object.defineProperty(Z,"ReactReduxContext",{enumerable:!0,get:function(){return ei.ReactReduxContext}}),Object.defineProperty(Z,"useDispatch",{enumerable:!0,get:function(){return es.useDispatch}}),Object.defineProperty(Z,"createDispatchHook",{enumerable:!0,get:function(){return es.createDispatchHook}}),Object.defineProperty(Z,"useSelector",{enumerable:!0,get:function(){return eu.useSelector}}),Object.defineProperty(Z,"createSelectorHook",{enumerable:!0,get:function(){return eu.createSelectorHook}}),Object.defineProperty(Z,"useStore",{enumerable:!0,get:function(){return el.useStore}}),Object.defineProperty(Z,"createStoreHook",{enumerable:!0,get:function(){return el.createStoreHook}}),Object.defineProperty(Z,"shallowEqual",{enumerable:!0,get:function(){return ec.default}});var eo=er(et(1111)),ea=er(et(53305)),ei=et(37778),es=et(37106),eu=et(9328),el=et(5136),ec=er(et(77373)),ed=et(8833);Object.keys(ed).forEach(function(B){!("default"===B||"__esModule"===B||Object.prototype.hasOwnProperty.call(en,B))&&(B in Z&&Z[B]===ed[B]||Object.defineProperty(Z,B,{enumerable:!0,get:function(){return ed[B]}}))})},37106:(B,Z,et)=>{"use strict";Z.__esModule=!0,Z.createDispatchHook=createDispatchHook,Z.useDispatch=void 0;var er=et(37778),en=et(5136);function createDispatchHook(B=er.ReactReduxContext){let Z=B===er.ReactReduxContext?en.useStore:(0,en.createStoreHook)(B);return function(){let B=Z();return B.dispatch}}let eo=createDispatchHook();Z.useDispatch=eo},19506:(B,Z,et)=>{"use strict";Z.__esModule=!0,Z.createReduxContextHook=createReduxContextHook,Z.useReduxContext=void 0;var er=et(9885),en=et(37778);function createReduxContextHook(B=en.ReactReduxContext){return function(){let Z=(0,er.useContext)(B);return Z}}let eo=createReduxContextHook();Z.useReduxContext=eo},9328:(B,Z,et)=>{"use strict";Z.__esModule=!0,Z.createSelectorHook=createSelectorHook,Z.useSelector=Z.initializeUseSelector=void 0;var er=et(9885),en=et(19506),eo=et(37778);let ea=et(96466).notInitialized;Z.initializeUseSelector=B=>{ea=B};let refEquality=(B,Z)=>B===Z;function createSelectorHook(B=eo.ReactReduxContext){let Z=B===eo.ReactReduxContext?en.useReduxContext:(0,en.createReduxContextHook)(B);return function(B,et={}){let{equalityFn:en=refEquality,stabilityCheck:eo,noopCheck:ei}="function"==typeof et?{equalityFn:et}:et,{store:es,subscription:eu,getServerState:el,stabilityCheck:ec,noopCheck:ed}=Z();(0,er.useRef)(!0);let ef=(0,er.useCallback)({[B.name](Z){let et=B(Z);return et}}[B.name],[B,ec,eo]),ep=ea(eu.addNestedSub,es.getState,el||es.getState,ef,en);return(0,er.useDebugValue)(ep),ep}}let ei=createSelectorHook();Z.useSelector=ei},5136:(B,Z,et)=>{"use strict";Z.__esModule=!0,Z.createStoreHook=createStoreHook,Z.useStore=void 0;var er=et(37778),en=et(19506);function createStoreHook(B=er.ReactReduxContext){let Z=B===er.ReactReduxContext?en.useReduxContext:(0,en.createReduxContextHook)(B);return function(){let{store:B}=Z();return B}}let eo=createStoreHook();Z.useStore=eo},8250:(B,Z,et)=>{"use strict";Z.__esModule=!0;var er={batch:!0};Object.defineProperty(Z,"batch",{enumerable:!0,get:function(){return ea.unstable_batchedUpdates}});var en=et(61928),eo=et(96122),ea=et(90644),ei=et(13932),es=et(9328),eu=et(53305),el=et(87643);Object.keys(el).forEach(function(B){!("default"===B||"__esModule"===B||Object.prototype.hasOwnProperty.call(er,B))&&(B in Z&&Z[B]===el[B]||Object.defineProperty(Z,B,{enumerable:!0,get:function(){return el[B]}}))}),(0,es.initializeUseSelector)(eo.useSyncExternalStoreWithSelector),(0,eu.initializeConnect)(en.useSyncExternalStore),(0,ei.setBatch)(ea.unstable_batchedUpdates)},8833:()=>{},65462:(B,Z,et)=>{"use strict";Z.__esModule=!0,Z.createSubscription=createSubscription;var er=et(13932);function createListenerCollection(){let B=(0,er.getBatch)(),Z=null,et=null;return{clear(){Z=null,et=null},notify(){B(()=>{let B=Z;for(;B;)B.callback(),B=B.next})},get(){let B=[],et=Z;for(;et;)B.push(et),et=et.next;return B},subscribe(B){let er=!0,en=et={callback:B,next:null,prev:et};return en.prev?en.prev.next=en:Z=en,function(){er&&null!==Z&&(er=!1,en.next?en.next.prev=en.prev:et=en.prev,en.prev?en.prev.next=en.next:Z=en.next)}}}}let en={notify(){},get:()=>[]};function createSubscription(B,Z){let et;let er=en,eo=0,ea=!1;function addNestedSub(B){trySubscribe();let Z=er.subscribe(B),et=!1;return()=>{et||(et=!0,Z(),tryUnsubscribe())}}function notifyNestedSubs(){er.notify()}function handleChangeWrapper(){ei.onStateChange&&ei.onStateChange()}function isSubscribed(){return ea}function trySubscribe(){eo++,et||(et=Z?Z.addNestedSub(handleChangeWrapper):B.subscribe(handleChangeWrapper),er=createListenerCollection())}function tryUnsubscribe(){eo--,et&&0===eo&&(et(),et=void 0,er.clear(),er=en)}function trySubscribeSelf(){ea||(ea=!0,trySubscribe())}function tryUnsubscribeSelf(){ea&&(ea=!1,tryUnsubscribe())}let ei={addNestedSub,notifyNestedSubs,handleChangeWrapper,isSubscribed,trySubscribe:trySubscribeSelf,tryUnsubscribe:tryUnsubscribeSelf,getListeners:()=>er};return ei}},13932:(B,Z)=>{"use strict";Z.__esModule=!0,Z.getBatch=Z.setBatch=void 0;let batch=function(B){B()};Z.setBatch=B=>batch=B,Z.getBatch=()=>batch},79198:(B,Z)=>{"use strict";function bindActionCreators(B,Z){let et={};for(let er in B){let en=B[er];"function"==typeof en&&(et[er]=(...B)=>Z(en(...B)))}return et}Z.__esModule=!0,Z.default=bindActionCreators},33480:(B,Z)=>{"use strict";function isPlainObject(B){if("object"!=typeof B||null===B)return!1;let Z=Object.getPrototypeOf(B);if(null===Z)return!0;let et=Z;for(;null!==Object.getPrototypeOf(et);)et=Object.getPrototypeOf(et);return Z===et}Z.__esModule=!0,Z.default=isPlainObject},90644:(B,Z,et)=>{"use strict";Z.__esModule=!0,Object.defineProperty(Z,"unstable_batchedUpdates",{enumerable:!0,get:function(){return er.unstable_batchedUpdates}});var er=et(88908)},77373:(B,Z)=>{"use strict";function is(B,Z){return B===Z?0!==B||0!==Z||1/B==1/Z:B!=B&&Z!=Z}function shallowEqual(B,Z){if(is(B,Z))return!0;if("object"!=typeof B||null===B||"object"!=typeof Z||null===Z)return!1;let et=Object.keys(B),er=Object.keys(Z);if(et.length!==er.length)return!1;for(let er=0;er<et.length;er++)if(!Object.prototype.hasOwnProperty.call(Z,et[er])||!is(B[et[er]],Z[et[er]]))return!1;return!0}Z.__esModule=!0,Z.default=shallowEqual},46275:(B,Z,et)=>{"use strict";Z.__esModule=!0,Z.useIsomorphicLayoutEffect=Z.canUseDOM=void 0;var er=_interopRequireWildcard(et(9885));function _getRequireWildcardCache(B){if("function"!=typeof WeakMap)return null;var Z=new WeakMap,et=new WeakMap;return(_getRequireWildcardCache=function(B){return B?et:Z})(B)}function _interopRequireWildcard(B,Z){if(!Z&&B&&B.__esModule)return B;if(null===B||"object"!=typeof B&&"function"!=typeof B)return{default:B};var et=_getRequireWildcardCache(Z);if(et&&et.has(B))return et.get(B);var er={},en=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var eo in B)if("default"!==eo&&Object.prototype.hasOwnProperty.call(B,eo)){var ea=en?Object.getOwnPropertyDescriptor(B,eo):null;ea&&(ea.get||ea.set)?Object.defineProperty(er,eo,ea):er[eo]=B[eo]}return er.default=B,et&&et.set(B,er),er}let en=!!("undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement);Z.canUseDOM=en;let eo=en?er.useLayoutEffect:er.useEffect;Z.useIsomorphicLayoutEffect=eo},96466:(B,Z)=>{"use strict";Z.__esModule=!0,Z.notInitialized=void 0,Z.notInitialized=()=>{throw Error("uSES not initialized!")}},67926:(B,Z,et)=>{"use strict";var er=et(92439);Z.__esModule=!0,Z.default=verifyPlainObject;var en=er(et(33480)),eo=er(et(69951));function verifyPlainObject(B,Z,et){(0,en.default)(B)||(0,eo.default)(`${et}() in ${Z} must return a plain object. Instead received ${B}.`)}},69951:(B,Z)=>{"use strict";function warning(B){"undefined"!=typeof console&&"function"==typeof console.error&&console.error(B);try{throw Error(B)}catch(B){}}Z.__esModule=!0,Z.default=warning},65752:(B,Z)=>{"use strict";/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var et,er=Symbol.for("react.element"),en=Symbol.for("react.portal"),eo=Symbol.for("react.fragment"),ea=Symbol.for("react.strict_mode"),ei=Symbol.for("react.profiler"),es=Symbol.for("react.provider"),eu=Symbol.for("react.context"),el=Symbol.for("react.server_context"),ec=Symbol.for("react.forward_ref"),ed=Symbol.for("react.suspense"),ef=Symbol.for("react.suspense_list"),ep=Symbol.for("react.memo"),eh=Symbol.for("react.lazy"),eg=Symbol.for("react.offscreen");function v(B){if("object"==typeof B&&null!==B){var Z=B.$$typeof;switch(Z){case er:switch(B=B.type){case eo:case ei:case ea:case ed:case ef:return B;default:switch(B=B&&B.$$typeof){case el:case eu:case ec:case eh:case ep:case es:return B;default:return Z}}case en:return Z}}}et=Symbol.for("react.module.reference"),Z.ContextConsumer=eu,Z.ContextProvider=es,Z.Element=er,Z.ForwardRef=ec,Z.Fragment=eo,Z.Lazy=eh,Z.Memo=ep,Z.Portal=en,Z.Profiler=ei,Z.StrictMode=ea,Z.Suspense=ed,Z.SuspenseList=ef,Z.isAsyncMode=function(){return!1},Z.isConcurrentMode=function(){return!1},Z.isContextConsumer=function(B){return v(B)===eu},Z.isContextProvider=function(B){return v(B)===es},Z.isElement=function(B){return"object"==typeof B&&null!==B&&B.$$typeof===er},Z.isForwardRef=function(B){return v(B)===ec},Z.isFragment=function(B){return v(B)===eo},Z.isLazy=function(B){return v(B)===eh},Z.isMemo=function(B){return v(B)===ep},Z.isPortal=function(B){return v(B)===en},Z.isProfiler=function(B){return v(B)===ei},Z.isStrictMode=function(B){return v(B)===ea},Z.isSuspense=function(B){return v(B)===ed},Z.isSuspenseList=function(B){return v(B)===ef},Z.isValidElementType=function(B){return"string"==typeof B||"function"==typeof B||B===eo||B===ei||B===ea||B===ed||B===ef||B===eg||"object"==typeof B&&null!==B&&(B.$$typeof===eh||B.$$typeof===ep||B.$$typeof===es||B.$$typeof===eu||B.$$typeof===ec||B.$$typeof===et||void 0!==B.getModuleId)},Z.typeOf=v},40181:(B,Z,et)=>{"use strict";B.exports=et(65752)},21890:(B,Z)=>{"use strict";function createThunkMiddleware(B){return function(Z){var et=Z.dispatch,er=Z.getState;return function(Z){return function(en){return"function"==typeof en?en(et,er,B):Z(en)}}}}Object.defineProperty(Z,"__esModule",{value:!0}),Z.default=void 0;var et=createThunkMiddleware();et.withExtraArgument=createThunkMiddleware,Z.default=et},58131:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0});var er=function(B){return B&&"object"==typeof B&&"default"in B?B:{default:B}}(et(89687));function formatProdErrorMessage(B){return"Minified Redux error #"+B+"; visit https://redux.js.org/Errors?code="+B+" for the full message or use the non-minified dev environment for full errors. "}var en="function"==typeof Symbol&&Symbol.observable||"@@observable",randomString=function(){return Math.random().toString(36).substring(7).split("").join(".")},eo={INIT:"@@redux/INIT"+randomString(),REPLACE:"@@redux/REPLACE"+randomString(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+randomString()}};function isPlainObject(B){if("object"!=typeof B||null===B)return!1;for(var Z=B;null!==Object.getPrototypeOf(Z);)Z=Object.getPrototypeOf(Z);return Object.getPrototypeOf(B)===Z}function createStore(B,Z,et){if("function"==typeof Z&&"function"==typeof et||"function"==typeof et&&"function"==typeof arguments[3])throw Error(formatProdErrorMessage(0));if("function"==typeof Z&&void 0===et&&(et=Z,Z=void 0),void 0!==et){if("function"!=typeof et)throw Error(formatProdErrorMessage(1));return et(createStore)(B,Z)}if("function"!=typeof B)throw Error(formatProdErrorMessage(2));var er,ea=B,ei=Z,es=[],eu=es,el=!1;function ensureCanMutateNextListeners(){eu===es&&(eu=es.slice())}function getState(){if(el)throw Error(formatProdErrorMessage(3));return ei}function subscribe(B){if("function"!=typeof B)throw Error(formatProdErrorMessage(4));if(el)throw Error(formatProdErrorMessage(5));var Z=!0;return ensureCanMutateNextListeners(),eu.push(B),function(){if(Z){if(el)throw Error(formatProdErrorMessage(6));Z=!1,ensureCanMutateNextListeners();var et=eu.indexOf(B);eu.splice(et,1),es=null}}}function dispatch(B){if(!isPlainObject(B))throw Error(formatProdErrorMessage(7));if(void 0===B.type)throw Error(formatProdErrorMessage(8));if(el)throw Error(formatProdErrorMessage(9));try{el=!0,ei=ea(ei,B)}finally{el=!1}for(var Z=es=eu,et=0;et<Z.length;et++)(0,Z[et])();return B}function replaceReducer(B){if("function"!=typeof B)throw Error(formatProdErrorMessage(10));ea=B,dispatch({type:eo.REPLACE})}function observable(){var B,Z=subscribe;return(B={subscribe:function(B){if("object"!=typeof B||null===B)throw Error(formatProdErrorMessage(11));function observeState(){B.next&&B.next(getState())}return observeState(),{unsubscribe:Z(observeState)}}})[en]=function(){return this},B}return dispatch({type:eo.INIT}),(er={dispatch:dispatch,subscribe:subscribe,getState:getState,replaceReducer:replaceReducer})[en]=observable,er}var ea=createStore;function assertReducerShape(B){Object.keys(B).forEach(function(Z){var et=B[Z];if(void 0===et(void 0,{type:eo.INIT}))throw Error(formatProdErrorMessage(12));if(void 0===et(void 0,{type:eo.PROBE_UNKNOWN_ACTION()}))throw Error(formatProdErrorMessage(13))})}function combineReducers(B){for(var Z,et=Object.keys(B),er={},en=0;en<et.length;en++){var eo=et[en];"function"==typeof B[eo]&&(er[eo]=B[eo])}var ea=Object.keys(er);try{assertReducerShape(er)}catch(B){Z=B}return function(B,et){if(void 0===B&&(B={}),Z)throw Z;for(var en=!1,eo={},ei=0;ei<ea.length;ei++){var es=ea[ei],eu=er[es],el=B[es],ec=eu(el,et);if(void 0===ec)throw et&&et.type,Error(formatProdErrorMessage(14));eo[es]=ec,en=en||ec!==el}return(en=en||ea.length!==Object.keys(B).length)?eo:B}}function bindActionCreator(B,Z){return function(){return Z(B.apply(this,arguments))}}function bindActionCreators(B,Z){if("function"==typeof B)return bindActionCreator(B,Z);if("object"!=typeof B||null===B)throw Error(formatProdErrorMessage(16));var et={};for(var er in B){var en=B[er];"function"==typeof en&&(et[er]=bindActionCreator(en,Z))}return et}function compose(){for(var B=arguments.length,Z=Array(B),et=0;et<B;et++)Z[et]=arguments[et];return 0===Z.length?function(B){return B}:1===Z.length?Z[0]:Z.reduce(function(B,Z){return function(){return B(Z.apply(void 0,arguments))}})}function applyMiddleware(){for(var B=arguments.length,Z=Array(B),et=0;et<B;et++)Z[et]=arguments[et];return function(B){return function(){var et=B.apply(void 0,arguments),_dispatch=function(){throw Error(formatProdErrorMessage(15))},en={getState:et.getState,dispatch:function(){return _dispatch.apply(void 0,arguments)}},eo=Z.map(function(B){return B(en)});return _dispatch=compose.apply(void 0,eo)(et.dispatch),er.default(er.default({},et),{},{dispatch:_dispatch})}}}Z.__DO_NOT_USE__ActionTypes=eo,Z.applyMiddleware=applyMiddleware,Z.bindActionCreators=bindActionCreators,Z.combineReducers=combineReducers,Z.compose=compose,Z.createStore=createStore,Z.legacy_createStore=ea},87971:(B,Z)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Z.createCacheKeyComparator=createCacheKeyComparator,Z.defaultEqualityCheck=void 0,Z.defaultMemoize=defaultMemoize;var et="NOT_FOUND";function createSingletonCache(B){var Z;return{get:function(er){return Z&&B(Z.key,er)?Z.value:et},put:function(B,et){Z={key:B,value:et}},getEntries:function(){return Z?[Z]:[]},clear:function(){Z=void 0}}}function createLruCache(B,Z){var er=[];function get(B){var en=er.findIndex(function(et){return Z(B,et.key)});if(en>-1){var eo=er[en];return en>0&&(er.splice(en,1),er.unshift(eo)),eo.value}return et}function put(Z,en){get(Z)===et&&(er.unshift({key:Z,value:en}),er.length>B&&er.pop())}return{get:get,put:put,getEntries:function(){return er},clear:function(){er=[]}}}var defaultEqualityCheck=function(B,Z){return B===Z};function createCacheKeyComparator(B){return function(Z,et){if(null===Z||null===et||Z.length!==et.length)return!1;for(var er=Z.length,en=0;en<er;en++)if(!B(Z[en],et[en]))return!1;return!0}}function defaultMemoize(B,Z){var er="object"==typeof Z?Z:{equalityCheck:Z},en=er.equalityCheck,eo=er.maxSize,ea=void 0===eo?1:eo,ei=er.resultEqualityCheck,es=createCacheKeyComparator(void 0===en?defaultEqualityCheck:en),eu=1===ea?createSingletonCache(es):createLruCache(ea,es);function memoized(){var Z=eu.get(arguments);if(Z===et){if(Z=B.apply(null,arguments),ei){var er=eu.getEntries().find(function(B){return ei(B.value,Z)});er&&(Z=er.value)}eu.put(arguments,Z)}return Z}return memoized.clearCache=function(){return eu.clear()},memoized}Z.defaultEqualityCheck=defaultEqualityCheck},49531:(B,Z,et)=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0}),Z.createSelector=void 0,Z.createSelectorCreator=createSelectorCreator,Z.createStructuredSelector=void 0,Object.defineProperty(Z,"defaultEqualityCheck",{enumerable:!0,get:function(){return er.defaultEqualityCheck}}),Object.defineProperty(Z,"defaultMemoize",{enumerable:!0,get:function(){return er.defaultMemoize}});var er=et(87971);function getDependencies(B){var Z=Array.isArray(B[0])?B[0]:B;if(!Z.every(function(B){return"function"==typeof B}))throw Error("createSelector expects all input-selectors to be functions, but received the following types: ["+Z.map(function(B){return"function"==typeof B?"function "+(B.name||"unnamed")+"()":typeof B}).join(", ")+"]");return Z}function createSelectorCreator(B){for(var Z=arguments.length,et=Array(Z>1?Z-1:0),er=1;er<Z;er++)et[er-1]=arguments[er];return function(){for(var Z,er=arguments.length,en=Array(er),eo=0;eo<er;eo++)en[eo]=arguments[eo];var ea=0,ei={memoizeOptions:void 0},es=en.pop();if("object"==typeof es&&(ei=es,es=en.pop()),"function"!=typeof es)throw Error("createSelector expects an output function after the inputs, but received: ["+typeof es+"]");var eu=ei.memoizeOptions,el=void 0===eu?et:eu,ec=Array.isArray(el)?el:[el],ed=getDependencies(en),ef=B.apply(void 0,[function(){return ea++,es.apply(null,arguments)}].concat(ec)),ep=B(function(){for(var B=[],et=ed.length,er=0;er<et;er++)B.push(ed[er].apply(null,arguments));return Z=ef.apply(null,B)});return Object.assign(ep,{resultFunc:es,memoizedResultFunc:ef,dependencies:ed,lastResult:function(){return Z},recomputations:function(){return ea},resetRecomputations:function(){return ea=0}}),ep}}var en=createSelectorCreator(er.defaultMemoize);Z.createSelector=en,Z.createStructuredSelector=function(B,Z){if(void 0===Z&&(Z=en),"object"!=typeof B)throw Error("createStructuredSelector expects first argument to be an object where each property is a selector, instead received a "+typeof B);var et=Object.keys(B);return Z(et.map(function(Z){return B[Z]}),function(){for(var B=arguments.length,Z=Array(B),er=0;er<B;er++)Z[er]=arguments[er];return Z.reduce(function(B,Z,er){return B[et[er]]=Z,B},{})})}},24484:(B,Z,et)=>{"use strict";/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var er=et(9885);function is(B,Z){return B===Z&&(0!==B||1/B==1/Z)||B!=B&&Z!=Z}var en="function"==typeof Object.is?Object.is:is,eo=er.useState,ea=er.useEffect,ei=er.useLayoutEffect,es=er.useDebugValue;function useSyncExternalStore$2(B,Z){var et=Z(),er=eo({inst:{value:et,getSnapshot:Z}}),en=er[0].inst,eu=er[1];return ei(function(){en.value=et,en.getSnapshot=Z,checkIfSnapshotChanged(en)&&eu({inst:en})},[B,et,Z]),ea(function(){return checkIfSnapshotChanged(en)&&eu({inst:en}),B(function(){checkIfSnapshotChanged(en)&&eu({inst:en})})},[B]),es(et),et}function checkIfSnapshotChanged(B){var Z=B.getSnapshot;B=B.value;try{var et=Z();return!en(B,et)}catch(B){return!0}}function useSyncExternalStore$1(B,Z){return Z()}var eu="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?useSyncExternalStore$1:useSyncExternalStore$2;Z.useSyncExternalStore=void 0!==er.useSyncExternalStore?er.useSyncExternalStore:eu},17768:(B,Z,et)=>{"use strict";/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var er=et(9885),en=et(61928);function is(B,Z){return B===Z&&(0!==B||1/B==1/Z)||B!=B&&Z!=Z}var eo="function"==typeof Object.is?Object.is:is,ea=en.useSyncExternalStore,ei=er.useRef,es=er.useEffect,eu=er.useMemo,el=er.useDebugValue;Z.useSyncExternalStoreWithSelector=function(B,Z,et,er,en){var ec=ei(null);if(null===ec.current){var ed={hasValue:!1,value:null};ec.current=ed}else ed=ec.current;var ef=ea(B,(ec=eu(function(){function memoizedSelector(Z){if(!ei){if(ei=!0,B=Z,Z=er(Z),void 0!==en&&ed.hasValue){var et=ed.value;if(en(et,Z))return ea=et}return ea=Z}if(et=ea,eo(B,Z))return et;var es=er(Z);return void 0!==en&&en(et,es)?(B=Z,et):(B=Z,ea=es)}var B,ea,ei=!1,es=void 0===et?null:et;return[function(){return memoizedSelector(Z())},null===es?void 0:function(){return memoizedSelector(es())}]},[Z,et,er,en]))[0],ec[1]);return es(function(){ed.hasValue=!0,ed.value=ef},[ef]),el(ef),ef}},61928:(B,Z,et)=>{"use strict";B.exports=et(24484)},96122:(B,Z,et)=>{"use strict";B.exports=et(17768)},83889:B=>{B.exports={area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0}},513:(B,Z,et)=>{var er=et(36651);function _defineProperty(B,Z,et){return(Z=er(Z))in B?Object.defineProperty(B,Z,{value:et,enumerable:!0,configurable:!0,writable:!0}):B[Z]=et,B}B.exports=_defineProperty,B.exports.__esModule=!0,B.exports.default=B.exports},43259:B=>{function _extends(){return B.exports=_extends=Object.assign?Object.assign.bind():function(B){for(var Z=1;Z<arguments.length;Z++){var et=arguments[Z];for(var er in et)({}).hasOwnProperty.call(et,er)&&(B[er]=et[er])}return B},B.exports.__esModule=!0,B.exports.default=B.exports,_extends.apply(null,arguments)}B.exports=_extends,B.exports.__esModule=!0,B.exports.default=B.exports},92439:B=>{function _interopRequireDefault(B){return B&&B.__esModule?B:{default:B}}B.exports=_interopRequireDefault,B.exports.__esModule=!0,B.exports.default=B.exports},89687:(B,Z,et)=>{var er=et(513);function ownKeys(B,Z){var et=Object.keys(B);if(Object.getOwnPropertySymbols){var er=Object.getOwnPropertySymbols(B);Z&&(er=er.filter(function(Z){return Object.getOwnPropertyDescriptor(B,Z).enumerable})),et.push.apply(et,er)}return et}function _objectSpread2(B){for(var Z=1;Z<arguments.length;Z++){var et=null!=arguments[Z]?arguments[Z]:{};Z%2?ownKeys(Object(et),!0).forEach(function(Z){er(B,Z,et[Z])}):Object.getOwnPropertyDescriptors?Object.defineProperties(B,Object.getOwnPropertyDescriptors(et)):ownKeys(Object(et)).forEach(function(Z){Object.defineProperty(B,Z,Object.getOwnPropertyDescriptor(et,Z))})}return B}B.exports=_objectSpread2,B.exports.__esModule=!0,B.exports.default=B.exports},54845:B=>{function _objectWithoutPropertiesLoose(B,Z){if(null==B)return{};var et={};for(var er in B)if(({}).hasOwnProperty.call(B,er)){if(-1!==Z.indexOf(er))continue;et[er]=B[er]}return et}B.exports=_objectWithoutPropertiesLoose,B.exports.__esModule=!0,B.exports.default=B.exports},49312:(B,Z,et)=>{var er=et(67236).default;function toPrimitive(B,Z){if("object"!=er(B)||!B)return B;var et=B[Symbol.toPrimitive];if(void 0!==et){var en=et.call(B,Z||"default");if("object"!=er(en))return en;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===Z?String:Number)(B)}B.exports=toPrimitive,B.exports.__esModule=!0,B.exports.default=B.exports},36651:(B,Z,et)=>{var er=et(67236).default,en=et(49312);function toPropertyKey(B){var Z=en(B,"string");return"symbol"==er(Z)?Z:Z+""}B.exports=toPropertyKey,B.exports.__esModule=!0,B.exports.default=B.exports},67236:B=>{function _typeof(Z){return B.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(B){return typeof B}:function(B){return B&&"function"==typeof Symbol&&B.constructor===Symbol&&B!==Symbol.prototype?"symbol":typeof B},B.exports.__esModule=!0,B.exports.default=B.exports,_typeof(Z)}B.exports=_typeof,B.exports.__esModule=!0,B.exports.default=B.exports},13592:(B,Z,et)=>{"use strict";function _class_private_field_loose_base(B,Z){if(!Object.prototype.hasOwnProperty.call(B,Z))throw TypeError("attempted to use private field on non-instance");return B}et.r(Z),et.d(Z,{_:()=>_class_private_field_loose_base,_class_private_field_loose_base:()=>_class_private_field_loose_base})},94941:(B,Z,et)=>{"use strict";et.r(Z),et.d(Z,{_:()=>_class_private_field_loose_key,_class_private_field_loose_key:()=>_class_private_field_loose_key});var er=0;function _class_private_field_loose_key(B){return"__private_"+er+++"_"+B}},80085:(B,Z,et)=>{"use strict";function _interop_require_default(B){return B&&B.__esModule?B:{default:B}}et.r(Z),et.d(Z,{_:()=>_interop_require_default,_interop_require_default:()=>_interop_require_default})},8425:(B,Z,et)=>{"use strict";function _getRequireWildcardCache(B){if("function"!=typeof WeakMap)return null;var Z=new WeakMap,et=new WeakMap;return(_getRequireWildcardCache=function(B){return B?et:Z})(B)}function _interop_require_wildcard(B,Z){if(!Z&&B&&B.__esModule)return B;if(null===B||"object"!=typeof B&&"function"!=typeof B)return{default:B};var et=_getRequireWildcardCache(Z);if(et&&et.has(B))return et.get(B);var er={},en=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var eo in B)if("default"!==eo&&Object.prototype.hasOwnProperty.call(B,eo)){var ea=en?Object.getOwnPropertyDescriptor(B,eo):null;ea&&(ea.get||ea.set)?Object.defineProperty(er,eo,ea):er[eo]=B[eo]}return er.default=B,et&&et.set(B,er),er}et.r(Z),et.d(Z,{_:()=>_interop_require_wildcard,_interop_require_wildcard:()=>_interop_require_wildcard})},28791:(B,Z,et)=>{"use strict";et.d(Z,{j:()=>getDefaultOptions});let er={};function getDefaultOptions(){return er}},52190:(B,Z,et)=>{"use strict";et.d(Z,{D:()=>getTimezoneOffsetInMilliseconds});var er=et(58134);function getTimezoneOffsetInMilliseconds(B){let Z=(0,er.Q)(B),et=new Date(Date.UTC(Z.getFullYear(),Z.getMonth(),Z.getDate(),Z.getHours(),Z.getMinutes(),Z.getSeconds(),Z.getMilliseconds()));return et.setUTCFullYear(Z.getFullYear()),+B-+et}},51748:(B,Z,et)=>{"use strict";et.d(Z,{d:()=>normalizeDates});var er=et(43491);function normalizeDates(B,...Z){let et=er.L.bind(null,B||Z.find(B=>"object"==typeof B));return Z.map(et)}},13838:(B,Z,et)=>{"use strict";et.d(Z,{H_:()=>ea,I7:()=>ei,dP:()=>en,fH:()=>eo,jE:()=>er});let er=6048e5,en=864e5,eo=43200,ea=1440,ei=Symbol.for("constructDateFrom")},43491:(B,Z,et)=>{"use strict";et.d(Z,{L:()=>constructFrom});var er=et(13838);function constructFrom(B,Z){return"function"==typeof B?B(Z):B&&"object"==typeof B&&er.I7 in B?B[er.I7](Z):B instanceof Date?new B.constructor(Z):new Date(Z)}},73531:(B,Z,et)=>{"use strict";et.d(Z,{Z:()=>el});var er=et(43491);function constructNow(B){return(0,er.L)(B,Date.now())}var en=et(7294),eo=et(28791),ea=et(52190),ei=et(51748),es=et(58134);function compareAsc(B,Z){let et=+(0,es.Q)(B)-+(0,es.Q)(Z);return et<0?-1:et>0?1:et}var eu=et(13838);function differenceInCalendarMonths(B,Z,et){let[er,en]=(0,ei.d)(et?.in,B,Z),eo=er.getFullYear()-en.getFullYear(),ea=er.getMonth()-en.getMonth();return 12*eo+ea}function endOfDay(B,Z){let et=(0,es.Q)(B,Z?.in);return et.setHours(23,59,59,999),et}function endOfMonth(B,Z){let et=(0,es.Q)(B,Z?.in),er=et.getMonth();return et.setFullYear(et.getFullYear(),er+1,0),et.setHours(23,59,59,999),et}function isLastDayOfMonth(B,Z){let et=(0,es.Q)(B,Z?.in);return+endOfDay(et,Z)==+endOfMonth(et,Z)}function differenceInMonths(B,Z,et){let[er,en,eo]=(0,ei.d)(et?.in,B,B,Z),ea=compareAsc(en,eo),es=Math.abs(differenceInCalendarMonths(en,eo));if(es<1)return 0;1===en.getMonth()&&en.getDate()>27&&en.setDate(30),en.setMonth(en.getMonth()-ea*es);let eu=compareAsc(en,eo)===-ea;isLastDayOfMonth(er)&&1===es&&1===compareAsc(er,eo)&&(eu=!1);let el=ea*(es-+eu);return 0===el?0:el}function getRoundingMethod(B){return Z=>{let et=B?Math[B]:Math.trunc,er=et(Z);return 0===er?0:er}}function differenceInMilliseconds(B,Z){return+(0,es.Q)(B)-+(0,es.Q)(Z)}function differenceInSeconds(B,Z,et){let er=differenceInMilliseconds(B,Z)/1e3;return getRoundingMethod(et?.roundingMethod)(er)}function formatDistance(B,Z,et){let er;let es=(0,eo.j)(),el=et?.locale??es.locale??en._,ec=compareAsc(B,Z);if(isNaN(ec))throw RangeError("Invalid time value");let ed=Object.assign({},et,{addSuffix:et?.addSuffix,comparison:ec}),[ef,ep]=(0,ei.d)(et?.in,...ec>0?[Z,B]:[B,Z]),eh=differenceInSeconds(ep,ef),eg=((0,ea.D)(ep)-(0,ea.D)(ef))/1e3,ey=Math.round((eh-eg)/60);if(ey<2){if(et?.includeSeconds){if(eh<5)return el.formatDistance("lessThanXSeconds",5,ed);if(eh<10)return el.formatDistance("lessThanXSeconds",10,ed);if(eh<20)return el.formatDistance("lessThanXSeconds",20,ed);if(eh<40)return el.formatDistance("halfAMinute",0,ed);else if(eh<60)return el.formatDistance("lessThanXMinutes",1,ed);else return el.formatDistance("xMinutes",1,ed)}return 0===ey?el.formatDistance("lessThanXMinutes",1,ed):el.formatDistance("xMinutes",ey,ed)}if(ey<45)return el.formatDistance("xMinutes",ey,ed);if(ey<90)return el.formatDistance("aboutXHours",1,ed);if(ey<eu.H_){let B=Math.round(ey/60);return el.formatDistance("aboutXHours",B,ed)}if(ey<2520)return el.formatDistance("xDays",1,ed);if(ey<eu.fH){let B=Math.round(ey/eu.H_);return el.formatDistance("xDays",B,ed)}if(ey<2*eu.fH)return er=Math.round(ey/eu.fH),el.formatDistance("aboutXMonths",er,ed);if((er=differenceInMonths(ep,ef))<12){let B=Math.round(ey/eu.fH);return el.formatDistance("xMonths",B,ed)}{let B=er%12,Z=Math.trunc(er/12);return B<3?el.formatDistance("aboutXYears",Z,ed):B<9?el.formatDistance("overXYears",Z,ed):el.formatDistance("almostXYears",Z+1,ed)}}function formatDistanceToNow(B,Z){return formatDistance(B,constructNow(B),Z)}let el=formatDistanceToNow},7294:(B,Z,et)=>{"use strict";et.d(Z,{_:()=>es});let er={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function buildFormatLongFn(B){return (Z={})=>{let et=Z.width?String(Z.width):B.defaultWidth,er=B.formats[et]||B.formats[B.defaultWidth];return er}}let en={date:buildFormatLongFn({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:buildFormatLongFn({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:buildFormatLongFn({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},eo={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function buildLocalizeFn(B){return(Z,et)=>{let er;let en=et?.context?String(et.context):"standalone";if("formatting"===en&&B.formattingValues){let Z=B.defaultFormattingWidth||B.defaultWidth,en=et?.width?String(et.width):Z;er=B.formattingValues[en]||B.formattingValues[Z]}else{let Z=B.defaultWidth,en=et?.width?String(et.width):B.defaultWidth;er=B.values[en]||B.values[Z]}let eo=B.argumentCallback?B.argumentCallback(Z):Z;return er[eo]}}let ea={ordinalNumber:(B,Z)=>{let et=Number(B),er=et%100;if(er>20||er<10)switch(er%10){case 1:return et+"st";case 2:return et+"nd";case 3:return et+"rd"}return et+"th"},era:buildLocalizeFn({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:buildLocalizeFn({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:B=>B-1}),month:buildLocalizeFn({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:buildLocalizeFn({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:buildLocalizeFn({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})};function buildMatchFn(B){return(Z,et={})=>{let er;let en=et.width,eo=en&&B.matchPatterns[en]||B.matchPatterns[B.defaultMatchWidth],ea=Z.match(eo);if(!ea)return null;let ei=ea[0],es=en&&B.parsePatterns[en]||B.parsePatterns[B.defaultParseWidth],eu=Array.isArray(es)?findIndex(es,B=>B.test(ei)):findKey(es,B=>B.test(ei));er=B.valueCallback?B.valueCallback(eu):eu,er=et.valueCallback?et.valueCallback(er):er;let el=Z.slice(ei.length);return{value:er,rest:el}}}function findKey(B,Z){for(let et in B)if(Object.prototype.hasOwnProperty.call(B,et)&&Z(B[et]))return et}function findIndex(B,Z){for(let et=0;et<B.length;et++)if(Z(B[et]))return et}function buildMatchPatternFn(B){return(Z,et={})=>{let er=Z.match(B.matchPattern);if(!er)return null;let en=er[0],eo=Z.match(B.parsePattern);if(!eo)return null;let ea=B.valueCallback?B.valueCallback(eo[0]):eo[0];ea=et.valueCallback?et.valueCallback(ea):ea;let ei=Z.slice(en.length);return{value:ea,rest:ei}}}let ei={ordinalNumber:buildMatchPatternFn({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:B=>parseInt(B,10)}),era:buildMatchFn({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:buildMatchFn({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:B=>B+1}),month:buildMatchFn({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:buildMatchFn({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:buildMatchFn({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},es={code:"en-US",formatDistance:(B,Z,et)=>{let en;let eo=er[B];return(en="string"==typeof eo?eo:1===Z?eo.one:eo.other.replace("{{count}}",Z.toString()),et?.addSuffix)?et.comparison&&et.comparison>0?"in "+en:en+" ago":en},formatLong:en,formatRelative:(B,Z,et,er)=>eo[B],localize:ea,match:ei,options:{weekStartsOn:0,firstWeekContainsDate:1}}},58134:(B,Z,et)=>{"use strict";et.d(Z,{Q:()=>toDate});var er=et(43491);function toDate(B,Z){return(0,er.L)(Z||B,B)}},32666:(B,Z,et)=>{"use strict";et.d(Z,{Z:()=>Browser});let{slice:er,forEach:en}=[];function defaults(B){return en.call(er.call(arguments,1),Z=>{if(Z)for(let et in Z)void 0===B[et]&&(B[et]=Z[et])}),B}function hasXSS(B){return"string"==typeof B&&[/<\s*script.*?>/i,/<\s*\/\s*script\s*>/i,/<\s*img.*?on\w+\s*=/i,/<\s*\w+\s*on\w+\s*=.*?>/i,/javascript\s*:/i,/vbscript\s*:/i,/expression\s*\(/i,/eval\s*\(/i,/alert\s*\(/i,/document\.cookie/i,/document\.write\s*\(/i,/window\.location/i,/innerHTML/i].some(Z=>Z.test(B))}let eo=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/,serializeCookie=function(B,Z){let et=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{path:"/"},er=encodeURIComponent(Z),en=`${B}=${er}`;if(et.maxAge>0){let B=et.maxAge-0;if(Number.isNaN(B))throw Error("maxAge should be a Number");en+=`; Max-Age=${Math.floor(B)}`}if(et.domain){if(!eo.test(et.domain))throw TypeError("option domain is invalid");en+=`; Domain=${et.domain}`}if(et.path){if(!eo.test(et.path))throw TypeError("option path is invalid");en+=`; Path=${et.path}`}if(et.expires){if("function"!=typeof et.expires.toUTCString)throw TypeError("option expires is invalid");en+=`; Expires=${et.expires.toUTCString()}`}if(et.httpOnly&&(en+="; HttpOnly"),et.secure&&(en+="; Secure"),et.sameSite){let B="string"==typeof et.sameSite?et.sameSite.toLowerCase():et.sameSite;switch(B){case!0:case"strict":en+="; SameSite=Strict";break;case"lax":en+="; SameSite=Lax";break;case"none":en+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}}return et.partitioned&&(en+="; Partitioned"),en},ea={create(B,Z,et,er){let en=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{path:"/",sameSite:"strict"};et&&(en.expires=new Date,en.expires.setTime(en.expires.getTime()+6e4*et)),er&&(en.domain=er),document.cookie=serializeCookie(B,encodeURIComponent(Z),en)},read(B){let Z=`${B}=`,et=document.cookie.split(";");for(let B=0;B<et.length;B++){let er=et[B];for(;" "===er.charAt(0);)er=er.substring(1,er.length);if(0===er.indexOf(Z))return er.substring(Z.length,er.length)}return null},remove(B){this.create(B,"",-1)}};var ei={name:"cookie",lookup(B){let{lookupCookie:Z}=B;if(Z&&"undefined"!=typeof document)return ea.read(Z)||void 0},cacheUserLanguage(B,Z){let{lookupCookie:et,cookieMinutes:er,cookieDomain:en,cookieOptions:eo}=Z;et&&"undefined"!=typeof document&&ea.create(et,B,er,en,eo)}},es={name:"querystring",lookup(B){let Z,{lookupQuerystring:et}=B;if("undefined"!=typeof window){let{search:B}=window.location;!window.location.search&&window.location.hash?.indexOf("?")>-1&&(B=window.location.hash.substring(window.location.hash.indexOf("?")));let er=B.substring(1),en=er.split("&");for(let B=0;B<en.length;B++){let er=en[B].indexOf("=");if(er>0){let eo=en[B].substring(0,er);eo===et&&(Z=en[B].substring(er+1))}}}return Z}};let eu=null,localStorageAvailable=()=>{if(null!==eu)return eu;try{if(!(eu="undefined"!=typeof window&&null!==window.localStorage))return!1;let B="i18next.translate.boo";window.localStorage.setItem(B,"foo"),window.localStorage.removeItem(B)}catch(B){eu=!1}return eu};var el={name:"localStorage",lookup(B){let{lookupLocalStorage:Z}=B;if(Z&&localStorageAvailable())return window.localStorage.getItem(Z)||void 0},cacheUserLanguage(B,Z){let{lookupLocalStorage:et}=Z;et&&localStorageAvailable()&&window.localStorage.setItem(et,B)}};let ec=null,sessionStorageAvailable=()=>{if(null!==ec)return ec;try{if(!(ec="undefined"!=typeof window&&null!==window.sessionStorage))return!1;let B="i18next.translate.boo";window.sessionStorage.setItem(B,"foo"),window.sessionStorage.removeItem(B)}catch(B){ec=!1}return ec};var ed={name:"sessionStorage",lookup(B){let{lookupSessionStorage:Z}=B;if(Z&&sessionStorageAvailable())return window.sessionStorage.getItem(Z)||void 0},cacheUserLanguage(B,Z){let{lookupSessionStorage:et}=Z;et&&sessionStorageAvailable()&&window.sessionStorage.setItem(et,B)}},ef={name:"navigator",lookup(B){let Z=[];if("undefined"!=typeof navigator){let{languages:B,userLanguage:et,language:er}=navigator;if(B)for(let et=0;et<B.length;et++)Z.push(B[et]);et&&Z.push(et),er&&Z.push(er)}return Z.length>0?Z:void 0}},ep={name:"htmlTag",lookup(B){let Z,{htmlTag:et}=B,er=et||("undefined"!=typeof document?document.documentElement:null);return er&&"function"==typeof er.getAttribute&&(Z=er.getAttribute("lang")),Z}},eh={name:"path",lookup(B){let{lookupFromPathIndex:Z}=B;if("undefined"==typeof window)return;let et=window.location.pathname.match(/\/([a-zA-Z-]*)/g);if(Array.isArray(et))return et["number"==typeof Z?Z:0]?.replace("/","")}},eg={name:"subdomain",lookup(B){let{lookupFromSubdomainIndex:Z}=B,et="undefined"!=typeof window&&window.location?.hostname?.match(/^(\w{2,5})\.(([a-z0-9-]{1,63}\.[a-z]{2,6})|localhost)/i);if(et)return et["number"==typeof Z?Z+1:1]}};let ey=!1;try{document.cookie,ey=!0}catch(B){}let em=["querystring","cookie","localStorage","sessionStorage","navigator","htmlTag"];ey||em.splice(1,1);let getDefaults=()=>({order:em,lookupQuerystring:"lng",lookupCookie:"i18next",lookupLocalStorage:"i18nextLng",lookupSessionStorage:"i18nextLng",caches:["localStorage"],excludeCacheFor:["cimode"],convertDetectedLanguage:B=>B});let Browser=class Browser{constructor(B){let Z=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.type="languageDetector",this.detectors={},this.init(B,Z)}init(){let B=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{languageUtils:{}},Z=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},et=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.services=B,this.options=defaults(Z,this.options||{},getDefaults()),"string"==typeof this.options.convertDetectedLanguage&&this.options.convertDetectedLanguage.indexOf("15897")>-1&&(this.options.convertDetectedLanguage=B=>B.replace("-","_")),this.options.lookupFromUrlIndex&&(this.options.lookupFromPathIndex=this.options.lookupFromUrlIndex),this.i18nOptions=et,this.addDetector(ei),this.addDetector(es),this.addDetector(el),this.addDetector(ed),this.addDetector(ef),this.addDetector(ep),this.addDetector(eh),this.addDetector(eg)}addDetector(B){return this.detectors[B.name]=B,this}detect(){let B=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.options.order,Z=[];return(B.forEach(B=>{if(this.detectors[B]){let et=this.detectors[B].lookup(this.options);et&&"string"==typeof et&&(et=[et]),et&&(Z=Z.concat(et))}}),Z=Z.filter(B=>null!=B&&!hasXSS(B)).map(B=>this.options.convertDetectedLanguage(B)),this.services&&this.services.languageUtils&&this.services.languageUtils.getBestMatchFromCodes)?Z:Z.length>0?Z[0]:null}cacheUserLanguage(B){let Z=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.options.caches;Z&&(this.options.excludeCacheFor&&this.options.excludeCacheFor.indexOf(B)>-1||Z.forEach(Z=>{this.detectors[Z]&&this.detectors[Z].cacheUserLanguage(B,this.options)}))}};Browser.type="languageDetector"},57963:(B,Z,et)=>{"use strict";et.d(Z,{Z:()=>resourcesToBackend});var resourcesToBackend=function(B){return{type:"backend",init:function(B,Z,et){},read:function(Z,et,er){if("function"==typeof B){if(B.length<3){try{var en=B(Z,et);en&&"function"==typeof en.then?en.then(function(B){return er(null,B&&B.default||B)}).catch(er):er(null,en)}catch(B){er(B)}return}B(Z,et,er);return}er(null,B&&B[Z]&&B[Z][et])}}}},41887:(B,Z,et)=>{"use strict";et.d(Z,{ZP:()=>ef});let isString=B=>"string"==typeof B,defer=()=>{let B,Z;let et=new Promise((et,er)=>{B=et,Z=er});return et.resolve=B,et.reject=Z,et},makeString=B=>null==B?"":""+B,copy=(B,Z,et)=>{B.forEach(B=>{Z[B]&&(et[B]=Z[B])})},er=/###/g,cleanKey=B=>B&&B.indexOf("###")>-1?B.replace(er,"."):B,canNotTraverseDeeper=B=>!B||isString(B),getLastOfPath=(B,Z,et)=>{let er=isString(Z)?Z.split("."):Z,en=0;for(;en<er.length-1;){if(canNotTraverseDeeper(B))return{};let Z=cleanKey(er[en]);!B[Z]&&et&&(B[Z]=new et),B=Object.prototype.hasOwnProperty.call(B,Z)?B[Z]:{},++en}return canNotTraverseDeeper(B)?{}:{obj:B,k:cleanKey(er[en])}},setPath=(B,Z,et)=>{let{obj:er,k:en}=getLastOfPath(B,Z,Object);if(void 0!==er||1===Z.length){er[en]=et;return}let eo=Z[Z.length-1],ea=Z.slice(0,Z.length-1),ei=getLastOfPath(B,ea,Object);for(;void 0===ei.obj&&ea.length;)eo=`${ea[ea.length-1]}.${eo}`,ei=getLastOfPath(B,ea=ea.slice(0,ea.length-1),Object),ei?.obj&&void 0!==ei.obj[`${ei.k}.${eo}`]&&(ei.obj=void 0);ei.obj[`${ei.k}.${eo}`]=et},pushPath=(B,Z,et,er)=>{let{obj:en,k:eo}=getLastOfPath(B,Z,Object);en[eo]=en[eo]||[],en[eo].push(et)},getPath=(B,Z)=>{let{obj:et,k:er}=getLastOfPath(B,Z);if(et&&Object.prototype.hasOwnProperty.call(et,er))return et[er]},getPathWithDefaults=(B,Z,et)=>{let er=getPath(B,et);return void 0!==er?er:getPath(Z,et)},deepExtend=(B,Z,et)=>{for(let er in Z)"__proto__"!==er&&"constructor"!==er&&(er in B?isString(B[er])||B[er]instanceof String||isString(Z[er])||Z[er]instanceof String?et&&(B[er]=Z[er]):deepExtend(B[er],Z[er],et):B[er]=Z[er]);return B},regexEscape=B=>B.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var en={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};let escape=B=>isString(B)?B.replace(/[&<>"'\/]/g,B=>en[B]):B;let RegExpCache=class RegExpCache{constructor(B){this.capacity=B,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(B){let Z=this.regExpMap.get(B);if(void 0!==Z)return Z;let et=new RegExp(B);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(B,et),this.regExpQueue.push(B),et}};let eo=[" ",",","?","!",";"],ea=new RegExpCache(20),looksLikeObjectPath=(B,Z,et)=>{Z=Z||"",et=et||"";let er=eo.filter(B=>0>Z.indexOf(B)&&0>et.indexOf(B));if(0===er.length)return!0;let en=ea.getRegExp(`(${er.map(B=>"?"===B?"\\?":B).join("|")})`),ei=!en.test(B);if(!ei){let Z=B.indexOf(et);Z>0&&!en.test(B.substring(0,Z))&&(ei=!0)}return ei},deepFind=(B,Z,et=".")=>{if(!B)return;if(B[Z]){if(!Object.prototype.hasOwnProperty.call(B,Z))return;return B[Z]}let er=Z.split(et),en=B;for(let B=0;B<er.length;){let Z;if(!en||"object"!=typeof en)return;let eo="";for(let ea=B;ea<er.length;++ea)if(ea!==B&&(eo+=et),eo+=er[ea],void 0!==(Z=en[eo])){if(["string","number","boolean"].indexOf(typeof Z)>-1&&ea<er.length-1)continue;B+=ea-B+1;break}en=Z}return en},getCleanedCode=B=>B?.replace("_","-"),ei={type:"logger",log(B){this.output("log",B)},warn(B){this.output("warn",B)},error(B){this.output("error",B)},output(B,Z){console?.[B]?.apply?.(console,Z)}};let Logger=class Logger{constructor(B,Z={}){this.init(B,Z)}init(B,Z={}){this.prefix=Z.prefix||"i18next:",this.logger=B||ei,this.options=Z,this.debug=Z.debug}log(...B){return this.forward(B,"log","",!0)}warn(...B){return this.forward(B,"warn","",!0)}error(...B){return this.forward(B,"error","")}deprecate(...B){return this.forward(B,"warn","WARNING DEPRECATED: ",!0)}forward(B,Z,et,er){return er&&!this.debug?null:(isString(B[0])&&(B[0]=`${et}${this.prefix} ${B[0]}`),this.logger[Z](B))}create(B){return new Logger(this.logger,{prefix:`${this.prefix}:${B}:`,...this.options})}clone(B){return(B=B||this.options).prefix=B.prefix||this.prefix,new Logger(this.logger,B)}};var es=new Logger;let EventEmitter=class EventEmitter{constructor(){this.observers={}}on(B,Z){return B.split(" ").forEach(B=>{this.observers[B]||(this.observers[B]=new Map);let et=this.observers[B].get(Z)||0;this.observers[B].set(Z,et+1)}),this}off(B,Z){if(this.observers[B]){if(!Z){delete this.observers[B];return}this.observers[B].delete(Z)}}emit(B,...Z){if(this.observers[B]){let et=Array.from(this.observers[B].entries());et.forEach(([B,et])=>{for(let er=0;er<et;er++)B(...Z)})}if(this.observers["*"]){let et=Array.from(this.observers["*"].entries());et.forEach(([et,er])=>{for(let en=0;en<er;en++)et.apply(et,[B,...Z])})}}};let ResourceStore=class ResourceStore extends EventEmitter{constructor(B,Z={ns:["translation"],defaultNS:"translation"}){super(),this.data=B||{},this.options=Z,void 0===this.options.keySeparator&&(this.options.keySeparator="."),void 0===this.options.ignoreJSONStructure&&(this.options.ignoreJSONStructure=!0)}addNamespaces(B){0>this.options.ns.indexOf(B)&&this.options.ns.push(B)}removeNamespaces(B){let Z=this.options.ns.indexOf(B);Z>-1&&this.options.ns.splice(Z,1)}getResource(B,Z,et,er={}){let en;let eo=void 0!==er.keySeparator?er.keySeparator:this.options.keySeparator,ea=void 0!==er.ignoreJSONStructure?er.ignoreJSONStructure:this.options.ignoreJSONStructure;B.indexOf(".")>-1?en=B.split("."):(en=[B,Z],et&&(Array.isArray(et)?en.push(...et):isString(et)&&eo?en.push(...et.split(eo)):en.push(et)));let ei=getPath(this.data,en);return(!ei&&!Z&&!et&&B.indexOf(".")>-1&&(B=en[0],Z=en[1],et=en.slice(2).join(".")),!ei&&ea&&isString(et))?deepFind(this.data?.[B]?.[Z],et,eo):ei}addResource(B,Z,et,er,en={silent:!1}){let eo=void 0!==en.keySeparator?en.keySeparator:this.options.keySeparator,ea=[B,Z];et&&(ea=ea.concat(eo?et.split(eo):et)),B.indexOf(".")>-1&&(ea=B.split("."),er=Z,Z=ea[1]),this.addNamespaces(Z),setPath(this.data,ea,er),en.silent||this.emit("added",B,Z,et,er)}addResources(B,Z,et,er={silent:!1}){for(let er in et)(isString(et[er])||Array.isArray(et[er]))&&this.addResource(B,Z,er,et[er],{silent:!0});er.silent||this.emit("added",B,Z,et)}addResourceBundle(B,Z,et,er,en,eo={silent:!1,skipCopy:!1}){let ea=[B,Z];B.indexOf(".")>-1&&(ea=B.split("."),er=et,et=Z,Z=ea[1]),this.addNamespaces(Z);let ei=getPath(this.data,ea)||{};eo.skipCopy||(et=JSON.parse(JSON.stringify(et))),er?deepExtend(ei,et,en):ei={...ei,...et},setPath(this.data,ea,ei),eo.silent||this.emit("added",B,Z,et)}removeResourceBundle(B,Z){this.hasResourceBundle(B,Z)&&delete this.data[B][Z],this.removeNamespaces(Z),this.emit("removed",B,Z)}hasResourceBundle(B,Z){return void 0!==this.getResource(B,Z)}getResourceBundle(B,Z){return Z||(Z=this.options.defaultNS),this.getResource(B,Z)}getDataByLanguage(B){return this.data[B]}hasLanguageSomeTranslations(B){let Z=this.getDataByLanguage(B),et=Z&&Object.keys(Z)||[];return!!et.find(B=>Z[B]&&Object.keys(Z[B]).length>0)}toJSON(){return this.data}};var eu={processors:{},addPostProcessor(B){this.processors[B.name]=B},handle(B,Z,et,er,en){return B.forEach(B=>{Z=this.processors[B]?.process(Z,et,er,en)??Z}),Z}};let el={},shouldHandleAsObject=B=>!isString(B)&&"boolean"!=typeof B&&"number"!=typeof B;let Translator=class Translator extends EventEmitter{constructor(B,Z={}){super(),copy(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],B,this),this.options=Z,void 0===this.options.keySeparator&&(this.options.keySeparator="."),this.logger=es.create("translator")}changeLanguage(B){B&&(this.language=B)}exists(B,Z={interpolation:{}}){let et={...Z};if(null==B)return!1;let er=this.resolve(B,et);return er?.res!==void 0}extractFromKey(B,Z){let et=void 0!==Z.nsSeparator?Z.nsSeparator:this.options.nsSeparator;void 0===et&&(et=":");let er=void 0!==Z.keySeparator?Z.keySeparator:this.options.keySeparator,en=Z.ns||this.options.defaultNS||[],eo=et&&B.indexOf(et)>-1,ea=!this.options.userDefinedKeySeparator&&!Z.keySeparator&&!this.options.userDefinedNsSeparator&&!Z.nsSeparator&&!looksLikeObjectPath(B,et,er);if(eo&&!ea){let Z=B.match(this.interpolator.nestingRegexp);if(Z&&Z.length>0)return{key:B,namespaces:isString(en)?[en]:en};let eo=B.split(et);(et!==er||et===er&&this.options.ns.indexOf(eo[0])>-1)&&(en=eo.shift()),B=eo.join(er)}return{key:B,namespaces:isString(en)?[en]:en}}translate(B,Z,et){let er="object"==typeof Z?{...Z}:Z;if("object"!=typeof er&&this.options.overloadTranslationOptionHandler&&(er=this.options.overloadTranslationOptionHandler(arguments)),"object"==typeof options&&(er={...er}),er||(er={}),null==B)return"";Array.isArray(B)||(B=[String(B)]);let en=void 0!==er.returnDetails?er.returnDetails:this.options.returnDetails,eo=void 0!==er.keySeparator?er.keySeparator:this.options.keySeparator,{key:ea,namespaces:ei}=this.extractFromKey(B[B.length-1],er),es=ei[ei.length-1],eu=void 0!==er.nsSeparator?er.nsSeparator:this.options.nsSeparator;void 0===eu&&(eu=":");let el=er.lng||this.language,ec=er.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if(el?.toLowerCase()==="cimode")return ec?en?{res:`${es}${eu}${ea}`,usedKey:ea,exactUsedKey:ea,usedLng:el,usedNS:es,usedParams:this.getUsedParamsDetails(er)}:`${es}${eu}${ea}`:en?{res:ea,usedKey:ea,exactUsedKey:ea,usedLng:el,usedNS:es,usedParams:this.getUsedParamsDetails(er)}:ea;let ed=this.resolve(B,er),ef=ed?.res,ep=ed?.usedKey||ea,eh=ed?.exactUsedKey||ea,eg=void 0!==er.joinArrays?er.joinArrays:this.options.joinArrays,ey=!this.i18nFormat||this.i18nFormat.handleAsObject,em=void 0!==er.count&&!isString(er.count),ev=Translator.hasDefaultValue(er),eb=em?this.pluralResolver.getSuffix(el,er.count,er):"",eS=er.ordinal&&em?this.pluralResolver.getSuffix(el,er.count,{ordinal:!1}):"",eP=em&&!er.ordinal&&0===er.count,eO=eP&&er[`defaultValue${this.options.pluralSeparator}zero`]||er[`defaultValue${eb}`]||er[`defaultValue${eS}`]||er.defaultValue,e_=ef;ey&&!ef&&ev&&(e_=eO);let ex=shouldHandleAsObject(e_),eR=Object.prototype.toString.apply(e_);if(ey&&e_&&ex&&0>["[object Number]","[object Function]","[object RegExp]"].indexOf(eR)&&!(isString(eg)&&Array.isArray(e_))){if(!er.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");let B=this.options.returnedObjectHandler?this.options.returnedObjectHandler(ep,e_,{...er,ns:ei}):`key '${ea} (${this.language})' returned an object instead of string.`;return en?(ed.res=B,ed.usedParams=this.getUsedParamsDetails(er),ed):B}if(eo){let B=Array.isArray(e_),Z=B?[]:{},et=B?eh:ep;for(let B in e_)if(Object.prototype.hasOwnProperty.call(e_,B)){let en=`${et}${eo}${B}`;ev&&!ef?Z[B]=this.translate(en,{...er,defaultValue:shouldHandleAsObject(eO)?eO[B]:void 0,joinArrays:!1,ns:ei}):Z[B]=this.translate(en,{...er,joinArrays:!1,ns:ei}),Z[B]===en&&(Z[B]=e_[B])}ef=Z}}else if(ey&&isString(eg)&&Array.isArray(ef))(ef=ef.join(eg))&&(ef=this.extendTranslation(ef,B,er,et));else{let Z=!1,en=!1;!this.isValidLookup(ef)&&ev&&(Z=!0,ef=eO),this.isValidLookup(ef)||(en=!0,ef=ea);let ei=er.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey,ec=ei&&en?void 0:ef,ep=ev&&eO!==ef&&this.options.updateMissing;if(en||Z||ep){if(this.logger.log(ep?"updateKey":"missingKey",el,es,ea,ep?eO:ef),eo){let B=this.resolve(ea,{...er,keySeparator:!1});B&&B.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let B=[],Z=this.languageUtils.getFallbackCodes(this.options.fallbackLng,er.lng||this.language);if("fallback"===this.options.saveMissingTo&&Z&&Z[0])for(let et=0;et<Z.length;et++)B.push(Z[et]);else"all"===this.options.saveMissingTo?B=this.languageUtils.toResolveHierarchy(er.lng||this.language):B.push(er.lng||this.language);let send=(B,Z,et)=>{let en=ev&&et!==ef?et:ec;this.options.missingKeyHandler?this.options.missingKeyHandler(B,es,Z,en,ep,er):this.backendConnector?.saveMissing&&this.backendConnector.saveMissing(B,es,Z,en,ep,er),this.emit("missingKey",B,es,Z,ef)};this.options.saveMissing&&(this.options.saveMissingPlurals&&em?B.forEach(B=>{let Z=this.pluralResolver.getSuffixes(B,er);eP&&er[`defaultValue${this.options.pluralSeparator}zero`]&&0>Z.indexOf(`${this.options.pluralSeparator}zero`)&&Z.push(`${this.options.pluralSeparator}zero`),Z.forEach(Z=>{send([B],ea+Z,er[`defaultValue${Z}`]||eO)})}):send(B,ea,eO))}ef=this.extendTranslation(ef,B,er,ed,et),en&&ef===ea&&this.options.appendNamespaceToMissingKey&&(ef=`${es}${eu}${ea}`),(en||Z)&&this.options.parseMissingKeyHandler&&(ef=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${es}${eu}${ea}`:ea,Z?ef:void 0,er))}return en?(ed.res=ef,ed.usedParams=this.getUsedParamsDetails(er),ed):ef}extendTranslation(B,Z,et,er,en){if(this.i18nFormat?.parse)B=this.i18nFormat.parse(B,{...this.options.interpolation.defaultVariables,...et},et.lng||this.language||er.usedLng,er.usedNS,er.usedKey,{resolved:er});else if(!et.skipInterpolation){let eo;et.interpolation&&this.interpolator.init({...et,interpolation:{...this.options.interpolation,...et.interpolation}});let ea=isString(B)&&(et?.interpolation?.skipOnVariables!==void 0?et.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);if(ea){let Z=B.match(this.interpolator.nestingRegexp);eo=Z&&Z.length}let ei=et.replace&&!isString(et.replace)?et.replace:et;if(this.options.interpolation.defaultVariables&&(ei={...this.options.interpolation.defaultVariables,...ei}),B=this.interpolator.interpolate(B,ei,et.lng||this.language||er.usedLng,et),ea){let Z=B.match(this.interpolator.nestingRegexp),er=Z&&Z.length;eo<er&&(et.nest=!1)}!et.lng&&er&&er.res&&(et.lng=this.language||er.usedLng),!1!==et.nest&&(B=this.interpolator.nest(B,(...B)=>en?.[0]!==B[0]||et.context?this.translate(...B,Z):(this.logger.warn(`It seems you are nesting recursively key: ${B[0]} in key: ${Z[0]}`),null),et)),et.interpolation&&this.interpolator.reset()}let eo=et.postProcess||this.options.postProcess,ea=isString(eo)?[eo]:eo;return null!=B&&ea?.length&&!1!==et.applyPostProcessor&&(B=eu.handle(ea,B,Z,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...er,usedParams:this.getUsedParamsDetails(et)},...et}:et,this)),B}resolve(B,Z={}){let et,er,en,eo,ea;return isString(B)&&(B=[B]),B.forEach(B=>{if(this.isValidLookup(et))return;let ei=this.extractFromKey(B,Z),es=ei.key;er=es;let eu=ei.namespaces;this.options.fallbackNS&&(eu=eu.concat(this.options.fallbackNS));let ec=void 0!==Z.count&&!isString(Z.count),ed=ec&&!Z.ordinal&&0===Z.count,ef=void 0!==Z.context&&(isString(Z.context)||"number"==typeof Z.context)&&""!==Z.context,ep=Z.lngs?Z.lngs:this.languageUtils.toResolveHierarchy(Z.lng||this.language,Z.fallbackLng);eu.forEach(B=>{this.isValidLookup(et)||(ea=B,!el[`${ep[0]}-${B}`]&&this.utils?.hasLoadedNamespace&&!this.utils?.hasLoadedNamespace(ea)&&(el[`${ep[0]}-${B}`]=!0,this.logger.warn(`key "${er}" for languages "${ep.join(", ")}" won't get resolved as namespace "${ea}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),ep.forEach(er=>{let ea;if(this.isValidLookup(et))return;eo=er;let ei=[es];if(this.i18nFormat?.addLookupKeys)this.i18nFormat.addLookupKeys(ei,es,er,B,Z);else{let B;ec&&(B=this.pluralResolver.getSuffix(er,Z.count,Z));let et=`${this.options.pluralSeparator}zero`,en=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(ec&&(ei.push(es+B),Z.ordinal&&0===B.indexOf(en)&&ei.push(es+B.replace(en,this.options.pluralSeparator)),ed&&ei.push(es+et)),ef){let er=`${es}${this.options.contextSeparator}${Z.context}`;ei.push(er),ec&&(ei.push(er+B),Z.ordinal&&0===B.indexOf(en)&&ei.push(er+B.replace(en,this.options.pluralSeparator)),ed&&ei.push(er+et))}}for(;ea=ei.pop();)this.isValidLookup(et)||(en=ea,et=this.getResource(er,B,ea,Z))}))})}),{res:et,usedKey:er,exactUsedKey:en,usedLng:eo,usedNS:ea}}isValidLookup(B){return void 0!==B&&!(!this.options.returnNull&&null===B)&&!(!this.options.returnEmptyString&&""===B)}getResource(B,Z,et,er={}){return this.i18nFormat?.getResource?this.i18nFormat.getResource(B,Z,et,er):this.resourceStore.getResource(B,Z,et,er)}getUsedParamsDetails(B={}){let Z=B.replace&&!isString(B.replace),et=Z?B.replace:B;if(Z&&void 0!==B.count&&(et.count=B.count),this.options.interpolation.defaultVariables&&(et={...this.options.interpolation.defaultVariables,...et}),!Z)for(let B of(et={...et},["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"]))delete et[B];return et}static hasDefaultValue(B){let Z="defaultValue";for(let et in B)if(Object.prototype.hasOwnProperty.call(B,et)&&Z===et.substring(0,Z.length)&&void 0!==B[et])return!0;return!1}};let LanguageUtil=class LanguageUtil{constructor(B){this.options=B,this.supportedLngs=this.options.supportedLngs||!1,this.logger=es.create("languageUtils")}getScriptPartFromCode(B){if(!(B=getCleanedCode(B))||0>B.indexOf("-"))return null;let Z=B.split("-");return 2===Z.length?null:(Z.pop(),"x"===Z[Z.length-1].toLowerCase())?null:this.formatLanguageCode(Z.join("-"))}getLanguagePartFromCode(B){if(!(B=getCleanedCode(B))||0>B.indexOf("-"))return B;let Z=B.split("-");return this.formatLanguageCode(Z[0])}formatLanguageCode(B){if(isString(B)&&B.indexOf("-")>-1){let Z;try{Z=Intl.getCanonicalLocales(B)[0]}catch(B){}return(Z&&this.options.lowerCaseLng&&(Z=Z.toLowerCase()),Z)?Z:this.options.lowerCaseLng?B.toLowerCase():B}return this.options.cleanCode||this.options.lowerCaseLng?B.toLowerCase():B}isSupportedCode(B){return("languageOnly"===this.options.load||this.options.nonExplicitSupportedLngs)&&(B=this.getLanguagePartFromCode(B)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(B)>-1}getBestMatchFromCodes(B){let Z;return B?(B.forEach(B=>{if(Z)return;let et=this.formatLanguageCode(B);(!this.options.supportedLngs||this.isSupportedCode(et))&&(Z=et)}),!Z&&this.options.supportedLngs&&B.forEach(B=>{if(Z)return;let et=this.getScriptPartFromCode(B);if(this.isSupportedCode(et))return Z=et;let er=this.getLanguagePartFromCode(B);if(this.isSupportedCode(er))return Z=er;Z=this.options.supportedLngs.find(B=>{if(B===er||!(0>B.indexOf("-")&&0>er.indexOf("-"))&&(B.indexOf("-")>0&&0>er.indexOf("-")&&B.substring(0,B.indexOf("-"))===er||0===B.indexOf(er)&&er.length>1))return B})}),Z||(Z=this.getFallbackCodes(this.options.fallbackLng)[0]),Z):null}getFallbackCodes(B,Z){if(!B)return[];if("function"==typeof B&&(B=B(Z)),isString(B)&&(B=[B]),Array.isArray(B))return B;if(!Z)return B.default||[];let et=B[Z];return et||(et=B[this.getScriptPartFromCode(Z)]),et||(et=B[this.formatLanguageCode(Z)]),et||(et=B[this.getLanguagePartFromCode(Z)]),et||(et=B.default),et||[]}toResolveHierarchy(B,Z){let et=this.getFallbackCodes(Z||this.options.fallbackLng||[],B),er=[],addCode=B=>{B&&(this.isSupportedCode(B)?er.push(B):this.logger.warn(`rejecting language code not found in supportedLngs: ${B}`))};return isString(B)&&(B.indexOf("-")>-1||B.indexOf("_")>-1)?("languageOnly"!==this.options.load&&addCode(this.formatLanguageCode(B)),"languageOnly"!==this.options.load&&"currentOnly"!==this.options.load&&addCode(this.getScriptPartFromCode(B)),"currentOnly"!==this.options.load&&addCode(this.getLanguagePartFromCode(B))):isString(B)&&addCode(this.formatLanguageCode(B)),et.forEach(B=>{0>er.indexOf(B)&&addCode(this.formatLanguageCode(B))}),er}};let ec={zero:0,one:1,two:2,few:3,many:4,other:5},ed={select:B=>1===B?"one":"other",resolvedOptions:()=>({pluralCategories:["one","other"]})};let PluralResolver=class PluralResolver{constructor(B,Z={}){this.languageUtils=B,this.options=Z,this.logger=es.create("pluralResolver"),this.pluralRulesCache={}}addRule(B,Z){this.rules[B]=Z}clearCache(){this.pluralRulesCache={}}getRule(B,Z={}){let et;let er=getCleanedCode("dev"===B?"en":B),en=Z.ordinal?"ordinal":"cardinal",eo=JSON.stringify({cleanedCode:er,type:en});if(eo in this.pluralRulesCache)return this.pluralRulesCache[eo];try{et=new Intl.PluralRules(er,{type:en})}catch(en){if(!Intl)return this.logger.error("No Intl support, please use an Intl polyfill!"),ed;if(!B.match(/-|_/))return ed;let er=this.languageUtils.getLanguagePartFromCode(B);et=this.getRule(er,Z)}return this.pluralRulesCache[eo]=et,et}needsPlural(B,Z={}){let et=this.getRule(B,Z);return et||(et=this.getRule("dev",Z)),et?.resolvedOptions().pluralCategories.length>1}getPluralFormsOfKey(B,Z,et={}){return this.getSuffixes(B,et).map(B=>`${Z}${B}`)}getSuffixes(B,Z={}){let et=this.getRule(B,Z);return(et||(et=this.getRule("dev",Z)),et)?et.resolvedOptions().pluralCategories.sort((B,Z)=>ec[B]-ec[Z]).map(B=>`${this.options.prepend}${Z.ordinal?`ordinal${this.options.prepend}`:""}${B}`):[]}getSuffix(B,Z,et={}){let er=this.getRule(B,et);return er?`${this.options.prepend}${et.ordinal?`ordinal${this.options.prepend}`:""}${er.select(Z)}`:(this.logger.warn(`no plural rule found for: ${B}`),this.getSuffix("dev",Z,et))}};let deepFindWithDefaults=(B,Z,et,er=".",en=!0)=>{let eo=getPathWithDefaults(B,Z,et);return!eo&&en&&isString(et)&&void 0===(eo=deepFind(B,et,er))&&(eo=deepFind(Z,et,er)),eo},regexSafe=B=>B.replace(/\$/g,"$$$$");let Interpolator=class Interpolator{constructor(B={}){this.logger=es.create("interpolator"),this.options=B,this.format=B?.interpolation?.format||(B=>B),this.init(B)}init(B={}){B.interpolation||(B.interpolation={escapeValue:!0});let{escape:Z,escapeValue:et,useRawValueToEscape:er,prefix:en,prefixEscaped:eo,suffix:ea,suffixEscaped:ei,formatSeparator:es,unescapeSuffix:eu,unescapePrefix:el,nestingPrefix:ec,nestingPrefixEscaped:ed,nestingSuffix:ef,nestingSuffixEscaped:ep,nestingOptionsSeparator:eh,maxReplaces:eg,alwaysFormat:ey}=B.interpolation;this.escape=void 0!==Z?Z:escape,this.escapeValue=void 0===et||et,this.useRawValueToEscape=void 0!==er&&er,this.prefix=en?regexEscape(en):eo||"{{",this.suffix=ea?regexEscape(ea):ei||"}}",this.formatSeparator=es||",",this.unescapePrefix=eu?"":el||"-",this.unescapeSuffix=this.unescapePrefix?"":eu||"",this.nestingPrefix=ec?regexEscape(ec):ed||regexEscape("$t("),this.nestingSuffix=ef?regexEscape(ef):ep||regexEscape(")"),this.nestingOptionsSeparator=eh||",",this.maxReplaces=eg||1e3,this.alwaysFormat=void 0!==ey&&ey,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){let getOrResetRegExp=(B,Z)=>B?.source===Z?(B.lastIndex=0,B):RegExp(Z,"g");this.regexp=getOrResetRegExp(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=getOrResetRegExp(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=getOrResetRegExp(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(B,Z,et,er){let en,eo,ea;let ei=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},handleFormat=B=>{if(0>B.indexOf(this.formatSeparator)){let en=deepFindWithDefaults(Z,ei,B,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(en,void 0,et,{...er,...Z,interpolationkey:B}):en}let en=B.split(this.formatSeparator),eo=en.shift().trim(),ea=en.join(this.formatSeparator).trim();return this.format(deepFindWithDefaults(Z,ei,eo,this.options.keySeparator,this.options.ignoreJSONStructure),ea,et,{...er,...Z,interpolationkey:eo})};this.resetRegExp();let es=er?.missingInterpolationHandler||this.options.missingInterpolationHandler,eu=er?.interpolation?.skipOnVariables!==void 0?er.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables,el=[{regex:this.regexpUnescape,safeValue:B=>regexSafe(B)},{regex:this.regexp,safeValue:B=>this.escapeValue?regexSafe(this.escape(B)):regexSafe(B)}];return el.forEach(Z=>{for(ea=0;en=Z.regex.exec(B);){let et=en[1].trim();if(void 0===(eo=handleFormat(et))){if("function"==typeof es){let Z=es(B,en,er);eo=isString(Z)?Z:""}else if(er&&Object.prototype.hasOwnProperty.call(er,et))eo="";else if(eu){eo=en[0];continue}else this.logger.warn(`missed to pass in variable ${et} for interpolating ${B}`),eo=""}else isString(eo)||this.useRawValueToEscape||(eo=makeString(eo));let ei=Z.safeValue(eo);if(B=B.replace(en[0],ei),eu?(Z.regex.lastIndex+=eo.length,Z.regex.lastIndex-=en[0].length):Z.regex.lastIndex=0,++ea>=this.maxReplaces)break}}),B}nest(B,Z,et={}){let er,en,eo;let handleHasOptions=(B,Z)=>{let et=this.nestingOptionsSeparator;if(0>B.indexOf(et))return B;let er=B.split(RegExp(`${et}[ ]*{`)),en=`{${er[1]}`;B=er[0],en=this.interpolate(en,eo);let ea=en.match(/'/g),ei=en.match(/"/g);((ea?.length??0)%2!=0||ei)&&ei.length%2==0||(en=en.replace(/'/g,'"'));try{eo=JSON.parse(en),Z&&(eo={...Z,...eo})}catch(Z){return this.logger.warn(`failed parsing options string in nesting for key ${B}`,Z),`${B}${et}${en}`}return eo.defaultValue&&eo.defaultValue.indexOf(this.prefix)>-1&&delete eo.defaultValue,B};for(;er=this.nestingRegexp.exec(B);){let ea=[];(eo=(eo={...et}).replace&&!isString(eo.replace)?eo.replace:eo).applyPostProcessor=!1,delete eo.defaultValue;let ei=!1;if(-1!==er[0].indexOf(this.formatSeparator)&&!/{.*}/.test(er[1])){let B=er[1].split(this.formatSeparator).map(B=>B.trim());er[1]=B.shift(),ea=B,ei=!0}if((en=Z(handleHasOptions.call(this,er[1].trim(),eo),eo))&&er[0]===B&&!isString(en))return en;isString(en)||(en=makeString(en)),en||(this.logger.warn(`missed to resolve ${er[1]} for nesting ${B}`),en=""),ei&&(en=ea.reduce((B,Z)=>this.format(B,Z,et.lng,{...et,interpolationkey:er[1].trim()}),en.trim())),B=B.replace(er[0],en),this.regexp.lastIndex=0}return B}};let parseFormatStr=B=>{let Z=B.toLowerCase().trim(),et={};if(B.indexOf("(")>-1){let er=B.split("(");Z=er[0].toLowerCase().trim();let en=er[1].substring(0,er[1].length-1);if("currency"===Z&&0>en.indexOf(":"))et.currency||(et.currency=en.trim());else if("relativetime"===Z&&0>en.indexOf(":"))et.range||(et.range=en.trim());else{let B=en.split(";");B.forEach(B=>{if(B){let[Z,...er]=B.split(":"),en=er.join(":").trim().replace(/^'+|'+$/g,""),eo=Z.trim();et[eo]||(et[eo]=en),"false"===en&&(et[eo]=!1),"true"===en&&(et[eo]=!0),isNaN(en)||(et[eo]=parseInt(en,10))}})}}return{formatName:Z,formatOptions:et}},createCachedFormatter=B=>{let Z={};return(et,er,en)=>{let eo=en;en&&en.interpolationkey&&en.formatParams&&en.formatParams[en.interpolationkey]&&en[en.interpolationkey]&&(eo={...eo,[en.interpolationkey]:void 0});let ea=er+JSON.stringify(eo),ei=Z[ea];return ei||(ei=B(getCleanedCode(er),en),Z[ea]=ei),ei(et)}};let Formatter=class Formatter{constructor(B={}){this.logger=es.create("formatter"),this.options=B,this.formats={number:createCachedFormatter((B,Z)=>{let et=new Intl.NumberFormat(B,{...Z});return B=>et.format(B)}),currency:createCachedFormatter((B,Z)=>{let et=new Intl.NumberFormat(B,{...Z,style:"currency"});return B=>et.format(B)}),datetime:createCachedFormatter((B,Z)=>{let et=new Intl.DateTimeFormat(B,{...Z});return B=>et.format(B)}),relativetime:createCachedFormatter((B,Z)=>{let et=new Intl.RelativeTimeFormat(B,{...Z});return B=>et.format(B,Z.range||"day")}),list:createCachedFormatter((B,Z)=>{let et=new Intl.ListFormat(B,{...Z});return B=>et.format(B)})},this.init(B)}init(B,Z={interpolation:{}}){this.formatSeparator=Z.interpolation.formatSeparator||","}add(B,Z){this.formats[B.toLowerCase().trim()]=Z}addCached(B,Z){this.formats[B.toLowerCase().trim()]=createCachedFormatter(Z)}format(B,Z,et,er={}){let en=Z.split(this.formatSeparator);if(en.length>1&&en[0].indexOf("(")>1&&0>en[0].indexOf(")")&&en.find(B=>B.indexOf(")")>-1)){let B=en.findIndex(B=>B.indexOf(")")>-1);en[0]=[en[0],...en.splice(1,B)].join(this.formatSeparator)}let eo=en.reduce((B,Z)=>{let{formatName:en,formatOptions:eo}=parseFormatStr(Z);if(this.formats[en]){let Z=B;try{let ea=er?.formatParams?.[er.interpolationkey]||{},ei=ea.locale||ea.lng||er.locale||er.lng||et;Z=this.formats[en](B,ei,{...eo,...er,...ea})}catch(B){this.logger.warn(B)}return Z}return this.logger.warn(`there was no format function for ${en}`),B},B);return eo}};let removePending=(B,Z)=>{void 0!==B.pending[Z]&&(delete B.pending[Z],B.pendingCount--)};let Connector=class Connector extends EventEmitter{constructor(B,Z,et,er={}){super(),this.backend=B,this.store=Z,this.services=et,this.languageUtils=et.languageUtils,this.options=er,this.logger=es.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=er.maxParallelReads||10,this.readingCalls=0,this.maxRetries=er.maxRetries>=0?er.maxRetries:5,this.retryTimeout=er.retryTimeout>=1?er.retryTimeout:350,this.state={},this.queue=[],this.backend?.init?.(et,er.backend,er)}queueLoad(B,Z,et,er){let en={},eo={},ea={},ei={};return B.forEach(B=>{let er=!0;Z.forEach(Z=>{let ea=`${B}|${Z}`;!et.reload&&this.store.hasResourceBundle(B,Z)?this.state[ea]=2:this.state[ea]<0||(1===this.state[ea]?void 0===eo[ea]&&(eo[ea]=!0):(this.state[ea]=1,er=!1,void 0===eo[ea]&&(eo[ea]=!0),void 0===en[ea]&&(en[ea]=!0),void 0===ei[Z]&&(ei[Z]=!0)))}),er||(ea[B]=!0)}),(Object.keys(en).length||Object.keys(eo).length)&&this.queue.push({pending:eo,pendingCount:Object.keys(eo).length,loaded:{},errors:[],callback:er}),{toLoad:Object.keys(en),pending:Object.keys(eo),toLoadLanguages:Object.keys(ea),toLoadNamespaces:Object.keys(ei)}}loaded(B,Z,et){let er=B.split("|"),en=er[0],eo=er[1];Z&&this.emit("failedLoading",en,eo,Z),!Z&&et&&this.store.addResourceBundle(en,eo,et,void 0,void 0,{skipCopy:!0}),this.state[B]=Z?-1:2,Z&&et&&(this.state[B]=0);let ea={};this.queue.forEach(et=>{pushPath(et.loaded,[en],eo),removePending(et,B),Z&&et.errors.push(Z),0!==et.pendingCount||et.done||(Object.keys(et.loaded).forEach(B=>{ea[B]||(ea[B]={});let Z=et.loaded[B];Z.length&&Z.forEach(Z=>{void 0===ea[B][Z]&&(ea[B][Z]=!0)})}),et.done=!0,et.errors.length?et.callback(et.errors):et.callback())}),this.emit("loaded",ea),this.queue=this.queue.filter(B=>!B.done)}read(B,Z,et,er=0,en=this.retryTimeout,eo){if(!B.length)return eo(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:B,ns:Z,fcName:et,tried:er,wait:en,callback:eo});return}this.readingCalls++;let resolver=(ea,ei)=>{if(this.readingCalls--,this.waitingReads.length>0){let B=this.waitingReads.shift();this.read(B.lng,B.ns,B.fcName,B.tried,B.wait,B.callback)}if(ea&&ei&&er<this.maxRetries){setTimeout(()=>{this.read.call(this,B,Z,et,er+1,2*en,eo)},en);return}eo(ea,ei)},ea=this.backend[et].bind(this.backend);if(2===ea.length){try{let et=ea(B,Z);et&&"function"==typeof et.then?et.then(B=>resolver(null,B)).catch(resolver):resolver(null,et)}catch(B){resolver(B)}return}return ea(B,Z,resolver)}prepareLoading(B,Z,et={},er){if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),er&&er();isString(B)&&(B=this.languageUtils.toResolveHierarchy(B)),isString(Z)&&(Z=[Z]);let en=this.queueLoad(B,Z,et,er);if(!en.toLoad.length)return en.pending.length||er(),null;en.toLoad.forEach(B=>{this.loadOne(B)})}load(B,Z,et){this.prepareLoading(B,Z,{},et)}reload(B,Z,et){this.prepareLoading(B,Z,{reload:!0},et)}loadOne(B,Z=""){let et=B.split("|"),er=et[0],en=et[1];this.read(er,en,"read",void 0,void 0,(et,eo)=>{et&&this.logger.warn(`${Z}loading namespace ${en} for language ${er} failed`,et),!et&&eo&&this.logger.log(`${Z}loaded namespace ${en} for language ${er}`,eo),this.loaded(B,et,eo)})}saveMissing(B,Z,et,er,en,eo={},ea=()=>{}){if(this.services?.utils?.hasLoadedNamespace&&!this.services?.utils?.hasLoadedNamespace(Z)){this.logger.warn(`did not save key "${et}" as the namespace "${Z}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(null!=et&&""!==et){if(this.backend?.create){let ei={...eo,isUpdate:en},es=this.backend.create.bind(this.backend);if(es.length<6)try{let en;(en=5===es.length?es(B,Z,et,er,ei):es(B,Z,et,er))&&"function"==typeof en.then?en.then(B=>ea(null,B)).catch(ea):ea(null,en)}catch(B){ea(B)}else es(B,Z,et,er,ea,ei)}B&&B[0]&&this.store.addResource(B[0],Z,et,er)}}};let get=()=>({debug:!1,initAsync:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:B=>{let Z={};if("object"==typeof B[1]&&(Z=B[1]),isString(B[1])&&(Z.defaultValue=B[1]),isString(B[2])&&(Z.tDescription=B[2]),"object"==typeof B[2]||"object"==typeof B[3]){let et=B[3]||B[2];Object.keys(et).forEach(B=>{Z[B]=et[B]})}return Z},interpolation:{escapeValue:!0,format:B=>B,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0}}),transformOptions=B=>(isString(B.ns)&&(B.ns=[B.ns]),isString(B.fallbackLng)&&(B.fallbackLng=[B.fallbackLng]),isString(B.fallbackNS)&&(B.fallbackNS=[B.fallbackNS]),B.supportedLngs?.indexOf?.("cimode")<0&&(B.supportedLngs=B.supportedLngs.concat(["cimode"])),"boolean"==typeof B.initImmediate&&(B.initAsync=B.initImmediate),B),noop=()=>{},bindMemberFunctions=B=>{let Z=Object.getOwnPropertyNames(Object.getPrototypeOf(B));Z.forEach(Z=>{"function"==typeof B[Z]&&(B[Z]=B[Z].bind(B))})};let I18n=class I18n extends EventEmitter{constructor(B={},Z){if(super(),this.options=transformOptions(B),this.services={},this.logger=es,this.modules={external:[]},bindMemberFunctions(this),Z&&!this.isInitialized&&!B.isClone){if(!this.options.initAsync)return this.init(B,Z),this;setTimeout(()=>{this.init(B,Z)},0)}}init(B={},Z){this.isInitializing=!0,"function"==typeof B&&(Z=B,B={}),null==B.defaultNS&&B.ns&&(isString(B.ns)?B.defaultNS=B.ns:0>B.ns.indexOf("translation")&&(B.defaultNS=B.ns[0]));let et=get();this.options={...et,...this.options,...transformOptions(B)},this.options.interpolation={...et.interpolation,...this.options.interpolation},void 0!==B.keySeparator&&(this.options.userDefinedKeySeparator=B.keySeparator),void 0!==B.nsSeparator&&(this.options.userDefinedNsSeparator=B.nsSeparator);let createClassOnDemand=B=>B?"function"==typeof B?new B:B:null;if(!this.options.isClone){let B;this.modules.logger?es.init(createClassOnDemand(this.modules.logger),this.options):es.init(null,this.options),B=this.modules.formatter?this.modules.formatter:Formatter;let Z=new LanguageUtil(this.options);this.store=new ResourceStore(this.options.resources,this.options);let er=this.services;er.logger=es,er.resourceStore=this.store,er.languageUtils=Z,er.pluralResolver=new PluralResolver(Z,{prepend:this.options.pluralSeparator,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),B&&(!this.options.interpolation.format||this.options.interpolation.format===et.interpolation.format)&&(er.formatter=createClassOnDemand(B),er.formatter.init(er,this.options),this.options.interpolation.format=er.formatter.format.bind(er.formatter)),er.interpolator=new Interpolator(this.options),er.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},er.backendConnector=new Connector(createClassOnDemand(this.modules.backend),er.resourceStore,er,this.options),er.backendConnector.on("*",(B,...Z)=>{this.emit(B,...Z)}),this.modules.languageDetector&&(er.languageDetector=createClassOnDemand(this.modules.languageDetector),er.languageDetector.init&&er.languageDetector.init(er,this.options.detection,this.options)),this.modules.i18nFormat&&(er.i18nFormat=createClassOnDemand(this.modules.i18nFormat),er.i18nFormat.init&&er.i18nFormat.init(this)),this.translator=new Translator(this.services,this.options),this.translator.on("*",(B,...Z)=>{this.emit(B,...Z)}),this.modules.external.forEach(B=>{B.init&&B.init(this)})}if(this.format=this.options.interpolation.format,Z||(Z=noop),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){let B=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);B.length>0&&"dev"!==B[0]&&(this.options.lng=B[0])}this.services.languageDetector||this.options.lng||this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(B=>{this[B]=(...Z)=>this.store[B](...Z)}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(B=>{this[B]=(...Z)=>(this.store[B](...Z),this)});let er=defer(),load=()=>{let finish=(B,et)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),er.resolve(et),Z(B,et)};if(this.languages&&!this.isInitialized)return finish(null,this.t.bind(this));this.changeLanguage(this.options.lng,finish)};return this.options.resources||!this.options.initAsync?load():setTimeout(load,0),er}loadResources(B,Z=noop){let et=Z,er=isString(B)?B:this.language;if("function"==typeof B&&(et=B),!this.options.resources||this.options.partialBundledLanguages){if(er?.toLowerCase()==="cimode"&&(!this.options.preload||0===this.options.preload.length))return et();let B=[],append=Z=>{if(!Z||"cimode"===Z)return;let et=this.services.languageUtils.toResolveHierarchy(Z);et.forEach(Z=>{"cimode"!==Z&&0>B.indexOf(Z)&&B.push(Z)})};if(er)append(er);else{let B=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);B.forEach(B=>append(B))}this.options.preload?.forEach?.(B=>append(B)),this.services.backendConnector.load(B,this.options.ns,B=>{B||this.resolvedLanguage||!this.language||this.setResolvedLanguage(this.language),et(B)})}else et(null)}reloadResources(B,Z,et){let er=defer();return"function"==typeof B&&(et=B,B=void 0),"function"==typeof Z&&(et=Z,Z=void 0),B||(B=this.languages),Z||(Z=this.options.ns),et||(et=noop),this.services.backendConnector.reload(B,Z,B=>{er.resolve(),et(B)}),er}use(B){if(!B)throw Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!B.type)throw Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return"backend"===B.type&&(this.modules.backend=B),("logger"===B.type||B.log&&B.warn&&B.error)&&(this.modules.logger=B),"languageDetector"===B.type&&(this.modules.languageDetector=B),"i18nFormat"===B.type&&(this.modules.i18nFormat=B),"postProcessor"===B.type&&eu.addPostProcessor(B),"formatter"===B.type&&(this.modules.formatter=B),"3rdParty"===B.type&&this.modules.external.push(B),this}setResolvedLanguage(B){if(B&&this.languages&&!(["cimode","dev"].indexOf(B)>-1)){for(let B=0;B<this.languages.length;B++){let Z=this.languages[B];if(!(["cimode","dev"].indexOf(Z)>-1)&&this.store.hasLanguageSomeTranslations(Z)){this.resolvedLanguage=Z;break}}!this.resolvedLanguage&&0>this.languages.indexOf(B)&&this.store.hasLanguageSomeTranslations(B)&&(this.resolvedLanguage=B,this.languages.unshift(B))}}changeLanguage(B,Z){this.isLanguageChangingTo=B;let et=defer();this.emit("languageChanging",B);let setLngProps=B=>{this.language=B,this.languages=this.services.languageUtils.toResolveHierarchy(B),this.resolvedLanguage=void 0,this.setResolvedLanguage(B)},done=(er,en)=>{en?this.isLanguageChangingTo===B&&(setLngProps(en),this.translator.changeLanguage(en),this.isLanguageChangingTo=void 0,this.emit("languageChanged",en),this.logger.log("languageChanged",en)):this.isLanguageChangingTo=void 0,et.resolve((...B)=>this.t(...B)),Z&&Z(er,(...B)=>this.t(...B))},setLng=Z=>{B||Z||!this.services.languageDetector||(Z=[]);let et=isString(Z)?Z:Z&&Z[0],er=this.store.hasLanguageSomeTranslations(et)?et:this.services.languageUtils.getBestMatchFromCodes(isString(Z)?[Z]:Z);er&&(this.language||setLngProps(er),this.translator.language||this.translator.changeLanguage(er),this.services.languageDetector?.cacheUserLanguage?.(er)),this.loadResources(er,B=>{done(B,er)})};return B||!this.services.languageDetector||this.services.languageDetector.async?!B&&this.services.languageDetector&&this.services.languageDetector.async?0===this.services.languageDetector.detect.length?this.services.languageDetector.detect().then(setLng):this.services.languageDetector.detect(setLng):setLng(B):setLng(this.services.languageDetector.detect()),et}getFixedT(B,Z,et){let fixedT=(B,Z,...er)=>{let en,eo;(en="object"!=typeof Z?this.options.overloadTranslationOptionHandler([B,Z].concat(er)):{...Z}).lng=en.lng||fixedT.lng,en.lngs=en.lngs||fixedT.lngs,en.ns=en.ns||fixedT.ns,""!==en.keyPrefix&&(en.keyPrefix=en.keyPrefix||et||fixedT.keyPrefix);let ea=this.options.keySeparator||".";return eo=en.keyPrefix&&Array.isArray(B)?B.map(B=>`${en.keyPrefix}${ea}${B}`):en.keyPrefix?`${en.keyPrefix}${ea}${B}`:B,this.t(eo,en)};return isString(B)?fixedT.lng=B:fixedT.lngs=B,fixedT.ns=Z,fixedT.keyPrefix=et,fixedT}t(...B){return this.translator?.translate(...B)}exists(...B){return this.translator?.exists(...B)}setDefaultNamespace(B){this.options.defaultNS=B}hasLoadedNamespace(B,Z={}){if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;let et=Z.lng||this.resolvedLanguage||this.languages[0],er=!!this.options&&this.options.fallbackLng,en=this.languages[this.languages.length-1];if("cimode"===et.toLowerCase())return!0;let loadNotPending=(B,Z)=>{let et=this.services.backendConnector.state[`${B}|${Z}`];return -1===et||0===et||2===et};if(Z.precheck){let B=Z.precheck(this,loadNotPending);if(void 0!==B)return B}return!!(this.hasResourceBundle(et,B)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||loadNotPending(et,B)&&(!er||loadNotPending(en,B)))}loadNamespaces(B,Z){let et=defer();return this.options.ns?(isString(B)&&(B=[B]),B.forEach(B=>{0>this.options.ns.indexOf(B)&&this.options.ns.push(B)}),this.loadResources(B=>{et.resolve(),Z&&Z(B)}),et):(Z&&Z(),Promise.resolve())}loadLanguages(B,Z){let et=defer();isString(B)&&(B=[B]);let er=this.options.preload||[],en=B.filter(B=>0>er.indexOf(B)&&this.services.languageUtils.isSupportedCode(B));return en.length?(this.options.preload=er.concat(en),this.loadResources(B=>{et.resolve(),Z&&Z(B)}),et):(Z&&Z(),Promise.resolve())}dir(B){if(B||(B=this.resolvedLanguage||(this.languages?.length>0?this.languages[0]:this.language)),!B)return"rtl";let Z=this.services?.languageUtils||new LanguageUtil(get());return["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"].indexOf(Z.getLanguagePartFromCode(B))>-1||B.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(B={},Z){return new I18n(B,Z)}cloneInstance(B={},Z=noop){let et=B.forkResourceStore;et&&delete B.forkResourceStore;let er={...this.options,...B,isClone:!0},en=new I18n(er);if((void 0!==B.debug||void 0!==B.prefix)&&(en.logger=en.logger.clone(B)),["store","services","language"].forEach(B=>{en[B]=this[B]}),en.services={...this.services},en.services.utils={hasLoadedNamespace:en.hasLoadedNamespace.bind(en)},et){let B=Object.keys(this.store.data).reduce((B,Z)=>(B[Z]={...this.store.data[Z]},B[Z]=Object.keys(B[Z]).reduce((et,er)=>(et[er]={...B[Z][er]},et),B[Z]),B),{});en.store=new ResourceStore(B,er),en.services.resourceStore=en.store}return en.translator=new Translator(en.services,er),en.translator.on("*",(B,...Z)=>{en.emit(B,...Z)}),en.init(er,Z),en.translator.options=er,en.translator.backendConnector.services.utils={hasLoadedNamespace:en.hasLoadedNamespace.bind(en)},en}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}};let ef=I18n.createInstance();ef.createInstance=I18n.createInstance,ef.createInstance,ef.dir,ef.init,ef.loadResources,ef.reloadResources,ef.use,ef.changeLanguage,ef.getFixedT,ef.t,ef.exists,ef.setDefaultNamespace,ef.hasLoadedNamespace,ef.loadNamespaces,ef.loadLanguages},95196:(B,Z,et)=>{"use strict";function _interop_require_default(B){return B&&B.__esModule?B:{default:B}}et.r(Z),et.d(Z,{_:()=>_interop_require_default,_interop_require_default:()=>_interop_require_default})},73685:(B,Z,et)=>{"use strict";et.d(Z,{Z:()=>resourcesToBackend});var resourcesToBackend=function(B){return{type:"backend",init:function(B,Z,et){},read:function(Z,et,er){if("function"==typeof B){if(B.length<3){try{var en=B(Z,et);en&&"function"==typeof en.then?en.then(function(B){return er(null,B&&B.default||B)}).catch(er):er(null,en)}catch(B){er(B)}return}B(Z,et,er);return}er(null,B&&B[Z]&&B[Z][et])}}}},41234:(B,Z,et)=>{"use strict";et.d(Z,{Fs:()=>ep});let isString=B=>"string"==typeof B,defer=()=>{let B,Z;let et=new Promise((et,er)=>{B=et,Z=er});return et.resolve=B,et.reject=Z,et},makeString=B=>null==B?"":""+B,copy=(B,Z,et)=>{B.forEach(B=>{Z[B]&&(et[B]=Z[B])})},er=/###/g,cleanKey=B=>B&&B.indexOf("###")>-1?B.replace(er,"."):B,canNotTraverseDeeper=B=>!B||isString(B),getLastOfPath=(B,Z,et)=>{let er=isString(Z)?Z.split("."):Z,en=0;for(;en<er.length-1;){if(canNotTraverseDeeper(B))return{};let Z=cleanKey(er[en]);!B[Z]&&et&&(B[Z]=new et),B=Object.prototype.hasOwnProperty.call(B,Z)?B[Z]:{},++en}return canNotTraverseDeeper(B)?{}:{obj:B,k:cleanKey(er[en])}},setPath=(B,Z,et)=>{let{obj:er,k:en}=getLastOfPath(B,Z,Object);if(void 0!==er||1===Z.length){er[en]=et;return}let eo=Z[Z.length-1],ea=Z.slice(0,Z.length-1),ei=getLastOfPath(B,ea,Object);for(;void 0===ei.obj&&ea.length;)eo=`${ea[ea.length-1]}.${eo}`,ei=getLastOfPath(B,ea=ea.slice(0,ea.length-1),Object),ei?.obj&&void 0!==ei.obj[`${ei.k}.${eo}`]&&(ei.obj=void 0);ei.obj[`${ei.k}.${eo}`]=et},pushPath=(B,Z,et,er)=>{let{obj:en,k:eo}=getLastOfPath(B,Z,Object);en[eo]=en[eo]||[],en[eo].push(et)},getPath=(B,Z)=>{let{obj:et,k:er}=getLastOfPath(B,Z);if(et&&Object.prototype.hasOwnProperty.call(et,er))return et[er]},getPathWithDefaults=(B,Z,et)=>{let er=getPath(B,et);return void 0!==er?er:getPath(Z,et)},deepExtend=(B,Z,et)=>{for(let er in Z)"__proto__"!==er&&"constructor"!==er&&(er in B?isString(B[er])||B[er]instanceof String||isString(Z[er])||Z[er]instanceof String?et&&(B[er]=Z[er]):deepExtend(B[er],Z[er],et):B[er]=Z[er]);return B},regexEscape=B=>B.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var en={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};let escape=B=>isString(B)?B.replace(/[&<>"'\/]/g,B=>en[B]):B;let RegExpCache=class RegExpCache{constructor(B){this.capacity=B,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(B){let Z=this.regExpMap.get(B);if(void 0!==Z)return Z;let et=new RegExp(B);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(B,et),this.regExpQueue.push(B),et}};let eo=[" ",",","?","!",";"],ea=new RegExpCache(20),looksLikeObjectPath=(B,Z,et)=>{Z=Z||"",et=et||"";let er=eo.filter(B=>0>Z.indexOf(B)&&0>et.indexOf(B));if(0===er.length)return!0;let en=ea.getRegExp(`(${er.map(B=>"?"===B?"\\?":B).join("|")})`),ei=!en.test(B);if(!ei){let Z=B.indexOf(et);Z>0&&!en.test(B.substring(0,Z))&&(ei=!0)}return ei},deepFind=(B,Z,et=".")=>{if(!B)return;if(B[Z]){if(!Object.prototype.hasOwnProperty.call(B,Z))return;return B[Z]}let er=Z.split(et),en=B;for(let B=0;B<er.length;){let Z;if(!en||"object"!=typeof en)return;let eo="";for(let ea=B;ea<er.length;++ea)if(ea!==B&&(eo+=et),eo+=er[ea],void 0!==(Z=en[eo])){if(["string","number","boolean"].indexOf(typeof Z)>-1&&ea<er.length-1)continue;B+=ea-B+1;break}en=Z}return en},getCleanedCode=B=>B?.replace("_","-"),ei={type:"logger",log(B){this.output("log",B)},warn(B){this.output("warn",B)},error(B){this.output("error",B)},output(B,Z){console?.[B]?.apply?.(console,Z)}};let Logger=class Logger{constructor(B,Z={}){this.init(B,Z)}init(B,Z={}){this.prefix=Z.prefix||"i18next:",this.logger=B||ei,this.options=Z,this.debug=Z.debug}log(...B){return this.forward(B,"log","",!0)}warn(...B){return this.forward(B,"warn","",!0)}error(...B){return this.forward(B,"error","")}deprecate(...B){return this.forward(B,"warn","WARNING DEPRECATED: ",!0)}forward(B,Z,et,er){return er&&!this.debug?null:(isString(B[0])&&(B[0]=`${et}${this.prefix} ${B[0]}`),this.logger[Z](B))}create(B){return new Logger(this.logger,{prefix:`${this.prefix}:${B}:`,...this.options})}clone(B){return(B=B||this.options).prefix=B.prefix||this.prefix,new Logger(this.logger,B)}};var es=new Logger;let EventEmitter=class EventEmitter{constructor(){this.observers={}}on(B,Z){return B.split(" ").forEach(B=>{this.observers[B]||(this.observers[B]=new Map);let et=this.observers[B].get(Z)||0;this.observers[B].set(Z,et+1)}),this}off(B,Z){if(this.observers[B]){if(!Z){delete this.observers[B];return}this.observers[B].delete(Z)}}emit(B,...Z){if(this.observers[B]){let et=Array.from(this.observers[B].entries());et.forEach(([B,et])=>{for(let er=0;er<et;er++)B(...Z)})}if(this.observers["*"]){let et=Array.from(this.observers["*"].entries());et.forEach(([et,er])=>{for(let en=0;en<er;en++)et.apply(et,[B,...Z])})}}};let ResourceStore=class ResourceStore extends EventEmitter{constructor(B,Z={ns:["translation"],defaultNS:"translation"}){super(),this.data=B||{},this.options=Z,void 0===this.options.keySeparator&&(this.options.keySeparator="."),void 0===this.options.ignoreJSONStructure&&(this.options.ignoreJSONStructure=!0)}addNamespaces(B){0>this.options.ns.indexOf(B)&&this.options.ns.push(B)}removeNamespaces(B){let Z=this.options.ns.indexOf(B);Z>-1&&this.options.ns.splice(Z,1)}getResource(B,Z,et,er={}){let en;let eo=void 0!==er.keySeparator?er.keySeparator:this.options.keySeparator,ea=void 0!==er.ignoreJSONStructure?er.ignoreJSONStructure:this.options.ignoreJSONStructure;B.indexOf(".")>-1?en=B.split("."):(en=[B,Z],et&&(Array.isArray(et)?en.push(...et):isString(et)&&eo?en.push(...et.split(eo)):en.push(et)));let ei=getPath(this.data,en);return(!ei&&!Z&&!et&&B.indexOf(".")>-1&&(B=en[0],Z=en[1],et=en.slice(2).join(".")),!ei&&ea&&isString(et))?deepFind(this.data?.[B]?.[Z],et,eo):ei}addResource(B,Z,et,er,en={silent:!1}){let eo=void 0!==en.keySeparator?en.keySeparator:this.options.keySeparator,ea=[B,Z];et&&(ea=ea.concat(eo?et.split(eo):et)),B.indexOf(".")>-1&&(ea=B.split("."),er=Z,Z=ea[1]),this.addNamespaces(Z),setPath(this.data,ea,er),en.silent||this.emit("added",B,Z,et,er)}addResources(B,Z,et,er={silent:!1}){for(let er in et)(isString(et[er])||Array.isArray(et[er]))&&this.addResource(B,Z,er,et[er],{silent:!0});er.silent||this.emit("added",B,Z,et)}addResourceBundle(B,Z,et,er,en,eo={silent:!1,skipCopy:!1}){let ea=[B,Z];B.indexOf(".")>-1&&(ea=B.split("."),er=et,et=Z,Z=ea[1]),this.addNamespaces(Z);let ei=getPath(this.data,ea)||{};eo.skipCopy||(et=JSON.parse(JSON.stringify(et))),er?deepExtend(ei,et,en):ei={...ei,...et},setPath(this.data,ea,ei),eo.silent||this.emit("added",B,Z,et)}removeResourceBundle(B,Z){this.hasResourceBundle(B,Z)&&delete this.data[B][Z],this.removeNamespaces(Z),this.emit("removed",B,Z)}hasResourceBundle(B,Z){return void 0!==this.getResource(B,Z)}getResourceBundle(B,Z){return Z||(Z=this.options.defaultNS),this.getResource(B,Z)}getDataByLanguage(B){return this.data[B]}hasLanguageSomeTranslations(B){let Z=this.getDataByLanguage(B),et=Z&&Object.keys(Z)||[];return!!et.find(B=>Z[B]&&Object.keys(Z[B]).length>0)}toJSON(){return this.data}};var eu={processors:{},addPostProcessor(B){this.processors[B.name]=B},handle(B,Z,et,er,en){return B.forEach(B=>{Z=this.processors[B]?.process(Z,et,er,en)??Z}),Z}};let el={},shouldHandleAsObject=B=>!isString(B)&&"boolean"!=typeof B&&"number"!=typeof B;let Translator=class Translator extends EventEmitter{constructor(B,Z={}){super(),copy(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],B,this),this.options=Z,void 0===this.options.keySeparator&&(this.options.keySeparator="."),this.logger=es.create("translator")}changeLanguage(B){B&&(this.language=B)}exists(B,Z={interpolation:{}}){let et={...Z};if(null==B)return!1;let er=this.resolve(B,et);return er?.res!==void 0}extractFromKey(B,Z){let et=void 0!==Z.nsSeparator?Z.nsSeparator:this.options.nsSeparator;void 0===et&&(et=":");let er=void 0!==Z.keySeparator?Z.keySeparator:this.options.keySeparator,en=Z.ns||this.options.defaultNS||[],eo=et&&B.indexOf(et)>-1,ea=!this.options.userDefinedKeySeparator&&!Z.keySeparator&&!this.options.userDefinedNsSeparator&&!Z.nsSeparator&&!looksLikeObjectPath(B,et,er);if(eo&&!ea){let Z=B.match(this.interpolator.nestingRegexp);if(Z&&Z.length>0)return{key:B,namespaces:isString(en)?[en]:en};let eo=B.split(et);(et!==er||et===er&&this.options.ns.indexOf(eo[0])>-1)&&(en=eo.shift()),B=eo.join(er)}return{key:B,namespaces:isString(en)?[en]:en}}translate(B,Z,et){let er="object"==typeof Z?{...Z}:Z;if("object"!=typeof er&&this.options.overloadTranslationOptionHandler&&(er=this.options.overloadTranslationOptionHandler(arguments)),"object"==typeof options&&(er={...er}),er||(er={}),null==B)return"";Array.isArray(B)||(B=[String(B)]);let en=void 0!==er.returnDetails?er.returnDetails:this.options.returnDetails,eo=void 0!==er.keySeparator?er.keySeparator:this.options.keySeparator,{key:ea,namespaces:ei}=this.extractFromKey(B[B.length-1],er),es=ei[ei.length-1],eu=void 0!==er.nsSeparator?er.nsSeparator:this.options.nsSeparator;void 0===eu&&(eu=":");let el=er.lng||this.language,ec=er.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if(el?.toLowerCase()==="cimode")return ec?en?{res:`${es}${eu}${ea}`,usedKey:ea,exactUsedKey:ea,usedLng:el,usedNS:es,usedParams:this.getUsedParamsDetails(er)}:`${es}${eu}${ea}`:en?{res:ea,usedKey:ea,exactUsedKey:ea,usedLng:el,usedNS:es,usedParams:this.getUsedParamsDetails(er)}:ea;let ed=this.resolve(B,er),ef=ed?.res,ep=ed?.usedKey||ea,eh=ed?.exactUsedKey||ea,eg=void 0!==er.joinArrays?er.joinArrays:this.options.joinArrays,ey=!this.i18nFormat||this.i18nFormat.handleAsObject,em=void 0!==er.count&&!isString(er.count),ev=Translator.hasDefaultValue(er),eb=em?this.pluralResolver.getSuffix(el,er.count,er):"",eS=er.ordinal&&em?this.pluralResolver.getSuffix(el,er.count,{ordinal:!1}):"",eP=em&&!er.ordinal&&0===er.count,eO=eP&&er[`defaultValue${this.options.pluralSeparator}zero`]||er[`defaultValue${eb}`]||er[`defaultValue${eS}`]||er.defaultValue,e_=ef;ey&&!ef&&ev&&(e_=eO);let ex=shouldHandleAsObject(e_),eR=Object.prototype.toString.apply(e_);if(ey&&e_&&ex&&0>["[object Number]","[object Function]","[object RegExp]"].indexOf(eR)&&!(isString(eg)&&Array.isArray(e_))){if(!er.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");let B=this.options.returnedObjectHandler?this.options.returnedObjectHandler(ep,e_,{...er,ns:ei}):`key '${ea} (${this.language})' returned an object instead of string.`;return en?(ed.res=B,ed.usedParams=this.getUsedParamsDetails(er),ed):B}if(eo){let B=Array.isArray(e_),Z=B?[]:{},et=B?eh:ep;for(let B in e_)if(Object.prototype.hasOwnProperty.call(e_,B)){let en=`${et}${eo}${B}`;ev&&!ef?Z[B]=this.translate(en,{...er,defaultValue:shouldHandleAsObject(eO)?eO[B]:void 0,joinArrays:!1,ns:ei}):Z[B]=this.translate(en,{...er,joinArrays:!1,ns:ei}),Z[B]===en&&(Z[B]=e_[B])}ef=Z}}else if(ey&&isString(eg)&&Array.isArray(ef))(ef=ef.join(eg))&&(ef=this.extendTranslation(ef,B,er,et));else{let Z=!1,en=!1;!this.isValidLookup(ef)&&ev&&(Z=!0,ef=eO),this.isValidLookup(ef)||(en=!0,ef=ea);let ei=er.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey,ec=ei&&en?void 0:ef,ep=ev&&eO!==ef&&this.options.updateMissing;if(en||Z||ep){if(this.logger.log(ep?"updateKey":"missingKey",el,es,ea,ep?eO:ef),eo){let B=this.resolve(ea,{...er,keySeparator:!1});B&&B.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let B=[],Z=this.languageUtils.getFallbackCodes(this.options.fallbackLng,er.lng||this.language);if("fallback"===this.options.saveMissingTo&&Z&&Z[0])for(let et=0;et<Z.length;et++)B.push(Z[et]);else"all"===this.options.saveMissingTo?B=this.languageUtils.toResolveHierarchy(er.lng||this.language):B.push(er.lng||this.language);let send=(B,Z,et)=>{let en=ev&&et!==ef?et:ec;this.options.missingKeyHandler?this.options.missingKeyHandler(B,es,Z,en,ep,er):this.backendConnector?.saveMissing&&this.backendConnector.saveMissing(B,es,Z,en,ep,er),this.emit("missingKey",B,es,Z,ef)};this.options.saveMissing&&(this.options.saveMissingPlurals&&em?B.forEach(B=>{let Z=this.pluralResolver.getSuffixes(B,er);eP&&er[`defaultValue${this.options.pluralSeparator}zero`]&&0>Z.indexOf(`${this.options.pluralSeparator}zero`)&&Z.push(`${this.options.pluralSeparator}zero`),Z.forEach(Z=>{send([B],ea+Z,er[`defaultValue${Z}`]||eO)})}):send(B,ea,eO))}ef=this.extendTranslation(ef,B,er,ed,et),en&&ef===ea&&this.options.appendNamespaceToMissingKey&&(ef=`${es}${eu}${ea}`),(en||Z)&&this.options.parseMissingKeyHandler&&(ef=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${es}${eu}${ea}`:ea,Z?ef:void 0,er))}return en?(ed.res=ef,ed.usedParams=this.getUsedParamsDetails(er),ed):ef}extendTranslation(B,Z,et,er,en){if(this.i18nFormat?.parse)B=this.i18nFormat.parse(B,{...this.options.interpolation.defaultVariables,...et},et.lng||this.language||er.usedLng,er.usedNS,er.usedKey,{resolved:er});else if(!et.skipInterpolation){let eo;et.interpolation&&this.interpolator.init({...et,interpolation:{...this.options.interpolation,...et.interpolation}});let ea=isString(B)&&(et?.interpolation?.skipOnVariables!==void 0?et.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);if(ea){let Z=B.match(this.interpolator.nestingRegexp);eo=Z&&Z.length}let ei=et.replace&&!isString(et.replace)?et.replace:et;if(this.options.interpolation.defaultVariables&&(ei={...this.options.interpolation.defaultVariables,...ei}),B=this.interpolator.interpolate(B,ei,et.lng||this.language||er.usedLng,et),ea){let Z=B.match(this.interpolator.nestingRegexp),er=Z&&Z.length;eo<er&&(et.nest=!1)}!et.lng&&er&&er.res&&(et.lng=this.language||er.usedLng),!1!==et.nest&&(B=this.interpolator.nest(B,(...B)=>en?.[0]!==B[0]||et.context?this.translate(...B,Z):(this.logger.warn(`It seems you are nesting recursively key: ${B[0]} in key: ${Z[0]}`),null),et)),et.interpolation&&this.interpolator.reset()}let eo=et.postProcess||this.options.postProcess,ea=isString(eo)?[eo]:eo;return null!=B&&ea?.length&&!1!==et.applyPostProcessor&&(B=eu.handle(ea,B,Z,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...er,usedParams:this.getUsedParamsDetails(et)},...et}:et,this)),B}resolve(B,Z={}){let et,er,en,eo,ea;return isString(B)&&(B=[B]),B.forEach(B=>{if(this.isValidLookup(et))return;let ei=this.extractFromKey(B,Z),es=ei.key;er=es;let eu=ei.namespaces;this.options.fallbackNS&&(eu=eu.concat(this.options.fallbackNS));let ec=void 0!==Z.count&&!isString(Z.count),ed=ec&&!Z.ordinal&&0===Z.count,ef=void 0!==Z.context&&(isString(Z.context)||"number"==typeof Z.context)&&""!==Z.context,ep=Z.lngs?Z.lngs:this.languageUtils.toResolveHierarchy(Z.lng||this.language,Z.fallbackLng);eu.forEach(B=>{this.isValidLookup(et)||(ea=B,!el[`${ep[0]}-${B}`]&&this.utils?.hasLoadedNamespace&&!this.utils?.hasLoadedNamespace(ea)&&(el[`${ep[0]}-${B}`]=!0,this.logger.warn(`key "${er}" for languages "${ep.join(", ")}" won't get resolved as namespace "${ea}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),ep.forEach(er=>{let ea;if(this.isValidLookup(et))return;eo=er;let ei=[es];if(this.i18nFormat?.addLookupKeys)this.i18nFormat.addLookupKeys(ei,es,er,B,Z);else{let B;ec&&(B=this.pluralResolver.getSuffix(er,Z.count,Z));let et=`${this.options.pluralSeparator}zero`,en=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(ec&&(ei.push(es+B),Z.ordinal&&0===B.indexOf(en)&&ei.push(es+B.replace(en,this.options.pluralSeparator)),ed&&ei.push(es+et)),ef){let er=`${es}${this.options.contextSeparator}${Z.context}`;ei.push(er),ec&&(ei.push(er+B),Z.ordinal&&0===B.indexOf(en)&&ei.push(er+B.replace(en,this.options.pluralSeparator)),ed&&ei.push(er+et))}}for(;ea=ei.pop();)this.isValidLookup(et)||(en=ea,et=this.getResource(er,B,ea,Z))}))})}),{res:et,usedKey:er,exactUsedKey:en,usedLng:eo,usedNS:ea}}isValidLookup(B){return void 0!==B&&!(!this.options.returnNull&&null===B)&&!(!this.options.returnEmptyString&&""===B)}getResource(B,Z,et,er={}){return this.i18nFormat?.getResource?this.i18nFormat.getResource(B,Z,et,er):this.resourceStore.getResource(B,Z,et,er)}getUsedParamsDetails(B={}){let Z=B.replace&&!isString(B.replace),et=Z?B.replace:B;if(Z&&void 0!==B.count&&(et.count=B.count),this.options.interpolation.defaultVariables&&(et={...this.options.interpolation.defaultVariables,...et}),!Z)for(let B of(et={...et},["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"]))delete et[B];return et}static hasDefaultValue(B){let Z="defaultValue";for(let et in B)if(Object.prototype.hasOwnProperty.call(B,et)&&Z===et.substring(0,Z.length)&&void 0!==B[et])return!0;return!1}};let LanguageUtil=class LanguageUtil{constructor(B){this.options=B,this.supportedLngs=this.options.supportedLngs||!1,this.logger=es.create("languageUtils")}getScriptPartFromCode(B){if(!(B=getCleanedCode(B))||0>B.indexOf("-"))return null;let Z=B.split("-");return 2===Z.length?null:(Z.pop(),"x"===Z[Z.length-1].toLowerCase())?null:this.formatLanguageCode(Z.join("-"))}getLanguagePartFromCode(B){if(!(B=getCleanedCode(B))||0>B.indexOf("-"))return B;let Z=B.split("-");return this.formatLanguageCode(Z[0])}formatLanguageCode(B){if(isString(B)&&B.indexOf("-")>-1){let Z;try{Z=Intl.getCanonicalLocales(B)[0]}catch(B){}return(Z&&this.options.lowerCaseLng&&(Z=Z.toLowerCase()),Z)?Z:this.options.lowerCaseLng?B.toLowerCase():B}return this.options.cleanCode||this.options.lowerCaseLng?B.toLowerCase():B}isSupportedCode(B){return("languageOnly"===this.options.load||this.options.nonExplicitSupportedLngs)&&(B=this.getLanguagePartFromCode(B)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(B)>-1}getBestMatchFromCodes(B){let Z;return B?(B.forEach(B=>{if(Z)return;let et=this.formatLanguageCode(B);(!this.options.supportedLngs||this.isSupportedCode(et))&&(Z=et)}),!Z&&this.options.supportedLngs&&B.forEach(B=>{if(Z)return;let et=this.getScriptPartFromCode(B);if(this.isSupportedCode(et))return Z=et;let er=this.getLanguagePartFromCode(B);if(this.isSupportedCode(er))return Z=er;Z=this.options.supportedLngs.find(B=>{if(B===er||!(0>B.indexOf("-")&&0>er.indexOf("-"))&&(B.indexOf("-")>0&&0>er.indexOf("-")&&B.substring(0,B.indexOf("-"))===er||0===B.indexOf(er)&&er.length>1))return B})}),Z||(Z=this.getFallbackCodes(this.options.fallbackLng)[0]),Z):null}getFallbackCodes(B,Z){if(!B)return[];if("function"==typeof B&&(B=B(Z)),isString(B)&&(B=[B]),Array.isArray(B))return B;if(!Z)return B.default||[];let et=B[Z];return et||(et=B[this.getScriptPartFromCode(Z)]),et||(et=B[this.formatLanguageCode(Z)]),et||(et=B[this.getLanguagePartFromCode(Z)]),et||(et=B.default),et||[]}toResolveHierarchy(B,Z){let et=this.getFallbackCodes(Z||this.options.fallbackLng||[],B),er=[],addCode=B=>{B&&(this.isSupportedCode(B)?er.push(B):this.logger.warn(`rejecting language code not found in supportedLngs: ${B}`))};return isString(B)&&(B.indexOf("-")>-1||B.indexOf("_")>-1)?("languageOnly"!==this.options.load&&addCode(this.formatLanguageCode(B)),"languageOnly"!==this.options.load&&"currentOnly"!==this.options.load&&addCode(this.getScriptPartFromCode(B)),"currentOnly"!==this.options.load&&addCode(this.getLanguagePartFromCode(B))):isString(B)&&addCode(this.formatLanguageCode(B)),et.forEach(B=>{0>er.indexOf(B)&&addCode(this.formatLanguageCode(B))}),er}};let ec={zero:0,one:1,two:2,few:3,many:4,other:5},ed={select:B=>1===B?"one":"other",resolvedOptions:()=>({pluralCategories:["one","other"]})};let PluralResolver=class PluralResolver{constructor(B,Z={}){this.languageUtils=B,this.options=Z,this.logger=es.create("pluralResolver"),this.pluralRulesCache={}}addRule(B,Z){this.rules[B]=Z}clearCache(){this.pluralRulesCache={}}getRule(B,Z={}){let et;let er=getCleanedCode("dev"===B?"en":B),en=Z.ordinal?"ordinal":"cardinal",eo=JSON.stringify({cleanedCode:er,type:en});if(eo in this.pluralRulesCache)return this.pluralRulesCache[eo];try{et=new Intl.PluralRules(er,{type:en})}catch(en){if(!Intl)return this.logger.error("No Intl support, please use an Intl polyfill!"),ed;if(!B.match(/-|_/))return ed;let er=this.languageUtils.getLanguagePartFromCode(B);et=this.getRule(er,Z)}return this.pluralRulesCache[eo]=et,et}needsPlural(B,Z={}){let et=this.getRule(B,Z);return et||(et=this.getRule("dev",Z)),et?.resolvedOptions().pluralCategories.length>1}getPluralFormsOfKey(B,Z,et={}){return this.getSuffixes(B,et).map(B=>`${Z}${B}`)}getSuffixes(B,Z={}){let et=this.getRule(B,Z);return(et||(et=this.getRule("dev",Z)),et)?et.resolvedOptions().pluralCategories.sort((B,Z)=>ec[B]-ec[Z]).map(B=>`${this.options.prepend}${Z.ordinal?`ordinal${this.options.prepend}`:""}${B}`):[]}getSuffix(B,Z,et={}){let er=this.getRule(B,et);return er?`${this.options.prepend}${et.ordinal?`ordinal${this.options.prepend}`:""}${er.select(Z)}`:(this.logger.warn(`no plural rule found for: ${B}`),this.getSuffix("dev",Z,et))}};let deepFindWithDefaults=(B,Z,et,er=".",en=!0)=>{let eo=getPathWithDefaults(B,Z,et);return!eo&&en&&isString(et)&&void 0===(eo=deepFind(B,et,er))&&(eo=deepFind(Z,et,er)),eo},regexSafe=B=>B.replace(/\$/g,"$$$$");let Interpolator=class Interpolator{constructor(B={}){this.logger=es.create("interpolator"),this.options=B,this.format=B?.interpolation?.format||(B=>B),this.init(B)}init(B={}){B.interpolation||(B.interpolation={escapeValue:!0});let{escape:Z,escapeValue:et,useRawValueToEscape:er,prefix:en,prefixEscaped:eo,suffix:ea,suffixEscaped:ei,formatSeparator:es,unescapeSuffix:eu,unescapePrefix:el,nestingPrefix:ec,nestingPrefixEscaped:ed,nestingSuffix:ef,nestingSuffixEscaped:ep,nestingOptionsSeparator:eh,maxReplaces:eg,alwaysFormat:ey}=B.interpolation;this.escape=void 0!==Z?Z:escape,this.escapeValue=void 0===et||et,this.useRawValueToEscape=void 0!==er&&er,this.prefix=en?regexEscape(en):eo||"{{",this.suffix=ea?regexEscape(ea):ei||"}}",this.formatSeparator=es||",",this.unescapePrefix=eu?"":el||"-",this.unescapeSuffix=this.unescapePrefix?"":eu||"",this.nestingPrefix=ec?regexEscape(ec):ed||regexEscape("$t("),this.nestingSuffix=ef?regexEscape(ef):ep||regexEscape(")"),this.nestingOptionsSeparator=eh||",",this.maxReplaces=eg||1e3,this.alwaysFormat=void 0!==ey&&ey,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){let getOrResetRegExp=(B,Z)=>B?.source===Z?(B.lastIndex=0,B):RegExp(Z,"g");this.regexp=getOrResetRegExp(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=getOrResetRegExp(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=getOrResetRegExp(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(B,Z,et,er){let en,eo,ea;let ei=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},handleFormat=B=>{if(0>B.indexOf(this.formatSeparator)){let en=deepFindWithDefaults(Z,ei,B,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(en,void 0,et,{...er,...Z,interpolationkey:B}):en}let en=B.split(this.formatSeparator),eo=en.shift().trim(),ea=en.join(this.formatSeparator).trim();return this.format(deepFindWithDefaults(Z,ei,eo,this.options.keySeparator,this.options.ignoreJSONStructure),ea,et,{...er,...Z,interpolationkey:eo})};this.resetRegExp();let es=er?.missingInterpolationHandler||this.options.missingInterpolationHandler,eu=er?.interpolation?.skipOnVariables!==void 0?er.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables,el=[{regex:this.regexpUnescape,safeValue:B=>regexSafe(B)},{regex:this.regexp,safeValue:B=>this.escapeValue?regexSafe(this.escape(B)):regexSafe(B)}];return el.forEach(Z=>{for(ea=0;en=Z.regex.exec(B);){let et=en[1].trim();if(void 0===(eo=handleFormat(et))){if("function"==typeof es){let Z=es(B,en,er);eo=isString(Z)?Z:""}else if(er&&Object.prototype.hasOwnProperty.call(er,et))eo="";else if(eu){eo=en[0];continue}else this.logger.warn(`missed to pass in variable ${et} for interpolating ${B}`),eo=""}else isString(eo)||this.useRawValueToEscape||(eo=makeString(eo));let ei=Z.safeValue(eo);if(B=B.replace(en[0],ei),eu?(Z.regex.lastIndex+=eo.length,Z.regex.lastIndex-=en[0].length):Z.regex.lastIndex=0,++ea>=this.maxReplaces)break}}),B}nest(B,Z,et={}){let er,en,eo;let handleHasOptions=(B,Z)=>{let et=this.nestingOptionsSeparator;if(0>B.indexOf(et))return B;let er=B.split(RegExp(`${et}[ ]*{`)),en=`{${er[1]}`;B=er[0],en=this.interpolate(en,eo);let ea=en.match(/'/g),ei=en.match(/"/g);((ea?.length??0)%2!=0||ei)&&ei.length%2==0||(en=en.replace(/'/g,'"'));try{eo=JSON.parse(en),Z&&(eo={...Z,...eo})}catch(Z){return this.logger.warn(`failed parsing options string in nesting for key ${B}`,Z),`${B}${et}${en}`}return eo.defaultValue&&eo.defaultValue.indexOf(this.prefix)>-1&&delete eo.defaultValue,B};for(;er=this.nestingRegexp.exec(B);){let ea=[];(eo=(eo={...et}).replace&&!isString(eo.replace)?eo.replace:eo).applyPostProcessor=!1,delete eo.defaultValue;let ei=!1;if(-1!==er[0].indexOf(this.formatSeparator)&&!/{.*}/.test(er[1])){let B=er[1].split(this.formatSeparator).map(B=>B.trim());er[1]=B.shift(),ea=B,ei=!0}if((en=Z(handleHasOptions.call(this,er[1].trim(),eo),eo))&&er[0]===B&&!isString(en))return en;isString(en)||(en=makeString(en)),en||(this.logger.warn(`missed to resolve ${er[1]} for nesting ${B}`),en=""),ei&&(en=ea.reduce((B,Z)=>this.format(B,Z,et.lng,{...et,interpolationkey:er[1].trim()}),en.trim())),B=B.replace(er[0],en),this.regexp.lastIndex=0}return B}};let parseFormatStr=B=>{let Z=B.toLowerCase().trim(),et={};if(B.indexOf("(")>-1){let er=B.split("(");Z=er[0].toLowerCase().trim();let en=er[1].substring(0,er[1].length-1);if("currency"===Z&&0>en.indexOf(":"))et.currency||(et.currency=en.trim());else if("relativetime"===Z&&0>en.indexOf(":"))et.range||(et.range=en.trim());else{let B=en.split(";");B.forEach(B=>{if(B){let[Z,...er]=B.split(":"),en=er.join(":").trim().replace(/^'+|'+$/g,""),eo=Z.trim();et[eo]||(et[eo]=en),"false"===en&&(et[eo]=!1),"true"===en&&(et[eo]=!0),isNaN(en)||(et[eo]=parseInt(en,10))}})}}return{formatName:Z,formatOptions:et}},createCachedFormatter=B=>{let Z={};return(et,er,en)=>{let eo=en;en&&en.interpolationkey&&en.formatParams&&en.formatParams[en.interpolationkey]&&en[en.interpolationkey]&&(eo={...eo,[en.interpolationkey]:void 0});let ea=er+JSON.stringify(eo),ei=Z[ea];return ei||(ei=B(getCleanedCode(er),en),Z[ea]=ei),ei(et)}};let Formatter=class Formatter{constructor(B={}){this.logger=es.create("formatter"),this.options=B,this.formats={number:createCachedFormatter((B,Z)=>{let et=new Intl.NumberFormat(B,{...Z});return B=>et.format(B)}),currency:createCachedFormatter((B,Z)=>{let et=new Intl.NumberFormat(B,{...Z,style:"currency"});return B=>et.format(B)}),datetime:createCachedFormatter((B,Z)=>{let et=new Intl.DateTimeFormat(B,{...Z});return B=>et.format(B)}),relativetime:createCachedFormatter((B,Z)=>{let et=new Intl.RelativeTimeFormat(B,{...Z});return B=>et.format(B,Z.range||"day")}),list:createCachedFormatter((B,Z)=>{let et=new Intl.ListFormat(B,{...Z});return B=>et.format(B)})},this.init(B)}init(B,Z={interpolation:{}}){this.formatSeparator=Z.interpolation.formatSeparator||","}add(B,Z){this.formats[B.toLowerCase().trim()]=Z}addCached(B,Z){this.formats[B.toLowerCase().trim()]=createCachedFormatter(Z)}format(B,Z,et,er={}){let en=Z.split(this.formatSeparator);if(en.length>1&&en[0].indexOf("(")>1&&0>en[0].indexOf(")")&&en.find(B=>B.indexOf(")")>-1)){let B=en.findIndex(B=>B.indexOf(")")>-1);en[0]=[en[0],...en.splice(1,B)].join(this.formatSeparator)}let eo=en.reduce((B,Z)=>{let{formatName:en,formatOptions:eo}=parseFormatStr(Z);if(this.formats[en]){let Z=B;try{let ea=er?.formatParams?.[er.interpolationkey]||{},ei=ea.locale||ea.lng||er.locale||er.lng||et;Z=this.formats[en](B,ei,{...eo,...er,...ea})}catch(B){this.logger.warn(B)}return Z}return this.logger.warn(`there was no format function for ${en}`),B},B);return eo}};let removePending=(B,Z)=>{void 0!==B.pending[Z]&&(delete B.pending[Z],B.pendingCount--)};let Connector=class Connector extends EventEmitter{constructor(B,Z,et,er={}){super(),this.backend=B,this.store=Z,this.services=et,this.languageUtils=et.languageUtils,this.options=er,this.logger=es.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=er.maxParallelReads||10,this.readingCalls=0,this.maxRetries=er.maxRetries>=0?er.maxRetries:5,this.retryTimeout=er.retryTimeout>=1?er.retryTimeout:350,this.state={},this.queue=[],this.backend?.init?.(et,er.backend,er)}queueLoad(B,Z,et,er){let en={},eo={},ea={},ei={};return B.forEach(B=>{let er=!0;Z.forEach(Z=>{let ea=`${B}|${Z}`;!et.reload&&this.store.hasResourceBundle(B,Z)?this.state[ea]=2:this.state[ea]<0||(1===this.state[ea]?void 0===eo[ea]&&(eo[ea]=!0):(this.state[ea]=1,er=!1,void 0===eo[ea]&&(eo[ea]=!0),void 0===en[ea]&&(en[ea]=!0),void 0===ei[Z]&&(ei[Z]=!0)))}),er||(ea[B]=!0)}),(Object.keys(en).length||Object.keys(eo).length)&&this.queue.push({pending:eo,pendingCount:Object.keys(eo).length,loaded:{},errors:[],callback:er}),{toLoad:Object.keys(en),pending:Object.keys(eo),toLoadLanguages:Object.keys(ea),toLoadNamespaces:Object.keys(ei)}}loaded(B,Z,et){let er=B.split("|"),en=er[0],eo=er[1];Z&&this.emit("failedLoading",en,eo,Z),!Z&&et&&this.store.addResourceBundle(en,eo,et,void 0,void 0,{skipCopy:!0}),this.state[B]=Z?-1:2,Z&&et&&(this.state[B]=0);let ea={};this.queue.forEach(et=>{pushPath(et.loaded,[en],eo),removePending(et,B),Z&&et.errors.push(Z),0!==et.pendingCount||et.done||(Object.keys(et.loaded).forEach(B=>{ea[B]||(ea[B]={});let Z=et.loaded[B];Z.length&&Z.forEach(Z=>{void 0===ea[B][Z]&&(ea[B][Z]=!0)})}),et.done=!0,et.errors.length?et.callback(et.errors):et.callback())}),this.emit("loaded",ea),this.queue=this.queue.filter(B=>!B.done)}read(B,Z,et,er=0,en=this.retryTimeout,eo){if(!B.length)return eo(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:B,ns:Z,fcName:et,tried:er,wait:en,callback:eo});return}this.readingCalls++;let resolver=(ea,ei)=>{if(this.readingCalls--,this.waitingReads.length>0){let B=this.waitingReads.shift();this.read(B.lng,B.ns,B.fcName,B.tried,B.wait,B.callback)}if(ea&&ei&&er<this.maxRetries){setTimeout(()=>{this.read.call(this,B,Z,et,er+1,2*en,eo)},en);return}eo(ea,ei)},ea=this.backend[et].bind(this.backend);if(2===ea.length){try{let et=ea(B,Z);et&&"function"==typeof et.then?et.then(B=>resolver(null,B)).catch(resolver):resolver(null,et)}catch(B){resolver(B)}return}return ea(B,Z,resolver)}prepareLoading(B,Z,et={},er){if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),er&&er();isString(B)&&(B=this.languageUtils.toResolveHierarchy(B)),isString(Z)&&(Z=[Z]);let en=this.queueLoad(B,Z,et,er);if(!en.toLoad.length)return en.pending.length||er(),null;en.toLoad.forEach(B=>{this.loadOne(B)})}load(B,Z,et){this.prepareLoading(B,Z,{},et)}reload(B,Z,et){this.prepareLoading(B,Z,{reload:!0},et)}loadOne(B,Z=""){let et=B.split("|"),er=et[0],en=et[1];this.read(er,en,"read",void 0,void 0,(et,eo)=>{et&&this.logger.warn(`${Z}loading namespace ${en} for language ${er} failed`,et),!et&&eo&&this.logger.log(`${Z}loaded namespace ${en} for language ${er}`,eo),this.loaded(B,et,eo)})}saveMissing(B,Z,et,er,en,eo={},ea=()=>{}){if(this.services?.utils?.hasLoadedNamespace&&!this.services?.utils?.hasLoadedNamespace(Z)){this.logger.warn(`did not save key "${et}" as the namespace "${Z}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(null!=et&&""!==et){if(this.backend?.create){let ei={...eo,isUpdate:en},es=this.backend.create.bind(this.backend);if(es.length<6)try{let en;(en=5===es.length?es(B,Z,et,er,ei):es(B,Z,et,er))&&"function"==typeof en.then?en.then(B=>ea(null,B)).catch(ea):ea(null,en)}catch(B){ea(B)}else es(B,Z,et,er,ea,ei)}B&&B[0]&&this.store.addResource(B[0],Z,et,er)}}};let get=()=>({debug:!1,initAsync:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:B=>{let Z={};if("object"==typeof B[1]&&(Z=B[1]),isString(B[1])&&(Z.defaultValue=B[1]),isString(B[2])&&(Z.tDescription=B[2]),"object"==typeof B[2]||"object"==typeof B[3]){let et=B[3]||B[2];Object.keys(et).forEach(B=>{Z[B]=et[B]})}return Z},interpolation:{escapeValue:!0,format:B=>B,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0}}),transformOptions=B=>(isString(B.ns)&&(B.ns=[B.ns]),isString(B.fallbackLng)&&(B.fallbackLng=[B.fallbackLng]),isString(B.fallbackNS)&&(B.fallbackNS=[B.fallbackNS]),B.supportedLngs?.indexOf?.("cimode")<0&&(B.supportedLngs=B.supportedLngs.concat(["cimode"])),"boolean"==typeof B.initImmediate&&(B.initAsync=B.initImmediate),B),noop=()=>{},bindMemberFunctions=B=>{let Z=Object.getOwnPropertyNames(Object.getPrototypeOf(B));Z.forEach(Z=>{"function"==typeof B[Z]&&(B[Z]=B[Z].bind(B))})};let I18n=class I18n extends EventEmitter{constructor(B={},Z){if(super(),this.options=transformOptions(B),this.services={},this.logger=es,this.modules={external:[]},bindMemberFunctions(this),Z&&!this.isInitialized&&!B.isClone){if(!this.options.initAsync)return this.init(B,Z),this;setTimeout(()=>{this.init(B,Z)},0)}}init(B={},Z){this.isInitializing=!0,"function"==typeof B&&(Z=B,B={}),null==B.defaultNS&&B.ns&&(isString(B.ns)?B.defaultNS=B.ns:0>B.ns.indexOf("translation")&&(B.defaultNS=B.ns[0]));let et=get();this.options={...et,...this.options,...transformOptions(B)},this.options.interpolation={...et.interpolation,...this.options.interpolation},void 0!==B.keySeparator&&(this.options.userDefinedKeySeparator=B.keySeparator),void 0!==B.nsSeparator&&(this.options.userDefinedNsSeparator=B.nsSeparator);let createClassOnDemand=B=>B?"function"==typeof B?new B:B:null;if(!this.options.isClone){let B;this.modules.logger?es.init(createClassOnDemand(this.modules.logger),this.options):es.init(null,this.options),B=this.modules.formatter?this.modules.formatter:Formatter;let Z=new LanguageUtil(this.options);this.store=new ResourceStore(this.options.resources,this.options);let er=this.services;er.logger=es,er.resourceStore=this.store,er.languageUtils=Z,er.pluralResolver=new PluralResolver(Z,{prepend:this.options.pluralSeparator,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),B&&(!this.options.interpolation.format||this.options.interpolation.format===et.interpolation.format)&&(er.formatter=createClassOnDemand(B),er.formatter.init(er,this.options),this.options.interpolation.format=er.formatter.format.bind(er.formatter)),er.interpolator=new Interpolator(this.options),er.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},er.backendConnector=new Connector(createClassOnDemand(this.modules.backend),er.resourceStore,er,this.options),er.backendConnector.on("*",(B,...Z)=>{this.emit(B,...Z)}),this.modules.languageDetector&&(er.languageDetector=createClassOnDemand(this.modules.languageDetector),er.languageDetector.init&&er.languageDetector.init(er,this.options.detection,this.options)),this.modules.i18nFormat&&(er.i18nFormat=createClassOnDemand(this.modules.i18nFormat),er.i18nFormat.init&&er.i18nFormat.init(this)),this.translator=new Translator(this.services,this.options),this.translator.on("*",(B,...Z)=>{this.emit(B,...Z)}),this.modules.external.forEach(B=>{B.init&&B.init(this)})}if(this.format=this.options.interpolation.format,Z||(Z=noop),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){let B=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);B.length>0&&"dev"!==B[0]&&(this.options.lng=B[0])}this.services.languageDetector||this.options.lng||this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(B=>{this[B]=(...Z)=>this.store[B](...Z)}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(B=>{this[B]=(...Z)=>(this.store[B](...Z),this)});let er=defer(),load=()=>{let finish=(B,et)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),er.resolve(et),Z(B,et)};if(this.languages&&!this.isInitialized)return finish(null,this.t.bind(this));this.changeLanguage(this.options.lng,finish)};return this.options.resources||!this.options.initAsync?load():setTimeout(load,0),er}loadResources(B,Z=noop){let et=Z,er=isString(B)?B:this.language;if("function"==typeof B&&(et=B),!this.options.resources||this.options.partialBundledLanguages){if(er?.toLowerCase()==="cimode"&&(!this.options.preload||0===this.options.preload.length))return et();let B=[],append=Z=>{if(!Z||"cimode"===Z)return;let et=this.services.languageUtils.toResolveHierarchy(Z);et.forEach(Z=>{"cimode"!==Z&&0>B.indexOf(Z)&&B.push(Z)})};if(er)append(er);else{let B=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);B.forEach(B=>append(B))}this.options.preload?.forEach?.(B=>append(B)),this.services.backendConnector.load(B,this.options.ns,B=>{B||this.resolvedLanguage||!this.language||this.setResolvedLanguage(this.language),et(B)})}else et(null)}reloadResources(B,Z,et){let er=defer();return"function"==typeof B&&(et=B,B=void 0),"function"==typeof Z&&(et=Z,Z=void 0),B||(B=this.languages),Z||(Z=this.options.ns),et||(et=noop),this.services.backendConnector.reload(B,Z,B=>{er.resolve(),et(B)}),er}use(B){if(!B)throw Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!B.type)throw Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return"backend"===B.type&&(this.modules.backend=B),("logger"===B.type||B.log&&B.warn&&B.error)&&(this.modules.logger=B),"languageDetector"===B.type&&(this.modules.languageDetector=B),"i18nFormat"===B.type&&(this.modules.i18nFormat=B),"postProcessor"===B.type&&eu.addPostProcessor(B),"formatter"===B.type&&(this.modules.formatter=B),"3rdParty"===B.type&&this.modules.external.push(B),this}setResolvedLanguage(B){if(B&&this.languages&&!(["cimode","dev"].indexOf(B)>-1)){for(let B=0;B<this.languages.length;B++){let Z=this.languages[B];if(!(["cimode","dev"].indexOf(Z)>-1)&&this.store.hasLanguageSomeTranslations(Z)){this.resolvedLanguage=Z;break}}!this.resolvedLanguage&&0>this.languages.indexOf(B)&&this.store.hasLanguageSomeTranslations(B)&&(this.resolvedLanguage=B,this.languages.unshift(B))}}changeLanguage(B,Z){this.isLanguageChangingTo=B;let et=defer();this.emit("languageChanging",B);let setLngProps=B=>{this.language=B,this.languages=this.services.languageUtils.toResolveHierarchy(B),this.resolvedLanguage=void 0,this.setResolvedLanguage(B)},done=(er,en)=>{en?this.isLanguageChangingTo===B&&(setLngProps(en),this.translator.changeLanguage(en),this.isLanguageChangingTo=void 0,this.emit("languageChanged",en),this.logger.log("languageChanged",en)):this.isLanguageChangingTo=void 0,et.resolve((...B)=>this.t(...B)),Z&&Z(er,(...B)=>this.t(...B))},setLng=Z=>{B||Z||!this.services.languageDetector||(Z=[]);let et=isString(Z)?Z:Z&&Z[0],er=this.store.hasLanguageSomeTranslations(et)?et:this.services.languageUtils.getBestMatchFromCodes(isString(Z)?[Z]:Z);er&&(this.language||setLngProps(er),this.translator.language||this.translator.changeLanguage(er),this.services.languageDetector?.cacheUserLanguage?.(er)),this.loadResources(er,B=>{done(B,er)})};return B||!this.services.languageDetector||this.services.languageDetector.async?!B&&this.services.languageDetector&&this.services.languageDetector.async?0===this.services.languageDetector.detect.length?this.services.languageDetector.detect().then(setLng):this.services.languageDetector.detect(setLng):setLng(B):setLng(this.services.languageDetector.detect()),et}getFixedT(B,Z,et){let fixedT=(B,Z,...er)=>{let en,eo;(en="object"!=typeof Z?this.options.overloadTranslationOptionHandler([B,Z].concat(er)):{...Z}).lng=en.lng||fixedT.lng,en.lngs=en.lngs||fixedT.lngs,en.ns=en.ns||fixedT.ns,""!==en.keyPrefix&&(en.keyPrefix=en.keyPrefix||et||fixedT.keyPrefix);let ea=this.options.keySeparator||".";return eo=en.keyPrefix&&Array.isArray(B)?B.map(B=>`${en.keyPrefix}${ea}${B}`):en.keyPrefix?`${en.keyPrefix}${ea}${B}`:B,this.t(eo,en)};return isString(B)?fixedT.lng=B:fixedT.lngs=B,fixedT.ns=Z,fixedT.keyPrefix=et,fixedT}t(...B){return this.translator?.translate(...B)}exists(...B){return this.translator?.exists(...B)}setDefaultNamespace(B){this.options.defaultNS=B}hasLoadedNamespace(B,Z={}){if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;let et=Z.lng||this.resolvedLanguage||this.languages[0],er=!!this.options&&this.options.fallbackLng,en=this.languages[this.languages.length-1];if("cimode"===et.toLowerCase())return!0;let loadNotPending=(B,Z)=>{let et=this.services.backendConnector.state[`${B}|${Z}`];return -1===et||0===et||2===et};if(Z.precheck){let B=Z.precheck(this,loadNotPending);if(void 0!==B)return B}return!!(this.hasResourceBundle(et,B)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||loadNotPending(et,B)&&(!er||loadNotPending(en,B)))}loadNamespaces(B,Z){let et=defer();return this.options.ns?(isString(B)&&(B=[B]),B.forEach(B=>{0>this.options.ns.indexOf(B)&&this.options.ns.push(B)}),this.loadResources(B=>{et.resolve(),Z&&Z(B)}),et):(Z&&Z(),Promise.resolve())}loadLanguages(B,Z){let et=defer();isString(B)&&(B=[B]);let er=this.options.preload||[],en=B.filter(B=>0>er.indexOf(B)&&this.services.languageUtils.isSupportedCode(B));return en.length?(this.options.preload=er.concat(en),this.loadResources(B=>{et.resolve(),Z&&Z(B)}),et):(Z&&Z(),Promise.resolve())}dir(B){if(B||(B=this.resolvedLanguage||(this.languages?.length>0?this.languages[0]:this.language)),!B)return"rtl";let Z=this.services?.languageUtils||new LanguageUtil(get());return["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"].indexOf(Z.getLanguagePartFromCode(B))>-1||B.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(B={},Z){return new I18n(B,Z)}cloneInstance(B={},Z=noop){let et=B.forkResourceStore;et&&delete B.forkResourceStore;let er={...this.options,...B,isClone:!0},en=new I18n(er);if((void 0!==B.debug||void 0!==B.prefix)&&(en.logger=en.logger.clone(B)),["store","services","language"].forEach(B=>{en[B]=this[B]}),en.services={...this.services},en.services.utils={hasLoadedNamespace:en.hasLoadedNamespace.bind(en)},et){let B=Object.keys(this.store.data).reduce((B,Z)=>(B[Z]={...this.store.data[Z]},B[Z]=Object.keys(B[Z]).reduce((et,er)=>(et[er]={...B[Z][er]},et),B[Z]),B),{});en.store=new ResourceStore(B,er),en.services.resourceStore=en.store}return en.translator=new Translator(en.services,er),en.translator.on("*",(B,...Z)=>{en.emit(B,...Z)}),en.init(er,Z),en.translator.options=er,en.translator.backendConnector.services.utils={hasLoadedNamespace:en.hasLoadedNamespace.bind(en)},en}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}};let ef=I18n.createInstance();ef.createInstance=I18n.createInstance;let ep=ef.createInstance;ef.dir,ef.init,ef.loadResources,ef.reloadResources,ef.use,ef.changeLanguage,ef.getFixedT,ef.t,ef.exists,ef.setDefaultNamespace,ef.hasLoadedNamespace,ef.loadNamespaces,ef.loadLanguages},34440:(B,Z,et)=>{"use strict";et.d(Z,{D:()=>ea});let er=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,en={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"\xa9","&#169;":"\xa9","&reg;":"\xae","&#174;":"\xae","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},unescapeHtmlEntity=B=>en[B],eo={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:B=>B.replace(er,unescapeHtmlEntity)},setDefaults=(B={})=>{eo={...eo,...B}},setI18n=B=>{},ea={type:"3rdParty",init(B){setDefaults(B.options.react),setI18n(B)}}},27870:(B,Z,et)=>{"use strict";let er;et.d(Z,{a3:()=>I18nextProvider,Db:()=>eu,$G:()=>useTranslation_useTranslation});var en=et(9885);et(37460);let utils_warn=(B,Z,et,er)=>{let en=[et,{code:Z,...er||{}}];if(B?.services?.logger?.forward)return B.services.logger.forward(en,"warn","react-i18next::",!0);utils_isString(en[0])&&(en[0]=`react-i18next:: ${en[0]}`),B?.services?.logger?.warn?B.services.logger.warn(...en):console?.warn&&console.warn(...en)},eo={},utils_warnOnce=(B,Z,et,er)=>{utils_isString(et)&&eo[et]||(utils_isString(et)&&(eo[et]=new Date),utils_warn(B,Z,et,er))},loadedClb=(B,Z)=>()=>{if(B.isInitialized)Z();else{let initialized=()=>{setTimeout(()=>{B.off("initialized",initialized)},0),Z()};B.on("initialized",initialized)}},loadNamespaces=(B,Z,et)=>{B.loadNamespaces(Z,loadedClb(B,et))},loadLanguages=(B,Z,et,er)=>{if(utils_isString(et)&&(et=[et]),B.options.preload&&B.options.preload.indexOf(Z)>-1)return loadNamespaces(B,et,er);et.forEach(Z=>{0>B.options.ns.indexOf(Z)&&B.options.ns.push(Z)}),B.loadLanguages(Z,loadedClb(B,er))},hasLoadedNamespace=(B,Z,et={})=>Z.languages&&Z.languages.length?Z.hasLoadedNamespace(B,{lng:et.lng,precheck:(Z,er)=>{if(et.bindI18n?.indexOf("languageChanging")>-1&&Z.services.backendConnector.backend&&Z.isLanguageChangingTo&&!er(Z.isLanguageChangingTo,B))return!1}}):(utils_warnOnce(Z,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:Z.languages}),!0),utils_isString=B=>"string"==typeof B,utils_isObject=B=>"object"==typeof B&&null!==B,ea=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,ei={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"\xa9","&#169;":"\xa9","&reg;":"\xae","&#174;":"\xae","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},unescapeHtmlEntity=B=>ei[B],es={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:B=>B.replace(ea,unescapeHtmlEntity)},setDefaults=(B={})=>{es={...es,...B}},defaults_getDefaults=()=>es,getAsArray=B=>Array.isArray(B)?B:[B],fixComponentProps=(B,Z,et)=>{let er=B.key||Z,en=cloneElement(B,{key:er});if(!en.props||!en.props.children||0>et.indexOf(`${Z}/>`)&&0>et.indexOf(`${Z} />`))return en;function Componentized(){return createElement(Fragment,null,en)}return createElement(Componentized,{key:er})},setI18n=B=>{er=B},i18nInstance_getI18n=()=>er,eu={type:"3rdParty",init(B){setDefaults(B.options.react),setI18n(B)}},el=(0,en.createContext)();let ReportNamespaces=class ReportNamespaces{constructor(){this.usedNamespaces={}}addUsedNamespaces(B){B.forEach(B=>{this.usedNamespaces[B]||(this.usedNamespaces[B]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}};let usePrevious=(B,Z)=>{let et=(0,en.useRef)();return(0,en.useEffect)(()=>{et.current=Z?et.current:B},[B,Z]),et.current},alwaysNewT=(B,Z,et,er)=>B.getFixedT(Z,et,er),useMemoizedT=(B,Z,et,er)=>(0,en.useCallback)(alwaysNewT(B,Z,et,er),[B,Z,et,er]),useTranslation_useTranslation=(B,Z={})=>{let{i18n:et}=Z,{i18n:er,defaultNS:eo}=(0,en.useContext)(el)||{},ea=et||er||i18nInstance_getI18n();if(ea&&!ea.reportNamespaces&&(ea.reportNamespaces=new ReportNamespaces),!ea){utils_warnOnce(ea,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");let notReadyT=(B,Z)=>utils_isString(Z)?Z:utils_isObject(Z)&&utils_isString(Z.defaultValue)?Z.defaultValue:Array.isArray(B)?B[B.length-1]:B,B=[notReadyT,{},!1];return B.t=notReadyT,B.i18n={},B.ready=!1,B}ea.options.react?.wait&&utils_warnOnce(ea,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");let ei={...defaults_getDefaults(),...ea.options.react,...Z},{useSuspense:es,keyPrefix:eu}=ei,ec=B||eo||ea.options?.defaultNS;ec=utils_isString(ec)?[ec]:ec||["translation"],ea.reportNamespaces.addUsedNamespaces?.(ec);let ed=(ea.isInitialized||ea.initializedStoreOnce)&&ec.every(B=>hasLoadedNamespace(B,ea,ei)),ef=useMemoizedT(ea,Z.lng||null,"fallback"===ei.nsMode?ec:ec[0],eu),getT=()=>ef,getNewT=()=>alwaysNewT(ea,Z.lng||null,"fallback"===ei.nsMode?ec:ec[0],eu),[ep,eh]=(0,en.useState)(getT),eg=ec.join();Z.lng&&(eg=`${Z.lng}${eg}`);let ey=usePrevious(eg),em=(0,en.useRef)(!0);(0,en.useEffect)(()=>{let{bindI18n:B,bindI18nStore:et}=ei;em.current=!0,ed||es||(Z.lng?loadLanguages(ea,Z.lng,ec,()=>{em.current&&eh(getNewT)}):loadNamespaces(ea,ec,()=>{em.current&&eh(getNewT)})),ed&&ey&&ey!==eg&&em.current&&eh(getNewT);let boundReset=()=>{em.current&&eh(getNewT)};return B&&ea?.on(B,boundReset),et&&ea?.store.on(et,boundReset),()=>{em.current=!1,ea&&B?.split(" ").forEach(B=>ea.off(B,boundReset)),et&&ea&&et.split(" ").forEach(B=>ea.store.off(B,boundReset))}},[ea,eg]),(0,en.useEffect)(()=>{em.current&&ed&&eh(getT)},[ea,eu,ed]);let ev=[ep,ea,ed];if(ev.t=ep,ev.i18n=ea,ev.ready=ed,ed||!ed&&!es)return ev;throw new Promise(B=>{Z.lng?loadLanguages(ea,Z.lng,ec,()=>B()):loadNamespaces(ea,ec,()=>B())})};function I18nextProvider({i18n:B,defaultNS:Z,children:et}){let er=(0,en.useMemo)(()=>({i18n:B,defaultNS:Z}),[B,Z]);return(0,en.createElement)(el.Provider,{value:er},et)}}};