import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  Logger,
  ParseUUIDPipe,
  ParseIntPipe,
  DefaultValuePipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiQuery } from '@nestjs/swagger';
import { MessagePattern } from '@nestjs/microservices';
import { OrderService } from '../services/order.service';
import { CreateOrderDto } from '../dto/create-order.dto';
import { UpdateOrderDto } from '../dto/update-order.dto';
import { OrderResponseDto } from '../dto/order-response.dto';
import { Order, OrderStatus } from '../entities/order.entity';
import { JwtAuthGuard } from '../../shared/guards/jwt-auth.guard';

@ApiTags('orders')
@Controller('orders')
export class OrderController {
  private readonly logger = new Logger(OrderController.name);

  constructor(private readonly orderService: OrderService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new order' })
  @ApiResponse({ status: 201, description: 'Order created successfully', type: OrderResponseDto })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async create(@Body() createOrderDto: CreateOrderDto, @Request() req: any): Promise<Order> {
    this.logger.log(`Creating order for user ${req.user.id}`);
    return this.orderService.createOrder(createOrderDto, req.user.id);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user orders' })
  @ApiResponse({ status: 200, description: 'Orders retrieved successfully', type: [OrderResponseDto] })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of orders to return' })
  @ApiQuery({ name: 'offset', required: false, description: 'Number of orders to skip' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by order status', enum: OrderStatus })
  async findUserOrders(
    @Request() req: any,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('offset', new DefaultValuePipe(0), ParseIntPipe) offset: number,
    @Query('status') status?: OrderStatus,
  ): Promise<{ orders: Order[]; total: number }> {
    this.logger.log(`Finding orders for user ${req.user.id}`);
    return this.orderService.findUserOrders(req.user.id, { limit, offset, status });
  }

  @Get('all')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all orders (admin only)' })
  @ApiResponse({ status: 200, description: 'All orders retrieved successfully', type: [OrderResponseDto] })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of orders to return' })
  @ApiQuery({ name: 'offset', required: false, description: 'Number of orders to skip' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by order status', enum: OrderStatus })
  @ApiQuery({ name: 'userId', required: false, description: 'Filter by user ID' })
  @ApiQuery({ name: 'storeId', required: false, description: 'Filter by store ID' })
  async findAll(
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('offset', new DefaultValuePipe(0), ParseIntPipe) offset: number,
    @Query('status') status?: OrderStatus,
    @Query('userId') userId?: string,
    @Query('storeId') storeId?: string,
  ): Promise<{ orders: Order[]; total: number }> {
    this.logger.log('Finding all orders');
    return this.orderService.findAll({ limit, offset, status, userId, storeId });
  }

  @Get('store/:storeId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get orders for a specific store' })
  @ApiResponse({ status: 200, description: 'Store orders retrieved successfully', type: [OrderResponseDto] })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of orders to return' })
  @ApiQuery({ name: 'offset', required: false, description: 'Number of orders to skip' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by order status', enum: OrderStatus })
  async findStoreOrders(
    @Param('storeId', ParseUUIDPipe) storeId: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('offset', new DefaultValuePipe(0), ParseIntPipe) offset: number,
    @Query('status') status?: OrderStatus,
  ): Promise<{ orders: Order[]; total: number }> {
    this.logger.log(`Finding orders for store ${storeId}`);
    return this.orderService.findStoreOrders(storeId, { limit, offset, status });
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get order by ID' })
  @ApiResponse({ status: 200, description: 'Order retrieved successfully', type: OrderResponseDto })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Order not found' })
  @ApiParam({ name: 'id', description: 'Order ID' })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<Order> {
    this.logger.log(`Finding order ${id}`);
    return this.orderService.findById(id);
  }

  @Get('number/:orderNumber')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get order by order number' })
  @ApiResponse({ status: 200, description: 'Order retrieved successfully', type: OrderResponseDto })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Order not found' })
  @ApiParam({ name: 'orderNumber', description: 'Order number' })
  async findByOrderNumber(@Param('orderNumber') orderNumber: string): Promise<Order> {
    this.logger.log(`Finding order by number ${orderNumber}`);
    return this.orderService.findByOrderNumber(orderNumber);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update order' })
  @ApiResponse({ status: 200, description: 'Order updated successfully', type: OrderResponseDto })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Order not found' })
  @ApiParam({ name: 'id', description: 'Order ID' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateOrderDto: UpdateOrderDto,
  ): Promise<Order> {
    this.logger.log(`Updating order ${id}`);
    return this.orderService.updateOrder(id, updateOrderDto);
  }

  @Post(':id/cancel')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Cancel order' })
  @ApiResponse({ status: 200, description: 'Order cancelled successfully', type: OrderResponseDto })
  @ApiResponse({ status: 400, description: 'Order cannot be cancelled' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Order not found' })
  @ApiParam({ name: 'id', description: 'Order ID' })
  async cancel(@Param('id', ParseUUIDPipe) id: string, @Request() req: any): Promise<Order> {
    this.logger.log(`Cancelling order ${id} for user ${req.user.id}`);
    return this.orderService.cancelOrder(id, req.user.id);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete order (admin only)' })
  @ApiResponse({ status: 200, description: 'Order deleted successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Order not found' })
  @ApiParam({ name: 'id', description: 'Order ID' })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    this.logger.log(`Deleting order ${id}`);
    return this.orderService.deleteOrder(id);
  }

  // ============================================================================
  // MICROSERVICE MESSAGE HANDLERS
  // ============================================================================

  @MessagePattern('create_order_from_cart')
  async createOrderFromCart(data: { cartId: string; userId: string; shippingAddress: any }) {
    this.logger.log(`[MICROSERVICE] Creating order from cart: ${data.cartId}`);
    try {
      const createOrderDto: CreateOrderDto = {
        cartId: data.cartId,
        shippingAddress: data.shippingAddress,
      };
      return await this.orderService.createOrder(createOrderDto, data.userId);
    } catch (error) {
      this.logger.error(`[MICROSERVICE] Error creating order from cart ${data.cartId}:`, error.message);
      throw error;
    }
  }

  @MessagePattern('find_order_by_id')
  async findOrderById(orderId: string): Promise<Order> {
    this.logger.log(`[MICROSERVICE] Finding order by ID: ${orderId}`);
    try {
      return await this.orderService.findById(orderId);
    } catch (error) {
      this.logger.error(`[MICROSERVICE] Error finding order ${orderId}:`, error.message);
      throw error;
    }
  }

  @MessagePattern('find_user_orders')
  async findUserOrdersMessage(data: { userId: string; limit?: number; offset?: number }) {
    this.logger.log(`[MICROSERVICE] Finding orders for user: ${data.userId}`);
    try {
      return await this.orderService.findUserOrders(data.userId, {
        limit: data.limit,
        offset: data.offset,
      });
    } catch (error) {
      this.logger.error(`[MICROSERVICE] Error finding orders for user ${data.userId}:`, error.message);
      throw error;
    }
  }

  @MessagePattern('update_order_status')
  async updateOrderStatus(data: { orderId: string; status: OrderStatus }) {
    this.logger.log(`[MICROSERVICE] Updating order ${data.orderId} status to ${data.status}`);
    try {
      return await this.orderService.updateOrder(data.orderId, { status: data.status });
    } catch (error) {
      this.logger.error(`[MICROSERVICE] Error updating order ${data.orderId}:`, error.message);
      throw error;
    }
  }

  @MessagePattern('cancel_order')
  async cancelOrderMessage(data: { orderId: string; userId: string }) {
    this.logger.log(`[MICROSERVICE] Cancelling order ${data.orderId} for user ${data.userId}`);
    try {
      return await this.orderService.cancelOrder(data.orderId, data.userId);
    } catch (error) {
      this.logger.error(`[MICROSERVICE] Error cancelling order ${data.orderId}:`, error.message);
      throw error;
    }
  }
}
