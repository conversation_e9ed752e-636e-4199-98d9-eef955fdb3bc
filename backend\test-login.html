<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        .response {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Test Login</h1>
    
    <div class="form-group">
        <label for="usernameOrEmail">Username or Email:</label>
        <input type="text" id="usernameOrEmail" value="simpletestuser">
    </div>
    
    <div class="form-group">
        <label for="password">Password:</label>
        <input type="password" id="password" value="password123">
    </div>
    
    <button onclick="testLogin()">Test Login</button>
    
    <div class="response" id="response"></div>
    
    <script>
        async function testLogin() {
            const usernameOrEmail = document.getElementById('usernameOrEmail').value;
            const password = document.getElementById('password').value;
            const responseElement = document.getElementById('response');
            
            responseElement.textContent = 'Sending request...';
            
            try {
                // Direct fetch call
                console.log('Making direct fetch call to /auth/login');
                const response = await fetch('http://localhost:3001/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        usernameOrEmail,
                        password,
                    }),
                    credentials: 'include',
                });
                
                console.log('Response status:', response.status);
                const data = await response.json().catch(() => ({ error: 'Failed to parse JSON response' }));
                console.log('Response data:', data);
                
                responseElement.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                console.error('Error:', error);
                responseElement.textContent = 'Error: ' + error.message;
            }
        }
    </script>
</body>
</html>
