/**
 * Base class for all events in the system
 */
export abstract class BaseEvent {
  /**
   * The type of the event
   */
  abstract readonly type: string;

  /**
   * The timestamp when the event was created
   */
  readonly timestamp: Date;

  /**
   * The ID of the event
   */
  readonly id: string;

  /**
   * The version of the event
   */
  readonly version: number = 1;

  constructor() {
    this.timestamp = new Date();
    this.id = this.generateId();
  }

  /**
   * Generate a unique ID for the event
   */
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}
