import { Injectable, Logger, NotFoundException, ForbiddenException } from '@nestjs/common';
import { StoreRepository } from '../repositories/store.repository';
import { CreateStoreDto } from '../dto/create-store.dto';
import { UpdateStoreDto } from '../dto/update-store.dto';
import { Store } from '../entities/store.entity';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class StoreService {
  private readonly logger = new Logger(StoreService.name);
  private readonly userServiceUrl: string;

  constructor(
    private readonly storeRepository: StoreRepository,
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.userServiceUrl = this.configService.get<string>('USER_SERVICE_URL', 'http://localhost:3001/api');
  }

  async findAll(): Promise<Store[]> {
    this.logger.log('Finding all stores');
    return this.storeRepository.findAll();
  }

  async findOne(id: string): Promise<Store> {
    this.logger.log(`Finding store with ID: ${id}`);
    return this.storeRepository.findOne(id);
  }

  async findByOwnerId(ownerId: string): Promise<Store[]> {
    this.logger.log(`Finding stores for owner with ID: ${ownerId}`);
    return this.storeRepository.findByOwnerId(ownerId);
  }

  async create(createStoreDto: CreateStoreDto): Promise<Store> {
    this.logger.log(`Creating store with name: ${createStoreDto.name}`);
    
    // Verify that the owner exists by calling the User Service
    try {
      await firstValueFrom(
        this.httpService.get(`${this.userServiceUrl}/users/${createStoreDto.ownerId}`, {
          headers: {
            Authorization: `Bearer ${this.configService.get<string>('JWT_SECRET')}`,
          },
        })
      );
    } catch (error) {
      this.logger.error(`Failed to verify owner: ${error.message}`);
      throw new NotFoundException(`Owner with ID ${createStoreDto.ownerId} not found`);
    }
    
    return this.storeRepository.create(createStoreDto);
  }

  async update(id: string, updateStoreDto: UpdateStoreDto, userId: string): Promise<Store> {
    this.logger.log(`Updating store with ID: ${id}`);
    
    // Check if the user is the owner of the store
    const store = await this.storeRepository.findOne(id);
    
    if (store.ownerId !== userId) {
      throw new ForbiddenException('You are not authorized to update this store');
    }
    
    return this.storeRepository.update(id, updateStoreDto);
  }

  async remove(id: string, userId: string): Promise<void> {
    this.logger.log(`Removing store with ID: ${id}`);
    
    // Check if the user is the owner of the store
    const store = await this.storeRepository.findOne(id);
    
    if (store.ownerId !== userId) {
      throw new ForbiddenException('You are not authorized to delete this store');
    }
    
    return this.storeRepository.remove(id);
  }

  async incrementFollowerCount(id: string): Promise<Store> {
    this.logger.log(`Incrementing follower count for store with ID: ${id}`);
    return this.storeRepository.incrementFollowerCount(id);
  }

  async decrementFollowerCount(id: string): Promise<Store> {
    this.logger.log(`Decrementing follower count for store with ID: ${id}`);
    return this.storeRepository.decrementFollowerCount(id);
  }

  async updateRating(id: string, rating: number, reviewCount: number): Promise<Store> {
    this.logger.log(`Updating rating for store with ID: ${id}`);
    return this.storeRepository.updateRating(id, rating, reviewCount);
  }
}
