# Service Creation Template

## Overview
This template provides a standardized structure for creating new microservices in the social commerce platform.

## Template Structure

```
{{SERVICE_NAME_KEBAB}}/
├── src/
│   ├── main.ts                           # Application entry point
│   ├── app.module.ts                     # Root module
│   ├── {{SERVICE_NAME}}-management/      # Main business feature
│   │   ├── controllers/
│   │   │   └── {{SERVICE_NAME}}.controller.ts
│   │   ├── services/
│   │   │   └── {{SERVICE_NAME}}.service.ts
│   │   ├── repositories/
│   │   │   └── {{SERVICE_NAME}}.repository.ts
│   │   ├── dto/
│   │   │   ├── create-{{SERVICE_NAME}}.dto.ts
│   │   │   └── update-{{SERVICE_NAME}}.dto.ts
│   │   └── entities/
│   │       └── {{SERVICE_NAME}}.entity.ts
│   ├── shared/
│   │   ├── controllers/
│   │   │   └── health.controller.ts
│   │   ├── decorators/
│   │   ├── dto/
│   │   ├── entities/
│   │   ├── filters/
│   │   ├── guards/
│   │   ├── interceptors/
│   │   ├── middleware/
│   │   ├── utils/
│   │   └── shared.module.ts
│   └── listeners/                        # Event listeners
│       └── {{SERVICE_NAME}}-events.listener.ts
├── test/
│   ├── app.e2e-spec.ts
│   └── jest-e2e.json
├── Dockerfile                            # Container build file
├── package.json                          # Dependencies and scripts
├── tsconfig.json                         # TypeScript configuration
├── nest-cli.json                         # NestJS CLI configuration
└── README.md                             # Service documentation
```

## Naming Convention Application

### Database Names
- Database: `{{SERVICE_NAME_SNAKE}}_service`
- Example: `product_service`

### Docker Names
- Image: `{{SERVICE_NAME_KEBAB}}-service`
- Container: `social-commerce-{{SERVICE_NAME_KEBAB}}-service`
- Examples: `product-service`, `social-commerce-product-service`

### Queue Names
- Queue: `{{SERVICE_NAME_SNAKE}}_queue`
- Example: `product_queue`

### Environment Variables
- Service URL: `{{SERVICE_NAME_UPPER}}_SERVICE_URL`
- Service Port: `{{SERVICE_NAME_UPPER}}_SERVICE_PORT`
- Database: `DB_DATABASE_{{SERVICE_NAME_UPPER}}`
- Examples: `PRODUCT_SERVICE_URL`, `PRODUCT_SERVICE_PORT`, `DB_DATABASE_PRODUCT`

## Usage Instructions

1. **Copy Template Directory**
   ```bash
   cp -r docs/templates/service-template services/{{SERVICE_NAME_KEBAB}}
   ```

2. **Replace Placeholders**
   - Replace all `{{SERVICE_NAME}}` with actual service name
   - Replace all `{{SERVICE_NAME_KEBAB}}` with kebab-case name
   - Replace all `{{SERVICE_NAME_SNAKE}}` with snake_case name
   - Replace all `{{SERVICE_NAME_UPPER}}` with UPPER_CASE name
   - Replace `{{SERVICE_PORT}}` with assigned port number
   - Replace `{{SERVICE_DESCRIPTION}}` with service description

3. **Update Configuration Files**
   - Update package.json with correct name and description
   - Update Dockerfile with correct service name
   - Update main.ts with correct service configuration

4. **Add to Infrastructure**
   - Add service to docker-compose.yml
   - Add database to POSTGRES_MULTIPLE_DATABASES
   - Add environment variables to .env files

5. **Test Service Creation**
   - Build Docker image
   - Start service
   - Verify health endpoint
   - Test basic functionality

## Placeholder Reference

| Placeholder | Example Value | Usage |
|-------------|---------------|-------|
| `{{SERVICE_NAME}}` | `product` | File names, class names |
| `{{SERVICE_NAME_KEBAB}}` | `product-service` | Directory names, Docker images |
| `{{SERVICE_NAME_SNAKE}}` | `product_service` | Database names, queues |
| `{{SERVICE_NAME_UPPER}}` | `PRODUCT` | Environment variables |
| `{{SERVICE_PORT}}` | `3003` | Port configuration |
| `{{SERVICE_DESCRIPTION}}` | `Product Management Service` | Documentation |

## Validation Checklist

- [ ] All placeholders replaced with actual values
- [ ] Naming conventions followed correctly
- [ ] Directory structure matches template
- [ ] All configuration files updated
- [ ] Service added to docker-compose.yml
- [ ] Environment variables configured
- [ ] Database configuration added
- [ ] Health endpoint implemented
- [ ] Basic tests created
- [ ] Documentation updated
