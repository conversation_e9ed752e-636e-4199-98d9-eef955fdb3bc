(()=>{var e={};e.id=1624,e.ids=[1624],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},20997:(e,a,s)=>{"use strict";s.r(a),s.d(a,{GlobalError:()=>d.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>g,tree:()=>l});var r=s(67096),t=s(16132),i=s(37284),d=s.n(i),o=s(32564),n={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);s.d(a,n);let l=["",{children:["affiliate",{children:["dashboard",{children:["commissions",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,97815)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\affiliate\\dashboard\\commissions\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9291,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\affiliate\\dashboard\\commissions\\page.tsx"],m="/affiliate/dashboard/commissions/page",x={require:s,loadChunk:()=>Promise.resolve()},g=new r.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/affiliate/dashboard/commissions/page",pathname:"/affiliate/dashboard/commissions",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},9598:(e,a,s)=>{Promise.resolve().then(s.bind(s,83874))},83874:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>AffiliateCommissionsPage});var r=s(30784),t=s(9885),i=s(27870),d=s(14379),o=s(11440),n=s.n(o),l=s(59872),c=s(94820);let affiliate_AffiliateCommissionsTable=({commissions:e,isLoading:a=!1,className:s=""})=>{let{t}=(0,i.$G)("affiliate"),{isRtl:o}=(0,d.g)(),formatDate=e=>new Date(e).toLocaleDateString(void 0,{year:"numeric",month:"short",day:"numeric"}),formatCurrency=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e),getStatusBadgeClass=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";case"approved":return"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";case"paid":return"bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";case"rejected":return"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400"}};return a?r.jsx("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden ${s}`,children:(0,r.jsxs)("div",{className:"animate-pulse",children:[r.jsx("div",{className:"h-12 bg-gray-100 dark:bg-gray-700"}),Array.from({length:3}).map((e,a)=>r.jsx("div",{className:"h-16 border-t border-gray-200 dark:border-gray-700 px-4 py-3",children:(0,r.jsxs)("div",{className:"grid grid-cols-5 gap-4",children:[r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-600 rounded"}),r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-600 rounded"}),r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-600 rounded"}),r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-600 rounded"}),r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-600 rounded"})]})},a))]})}):0===e.length?(0,r.jsxs)("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 text-center ${s}`,children:[r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-12 w-12 text-gray-400 dark:text-gray-600 mx-auto mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})}),r.jsx("p",{className:"text-gray-500 dark:text-gray-400 mb-4",children:t("commissions.noCommissions","No commissions found")})]}):r.jsx("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden ${s}`,children:r.jsx("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[r.jsx("thead",{className:"bg-gray-50 dark:bg-gray-900",children:(0,r.jsxs)("tr",{children:[r.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:t("commissions.orderId","Order ID")}),r.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:t("commissions.product","Product")}),r.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:t("commissions.amount","Amount")}),r.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:t("commissions.status","Status")}),r.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:t("commissions.date","Date")})]})}),r.jsx("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:e.map(e=>(0,r.jsxs)("tr",{children:[r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100",children:e.orderId}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:e.productId}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-green-600 dark:text-green-400",children:formatCurrency(e.amount)}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm",children:r.jsx("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${getStatusBadgeClass(e.status)}`,children:t(`commissions.${e.status}`,e.status)})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:formatDate(e.createdAt)})]},e.id))})]})})})};var m=s(3619);function AffiliateCommissionsPage(){let{t:e}=(0,i.$G)("affiliate"),{isRtl:a}=(0,d.g)(),[s,o]=(0,t.useState)("all"),{data:x,isLoading:g}=(0,m.nK)({}),u=x?.accounts?.[0],{data:p,isLoading:h}=(0,m.d4)({accountId:u?.id||"",status:"all"!==s?s:void 0},{skip:!u}),y=[{value:"all",label:e("commissions.filter.all","All")},{value:"pending",label:e("commissions.filter.pending","Pending")},{value:"approved",label:e("commissions.filter.approved","Approved")},{value:"rejected",label:e("commissions.filter.rejected","Rejected")},{value:"paid",label:e("commissions.filter.paid","Paid")}];return r.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2",children:e("commissions.title","Commissions")}),r.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:e("commissions.description","Track your earned commissions")})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[r.jsx("div",{className:"lg:col-span-1",children:r.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4",children:r.jsx(c.Z,{})})}),r.jsx("div",{className:"lg:col-span-3",children:u?(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"mb-6",children:(0,r.jsxs)("div",{className:"flex flex-wrap items-center justify-between",children:[r.jsx("h2",{className:"text-xl font-bold text-gray-900 dark:text-gray-100 mb-4 md:mb-0",children:e("commissions.yourCommissions","Your Commissions")}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("label",{htmlFor:"status-filter",className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:[e("commissions.status","Status"),":"]}),r.jsx("select",{id:"status-filter",value:s,onChange:e=>o(e.target.value),className:"px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 text-sm",children:y.map(e=>r.jsx("option",{value:e.value,children:e.label},e.value))})]})]})}),r.jsx(affiliate_AffiliateCommissionsTable,{commissions:p?.commissions||[],isLoading:h})]}):r.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6",children:(0,r.jsxs)("div",{className:"text-center py-8",children:[r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-16 w-16 text-gray-400 dark:text-gray-600 mx-auto mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})}),r.jsx("h3",{className:"text-xl font-medium text-gray-900 dark:text-gray-100 mb-2",children:e("noAffiliateAccount","No Affiliate Account")}),r.jsx("p",{className:"text-gray-500 dark:text-gray-400 mb-6 max-w-md mx-auto",children:e("joinProgramFirst","Join an affiliate program to track your commissions")}),r.jsx(n(),{href:"/affiliate/programs",children:r.jsx(l.Z,{variant:"primary",children:e("browsePrograms","Browse Programs")})})]})})})]})]})})}},97815:(e,a,s)=>{"use strict";s.r(a),s.d(a,{$$typeof:()=>d,__esModule:()=>i,default:()=>n});var r=s(95153);let t=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\affiliate\dashboard\commissions\page.tsx`),{__esModule:i,$$typeof:d}=t,o=t.default,n=o}};var a=require("../../../../webpack-runtime.js");a.C(e);var __webpack_exec__=e=>a(a.s=e),s=a.X(0,[2103,2765,3619,9522],()=>__webpack_exec__(20997));module.exports=s})();