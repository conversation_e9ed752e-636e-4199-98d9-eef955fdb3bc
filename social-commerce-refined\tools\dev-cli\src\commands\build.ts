import { Command } from 'commander';
import * as chalk from 'chalk';
import * as inquirer from 'inquirer';
import { getAllServices, serviceExists } from '../utils/paths';
import { buildService } from '../utils/npm';

export function buildCommand(program: Command): void {
  program
    .command('build [service]')
    .description('Build services')
    .option('-a, --all', 'Build all services')
    .action(async (service: string | undefined, options: { all?: boolean }) => {
      try {
        // If no service is specified and --all is not set, prompt for service
        if (!service && !options.all) {
          const services = getAllServices();
          
          if (services.length === 0) {
            console.log(chalk.yellow('No services found'));
            return;
          }
          
          const answers = await inquirer.prompt([
            {
              type: 'list',
              name: 'service',
              message: 'Which service do you want to build?',
              choices: [...services, 'all'],
            },
          ]);
          
          if (answers.service === 'all') {
            options.all = true;
          } else {
            service = answers.service;
          }
        }

        // Build all services
        if (options.all) {
          const services = getAllServices();
          
          if (services.length === 0) {
            console.log(chalk.yellow('No services found'));
            return;
          }
          
          console.log(chalk.blue(`Building all services: ${services.join(', ')}...`));
          
          for (const svc of services) {
            try {
              await buildService(svc);
            } catch (error) {
              console.error(chalk.red(`Error building ${svc}-service: ${error.message}`));
            }
          }
          
          console.log(chalk.green('All services built successfully'));
          return;
        }

        // Build specific service
        if (service) {
          if (!serviceExists(service)) {
            console.error(chalk.red(`Service ${service}-service does not exist`));
            return;
          }
          
          console.log(chalk.blue(`Building ${service}-service...`));
          await buildService(service);
          console.log(chalk.green(`${service}-service built successfully`));
          return;
        }
      } catch (error) {
        console.error(chalk.red(`Error: ${error.message}`));
        process.exit(1);
      }
    });
}
