# 🚫 ANTI-DUPLICATION RULES & GUIDELINES

## **CRITICAL RULES TO PREVENT DUPLICATE PATHS AND CODE**

### **📍 RULE 1: SINGLE SOURCE OF TRUTH**
- ✅ **USE**: `~/Documents/Augment/social-nft-platform-v2/` as the ONLY working directory
- ❌ **NEVER CREATE**: Nested duplicates like `social-nft-platform/social-nft-platform-v2/`
- ❌ **NEVER CREATE**: Concurrent directories with similar names

### **📍 RULE 2: TERMINAL DIRECTORY MANAGEMENT**
- ✅ **ALWAYS START**: From project root `~/Documents/Augment/social-nft-platform-v2/`
- ✅ **USE CWD PARAMETER**: Set `cwd: ~/Documents/Augment/social-nft-platform-v2/...` in launch-process
- ❌ **NEVER START**: From VS Code directory and then navigate
- ❌ **NEVER USE**: Long navigation commands like `cd ~/Documents/Augment/...`

### **📍 RULE 3: BEFORE CREATING NEW DIRECTORIES**
1. **CHECK**: Does this directory already exist?
2. **VERIFY**: Is this the intended location?
3. **CONFIRM**: Will this create duplicates?
4. **ASK USER**: If uncertain about directory structure

### **📍 RULE 4: BEFORE REMOVING DIRECTORIES**
1. **COPY/MOVE**: All important code to safe location
2. **VERIFY**: Copy was successful
3. **TEST**: Services work in new location
4. **ONLY THEN**: Remove old directory

### **📍 RULE 5: CONSISTENT NAMING**
- ✅ **USE**: Standard NestJS files (main.ts, app.module.ts, app.controller.ts)
- ❌ **AVOID**: Temporary prefixes like 'simple-' unless explicitly needed
- ❌ **AVOID**: Duplicate service names in different directories

### **📍 RULE 6: VERIFICATION STEPS**
Before any major operation:
1. **List current structure**: `find . -name "*social-nft*" -type d`
2. **Verify working directory**: `pwd`
3. **Check for duplicates**: Look for similar directory names
4. **Confirm with user**: If structure changes are needed

## **🚀 IMPLEMENTATION CHECKLIST**

### **For AI Agents:**
- [ ] Always check current directory structure before creating new directories
- [ ] Use cwd parameter instead of navigation commands
- [ ] Verify no duplicates exist before proceeding
- [ ] Ask user for confirmation on structural changes

### **For Development:**
- [ ] Maintain single project root
- [ ] Use relative paths from project root
- [ ] Avoid nested project structures
- [ ] Regular cleanup of unused directories

## **🎯 SUCCESS METRICS**
- ✅ Single project directory structure
- ✅ No duplicate service implementations
- ✅ Clean, consistent file organization
- ✅ Efficient terminal operations
- ✅ No confusion about working directories

---
**Last Updated**: May 23, 2025
**Status**: Active - Enforced for all development operations
