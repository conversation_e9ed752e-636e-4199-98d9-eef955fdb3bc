"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductUpdatedEvent = void 0;
class ProductUpdatedEvent {
    constructor(payload) {
        this.type = 'product.updated';
        this.version = '1.0';
        this.producer = 'product-service';
        this.id = payload.id;
        this.timestamp = new Date().toISOString();
        this.payload = payload;
    }
}
exports.ProductUpdatedEvent = ProductUpdatedEvent;
//# sourceMappingURL=product-updated.event.js.map