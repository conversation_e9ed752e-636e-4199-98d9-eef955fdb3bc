"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var EventSubscriberModule_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventSubscriberModule = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const rabbitmq_module_1 = require("./rabbitmq.module");
const event_subscriber_explorer_service_1 = require("./event-subscriber-explorer.service");
let EventSubscriberModule = EventSubscriberModule_1 = class EventSubscriberModule {
    static register(options) {
        return {
            module: EventSubscriberModule_1,
            imports: [core_1.DiscoveryModule, rabbitmq_module_1.RabbitMQModule.register(options)],
            providers: [
                event_subscriber_explorer_service_1.EventSubscriberExplorerService,
                core_1.MetadataScanner,
                core_1.Reflector,
            ],
            exports: [],
        };
    }
    static registerAsync(options) {
        return {
            module: EventSubscriberModule_1,
            imports: [core_1.DiscoveryModule, rabbitmq_module_1.RabbitMQModule.registerAsync(options)],
            providers: [
                event_subscriber_explorer_service_1.EventSubscriberExplorerService,
                core_1.MetadataScanner,
                core_1.Reflector,
            ],
            exports: [],
        };
    }
};
exports.EventSubscriberModule = EventSubscriberModule;
exports.EventSubscriberModule = EventSubscriberModule = EventSubscriberModule_1 = __decorate([
    (0, common_1.Module)({})
], EventSubscriberModule);
//# sourceMappingURL=event-subscriber.module.js.map