(()=>{var e={};e.id=4982,e.ids=[4982],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},11951:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>a.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>l,routeModule:()=>p,tree:()=>c});var r=s(67096),o=s(16132),n=s(37284),a=s.n(n),i=s(32564),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);s.d(t,d);let c=["",{children:["stores",{children:["[username]",{children:["products",{children:["[productId]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,66722)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\stores\\[username]\\products\\[productId]\\edit\\page.tsx"]}]},{}]},{}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9291,23)),"next/dist/client/components/not-found-error"]}],l=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\stores\\[username]\\products\\[productId]\\edit\\page.tsx"],u="/stores/[username]/products/[productId]/edit/page",m={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/stores/[username]/products/[productId]/edit/page",pathname:"/stores/[username]/products/[productId]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},83711:(e,t,s)=>{Promise.resolve().then(s.bind(s,50251))},50251:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>EditProductPage});var r=s(30784);s(9885);var o=s(57114),n=s(41245),a=s(59872),i=s(77783),d=s(48042);function EditProductPage({params:e}){let{username:t,productId:s}=e,c=(0,o.useRouter)(),{data:l,isLoading:u}=(0,i.YU)(t),{data:m,isLoading:p}=(0,d.vL)(s,{skip:!l});return u||p?r.jsx("div",{className:"min-h-screen p-6 md:p-12",children:(0,r.jsxs)("div",{className:"max-w-3xl mx-auto text-center",children:[r.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600 mx-auto"}),r.jsx("p",{className:"mt-4 text-lg",children:"Loading..."})]})}):l&&m?m.storeId!==l.id?r.jsx("div",{className:"min-h-screen p-6 md:p-12",children:(0,r.jsxs)("div",{className:"max-w-3xl mx-auto text-center",children:[r.jsx("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:"Permission Denied"}),r.jsx("p",{className:"mb-6",children:"This product doesn't belong to the specified store."}),r.jsx(a.Z,{onClick:()=>c.push(`/stores/${t}`),children:"Back to Store"})]})}):r.jsx("div",{className:"min-h-screen p-6 md:p-12",children:(0,r.jsxs)("div",{className:"max-w-3xl mx-auto",children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold mb-8",children:["Edit Product: ",m.title]}),r.jsx(n.Z,{storeId:l.id,storeUsername:l.username,product:m,isEditing:!0})]})}):r.jsx("div",{className:"min-h-screen p-6 md:p-12",children:(0,r.jsxs)("div",{className:"max-w-3xl mx-auto text-center",children:[r.jsx("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:"Not Found"}),r.jsx("p",{className:"mb-6",children:"The store or product you're looking for doesn't exist or you don't have permission to edit it."}),r.jsx(a.Z,{onClick:()=>c.push("/stores"),children:"Back to Stores"})]})})}},66722:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>a,__esModule:()=>n,default:()=>d});var r=s(95153);let o=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\stores\[username]\products\[productId]\edit\page.tsx`),{__esModule:n,$$typeof:a}=o,i=o.default,d=i}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),s=t.X(0,[2103,2765,706,8042,7783,1245],()=>__webpack_exec__(11951));module.exports=s})();