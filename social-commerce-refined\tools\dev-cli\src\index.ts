#!/usr/bin/env node

import { Command } from 'commander';
import * as figlet from 'figlet';
import * as chalk from 'chalk';
import * as path from 'path';
import * as fs from 'fs-extra';
import { startCommand } from './commands/start';
import { stopCommand } from './commands/stop';
import { statusCommand } from './commands/status';
import { installCommand } from './commands/install';
import { buildCommand } from './commands/build';
import { testCommand } from './commands/test';
import { generateCommand } from './commands/generate';

// Get package.json version
const packageJsonPath = path.join(__dirname, '..', 'package.json');
const packageJson = fs.readJsonSync(packageJsonPath);

// Create CLI program
const program = new Command();

// Display banner
console.log(
  chalk.cyan(
    figlet.textSync('Social Commerce Dev CLI', {
      horizontalLayout: 'full',
    }),
  ),
);

// Set program info
program
  .name('dev')
  .description('Social Commerce Platform Development CLI')
  .version(packageJson.version);

// Register commands
startCommand(program);
stopCommand(program);
statusCommand(program);
installCommand(program);
buildCommand(program);
testCommand(program);
generateCommand(program);

// Add help command
program
  .command('help')
  .description('Display help information')
  .action(() => {
    program.outputHelp();
  });

// Parse arguments
program.parse(process.argv);

// If no arguments, show help
if (!process.argv.slice(2).length) {
  program.outputHelp();
}
