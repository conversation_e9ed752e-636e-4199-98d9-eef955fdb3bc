import { Test, TestingModule } from '@nestjs/testing';
import { ProfileController } from './profile.controller';
import { ProfileService } from '../services/profile.service';
import { Profile } from '../entities/profile.entity';
import { CreateProfileDto } from '../dto/create-profile.dto';
import { UpdateProfileDto } from '../dto/update-profile.dto';

describe('ProfileController', () => {
  let controller: ProfileController;
  let profileService: ProfileService;

  beforeEach(async () => {
    // Create mock implementation
    const mockProfileService = {
      findAll: jest.fn(),
      findOne: jest.fn(),
      findByUserId: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      updateByUserId: jest.fn(),
      remove: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [ProfileController],
      providers: [
        {
          provide: ProfileService,
          useValue: mockProfileService,
        },
      ],
    }).compile();

    controller = module.get<ProfileController>(ProfileController);
    profileService = module.get<ProfileService>(ProfileService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return all profiles', async () => {
      // Arrange
      const profiles = [
        { id: '1', firstName: 'John', lastName: 'Doe' },
        { id: '2', firstName: 'Jane', lastName: 'Smith' },
      ] as Profile[];

      jest.spyOn(profileService, 'findAll').mockResolvedValue(profiles);

      // Act
      const result = await controller.findAll();

      // Assert
      expect(result).toEqual(profiles);
      expect(profileService.findAll).toHaveBeenCalled();
    });
  });

  describe('findOne', () => {
    it('should return a profile by ID', async () => {
      // Arrange
      const id = '123';
      const profile = {
        id,
        firstName: 'John',
        lastName: 'Doe',
      } as Profile;

      jest.spyOn(profileService, 'findOne').mockResolvedValue(profile);

      // Act
      const result = await controller.findOne(id);

      // Assert
      expect(result).toEqual(profile);
      expect(profileService.findOne).toHaveBeenCalledWith(id);
    });
  });

  describe('findByUserId', () => {
    it('should return a profile by user ID', async () => {
      // Arrange
      const userId = '123';
      const profile = {
        id: '456',
        firstName: 'John',
        lastName: 'Doe',
      } as Profile;

      jest.spyOn(profileService, 'findByUserId').mockResolvedValue(profile);

      // Act
      const result = await controller.findByUserId(userId);

      // Assert
      expect(result).toEqual(profile);
      expect(profileService.findByUserId).toHaveBeenCalledWith(userId);
    });
  });

  describe('create', () => {
    it('should create a profile for a user', async () => {
      // Arrange
      const userId = '123';
      const createProfileDto: CreateProfileDto = {
        firstName: 'John',
        lastName: 'Doe',
      };
      const profile = {
        id: '456',
        ...createProfileDto,
      } as Profile;

      jest.spyOn(profileService, 'create').mockResolvedValue(profile);

      // Act
      const result = await controller.create(userId, createProfileDto);

      // Assert
      expect(result).toEqual(profile);
      expect(profileService.create).toHaveBeenCalledWith(userId, createProfileDto);
    });
  });

  describe('update', () => {
    it('should update a profile', async () => {
      // Arrange
      const id = '123';
      const updateProfileDto: UpdateProfileDto = {
        firstName: 'Jane',
        lastName: 'Smith',
      };
      const updatedProfile = {
        id,
        ...updateProfileDto,
      } as Profile;

      jest.spyOn(profileService, 'update').mockResolvedValue(updatedProfile);

      // Act
      const result = await controller.update(id, updateProfileDto);

      // Assert
      expect(result).toEqual(updatedProfile);
      expect(profileService.update).toHaveBeenCalledWith(id, updateProfileDto);
    });
  });

  describe('updateByUserId', () => {
    it('should update a profile by user ID', async () => {
      // Arrange
      const userId = '123';
      const updateProfileDto: UpdateProfileDto = {
        firstName: 'Jane',
        lastName: 'Smith',
      };
      const updatedProfile = {
        id: '456',
        ...updateProfileDto,
      } as Profile;

      jest.spyOn(profileService, 'updateByUserId').mockResolvedValue(updatedProfile);

      // Act
      const result = await controller.updateByUserId(userId, updateProfileDto);

      // Assert
      expect(result).toEqual(updatedProfile);
      expect(profileService.updateByUserId).toHaveBeenCalledWith(userId, updateProfileDto);
    });
  });

  describe('remove', () => {
    it('should remove a profile', async () => {
      // Arrange
      const id = '123';
      jest.spyOn(profileService, 'remove').mockResolvedValue(undefined);

      // Act
      await controller.remove(id);

      // Assert
      expect(profileService.remove).toHaveBeenCalledWith(id);
    });
  });

  describe('microservice endpoints', () => {
    it('should find a profile by user ID via microservice', async () => {
      // Arrange
      const userId = '123';
      const profile = {
        id: '456',
        firstName: 'John',
        lastName: 'Doe',
      } as Profile;

      jest.spyOn(profileService, 'findByUserId').mockResolvedValue(profile);

      // Act
      const result = await controller.findProfileByUserId(userId);

      // Assert
      expect(result).toEqual(profile);
      expect(profileService.findByUserId).toHaveBeenCalledWith(userId);
    });

    it('should create a profile via microservice', async () => {
      // Arrange
      const data = {
        userId: '123',
        profile: {
          firstName: 'John',
          lastName: 'Doe',
        },
      };
      const profile = {
        id: '456',
        ...data.profile,
      } as Profile;

      jest.spyOn(profileService, 'create').mockResolvedValue(profile);

      // Act
      const result = await controller.createProfile(data);

      // Assert
      expect(result).toEqual(profile);
      expect(profileService.create).toHaveBeenCalledWith(data.userId, data.profile);
    });

    it('should update a profile via microservice', async () => {
      // Arrange
      const data = {
        userId: '123',
        profile: {
          firstName: 'Jane',
          lastName: 'Smith',
        },
      };
      const updatedProfile = {
        id: '456',
        ...data.profile,
      } as Profile;

      jest.spyOn(profileService, 'updateByUserId').mockResolvedValue(updatedProfile);

      // Act
      const result = await controller.updateProfile(data);

      // Assert
      expect(result).toEqual(updatedProfile);
      expect(profileService.updateByUserId).toHaveBeenCalledWith(data.userId, data.profile);
    });
  });
});
