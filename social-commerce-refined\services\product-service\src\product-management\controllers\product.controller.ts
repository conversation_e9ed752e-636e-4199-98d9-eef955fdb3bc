import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  Logger,
  ParseUUIDPipe,
  ParseIntPipe,
  DefaultValuePipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { MessagePattern } from '@nestjs/microservices';
import { ProductService } from '../services/product.service';
import { CreateProductDto, UpdateProductDto } from '../dto';
import { Product, ProductStatus } from '../entities/product.entity';
import { JwtAuthGuard } from '../../shared/guards/jwt-auth.guard';
import { ProductFilters, ProductSearchOptions } from '../repositories/product.repository';

@ApiTags('products')
@Controller('products')
export class ProductController {
  private readonly logger = new Logger(ProductController.name);

  constructor(private readonly productService: ProductService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new product' })
  @ApiResponse({
    status: 201,
    description: 'Product created successfully',
    type: Product,
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async create(@Body() createProductDto: CreateProductDto, @Request() req: any): Promise<Product> {
    this.logger.log(`Creating product for store ${createProductDto.storeId} by user ${req.user.id}`);
    return this.productService.create(createProductDto, req.user.id);
  }

  @Get()
  @ApiOperation({ summary: 'Get all products with filtering and pagination' })
  @ApiResponse({
    status: 200,
    description: 'Products retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        products: { type: 'array', items: { $ref: '#/components/schemas/Product' } },
        total: { type: 'number' },
        page: { type: 'number' },
        limit: { type: 'number' },
      },
    },
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiQuery({ name: 'storeId', required: false, type: String, description: 'Filter by store ID' })
  @ApiQuery({ name: 'categoryId', required: false, type: String, description: 'Filter by category ID' })
  @ApiQuery({ name: 'status', required: false, enum: ProductStatus, description: 'Filter by status' })
  @ApiQuery({ name: 'minPrice', required: false, type: Number, description: 'Minimum price filter' })
  @ApiQuery({ name: 'maxPrice', required: false, type: Number, description: 'Maximum price filter' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search in name and description' })
  @ApiQuery({ name: 'inStock', required: false, type: Boolean, description: 'Filter products in stock' })
  @ApiQuery({ name: 'sortBy', required: false, enum: ['name', 'price', 'createdAt', 'rating', 'salesCount'], description: 'Sort by field' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['ASC', 'DESC'], description: 'Sort order' })
  async findAll(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('storeId') storeId?: string,
    @Query('categoryId') categoryId?: string,
    @Query('status') status?: ProductStatus,
    @Query('minPrice') minPrice?: number,
    @Query('maxPrice') maxPrice?: number,
    @Query('search') search?: string,
    @Query('inStock') inStock?: boolean,
    @Query('sortBy') sortBy?: 'name' | 'price' | 'createdAt' | 'rating' | 'salesCount',
    @Query('sortOrder') sortOrder?: 'ASC' | 'DESC',
  ) {
    this.logger.log('Finding products with filters');

    const filters: ProductFilters = {
      storeId,
      categoryId,
      status,
      minPrice,
      maxPrice,
      search,
      inStock,
    };

    const options: ProductSearchOptions = {
      page,
      limit,
      sortBy,
      sortOrder,
    };

    return this.productService.findAll(filters, options);
  }

  @Get('search')
  @ApiOperation({ summary: 'Search products' })
  @ApiResponse({
    status: 200,
    description: 'Search results retrieved successfully',
  })
  @ApiQuery({ name: 'q', required: true, type: String, description: 'Search query' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  async search(
    @Query('q') query: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('storeId') storeId?: string,
    @Query('categoryId') categoryId?: string,
    @Query('minPrice') minPrice?: number,
    @Query('maxPrice') maxPrice?: number,
  ) {
    this.logger.log(`Searching products with query: "${query}"`);

    const filters: ProductFilters = {
      storeId,
      categoryId,
      minPrice,
      maxPrice,
    };

    const options: ProductSearchOptions = {
      page,
      limit,
    };

    return this.productService.search(query, filters, options);
  }

  @Get('limits/:storeId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get product creation limits for a store' })
  @ApiResponse({
    status: 200,
    description: 'Product limits retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        maxProducts: { type: 'number', example: 100 },
        currentProducts: { type: 'number', example: 25 },
        remainingProducts: { type: 'number', example: 75 },
        canCreateMore: { type: 'boolean', example: true },
      },
    },
  })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  async getProductLimits(@Param('storeId', ParseUUIDPipe) storeId: string) {
    return this.productService.getProductLimits(storeId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a product by ID' })
  @ApiResponse({
    status: 200,
    description: 'Product retrieved successfully',
    type: Product,
  })
  @ApiResponse({ status: 404, description: 'Product not found' })
  @ApiParam({ name: 'id', description: 'Product ID' })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<Product> {
    this.logger.log(`Finding product with ID: ${id}`);
    return this.productService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update a product' })
  @ApiResponse({
    status: 200,
    description: 'Product updated successfully',
    type: Product,
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Product not found' })
  @ApiParam({ name: 'id', description: 'Product ID' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateProductDto: UpdateProductDto,
    @Request() req: any,
  ): Promise<Product> {
    this.logger.log(`Updating product ${id} by user ${req.user.id}`);
    return this.productService.update(id, updateProductDto, req.user.id);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete a product' })
  @ApiResponse({ status: 200, description: 'Product deleted successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Product not found' })
  @ApiParam({ name: 'id', description: 'Product ID' })
  async remove(@Param('id', ParseUUIDPipe) id: string, @Request() req: any): Promise<void> {
    this.logger.log(`Removing product ${id} by user ${req.user.id}`);
    return this.productService.remove(id, req.user.id);
  }

  // ============================================================================
  // MICROSERVICE MESSAGE HANDLERS
  // ============================================================================

  @MessagePattern('find_product_by_id')
  async findProductById(productId: string): Promise<Product> {
    this.logger.log(`[MICROSERVICE] Finding product by ID: ${productId}`);
    try {
      return await this.productService.findOne(productId);
    } catch (error) {
      this.logger.error(`[MICROSERVICE] Error finding product ${productId}:`, error.message);
      throw error;
    }
  }

  @MessagePattern('find_all_products')
  async findAllProducts(data: { filters?: any; options?: any }) {
    this.logger.log('[MICROSERVICE] Finding all products');
    try {
      return await this.productService.findAll(data.filters || {}, data.options || {});
    } catch (error) {
      this.logger.error('[MICROSERVICE] Error finding all products:', error.message);
      throw error;
    }
  }

  @MessagePattern('find_products_by_store_id')
  async findProductsByStoreId(storeId: string) {
    this.logger.log(`[MICROSERVICE] Finding products by store ID: ${storeId}`);
    try {
      const filters = { storeId };
      return await this.productService.findAll(filters, {});
    } catch (error) {
      this.logger.error(`[MICROSERVICE] Error finding products for store ${storeId}:`, error.message);
      throw error;
    }
  }

  @MessagePattern('create_product')
  async createProductMessage(data: { createProductDto: CreateProductDto; userId: string }) {
    this.logger.log(`[MICROSERVICE] Creating product for user: ${data.userId}`);
    try {
      return await this.productService.create(data.createProductDto, data.userId);
    } catch (error) {
      this.logger.error('[MICROSERVICE] Error creating product:', error.message);
      throw error;
    }
  }

  @MessagePattern('update_product')
  async updateProductMessage(data: { id: string; updateProductDto: UpdateProductDto; userId: string }) {
    this.logger.log(`[MICROSERVICE] Updating product ${data.id} for user: ${data.userId}`);
    try {
      return await this.productService.update(data.id, data.updateProductDto, data.userId);
    } catch (error) {
      this.logger.error(`[MICROSERVICE] Error updating product ${data.id}:`, error.message);
      throw error;
    }
  }

  @MessagePattern('delete_product')
  async deleteProductMessage(data: { id: string; userId: string }) {
    this.logger.log(`[MICROSERVICE] Deleting product ${data.id} for user: ${data.userId}`);
    try {
      return await this.productService.remove(data.id, data.userId);
    } catch (error) {
      this.logger.error(`[MICROSERVICE] Error deleting product ${data.id}:`, error.message);
      throw error;
    }
  }
}
