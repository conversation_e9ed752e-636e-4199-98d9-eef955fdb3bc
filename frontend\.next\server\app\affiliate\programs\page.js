(()=>{var e={};e.id=9673,e.ids=[9673],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},22445:(e,r,a)=>{"use strict";a.r(r),a.d(r,{GlobalError:()=>o.a,__next_app__:()=>g,originalPathname:()=>c,pages:()=>m,routeModule:()=>x,tree:()=>l});var s=a(67096),t=a(16132),i=a(37284),o=a.n(i),n=a(32564),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);a.d(r,d);let l=["",{children:["affiliate",{children:["programs",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,9492)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\affiliate\\programs\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,9291,23)),"next/dist/client/components/not-found-error"]}],m=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\affiliate\\programs\\page.tsx"],c="/affiliate/programs/page",g={require:a,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/affiliate/programs/page",pathname:"/affiliate/programs",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},69953:(e,r,a)=>{Promise.resolve().then(a.bind(a,84435))},84435:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>AffiliateProgramsPage});var s=a(30784),t=a(9885),i=a(27870),o=a(14379),n=a(59872),d=a(11440),l=a.n(d),m=a(52451),c=a.n(m),g=a(26352),x=a(56663);let affiliate_AffiliateCard=({program:e,isJoined:r=!1,onJoin:a,isJoining:t=!1,className:d=""})=>{let{t:m}=(0,i.$G)("affiliate"),{isRtl:p}=(0,o.g)();return(0,s.jsxs)("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden ${d}`,children:[(0,s.jsxs)("div",{className:"bg-gradient-to-r from-primary-500/10 to-primary-600/10 dark:from-primary-900/20 dark:to-primary-800/20 p-4 flex items-center",children:[s.jsx("div",{className:"flex-shrink-0 mr-4",children:e.store?.logoUrl?s.jsx("div",{className:"w-12 h-12 relative rounded-full overflow-hidden",children:s.jsx(c(),{src:e.store.logoUrl,alt:e.store.name,fill:!0,className:"object-cover"})}):s.jsx("div",{className:"w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center",children:s.jsx("span",{className:"text-primary-600 dark:text-primary-400 text-lg font-bold",children:e.store?.name?.charAt(0)||e.name.charAt(0)})})}),(0,s.jsxs)("div",{className:"flex-1",children:[s.jsx("h3",{className:"text-lg font-bold text-gray-900 dark:text-gray-100",children:e.name}),s.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.store?.name||m("unknownStore","Unknown Store")})]}),e.isActive?s.jsx("span",{className:"px-2 py-1 text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 rounded-full",children:m("active","Active")}):s.jsx("span",{className:"px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400 rounded-full",children:m("inactive","Inactive")})]}),(0,s.jsxs)("div",{className:"p-4",children:[e.description&&s.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 mb-4 line-clamp-2",children:e.description}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[(0,s.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-900 p-3 rounded-lg",children:[s.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mb-1",children:m("commission","Commission")}),s.jsx("p",{className:"text-lg font-bold text-primary-600 dark:text-primary-400",children:e.commissionType===g.RE.PERCENTAGE?`${e.commissionValue}%`:(0,x.xG)(e.commissionValue)}),s.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:e.commissionType===g.RE.PERCENTAGE?m("perSale","per sale"):m("perItem","per item")})]}),(0,s.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-900 p-3 rounded-lg",children:[s.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mb-1",children:m("cookieDuration","Cookie Duration")}),s.jsx("p",{className:"text-lg font-bold text-gray-900 dark:text-gray-100",children:e.cookieDuration}),s.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:m("days","days")})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("span",{className:"font-medium",children:[m("minPayout","Min. Payout"),":"]})," ",(0,x.xG)(e.minimumPayoutAmount)]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("span",{className:"font-medium",children:[m("payoutFrequency","Payout"),":"]})," ",m(`payoutFrequency.${e.payoutFrequency}`,e.payoutFrequency)]})]}),(0,s.jsxs)("div",{className:"flex space-x-3",children:[r?s.jsx(l(),{href:`/affiliate/dashboard/${e.id}`,className:"flex-1",children:s.jsx(n.Z,{variant:"outline",fullWidth:!0,children:m("manageCampaign","Manage Campaign")})}):s.jsx(n.Z,{variant:"primary",fullWidth:!0,onClick:()=>a?.(e.id),isLoading:t,disabled:t||!e.isActive,children:m("joinProgram","Join Program")}),s.jsx(l(),{href:`/affiliate/programs/${e.id}`,children:s.jsx(n.Z,{variant:"outline",children:m("details","Details")})})]})]})]})};var p=a(3619);function AffiliateProgramsPage(){let{t:e}=(0,i.$G)("affiliate"),{isRtl:r}=(0,o.g)(),[a,d]=(0,t.useState)("available"),{data:l,isLoading:m}=(0,p.PF)({isActive:!0}),{data:c,isLoading:g}=(0,p.nK)({}),[x,{isLoading:h}]=(0,p.TW)(),handleJoinProgram=async e=>{try{await x({programId:e}).unwrap()}catch(e){console.error("Failed to join program:",e)}},hasJoinedProgram=e=>!!c?.accounts&&c.accounts.some(r=>r.programId===e),y=l?.programs?.filter(e=>"available"===a?!hasJoinedProgram(e.id):hasJoinedProgram(e.id))||[];return s.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsxs)("div",{className:"mb-8",children:[s.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2",children:e("programs.title","Affiliate Programs")}),s.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:e("programs.description","Join affiliate programs to earn commissions")})]}),s.jsx("div",{className:"border-b border-gray-200 dark:border-gray-700 mb-6",children:(0,s.jsxs)("nav",{className:"-mb-px flex space-x-8","aria-label":"Tabs",children:[s.jsx("button",{onClick:()=>d("available"),className:`${"available"===a?"border-primary-500 text-primary-600 dark:text-primary-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600"} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`,children:e("programs.available","Available Programs")}),s.jsx("button",{onClick:()=>d("joined"),className:`${"joined"===a?"border-primary-500 text-primary-600 dark:text-primary-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600"} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`,children:e("programs.joined","Joined Programs")})]})}),m||g?s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:3}).map((e,r)=>(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden animate-pulse",children:[s.jsx("div",{className:"h-16 bg-gray-200 dark:bg-gray-700"}),(0,s.jsxs)("div",{className:"p-4",children:[s.jsx("div",{className:"h-6 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-3"}),s.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-4"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[s.jsx("div",{className:"h-20 bg-gray-200 dark:bg-gray-700 rounded"}),s.jsx("div",{className:"h-20 bg-gray-200 dark:bg-gray-700 rounded"})]}),s.jsx("div",{className:"h-10 bg-gray-200 dark:bg-gray-700 rounded"})]})]},r))}):0===y.length?(0,s.jsxs)("div",{className:"text-center py-12",children:[s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-16 w-16 text-gray-400 dark:text-gray-600 mx-auto mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})}),s.jsx("h3",{className:"text-xl font-medium text-gray-900 dark:text-gray-100 mb-2",children:"available"===a?e("programs.noAvailablePrograms","No available programs found"):e("programs.noJoinedPrograms","You haven't joined any programs yet")}),"joined"===a&&s.jsx("div",{className:"mt-4",children:s.jsx(n.Z,{variant:"primary",onClick:()=>d("available"),children:e("programs.browsePrograms","Browse Available Programs")})})]}):s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:y.map(e=>s.jsx(affiliate_AffiliateCard,{program:e,isJoined:hasJoinedProgram(e.id),onJoin:handleJoinProgram,isJoining:h},e.id))})]})})}},9492:(e,r,a)=>{"use strict";a.r(r),a.d(r,{$$typeof:()=>o,__esModule:()=>i,default:()=>d});var s=a(95153);let t=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\affiliate\programs\page.tsx`),{__esModule:i,$$typeof:o}=t,n=t.default,d=n}};var r=require("../../../webpack-runtime.js");r.C(e);var __webpack_exec__=e=>r(r.s=e),a=r.X(0,[2103,2765,3619,6663,6352],()=>__webpack_exec__(22445));module.exports=a})();