"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.buildCommand = buildCommand;
const chalk = require("chalk");
const inquirer = require("inquirer");
const paths_1 = require("../utils/paths");
const npm_1 = require("../utils/npm");
function buildCommand(program) {
    program
        .command('build [service]')
        .description('Build services')
        .option('-a, --all', 'Build all services')
        .action(async (service, options) => {
        try {
            if (!service && !options.all) {
                const services = (0, paths_1.getAllServices)();
                if (services.length === 0) {
                    console.log(chalk.yellow('No services found'));
                    return;
                }
                const answers = await inquirer.prompt([
                    {
                        type: 'list',
                        name: 'service',
                        message: 'Which service do you want to build?',
                        choices: [...services, 'all'],
                    },
                ]);
                if (answers.service === 'all') {
                    options.all = true;
                }
                else {
                    service = answers.service;
                }
            }
            if (options.all) {
                const services = (0, paths_1.getAllServices)();
                if (services.length === 0) {
                    console.log(chalk.yellow('No services found'));
                    return;
                }
                console.log(chalk.blue(`Building all services: ${services.join(', ')}...`));
                for (const svc of services) {
                    try {
                        await (0, npm_1.buildService)(svc);
                    }
                    catch (error) {
                        console.error(chalk.red(`Error building ${svc}-service: ${error.message}`));
                    }
                }
                console.log(chalk.green('All services built successfully'));
                return;
            }
            if (service) {
                if (!(0, paths_1.serviceExists)(service)) {
                    console.error(chalk.red(`Service ${service}-service does not exist`));
                    return;
                }
                console.log(chalk.blue(`Building ${service}-service...`));
                await (0, npm_1.buildService)(service);
                console.log(chalk.green(`${service}-service built successfully`));
                return;
            }
        }
        catch (error) {
            console.error(chalk.red(`Error: ${error.message}`));
            process.exit(1);
        }
    });
}
//# sourceMappingURL=build.js.map