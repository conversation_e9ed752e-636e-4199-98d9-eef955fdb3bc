# Store Service

The Store Service is responsible for managing stores and products in the Social Commerce Platform.

## Features

- **Store Management**: Create, read, update, and delete stores
- **Product Management**: Create, read, update, and delete products
- **Store Analytics**: Track store followers, ratings, and reviews
- **Product Analytics**: Track product views, sales, ratings, and reviews

## Architecture

The Store Service follows a feature-based organization pattern:

```
src/
├── store-management/       # Store management feature
│   ├── controllers/        # REST API controllers
│   ├── services/           # Business logic
│   ├── repositories/       # Data access
│   ├── entities/           # Database entities
│   └── dto/                # Data transfer objects
├── product-management/     # Product management feature
│   ├── controllers/
│   ├── services/
│   ├── repositories/
│   ├── entities/
│   └── dto/
└── shared/                 # Shared modules and utilities
    ├── controllers/
    ├── guards/
    ├── decorators/
    ├── filters/
    ├── interceptors/
    ├── middleware/
    └── utils/
```

## API Endpoints

### Store Management

- `GET /stores` - Get all stores
- `GET /stores/:id` - Get a store by ID
- `GET /stores/owner/:ownerId` - Get stores by owner ID
- `POST /stores` - Create a new store
- `PUT /stores/:id` - Update a store
- `DELETE /stores/:id` - Delete a store

### Product Management

- `GET /products` - Get all products
- `GET /products/:id` - Get a product by ID
- `GET /products/store/:storeId` - Get products by store ID
- `POST /products` - Create a new product
- `PUT /products/:id` - Update a product
- `DELETE /products/:id` - Delete a product

### Health

- `GET /health` - Check service health

## Microservice Endpoints

The Store Service also exposes microservice endpoints for internal communication:

### Store Management

- `find_all_stores` - Find all stores
- `find_store_by_id` - Find a store by ID
- `find_stores_by_owner_id` - Find stores by owner ID
- `create_store` - Create a new store
- `update_store` - Update a store
- `remove_store` - Delete a store
- `increment_follower_count` - Increment the follower count of a store
- `decrement_follower_count` - Decrement the follower count of a store
- `update_store_rating` - Update the rating of a store

### Product Management

- `find_all_products` - Find all products
- `find_product_by_id` - Find a product by ID
- `find_products_by_store_id` - Find products by store ID
- `create_product` - Create a new product
- `update_product` - Update a product
- `remove_product` - Delete a product
- `update_product_rating` - Update the rating of a product
- `increment_product_sales` - Increment the sales count of a product
- `update_product_quantity` - Update the quantity of a product

## Getting Started

### Prerequisites

- Node.js (v18 or later)
- PostgreSQL (v14 or later)

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   cd services/store-service
   npm install
   ```
3. Configure environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```
4. Start the service:
   ```bash
   npm run start:dev
   ```

### Docker

You can also run the service using Docker:

```bash
docker build -t store-service .
docker run -p 3002:3002 store-service
```

## Testing

```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# Test coverage
npm run test:cov
```

## Documentation

API documentation is available at `/api/docs` when the service is running.

## License

MIT
