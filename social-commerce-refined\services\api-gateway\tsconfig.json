{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "es2017", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@app/common": ["../../libs/common/src"], "@app/common/*": ["../../libs/common/src/*"], "@app/messaging": ["../../libs/messaging/src"], "@app/messaging/*": ["../../libs/messaging/src/*"]}}, "exclude": ["**/*.spec.ts", "**/*.test.ts", "test/**/*", "src/**/*.spec.ts", "src/**/*.test.ts"]}