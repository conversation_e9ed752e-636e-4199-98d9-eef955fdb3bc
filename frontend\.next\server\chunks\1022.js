"use strict";exports.id=1022,exports.ids=[1022],exports.modules={21022:(e,s,i)=>{i.d(s,{Z:()=>wishlist_WishlistItemList});var r=i(30784),a=i(9885),t=i(52451),l=i.n(t),d=i(11440),n=i.n(d),o=i(59872);let wishlist_WishlistItem=({item:e,onRemove:s,onMoveToCart:i,isRemoving:a=!1})=>{var t;let{id:d,product:c}=e;return c?(0,r.jsxs)("div",{className:"flex items-center py-4 border-b border-gray-200 dark:border-gray-700",children:[r.jsx("div",{className:"flex-shrink-0 h-20 w-20 bg-gray-200 dark:bg-gray-700 rounded-md overflow-hidden relative",children:c.mediaUrls&&c.mediaUrls.length>0?r.jsx(l(),{src:c.mediaUrls[0],alt:c.title,fill:!0,className:"object-cover"}):r.jsx("div",{className:"flex items-center justify-center h-full text-gray-400",children:"No image"})}),(0,r.jsxs)("div",{className:"ml-4 flex-1 min-w-0",children:[r.jsx(n(),{href:`/products/${c.id}`,className:"text-sm font-medium text-gray-900 dark:text-gray-100 hover:text-primary-600 dark:hover:text-primary-400",children:c.title}),r.jsx("p",{className:"mt-1 text-sm text-primary-600 dark:text-primary-400 font-medium",children:void 0===(t=c.price)?"Contact for price":new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(t)})]}),(0,r.jsxs)("div",{className:"ml-4 flex-shrink-0 flex flex-col sm:flex-row gap-2",children:[i&&r.jsx(o.Z,{size:"sm",onClick:()=>i(c.id),children:"Add to Cart"}),r.jsx(o.Z,{size:"sm",variant:"outline",onClick:()=>s(d),isLoading:a,children:"Remove"})]})]}):null},wishlist_WishlistItemList=({items:e,isLoading:s=!1,onRemoveItem:i,onMoveToCart:t,onClearWishlist:l,emptyMessage:d="Your wishlist is empty"})=>{let[n,c]=(0,a.useState)([]),[m,h]=(0,a.useState)(!1),handleRemoveItem=async e=>{c(s=>[...s,e]);try{await i(e)}catch(e){console.error("Failed to remove item:",e)}finally{c(s=>s.filter(s=>s!==e))}},handleMoveToCart=async e=>{if(t)try{await t(e)}catch(e){console.error("Failed to move item to cart:",e)}},handleClearWishlist=async()=>{if(l&&window.confirm("Are you sure you want to clear your wishlist?")){h(!0);try{await l()}catch(e){console.error("Failed to clear wishlist:",e)}finally{h(!1)}}};return s?r.jsx("div",{className:"animate-pulse space-y-4",children:[void 0,void 0,void 0].map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center py-4",children:[r.jsx("div",{className:"h-20 w-20 bg-gray-200 dark:bg-gray-700 rounded-md"}),(0,r.jsxs)("div",{className:"ml-4 flex-1",children:[r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"}),r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"})]}),r.jsx("div",{className:"ml-4 h-8 w-20 bg-gray-200 dark:bg-gray-700 rounded"})]},s))}):e&&0!==e.length?(0,r.jsxs)("div",{children:[l&&e.length>1&&r.jsx("div",{className:"flex justify-end mb-4",children:r.jsx(o.Z,{variant:"outline",size:"sm",onClick:handleClearWishlist,isLoading:m,children:"Clear Wishlist"})}),r.jsx("div",{children:e.map(e=>r.jsx(wishlist_WishlistItem,{item:e,onRemove:handleRemoveItem,onMoveToCart:t?handleMoveToCart:void 0,isRemoving:n.includes(e.id)},e.id))})]}):r.jsx("div",{className:"py-8 text-center",children:r.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:d})})}}};