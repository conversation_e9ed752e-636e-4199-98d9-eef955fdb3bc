"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var EventPublisherService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventPublisherService = void 0;
const common_1 = require("@nestjs/common");
const rabbitmq_service_1 = require("./rabbitmq.service");
let EventPublisherService = EventPublisherService_1 = class EventPublisherService {
    constructor(rabbitMQService) {
        this.rabbitMQService = rabbitMQService;
        this.logger = new common_1.Logger(EventPublisherService_1.name);
        this.exchange = 'events';
        this.initialize();
    }
    async initialize() {
        try {
            await this.rabbitMQService.createExchange(this.exchange, 'topic', {
                durable: true,
            });
            this.logger.log(`Exchange ${this.exchange} initialized`);
        }
        catch (error) {
            this.logger.error(`Error initializing event publisher: ${error.message}`);
        }
    }
    async publish(event) {
        try {
            const routingKey = event.type;
            const result = await this.rabbitMQService.publish(this.exchange, routingKey, event);
            this.logger.debug(`Event ${event.type} published with ID ${event.id}`);
            return result;
        }
        catch (error) {
            this.logger.error(`Error publishing event ${event.type}: ${error.message}`);
            throw error;
        }
    }
};
exports.EventPublisherService = EventPublisherService;
exports.EventPublisherService = EventPublisherService = EventPublisherService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [rabbitmq_service_1.RabbitMQService])
], EventPublisherService);
//# sourceMappingURL=event-publisher.service.js.map