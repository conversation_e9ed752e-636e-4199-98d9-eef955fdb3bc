import { <PERSON>, Get, Post, Body, Param, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { MessagePattern } from '@nestjs/microservices';
import { NotificationService } from '../services/notification.service';
import { CreateNotificationDto } from '../dto/create-notification.dto';
import { Notification } from '../entities/notification.entity';

@ApiTags('notifications')
@Controller('notifications')
export class NotificationController {
  private readonly logger = new Logger(NotificationController.name);

  constructor(private readonly notificationService: NotificationService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new notification' })
  @ApiResponse({ status: 201, description: 'Notification created successfully' })
  async create(@Body() createNotificationDto: CreateNotificationDto): Promise<Notification> {
    this.logger.log('Creating notification via HTTP');
    return this.notificationService.create(createNotificationDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all notifications' })
  @ApiResponse({ status: 200, description: 'List of all notifications' })
  async findAll(): Promise<Notification[]> {
    return this.notificationService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get notification by ID' })
  @ApiResponse({ status: 200, description: 'Notification found' })
  @ApiResponse({ status: 404, description: 'Notification not found' })
  async findOne(@Param('id') id: string): Promise<Notification> {
    return this.notificationService.findById(id);
  }

  @Get('user/:userId')
  @ApiOperation({ summary: 'Get notifications for a user' })
  @ApiResponse({ status: 200, description: 'User notifications' })
  async findByUser(@Param('userId') userId: string): Promise<Notification[]> {
    return this.notificationService.findByUserId(userId);
  }

  // Microservice message patterns
  @MessagePattern('create_notification')
  async createNotificationMessage(data: CreateNotificationDto): Promise<Notification> {
    this.logger.log('Creating notification via microservice');
    return this.notificationService.create(data);
  }

  @MessagePattern('find_notification')
  async findNotificationMessage(id: string): Promise<Notification> {
    return this.notificationService.findById(id);
  }

  @MessagePattern('find_user_notifications')
  async findUserNotificationsMessage(userId: string): Promise<Notification[]> {
    return this.notificationService.findByUserId(userId);
  }
}
