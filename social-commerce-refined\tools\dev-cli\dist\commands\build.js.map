{"version": 3, "file": "build.js", "sourceRoot": "", "sources": ["../../src/commands/build.ts"], "names": [], "mappings": ";;AAMA,oCAwEC;AA7ED,+BAA+B;AAC/B,qCAAqC;AACrC,0CAA+D;AAC/D,sCAA4C;AAE5C,SAAgB,YAAY,CAAC,OAAgB;IAC3C,OAAO;SACJ,OAAO,CAAC,iBAAiB,CAAC;SAC1B,WAAW,CAAC,gBAAgB,CAAC;SAC7B,MAAM,CAAC,WAAW,EAAE,oBAAoB,CAAC;SACzC,MAAM,CAAC,KAAK,EAAE,OAA2B,EAAE,OAA0B,EAAE,EAAE;QACxE,IAAI,CAAC;YAEH,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;gBAC7B,MAAM,QAAQ,GAAG,IAAA,sBAAc,GAAE,CAAC;gBAElC,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC1B,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAAC;oBAC/C,OAAO;gBACT,CAAC;gBAED,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,MAAM,CAAC;oBACpC;wBACE,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,SAAS;wBACf,OAAO,EAAE,qCAAqC;wBAC9C,OAAO,EAAE,CAAC,GAAG,QAAQ,EAAE,KAAK,CAAC;qBAC9B;iBACF,CAAC,CAAC;gBAEH,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;oBAC9B,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC;gBACrB,CAAC;qBAAM,CAAC;oBACN,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;gBAC5B,CAAC;YACH,CAAC;YAGD,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;gBAChB,MAAM,QAAQ,GAAG,IAAA,sBAAc,GAAE,CAAC;gBAElC,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC1B,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAAC;oBAC/C,OAAO;gBACT,CAAC;gBAED,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,0BAA0B,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;gBAE5E,KAAK,MAAM,GAAG,IAAI,QAAQ,EAAE,CAAC;oBAC3B,IAAI,CAAC;wBACH,MAAM,IAAA,kBAAY,EAAC,GAAG,CAAC,CAAC;oBAC1B,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,kBAAkB,GAAG,aAAa,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;oBAC9E,CAAC;gBACH,CAAC;gBAED,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC,CAAC;gBAC5D,OAAO;YACT,CAAC;YAGD,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,IAAA,qBAAa,EAAC,OAAO,CAAC,EAAE,CAAC;oBAC5B,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,OAAO,yBAAyB,CAAC,CAAC,CAAC;oBACtE,OAAO;gBACT,CAAC;gBAED,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,OAAO,aAAa,CAAC,CAAC,CAAC;gBAC1D,MAAM,IAAA,kBAAY,EAAC,OAAO,CAAC,CAAC;gBAC5B,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,OAAO,6BAA6B,CAAC,CAAC,CAAC;gBAClE,OAAO;YACT,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YACpD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;IACH,CAAC,CAAC,CAAC;AACP,CAAC"}