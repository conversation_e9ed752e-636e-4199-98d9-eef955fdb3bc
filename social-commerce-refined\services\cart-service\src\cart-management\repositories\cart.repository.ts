import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Cart, CartStatus } from '../entities/cart.entity';
import { CartItem } from '../entities/cart-item.entity';

@Injectable()
export class CartRepository {
  constructor(
    @InjectRepository(Cart)
    private readonly cartRepository: Repository<Cart>,
    @InjectRepository(CartItem)
    private readonly cartItemRepository: Repository<CartItem>,
  ) {}

  async create(cartData: Partial<Cart>): Promise<Cart> {
    const cart = this.cartRepository.create(cartData);
    return await this.cartRepository.save(cart);
  }

  async findById(id: string): Promise<Cart | null> {
    return await this.cartRepository.findOne({
      where: { id },
      relations: ['items'],
    });
  }

  async findByUserId(userId: string): Promise<Cart | null> {
    return await this.cartRepository.findOne({
      where: { userId, status: CartStatus.ACTIVE },
      relations: ['items'],
    });
  }

  async findBySessionId(sessionId: string): Promise<Cart | null> {
    return await this.cartRepository.findOne({
      where: { sessionId, status: CartStatus.ACTIVE },
      relations: ['items'],
    });
  }

  async save(cart: Cart): Promise<Cart> {
    return await this.cartRepository.save(cart);
  }

  async delete(id: string): Promise<void> {
    await this.cartRepository.delete(id);
  }

  async addItem(cartId: string, itemData: Partial<CartItem>): Promise<CartItem> {
    const cartItem = this.cartItemRepository.create({
      ...itemData,
      cartId,
    });
    return await this.cartItemRepository.save(cartItem);
  }

  async findCartItem(cartId: string, productId: string, variantId?: string): Promise<CartItem | null> {
    const whereCondition: any = { cartId, productId };
    if (variantId) {
      whereCondition.variantId = variantId;
    }

    return await this.cartItemRepository.findOne({
      where: whereCondition,
    });
  }

  async updateCartItem(id: string, updateData: Partial<CartItem>): Promise<CartItem> {
    await this.cartItemRepository.update(id, updateData);
    return await this.cartItemRepository.findOne({ where: { id } });
  }

  async removeCartItem(id: string): Promise<void> {
    await this.cartItemRepository.delete(id);
  }

  async clearCart(cartId: string): Promise<void> {
    await this.cartItemRepository.delete({ cartId });
  }

  async findAbandonedCarts(olderThanHours: number = 24): Promise<Cart[]> {
    const cutoffDate = new Date();
    cutoffDate.setHours(cutoffDate.getHours() - olderThanHours);

    return await this.cartRepository.find({
      where: {
        status: CartStatus.ACTIVE,
        lastActivity: cutoffDate,
      },
      relations: ['items'],
    });
  }

  async markAsAbandoned(cartId: string): Promise<void> {
    await this.cartRepository.update(cartId, { status: CartStatus.ABANDONED });
  }

  async markAsConverted(cartId: string): Promise<void> {
    await this.cartRepository.update(cartId, { status: CartStatus.CONVERTED });
  }

  async getCartStats(userId?: string): Promise<any> {
    const query = this.cartRepository.createQueryBuilder('cart')
      .leftJoin('cart.items', 'item')
      .select([
        'COUNT(DISTINCT cart.id) as totalCarts',
        'COUNT(item.id) as totalItems',
        'SUM(cart.total) as totalValue',
        'AVG(cart.total) as averageValue',
      ]);

    if (userId) {
      query.where('cart.userId = :userId', { userId });
    }

    return await query.getRawOne();
  }
}
