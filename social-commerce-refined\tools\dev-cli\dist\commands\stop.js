"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.stopCommand = stopCommand;
const chalk = require("chalk");
const inquirer = require("inquirer");
const paths_1 = require("../utils/paths");
const docker_1 = require("../utils/docker");
function stopCommand(program) {
    program
        .command('stop [service]')
        .description('Stop services')
        .option('-a, --all', 'Stop all services')
        .option('-i, --infrastructure', 'Stop infrastructure services only')
        .action(async (service, options) => {
        try {
            if (!service && !options.all && !options.infrastructure) {
                const services = (0, paths_1.getAllServices)();
                if (services.length === 0) {
                    console.log(chalk.yellow('No services found'));
                    return;
                }
                const answers = await inquirer.prompt([
                    {
                        type: 'list',
                        name: 'service',
                        message: 'Which service do you want to stop?',
                        choices: [...services, 'all', 'infrastructure'],
                    },
                ]);
                if (answers.service === 'all') {
                    options.all = true;
                }
                else if (answers.service === 'infrastructure') {
                    options.infrastructure = true;
                }
                else {
                    service = answers.service;
                }
            }
            if (options.all) {
                console.log(chalk.blue('Stopping all services...'));
                await (0, docker_1.stopServices)();
                console.log(chalk.green('All services stopped successfully'));
                return;
            }
            if (options.infrastructure) {
                console.log(chalk.blue('Stopping infrastructure services...'));
                await (0, docker_1.stopServices)();
                console.log(chalk.green('Infrastructure services stopped successfully'));
                return;
            }
            if (service) {
                if (!(0, paths_1.serviceExists)(service)) {
                    console.error(chalk.red(`Service ${service}-service does not exist`));
                    return;
                }
                console.log(chalk.blue(`Stopping ${service}-service...`));
                await (0, docker_1.stopServices)([`${service}-service`]);
                console.log(chalk.green(`${service}-service stopped successfully`));
                return;
            }
        }
        catch (error) {
            console.error(chalk.red(`Error: ${error.message}`));
            process.exit(1);
        }
    });
}
//# sourceMappingURL=stop.js.map