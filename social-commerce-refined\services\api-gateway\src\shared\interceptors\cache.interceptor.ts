import { Injectable, NestInterceptor, Execution<PERSON><PERSON>x<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Logger } from '@nestjs/common';
import { Observable, of } from 'rxjs';
import { tap } from 'rxjs/operators';
import { CacheService } from '../services/cache.service';
import { Request } from 'express';

/**
 * Interceptor that caches GET requests
 */
@Injectable()
export class CacheInterceptor implements NestInterceptor {
  private readonly logger = new Logger(CacheInterceptor.name);
  private readonly cacheTtl = 60 * 1000; // 1 minute
  
  // List of paths that should not be cached
  private readonly excludedPaths = [
    '/api/health',
    '/api/users/auth/login',
    '/api/users/auth/register',
  ];

  constructor(private readonly cacheService: CacheService) {}

  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest<Request>();
    
    // Only cache GET requests
    if (request.method !== 'GET') {
      return next.handle();
    }
    
    // Skip caching for excluded paths
    if (this.isExcluded(request.path)) {
      return next.handle();
    }
    
    // Generate a cache key based on the request
    const cacheKey = this.generateCacheKey(request);
    
    // Try to get the response from cache
    const cachedResponse = await this.cacheService.get(cacheKey);
    
    if (cachedResponse) {
      this.logger.debug(`Returning cached response for ${request.method} ${request.url}`);
      return of(cachedResponse);
    }
    
    // If not in cache, call the handler and cache the response
    return next.handle().pipe(
      tap(response => {
        this.logger.debug(`Caching response for ${request.method} ${request.url}`);
        this.cacheService.set(cacheKey, response, this.cacheTtl);
      }),
    );
  }

  /**
   * Generate a cache key based on the request
   * @param request The request object
   * @returns A cache key
   */
  private generateCacheKey(request: Request): string {
    // Include query parameters in the cache key
    const queryString = Object.keys(request.query)
      .sort()
      .map(key => `${key}=${request.query[key]}`)
      .join('&');
    
    // Include the path and query string in the cache key
    return `${request.path}${queryString ? `?${queryString}` : ''}`;
  }

  /**
   * Check if a path should be excluded from caching
   * @param path The request path
   * @returns Whether the path should be excluded
   */
  private isExcluded(path: string): boolean {
    return this.excludedPaths.some(excludedPath => path.startsWith(excludedPath));
  }
}
