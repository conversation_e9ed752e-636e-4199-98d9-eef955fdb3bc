import { BaseEvent } from '../base-event.interface';

/**
 * Event emitted when a cart is cleared
 */
export class CartClearedEvent implements BaseEvent<CartClearedPayload> {
  id: string;
  type: string = 'cart.cleared';
  version: string = '1.0';
  timestamp: string;
  producer: string = 'cart-service';
  payload: CartClearedPayload;

  constructor(payload: CartClearedPayload) {
    this.id = `${payload.cartId}-${Date.now()}`;
    this.timestamp = new Date().toISOString();
    this.payload = payload;
  }
}

/**
 * Payload for CartClearedEvent
 */
export interface CartClearedPayload {
  /**
   * Cart ID
   */
  cartId: string;

  /**
   * User ID
   */
  userId: string;

  /**
   * Reason for clearing the cart
   */
  reason: string;

  /**
   * Timestamp
   */
  clearedAt: string;
}
