import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProductController } from './controllers/product.controller';
import { ProductService } from './services/product.service';
import { ProductRepository } from './repositories/product.repository';
import { Product } from './entities/product.entity';
import { StoreManagementModule } from '../store-management/store-management.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Product]),
    StoreManagementModule,
  ],
  controllers: [ProductController],
  providers: [
    ProductService,
    ProductRepository,
  ],
  exports: [
    ProductService,
    ProductRepository,
  ],
})
export class ProductManagementModule {}
