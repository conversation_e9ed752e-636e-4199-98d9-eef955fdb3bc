import { Command } from 'commander';
import * as chalk from 'chalk';
import * as inquirer from 'inquirer';
import { getAllServices, serviceExists } from '../utils/paths';
import { testService } from '../utils/npm';

export function testCommand(program: Command): void {
  program
    .command('test [service]')
    .description('Run tests')
    .option('-a, --all', 'Test all services')
    .option('-w, --watch', 'Run tests in watch mode')
    .action(async (service: string | undefined, options: { all?: boolean; watch?: boolean }) => {
      try {
        // If no service is specified and --all is not set, prompt for service
        if (!service && !options.all) {
          const services = getAllServices();
          
          if (services.length === 0) {
            console.log(chalk.yellow('No services found'));
            return;
          }
          
          const answers = await inquirer.prompt([
            {
              type: 'list',
              name: 'service',
              message: 'Which service do you want to test?',
              choices: [...services, 'all'],
            },
          ]);
          
          if (answers.service === 'all') {
            options.all = true;
          } else {
            service = answers.service;
          }
        }

        // Test all services
        if (options.all) {
          const services = getAllServices();
          
          if (services.length === 0) {
            console.log(chalk.yellow('No services found'));
            return;
          }
          
          console.log(chalk.blue(`Testing all services: ${services.join(', ')}...`));
          
          for (const svc of services) {
            try {
              await testService(svc, options.watch);
            } catch (error) {
              console.error(chalk.red(`Error testing ${svc}-service: ${error.message}`));
            }
          }
          
          console.log(chalk.green('All services tested successfully'));
          return;
        }

        // Test specific service
        if (service) {
          if (!serviceExists(service)) {
            console.error(chalk.red(`Service ${service}-service does not exist`));
            return;
          }
          
          console.log(chalk.blue(`Testing ${service}-service...`));
          await testService(service, options.watch);
          console.log(chalk.green(`${service}-service tested successfully`));
          return;
        }
      } catch (error) {
        console.error(chalk.red(`Error: ${error.message}`));
        process.exit(1);
      }
    });
}
