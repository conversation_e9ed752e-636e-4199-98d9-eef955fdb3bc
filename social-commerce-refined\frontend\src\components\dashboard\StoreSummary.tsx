import React from 'react';
import {
  Box,
  Flex,
  Text,
  Heading,
  Button,
  useColorModeValue,
  Stack,
  Badge,
  Image,
} from '@chakra-ui/react';
import NextLink from 'next/link';

interface Store {
  id: string;
  name: string;
  description: string;
  productCount: number;
  orderCount: number;
  imageUrl?: string;
  isActive: boolean;
}

interface StoreSummaryProps {
  stores: Store[];
  isLoading?: boolean;
}

const StoreSummary = ({ stores, isLoading = false }: StoreSummaryProps) => {
  if (isLoading) {
    return (
      <Box
        bg={useColorModeValue('white', 'gray.700')}
        borderRadius="lg"
        shadow="md"
        borderWidth="1px"
        p={5}
      >
        <Heading size="md" mb={4}>
          Your Stores
        </Heading>
        <Text>Loading...</Text>
      </Box>
    );
  }

  return (
    <Box
      bg={useColorModeValue('white', 'gray.700')}
      borderRadius="lg"
      shadow="md"
      borderWidth="1px"
      p={5}
    >
      <Flex justifyContent="space-between" alignItems="center" mb={4}>
        <Heading size="md">Your Stores</Heading>
        <Button
          as={NextLink}
          href="/stores/create"
          size="sm"
          colorScheme="brand"
          variant="outline"
        >
          Create Store
        </Button>
      </Flex>

      {stores.length === 0 ? (
        <Box textAlign="center" py={6}>
          <Text mb={4}>You don't have any stores yet.</Text>
          <Button
            as={NextLink}
            href="/stores/create"
            colorScheme="brand"
          >
            Create Your First Store
          </Button>
        </Box>
      ) : (
        <Stack spacing={4}>
          {stores.map((store) => (
            <Flex
              key={store.id}
              borderWidth="1px"
              borderRadius="md"
              overflow="hidden"
              p={2}
              alignItems="center"
              _hover={{ bg: 'gray.50' }}
            >
              <Box
                width="60px"
                height="60px"
                borderRadius="md"
                overflow="hidden"
                mr={3}
              >
                <Image
                  src={store.imageUrl || 'https://via.placeholder.com/60?text=Store'}
                  alt={store.name}
                  width="100%"
                  height="100%"
                  objectFit="cover"
                />
              </Box>
              
              <Box flex="1">
                <Flex justifyContent="space-between" alignItems="center">
                  <Heading size="sm">{store.name}</Heading>
                  <Badge colorScheme={store.isActive ? 'green' : 'gray'}>
                    {store.isActive ? 'Active' : 'Inactive'}
                  </Badge>
                </Flex>
                
                <Text fontSize="sm" color="gray.600" noOfLines={1}>
                  {store.description}
                </Text>
                
                <Flex mt={1} fontSize="xs" color="gray.500">
                  <Text mr={3}>{store.productCount} Products</Text>
                  <Text>{store.orderCount} Orders</Text>
                </Flex>
              </Box>
              
              <Button
                as={NextLink}
                href={`/stores/${store.id}`}
                size="sm"
                colorScheme="brand"
                variant="ghost"
                ml={2}
              >
                Manage
              </Button>
            </Flex>
          ))}
        </Stack>
      )}
    </Box>
  );
};

export default StoreSummary;
