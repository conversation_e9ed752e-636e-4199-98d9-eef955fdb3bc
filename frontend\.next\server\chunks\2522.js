"use strict";exports.id=2522,exports.ids=[2522],exports.modules={2522:(e,r,s)=>{s.d(r,{Z:()=>__WEBPACK_DEFAULT_EXPORT__});var a=s(30784),t=s(9885),l=s(27870),n=s(14379),i=s(62763),o=s(59872),d=s(3902),c=s(51020),h=s(40446);let __WEBPACK_DEFAULT_EXPORT__=({post:e,isOpen:r,onClose:s,onSuccess:m})=>{let{t:x}=(0,l.$G)("social"),{isRtl:p}=(0,n.g)(),[g,u]=(0,t.useState)(""),[j,y]=(0,t.useState)(d.uk.PUBLIC),[v,{isLoading:f}]=(0,c.u3)(),handleSharePost=async()=>{try{let r={type:d.hQ.SHARE,content:g.trim(),visibility:j,sharedPostId:e.id};await v(r).unwrap(),u(""),y(d.uk.PUBLIC),s(),m?.()}catch(e){console.error("Failed to share post:",e)}},handleExternalShare=r=>{let s="",a=`${window.location.origin}/social/posts/${e.id}`,t=g||x("checkOutThisPost","Check out this post!");switch(r){case"facebook":s=`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(a)}`;break;case"twitter":s=`https://twitter.com/intent/tweet?text=${encodeURIComponent(t)}&url=${encodeURIComponent(a)}`;break;case"whatsapp":s=`https://api.whatsapp.com/send?text=${encodeURIComponent(`${t} ${a}`)}`;break;case"email":s=`mailto:?subject=${encodeURIComponent(x("sharedPost","Shared Post"))}&body=${encodeURIComponent(`${t} ${a}`)}`;break;case"copy":navigator.clipboard.writeText(a),alert(x("linkCopied","Link copied to clipboard!"));return}s&&window.open(s,"_blank","noopener,noreferrer")};return a.jsx(i.Z,{isOpen:r,onClose:s,title:x("sharePost","Share Post"),size:"lg",children:(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("div",{className:"border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden",children:a.jsx(h.Z,{post:e,isPreview:!0})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:x("shareWithComment","Share with Comment")}),a.jsx("textarea",{className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 resize-none",placeholder:x("addComment","Add a comment..."),rows:3,value:g,onChange:e=>{u(e.target.value)},disabled:f}),(0,a.jsxs)("div",{className:"mt-2",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:x("visibility","Visibility")}),(0,a.jsxs)("select",{className:"w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-700 dark:text-gray-300 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50",value:j,onChange:e=>{y(e.target.value)},disabled:f,children:[a.jsx("option",{value:d.uk.PUBLIC,children:x("public","Public")}),a.jsx("option",{value:d.uk.CONNECTIONS,children:x("connections","Connections Only")}),a.jsx("option",{value:d.uk.PRIVATE,children:x("private","Private")})]})]}),a.jsx(o.Z,{variant:"primary",className:"w-full mt-4",onClick:handleSharePost,isLoading:f,disabled:f,children:x("sharePost","Share Post")})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:x("shareToOtherPlatforms","Share to Other Platforms")}),(0,a.jsxs)("div",{className:"grid grid-cols-2 sm:grid-cols-3 gap-2",children:[a.jsx(o.Z,{variant:"outline",onClick:()=>handleExternalShare("facebook"),className:"flex items-center justify-center",children:a.jsx("span",{className:"mr-2",children:"Facebook"})}),a.jsx(o.Z,{variant:"outline",onClick:()=>handleExternalShare("twitter"),className:"flex items-center justify-center",children:a.jsx("span",{className:"mr-2",children:"Twitter"})}),a.jsx(o.Z,{variant:"outline",onClick:()=>handleExternalShare("whatsapp"),className:"flex items-center justify-center",children:a.jsx("span",{className:"mr-2",children:"WhatsApp"})}),a.jsx(o.Z,{variant:"outline",onClick:()=>handleExternalShare("email"),className:"flex items-center justify-center",children:a.jsx("span",{className:"mr-2",children:"Email"})}),a.jsx(o.Z,{variant:"outline",onClick:()=>handleExternalShare("copy"),className:"flex items-center justify-center",children:a.jsx("span",{className:"mr-2",children:"Copy Link"})})]})]})]})]})})}},40446:(e,r,s)=>{s.d(r,{Z:()=>p});var a=s(30784),t=s(9885),l=s(27870),n=s(14379),i=s(11440),o=s.n(i),d=s(52451),c=s.n(d),h=s(73531),m=s(3902);let social_SocialPostMedia=({type:e,mediaUrls:r,className:s=""})=>{let[l,n]=(0,t.useState)(0),handleImageClick=e=>{n(e)};if(!r||0===r.length)return null;if(1===r.length){if(e===m.hQ.IMAGE)return a.jsx("div",{className:`relative aspect-auto max-h-[500px] overflow-hidden ${s}`,children:a.jsx(c(),{src:r[0],alt:"Post image",fill:!0,className:"object-contain"})});if(e===m.hQ.VIDEO)return a.jsx("div",{className:`relative aspect-video ${s}`,children:a.jsx("video",{src:r[0],controls:!0,className:"w-full h-full",poster:"/images/video-placeholder.png"})})}return e===m.hQ.IMAGE&&r.length>1?(0,a.jsxs)("div",{className:`${s}`,children:[(0,a.jsxs)("div",{className:"relative aspect-auto max-h-[400px] overflow-hidden mb-1",children:[a.jsx(c(),{src:r[l],alt:`Post image ${l+1}`,fill:!0,className:"object-contain"}),r.length>1&&(0,a.jsxs)(a.Fragment,{children:[a.jsx("button",{className:"absolute left-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full p-1",onClick:()=>n(e=>0===e?r.length-1:e-1),children:a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),a.jsx("button",{className:"absolute right-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full p-1",onClick:()=>n(e=>e===r.length-1?0:e+1),children:a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]})]}),r.length>1&&a.jsx("div",{className:"flex space-x-1 overflow-x-auto",children:r.map((e,r)=>a.jsx("button",{className:`relative w-16 h-16 flex-shrink-0 ${r===l?"ring-2 ring-primary-500":""}`,onClick:()=>handleImageClick(r),children:a.jsx(c(),{src:e,alt:`Thumbnail ${r+1}`,fill:!0,className:"object-cover"})},r))})]}):e===m.hQ.VIDEO&&r.length>1?(0,a.jsxs)("div",{className:`${s}`,children:[a.jsx("div",{className:"relative aspect-video",children:a.jsx("video",{src:r[l],controls:!0,className:"w-full h-full",poster:"/images/video-placeholder.png"})}),a.jsx("div",{className:"flex space-x-1 mt-1 overflow-x-auto",children:r.map((e,r)=>a.jsx("button",{className:`relative w-16 h-16 flex-shrink-0 ${r===l?"ring-2 ring-primary-500":""}`,onClick:()=>handleImageClick(r),children:a.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-900",children:(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"}),a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})]})})},r))})]}):null};var x=s(15483);let social_SocialPostContent=({post:e,className:r=""})=>{let{t:s}=(0,l.$G)("social"),formatText=e=>(0,x.fL)(e);return e.type===m.hQ.TEXT?a.jsx("div",{className:`px-4 pb-3 ${r}`,children:a.jsx("p",{className:"text-gray-800 dark:text-gray-200 whitespace-pre-line",dangerouslySetInnerHTML:{__html:formatText(e.content)}})}):e.type===m.hQ.PRODUCT&&e.product?(0,a.jsxs)("div",{className:`px-4 pb-3 ${r}`,children:[a.jsx("p",{className:"text-gray-800 dark:text-gray-200 whitespace-pre-line mb-3",dangerouslySetInnerHTML:{__html:formatText(e.content)}}),a.jsx(o(),{href:`/products/${e.productId}`,className:"block",children:a.jsx("div",{className:"border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden",children:(0,a.jsxs)("div",{className:"flex items-center p-3",children:[a.jsx("div",{className:"w-16 h-16 relative flex-shrink-0",children:a.jsx(c(),{src:e.product.mediaUrls?.[0]||"/images/placeholder-product.png",alt:e.product.title,fill:!0,className:"object-cover rounded-md"})}),(0,a.jsxs)("div",{className:"ml-3",children:[a.jsx("h3",{className:"font-medium text-gray-900 dark:text-gray-100",children:e.product.title}),a.jsx("p",{className:"text-sm text-primary-600 dark:text-primary-400",children:s("viewProduct","View Product")})]})]})})})]}):e.type===m.hQ.STORE&&e.store?(0,a.jsxs)("div",{className:`px-4 pb-3 ${r}`,children:[a.jsx("p",{className:"text-gray-800 dark:text-gray-200 whitespace-pre-line mb-3",dangerouslySetInnerHTML:{__html:formatText(e.content)}}),a.jsx(o(),{href:`/stores/${e.storeId}`,className:"block",children:a.jsx("div",{className:"border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden",children:(0,a.jsxs)("div",{className:"flex items-center p-3",children:[a.jsx("div",{className:"w-12 h-12 relative flex-shrink-0",children:a.jsx(c(),{src:e.store.logoUrl||"/images/placeholder-store.png",alt:e.store.name,fill:!0,className:"object-cover rounded-full"})}),(0,a.jsxs)("div",{className:"ml-3",children:[a.jsx("h3",{className:"font-medium text-gray-900 dark:text-gray-100",children:e.store.name}),a.jsx("p",{className:"text-sm text-primary-600 dark:text-primary-400",children:s("viewStore","View Store")})]})]})})})]}):e.type===m.hQ.POLL&&e.pollOptions?(0,a.jsxs)("div",{className:`px-4 pb-3 ${r}`,children:[a.jsx("p",{className:"text-gray-800 dark:text-gray-200 whitespace-pre-line mb-3",dangerouslySetInnerHTML:{__html:formatText(e.content)}}),a.jsx("div",{className:"space-y-2",children:e.pollOptions.map(r=>{let s=e.pollOptions?.reduce((e,r)=>e+r.votes,0)||0,t=s>0?Math.round(r.votes/s*100):0;return a.jsx("button",{className:"w-full text-left",children:(0,a.jsxs)("div",{className:"relative border border-gray-200 dark:border-gray-700 rounded-lg p-3",children:[a.jsx("div",{className:"absolute top-0 left-0 bottom-0 bg-primary-100 dark:bg-primary-900/20 rounded-lg",style:{width:`${t}%`}}),(0,a.jsxs)("div",{className:"relative flex justify-between",children:[a.jsx("span",{children:r.text}),(0,a.jsxs)("span",{className:"font-medium",children:[t,"%"]})]})]})},r.id)})}),e.pollEndsAt&&a.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-2",children:new Date(e.pollEndsAt)>new Date?s("pollEndsIn","Poll ends in {{time}}",{time:new Date(e.pollEndsAt).toLocaleString()}):s("pollEnded","Poll ended")})]}):a.jsx("div",{className:`px-4 pb-3 ${r}`,children:a.jsx("p",{className:"text-gray-800 dark:text-gray-200 whitespace-pre-line",dangerouslySetInnerHTML:{__html:formatText(e.content)}})})},SocialPostCard=({post:e,onLike:r,onComment:s,onShare:t,isPreview:i=!1,className:d=""})=>{let{t:x}=(0,l.$G)("social"),{isRtl:p}=(0,n.g)(),g=e.reactions?.some(e=>"LIKE"===e.type&&e.userReacted);return(0,a.jsxs)("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden ${d}`,children:[(0,a.jsxs)("div",{className:"p-4 flex items-center",children:[a.jsx(o(),{href:`/profile/${e.user?.username||e.userId}`,className:"flex-shrink-0",children:a.jsx("div",{className:"w-10 h-10 relative rounded-full overflow-hidden",children:a.jsx(c(),{src:e.user?.avatarUrl||"/images/default-avatar.png",alt:e.user?.displayName||"User",fill:!0,className:"object-cover"})})}),(0,a.jsxs)("div",{className:"ml-3 flex-1",children:[a.jsx(o(),{href:`/profile/${e.user?.username||e.userId}`,className:"font-medium text-gray-900 dark:text-gray-100 hover:underline",children:e.user?.displayName||"User"}),a.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:(e=>{try{return(0,h.Z)(new Date(e),{addSuffix:!0})}catch(r){return e}})(e.createdAt)})]})]}),a.jsx(social_SocialPostContent,{post:e}),e.mediaUrls&&e.mediaUrls.length>0&&a.jsx(social_SocialPostMedia,{type:e.type,mediaUrls:e.mediaUrls,className:"mb-3"}),e.type===m.hQ.SHARE&&e.sharedPost&&a.jsx("div",{className:"mx-4 mb-4 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden",children:a.jsx(SocialPostCard,{post:e.sharedPost,isPreview:!0})}),!i&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"px-4 py-2 border-t border-gray-100 dark:border-gray-700 flex justify-between text-xs text-gray-500 dark:text-gray-400",children:[(0,a.jsxs)("span",{children:[e.totalReactions," ",x("reactions","reactions")]}),(0,a.jsxs)("span",{children:[e.commentCount," ",x("comments","comments")," • ",e.shareCount," ",x("shares","shares")]})]}),(0,a.jsxs)("div",{className:"px-4 py-2 border-t border-gray-100 dark:border-gray-700 flex justify-between",children:[(0,a.jsxs)("button",{className:`flex items-center justify-center w-1/3 py-1 rounded-md ${g?"text-primary-600 dark:text-primary-400":"text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700"}`,onClick:()=>{r?.(e.id,"LIKE")},children:[a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-1",fill:g?"currentColor":"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:g?0:1.5,d:"M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"})}),x("like","Like")]}),(0,a.jsxs)("button",{className:"flex items-center justify-center w-1/3 py-1 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-md",onClick:()=>{s?.(e.id)},children:[a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})}),x("comment","Comment")]}),(0,a.jsxs)("button",{className:"flex items-center justify-center w-1/3 py-1 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-md",onClick:()=>{t?.(e.id)},children:[a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"})}),x("share","Share")]})]})]})]})},p=SocialPostCard},15483:(e,r,s)=>{s.d(r,{Ef:()=>extractHashtags,TZ:()=>extractMentions,fL:()=>formatTextWithLinks});let extractHashtags=e=>{if(!e)return[];let r=e.match(/(?:^|\s)(#[a-zA-Z0-9_]+)(?=\s|$|[.,!?;:])/g);return r?r.map(e=>e.trim().substring(1)):[]},extractMentions=e=>{if(!e)return[];let r=e.match(/(?:^|\s)(@[a-zA-Z0-9_]+)(?=\s|$|[.,!?;:])/g);return r?r.map(e=>e.trim().substring(1)):[]},formatTextWithLinks=e=>e?e.replace(/(?:^|\s)(#[a-zA-Z0-9_]+)(?=\s|$|[.,!?;:])/g,' <a href="/search?tag=$1" class="text-primary-600 dark:text-primary-400 hover:underline">$1</a>').replace(/(?:^|\s)(@[a-zA-Z0-9_]+)(?=\s|$|[.,!?;:])/g,' <a href="/profile/$1" class="text-primary-600 dark:text-primary-400 hover:underline">$1</a>'):""}};