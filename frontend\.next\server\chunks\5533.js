"use strict";exports.id=5533,exports.ids=[5533],exports.modules={25533:(e,t,r)=>{r.d(t,{Z:()=>__WEBPACK_DEFAULT_EXPORT__});var a=r(30784),s=r(9885);let __WEBPACK_DEFAULT_EXPORT__=({url:e,title:t,size:r="md"})=>{let[o,n]=(0,s.useState)(!1),handleShare=async()=>{try{navigator.share?await navigator.share({title:t,url:e}):await navigator.clipboard.writeText(e),n(!0),setTimeout(()=>n(!1),2e3)}catch(e){console.error("Error sharing:",e)}};return(0,a.jsxs)("button",{type:"button",onClick:handleShare,className:"flex items-center space-x-1 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300",children:[a.jsx("svg",{className:{sm:"h-4 w-4",md:"h-5 w-5",lg:"h-6 w-6"}[r],xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"})}),a.jsx("span",{className:{sm:"text-xs",md:"text-sm",lg:"text-base"}[r],children:o?"Shared!":"Share"})]})}}};