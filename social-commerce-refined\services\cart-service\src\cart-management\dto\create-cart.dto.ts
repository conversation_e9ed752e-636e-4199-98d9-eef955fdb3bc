import { IsOptional, IsString, IsBoolean, IsObject, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateCartDto {
  @ApiProperty({
    description: 'User ID for authenticated users',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  userId?: string;

  @ApiProperty({
    description: 'Session ID for guest users',
    example: 'sess_123456789',
    required: false,
  })
  @IsOptional()
  @IsString()
  sessionId?: string;

  @ApiProperty({
    description: 'Whether this is a guest cart',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isGuestCart?: boolean;

  @ApiProperty({
    description: 'Additional metadata for the cart',
    example: { source: 'mobile_app', campaign: 'summer_sale' },
    required: false,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
