import { Injectable, Logger, NotFoundException, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ClientProxy } from '@nestjs/microservices';
import { Notification, NotificationType, NotificationStatus } from '../entities/notification.entity';
import { CreateNotificationDto } from '../dto/create-notification.dto';
import { SendEmailDto } from '../dto/send-email.dto';
import { SendSmsDto } from '../dto/send-sms.dto';
import { EmailService } from './email.service';
import { SmsService } from './sms.service';

@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);

  constructor(
    @InjectRepository(Notification)
    private readonly notificationRepository: Repository<Notification>,
    private readonly emailService: EmailService,
    private readonly smsService: SmsService,
    @Inject('USER_SERVICE') private readonly userClient: ClientProxy,
  ) {}

  async createNotification(createNotificationDto: CreateNotificationDto): Promise<Notification> {
    this.logger.log(`Creating notification for user: ${createNotificationDto.userId}`);

    const notification = this.notificationRepository.create({
      ...createNotificationDto,
      status: NotificationStatus.PENDING,
    });

    const savedNotification = await this.notificationRepository.save(notification);

    // Process notification based on type
    await this.processNotification(savedNotification);

    return savedNotification;
  }

  async sendEmail(sendEmailDto: SendEmailDto): Promise<Notification> {
    this.logger.log(`Sending email to: ${sendEmailDto.to}`);

    // Create notification record
    const notification = this.notificationRepository.create({
      userId: sendEmailDto.userId || 'system',
      type: NotificationType.EMAIL,
      subject: sendEmailDto.subject,
      content: sendEmailDto.content,
      recipient: sendEmailDto.to,
      status: NotificationStatus.PENDING,
    });

    const savedNotification = await this.notificationRepository.save(notification);

    // Send email
    try {
      await this.emailService.sendEmail(sendEmailDto);
      
      // Update notification status
      savedNotification.status = NotificationStatus.SENT;
      savedNotification.sentAt = new Date();
      await this.notificationRepository.save(savedNotification);

      this.logger.log(`Email sent successfully to: ${sendEmailDto.to}`);
    } catch (error) {
      this.logger.error(`Failed to send email to: ${sendEmailDto.to}`, error.stack);
      
      // Update notification status
      savedNotification.status = NotificationStatus.FAILED;
      savedNotification.errorMessage = error.message;
      savedNotification.retryCount += 1;
      await this.notificationRepository.save(savedNotification);
    }

    return savedNotification;
  }

  async sendSms(sendSmsDto: SendSmsDto): Promise<Notification> {
    this.logger.log(`Sending SMS to: ${sendSmsDto.to}`);

    // Create notification record
    const notification = this.notificationRepository.create({
      userId: sendSmsDto.userId || 'system',
      type: NotificationType.SMS,
      subject: 'SMS Notification',
      content: sendSmsDto.message,
      recipient: sendSmsDto.to,
      status: NotificationStatus.PENDING,
    });

    const savedNotification = await this.notificationRepository.save(notification);

    // Send SMS
    try {
      await this.smsService.sendSms(sendSmsDto);
      
      // Update notification status
      savedNotification.status = NotificationStatus.SENT;
      savedNotification.sentAt = new Date();
      await this.notificationRepository.save(savedNotification);

      this.logger.log(`SMS sent successfully to: ${sendSmsDto.to}`);
    } catch (error) {
      this.logger.error(`Failed to send SMS to: ${sendSmsDto.to}`, error.stack);
      
      // Update notification status
      savedNotification.status = NotificationStatus.FAILED;
      savedNotification.errorMessage = error.message;
      savedNotification.retryCount += 1;
      await this.notificationRepository.save(savedNotification);
    }

    return savedNotification;
  }

  async findAll(): Promise<Notification[]> {
    this.logger.log('Finding all notifications');
    return this.notificationRepository.find({
      order: { createdAt: 'DESC' },
    });
  }

  async findByUserId(userId: string): Promise<Notification[]> {
    this.logger.log(`Finding notifications for user: ${userId}`);
    return this.notificationRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string): Promise<Notification> {
    this.logger.log(`Finding notification with ID: ${id}`);
    
    const notification = await this.notificationRepository.findOne({
      where: { id },
    });

    if (!notification) {
      throw new NotFoundException(`Notification with ID ${id} not found`);
    }

    return notification;
  }

  private async processNotification(notification: Notification): Promise<void> {
    this.logger.log(`Processing notification: ${notification.id}`);

    try {
      switch (notification.type) {
        case NotificationType.EMAIL:
          await this.emailService.sendEmail({
            to: notification.recipient,
            subject: notification.subject,
            content: notification.content,
            userId: notification.userId,
          });
          break;

        case NotificationType.SMS:
          await this.smsService.sendSms({
            to: notification.recipient,
            message: notification.content,
            userId: notification.userId,
          });
          break;

        default:
          this.logger.warn(`Unsupported notification type: ${notification.type}`);
          return;
      }

      // Update notification status
      notification.status = NotificationStatus.SENT;
      notification.sentAt = new Date();
      await this.notificationRepository.save(notification);

    } catch (error) {
      this.logger.error(`Failed to process notification: ${notification.id}`, error.stack);
      
      // Update notification status
      notification.status = NotificationStatus.FAILED;
      notification.errorMessage = error.message;
      notification.retryCount += 1;
      await this.notificationRepository.save(notification);
    }
  }
}
