(()=>{var e={};e.id=5522,e.ids=[5522],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},7456:(e,a,r)=>{"use strict";r.r(a),r.d(a,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>g,tree:()=>l});var t=r(67096),s=r(16132),i=r(37284),n=r.n(i),o=r(32564),d={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(a,d);let l=["",{children:["store",{children:["[id]",{children:["affiliate",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1391)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\store\\[id]\\affiliate\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9291,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\store\\[id]\\affiliate\\page.tsx"],m="/store/[id]/affiliate/page",x={require:r,loadChunk:()=>Promise.resolve()},g=new t.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/store/[id]/affiliate/page",pathname:"/store/[id]/affiliate",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},1953:(e,a,r)=>{Promise.resolve().then(r.bind(r,21210))},21210:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>StoreAffiliateProgramsPage});var t={};r.r(t);var s=r(30784),i=r(9885),n=r(57114),o=r(27870),d=r(3619),l=r(77783),c=r(59872),m=r(63048);r(706),r(26352);let affiliate_AffiliateProgramList=({programs:e,isLoading:a=!1,onEdit:r,onDelete:i,editingProgramId:n,onUpdate:d,onCancelEdit:l,isUpdating:x=!1,isDeleting:g=!1})=>{let{t:u}=(0,o.$G)("affiliate"),formatDate=e=>(0,m.ZP)(new Date(e),"MMM d, yyyy"),formatCurrency=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e);return a?(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 animate-pulse",children:[s.jsx("div",{className:"h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-6"}),[void 0,void 0,void 0].map((e,a)=>s.jsx("div",{className:"mb-4",children:s.jsx("div",{className:"h-24 bg-gray-200 dark:bg-gray-700 rounded mb-2"})},a))]}):e&&0!==e.length?(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6",children:[s.jsx("h2",{className:"text-xl font-semibold mb-6",children:u("yourPrograms","Your Affiliate Programs")}),s.jsx("div",{className:"space-y-6",children:e.map(e=>s.jsx("div",{className:"border border-gray-200 dark:border-gray-700 rounded-lg p-4",children:n===e.id?s.jsx(t.default,{initialData:e,onSubmit:a=>d(e.id,a),onCancel:l,isLoading:x,isEdit:!0}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"text-lg font-medium",children:e.name}),s.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:u("createdOn","Created on {{date}}",{date:formatDate(e.createdAt)})})]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[s.jsx(c.Z,{variant:"outline",size:"sm",onClick:()=>r(e.id),children:u("edit","Edit")}),s.jsx(c.Z,{variant:"danger",size:"sm",onClick:()=>i(e.id),isLoading:g,children:u("delete","Delete")})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[(0,s.jsxs)("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:u("commission","Commission")}),s.jsx("p",{className:"text-lg",children:"PERCENTAGE"===e.commissionType?`${e.commissionValue}%`:formatCurrency(e.commissionValue)})]}),(0,s.jsxs)("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:u("cookieDuration","Cookie Duration")}),(0,s.jsxs)("p",{className:"text-lg",children:[e.cookieDuration," ",u("days","days")]})]}),(0,s.jsxs)("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:u("minimumPayout","Minimum Payout")}),s.jsx("p",{className:"text-lg",children:formatCurrency(e.minimumPayoutAmount)})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[s.jsx("div",{className:"flex items-center",children:s.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${e.isActive?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"}`,children:e.isActive?u("active","Active"):u("inactive","Inactive")})}),s.jsx(c.Z,{variant:"link",size:"sm",onClick:()=>window.open(`/affiliate/programs/${e.id}`,"_blank"),children:u("viewDetails","View Details")})]})]})},e.id))})]}):(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center",children:[s.jsx("h2",{className:"text-xl font-semibold mb-4",children:u("noPrograms","No Affiliate Programs")}),s.jsx("p",{className:"text-gray-500 dark:text-gray-400 mb-6",children:u("noProgramsDesc","You haven't created any affiliate programs yet. Create one to start growing your sales through affiliates.")})]})};function StoreAffiliateProgramsPage({params:e}){let{id:a}=e,r=(0,n.useRouter)(),{t:m}=(0,o.$G)("affiliate"),[x,g]=(0,i.useState)(!1),[u,f]=(0,i.useState)(null),{data:h,isLoading:p}=(0,l.c3)(a),{data:y,isLoading:j}=(0,d.PF)({storeId:a}),[b,{isLoading:v}]=(0,d.QZ)(),[N,{isLoading:P}]=(0,d.ZM)(),[w,{isLoading:k}]=(0,d.$e)(),handleCreateProgram=async e=>{try{await b({...e,storeId:a}).unwrap(),g(!1)}catch(e){console.error("Failed to create affiliate program:",e)}},handleUpdateProgram=async(e,a)=>{try{await N({id:e,data:a}).unwrap(),f(null)}catch(e){console.error("Failed to update affiliate program:",e)}},handleDeleteProgram=async e=>{if(window.confirm(m("confirmDeleteProgram","Are you sure you want to delete this affiliate program?")))try{await w(e).unwrap()}catch(e){console.error("Failed to delete affiliate program:",e)}};return p?s.jsx("div",{className:"min-h-screen p-6",children:s.jsx("div",{className:"max-w-6xl mx-auto",children:s.jsx("div",{className:"flex items-center justify-center py-12",children:s.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"})})})}):h?s.jsx("div",{className:"min-h-screen p-6",children:(0,s.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,s.jsxs)("div",{children:[s.jsx("h1",{className:"text-3xl font-bold",children:m("affiliatePrograms","Affiliate Programs")}),s.jsx("p",{className:"text-gray-500 dark:text-gray-400 mt-1",children:m("manageAffiliatePrograms","Manage affiliate programs for your store")})]}),(0,s.jsxs)("div",{className:"flex space-x-4",children:[s.jsx(c.Z,{variant:"outline",onClick:()=>r.push(`/store/${a}`),children:m("backToStore","Back to Store")}),!x&&s.jsx(c.Z,{onClick:()=>g(!0),children:m("createProgram","Create Program")})]})]}),x&&(0,s.jsxs)("div",{className:"mb-8 bg-white dark:bg-gray-800 rounded-lg shadow-md p-6",children:[s.jsx("h2",{className:"text-xl font-semibold mb-4",children:m("createNewProgram","Create New Affiliate Program")}),s.jsx(t.default,{onSubmit:handleCreateProgram,onCancel:()=>g(!1),isLoading:v})]}),s.jsx(affiliate_AffiliateProgramList,{programs:y?.programs||[],isLoading:j,onEdit:e=>f(e),onDelete:handleDeleteProgram,editingProgramId:u,onUpdate:handleUpdateProgram,onCancelEdit:()=>f(null),isUpdating:P,isDeleting:k})]})}):s.jsx("div",{className:"min-h-screen p-6",children:s.jsx("div",{className:"max-w-6xl mx-auto",children:(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center",children:[s.jsx("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:"Store Not Found"}),s.jsx("p",{className:"mb-6",children:"The store you're looking for doesn't exist or you don't have access to it."}),s.jsx(c.Z,{onClick:()=>r.push("/stores"),children:"Back to Stores"})]})})})}},1391:(e,a,r)=>{"use strict";r.r(a),r.d(a,{$$typeof:()=>n,__esModule:()=>i,default:()=>d});var t=r(95153);let s=(0,t.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\store\[id]\affiliate\page.tsx`),{__esModule:i,$$typeof:n}=s,o=s.default,d=o}};var a=require("../../../../webpack-runtime.js");a.C(e);var __webpack_exec__=e=>a(a.s=e),r=a.X(0,[2103,3048,2765,706,3619,7783,6352],()=>__webpack_exec__(7456));module.exports=r})();