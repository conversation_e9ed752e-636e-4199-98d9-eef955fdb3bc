import { Test, TestingModule } from '@nestjs/testing';
import { JwtService } from '@nestjs/jwt';
import { ConflictException, UnauthorizedException, NotFoundException } from '@nestjs/common';
import { AuthenticationService } from './authentication.service';
import { UserRepository } from '../repositories/user.repository';
import { User } from '../entities/user.entity';
import { CreateUserDto } from '../dto/create-user.dto';

// Mock EventPublisherService since it's from an external library
class MockEventPublisherService {
  publish = jest.fn().mockResolvedValue(undefined);
}

describe('AuthenticationService', () => {
  let service: AuthenticationService;
  let userRepository: UserRepository;
  let jwtService: JwtService;
  let eventPublisher: MockEventPublisherService;

  beforeEach(async () => {
    // Create mock implementations
    const mockUserRepository = {
      findByEmail: jest.fn(),
      create: jest.fn(),
      findOne: jest.fn(),
      updateLastLogin: jest.fn(),
    };

    const mockJwtService = {
      sign: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthenticationService,
        {
          provide: UserRepository,
          useValue: mockUserRepository,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: 'EventPublisherService',
          useClass: MockEventPublisherService,
        },
      ],
    }).compile();

    service = module.get<AuthenticationService>(AuthenticationService);
    userRepository = module.get<UserRepository>(UserRepository);
    jwtService = module.get<JwtService>(JwtService);
    eventPublisher = module.get('EventPublisherService');
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('register', () => {
    it('should register a new user successfully', async () => {
      // Arrange
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        password: 'password123',
        profile: {
          firstName: 'John',
          lastName: 'Doe',
        },
      };

      const createdUser = {
        id: '123',
        email: createUserDto.email,
        profile: {
          firstName: createUserDto.profile.firstName,
          lastName: createUserDto.profile.lastName,
        },
        isEmailVerified: false,
        createdAt: new Date(),
      } as User;

      jest.spyOn(userRepository, 'findByEmail').mockResolvedValue(null);
      jest.spyOn(userRepository, 'create').mockResolvedValue(createdUser);
      jest.spyOn(eventPublisher, 'publish').mockResolvedValue(undefined);

      // Act
      const result = await service.register(createUserDto);

      // Assert
      expect(result).toEqual(createdUser);
      expect(userRepository.findByEmail).toHaveBeenCalledWith(createUserDto.email);
      expect(userRepository.create).toHaveBeenCalledWith(createUserDto);
      expect(eventPublisher.publish).toHaveBeenCalled();
    });

    it('should throw ConflictException if user already exists', async () => {
      // Arrange
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const existingUser = {
        id: '123',
        email: createUserDto.email,
      } as User;

      jest.spyOn(userRepository, 'findByEmail').mockResolvedValue(existingUser);

      // Act & Assert
      await expect(service.register(createUserDto)).rejects.toThrow(ConflictException);
      expect(userRepository.findByEmail).toHaveBeenCalledWith(createUserDto.email);
      expect(userRepository.create).not.toHaveBeenCalled();
    });
  });

  describe('validateUser', () => {
    it('should validate user credentials successfully', async () => {
      // Arrange
      const email = '<EMAIL>';
      const password = 'password123';

      const user = {
        id: '123',
        email,
        validatePassword: jest.fn().mockResolvedValue(true),
      } as unknown as User;

      jest.spyOn(userRepository, 'findByEmail').mockResolvedValue(user);
      jest.spyOn(userRepository, 'updateLastLogin').mockResolvedValue(user);

      // Act
      const result = await service.validateUser(email, password);

      // Assert
      expect(result).toEqual(user);
      expect(userRepository.findByEmail).toHaveBeenCalledWith(email);
      expect(user.validatePassword).toHaveBeenCalledWith(password);
      expect(userRepository.updateLastLogin).toHaveBeenCalledWith(user.id);
    });

    it('should throw UnauthorizedException if user not found', async () => {
      // Arrange
      const email = '<EMAIL>';
      const password = 'password123';

      jest.spyOn(userRepository, 'findByEmail').mockResolvedValue(null);

      // Act & Assert
      await expect(service.validateUser(email, password)).rejects.toThrow(UnauthorizedException);
      expect(userRepository.findByEmail).toHaveBeenCalledWith(email);
    });

    it('should throw UnauthorizedException if password is invalid', async () => {
      // Arrange
      const email = '<EMAIL>';
      const password = 'password123';

      const user = {
        id: '123',
        email,
        validatePassword: jest.fn().mockResolvedValue(false),
      } as unknown as User;

      jest.spyOn(userRepository, 'findByEmail').mockResolvedValue(user);

      // Act & Assert
      await expect(service.validateUser(email, password)).rejects.toThrow(UnauthorizedException);
      expect(userRepository.findByEmail).toHaveBeenCalledWith(email);
      expect(user.validatePassword).toHaveBeenCalledWith(password);
    });
  });

  describe('login', () => {
    it('should generate JWT token and return user with token', async () => {
      // Arrange
      const user = {
        id: '123',
        email: '<EMAIL>',
        role: 'user',
      } as User;

      const token = 'jwt-token';
      jest.spyOn(jwtService, 'sign').mockReturnValue(token);

      // Act
      const result = await service.login(user);

      // Assert
      expect(result).toEqual({
        accessToken: token,
        user,
      });
      expect(jwtService.sign).toHaveBeenCalledWith({
        sub: user.id,
        email: user.email,
        role: user.role,
      });
    });
  });

  describe('getProfile', () => {
    it('should return user profile by ID', async () => {
      // Arrange
      const userId = '123';
      const user = {
        id: userId,
        email: '<EMAIL>',
      } as User;

      jest.spyOn(userRepository, 'findOne').mockResolvedValue(user);

      // Act
      const result = await service.getProfile(userId);

      // Assert
      expect(result).toEqual(user);
      expect(userRepository.findOne).toHaveBeenCalledWith(userId);
    });
  });

  describe('findByEmail', () => {
    it('should return user by email', async () => {
      // Arrange
      const email = '<EMAIL>';
      const user = {
        id: '123',
        email,
      } as User;

      jest.spyOn(userRepository, 'findByEmail').mockResolvedValue(user);

      // Act
      const result = await service.findByEmail(email);

      // Assert
      expect(result).toEqual(user);
      expect(userRepository.findByEmail).toHaveBeenCalledWith(email);
    });

    it('should throw NotFoundException if user not found', async () => {
      // Arrange
      const email = '<EMAIL>';
      jest.spyOn(userRepository, 'findByEmail').mockResolvedValue(null);

      // Act & Assert
      await expect(service.findByEmail(email)).rejects.toThrow(NotFoundException);
      expect(userRepository.findByEmail).toHaveBeenCalledWith(email);
    });
  });
});
