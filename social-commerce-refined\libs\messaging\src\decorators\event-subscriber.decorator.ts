import { SetMetadata } from '@nestjs/common';

export const EVENT_SUBSCRIBER_METADATA = 'event_subscriber_metadata';

export interface EventSubscriberOptions {
  /**
   * Event type to subscribe to
   * @example user.created
   */
  event: string;

  /**
   * Queue name
   * @example user_service.user_created
   */
  queue: string;
}

/**
 * Decorator for event subscriber methods
 * @param options Event subscriber options
 * @returns Method decorator
 */
export const EventSubscriber = (options: EventSubscriberOptions) =>
  SetMetadata(EVENT_SUBSCRIBER_METADATA, options);
