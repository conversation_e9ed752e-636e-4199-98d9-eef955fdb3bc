{"version": 3, "file": "test.js", "sourceRoot": "", "sources": ["../../src/commands/test.ts"], "names": [], "mappings": ";;AAMA,kCAyEC;AA9ED,+BAA+B;AAC/B,qCAAqC;AACrC,0CAA+D;AAC/D,sCAA2C;AAE3C,SAAgB,WAAW,CAAC,OAAgB;IAC1C,OAAO;SACJ,OAAO,CAAC,gBAAgB,CAAC;SACzB,WAAW,CAAC,WAAW,CAAC;SACxB,MAAM,CAAC,WAAW,EAAE,mBAAmB,CAAC;SACxC,MAAM,CAAC,aAAa,EAAE,yBAAyB,CAAC;SAChD,MAAM,CAAC,KAAK,EAAE,OAA2B,EAAE,OAA2C,EAAE,EAAE;QACzF,IAAI,CAAC;YAEH,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;gBAC7B,MAAM,QAAQ,GAAG,IAAA,sBAAc,GAAE,CAAC;gBAElC,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC1B,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAAC;oBAC/C,OAAO;gBACT,CAAC;gBAED,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,MAAM,CAAC;oBACpC;wBACE,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,SAAS;wBACf,OAAO,EAAE,oCAAoC;wBAC7C,OAAO,EAAE,CAAC,GAAG,QAAQ,EAAE,KAAK,CAAC;qBAC9B;iBACF,CAAC,CAAC;gBAEH,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;oBAC9B,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC;gBACrB,CAAC;qBAAM,CAAC;oBACN,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;gBAC5B,CAAC;YACH,CAAC;YAGD,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;gBAChB,MAAM,QAAQ,GAAG,IAAA,sBAAc,GAAE,CAAC;gBAElC,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC1B,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAAC;oBAC/C,OAAO;gBACT,CAAC;gBAED,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,yBAAyB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;gBAE3E,KAAK,MAAM,GAAG,IAAI,QAAQ,EAAE,CAAC;oBAC3B,IAAI,CAAC;wBACH,MAAM,IAAA,iBAAW,EAAC,GAAG,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;oBACxC,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,iBAAiB,GAAG,aAAa,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;oBAC7E,CAAC;gBACH,CAAC;gBAED,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC,CAAC;gBAC7D,OAAO;YACT,CAAC;YAGD,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,IAAA,qBAAa,EAAC,OAAO,CAAC,EAAE,CAAC;oBAC5B,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,OAAO,yBAAyB,CAAC,CAAC,CAAC;oBACtE,OAAO;gBACT,CAAC;gBAED,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,OAAO,aAAa,CAAC,CAAC,CAAC;gBACzD,MAAM,IAAA,iBAAW,EAAC,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;gBAC1C,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,OAAO,8BAA8B,CAAC,CAAC,CAAC;gBACnE,OAAO;YACT,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YACpD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;IACH,CAAC,CAAC,CAAC;AACP,CAAC"}