import { OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import * as amqp from 'amqplib';
export declare class RabbitMQService implements OnModuleInit, OnModuleDestroy {
    private readonly logger;
    private connection;
    private channel;
    private readonly url;
    constructor(url?: string);
    onModuleInit(): Promise<void>;
    onModuleDestroy(): Promise<void>;
    connect(): Promise<void>;
    private reconnect;
    close(): Promise<void>;
    createQueue(queue: string, options?: amqp.Options.AssertQueue): Promise<void>;
    createExchange(exchange: string, type: string, options?: amqp.Options.AssertExchange): Promise<void>;
    bindQueue(queue: string, exchange: string, routingKey: string): Promise<void>;
    publish(exchange: string, routingKey: string, message: any, options?: amqp.Options.Publish): Promise<boolean>;
    sendToQueue(queue: string, message: any, options?: amqp.Options.Publish): Promise<boolean>;
    consume(queue: string, onMessage: (msg: amqp.ConsumeMessage) => Promise<void>, options?: amqp.Options.Consume): Promise<void>;
    purgeQueue(queue: string): Promise<void>;
    deleteQueue(queue: string, options?: amqp.Options.DeleteQueue): Promise<void>;
    deleteExchange(exchange: string, options?: amqp.Options.DeleteExchange): Promise<void>;
}
