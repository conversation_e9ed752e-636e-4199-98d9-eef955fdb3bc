/**
 * Interface for user data
 */
export interface IUser {
  /**
   * User ID
   */
  id: string;

  /**
   * User email
   */
  email: string;

  /**
   * User name
   */
  name: string;

  /**
   * Whether the user's email is verified
   */
  isEmailVerified: boolean;

  /**
   * User roles
   */
  roles?: string[];

  /**
   * User creation timestamp
   */
  createdAt: Date | string;

  /**
   * User update timestamp
   */
  updatedAt: Date | string;
}
