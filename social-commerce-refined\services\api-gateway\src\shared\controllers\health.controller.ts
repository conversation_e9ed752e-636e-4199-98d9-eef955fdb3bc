import { <PERSON>, Get, Logger, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import {
  HealthCheckService,
  HealthCheck,
  HttpHealthIndicator,
  HealthCheckResult,
  HealthIndicatorResult,
  DiskHealthIndicator,
  MemoryHealthIndicator
} from '@nestjs/terminus';
import { ConfigService } from '@nestjs/config';
import { CircuitBreakerService } from '../services/circuit-breaker.service';

@ApiTags('health')
@Controller('health')
export class HealthController {
  private readonly logger = new Logger(HealthController.name);

  constructor(
    private readonly health: HealthCheckService,
    private readonly http: HttpHealthIndicator,
    private readonly disk: DiskHealthIndicator,
    private readonly memory: MemoryHealthIndicator,
    private readonly configService: ConfigService,
    private readonly circuitBreaker: CircuitBreakerService,
  ) {}

  @Get()
  @HealthCheck()
  @ApiOperation({ summary: 'Check overall system health' })
  @ApiResponse({ status: 200, description: 'System is healthy' })
  @ApiResponse({ status: 503, description: 'System is unhealthy' })
  async check() {
    this.logger.log('Overall health check requested');

    return this.health.check([
      // Check API Gateway itself
      () => Promise.resolve({ apiGateway: { status: 'up' } }),

      // Check system resources
      async () => this.disk.checkStorage('storage', { path: '/', thresholdPercent: 0.9 }),
      async () => this.memory.checkHeap('memory_heap', 300 * 1024 * 1024), // 300MB
      async () => this.memory.checkRSS('memory_rss', 300 * 1024 * 1024), // 300MB

      // Check User Service
      async () => {
        const userServiceUrl = this.configService.get<string>('USER_SERVICE_URL', 'http://localhost:3001/api');
        try {
          return await this.http.pingCheck('userService', `${userServiceUrl}/health`);
        } catch (error) {
          this.logger.warn(`User Service health check failed: ${error.message}`);
          return { userService: { status: 'down', message: error.message } };
        }
      },

      // Check Store Service
      async () => {
        const storeServiceUrl = this.configService.get<string>('STORE_SERVICE_URL', 'http://localhost:3002/api');
        try {
          return await this.http.pingCheck('storeService', `${storeServiceUrl}/health`);
        } catch (error) {
          this.logger.warn(`Store Service health check failed: ${error.message}`);
          return { storeService: { status: 'down', message: error.message } };
        }
      },

      // Check Product Service
      async () => {
        const productServiceUrl = this.configService.get<string>('PRODUCT_SERVICE_URL', 'http://localhost:3004/api');
        try {
          return await this.http.pingCheck('productService', `${productServiceUrl}/health`);
        } catch (error) {
          this.logger.warn(`Product Service health check failed: ${error.message}`);
          return { productService: { status: 'down', message: error.message } };
        }
      },

      // Check Cart Service
      async () => {
        const cartServiceUrl = this.configService.get<string>('CART_SERVICE_URL', 'http://localhost:3005/api');
        try {
          return await this.http.pingCheck('cartService', `${cartServiceUrl}/health`);
        } catch (error) {
          this.logger.warn(`Cart Service health check failed: ${error.message}`);
          return { cartService: { status: 'down', message: error.message } };
        }
      },

      // Check Order Service
      async () => {
        const orderServiceUrl = this.configService.get<string>('ORDER_SERVICE_URL', 'http://localhost:3006/api');
        try {
          return await this.http.pingCheck('orderService', `${orderServiceUrl}/health`);
        } catch (error) {
          this.logger.warn(`Order Service health check failed: ${error.message}`);
          return { orderService: { status: 'down', message: error.message } };
        }
      },
    ]);
  }

  @Get('services')
  @HealthCheck()
  @ApiOperation({ summary: 'Check all microservices health' })
  @ApiResponse({ status: 200, description: 'All services health status' })
  async checkServices() {
    this.logger.log('Services health check requested');

    return this.health.check([
      // Check User Service
      async () => {
        const userServiceUrl = this.configService.get<string>('USER_SERVICE_URL', 'http://localhost:3001/api');
        try {
          return await this.http.pingCheck('userService', `${userServiceUrl}/health`);
        } catch (error) {
          this.logger.warn(`User Service health check failed: ${error.message}`);
          return { userService: { status: 'down', message: error.message } };
        }
      },

      // Check Store Service
      async () => {
        const storeServiceUrl = this.configService.get<string>('STORE_SERVICE_URL', 'http://localhost:3002/api');
        try {
          return await this.http.pingCheck('storeService', `${storeServiceUrl}/health`);
        } catch (error) {
          this.logger.warn(`Store Service health check failed: ${error.message}`);
          return { storeService: { status: 'down', message: error.message } };
        }
      },

      // Check Product Service
      async () => {
        const productServiceUrl = this.configService.get<string>('PRODUCT_SERVICE_URL', 'http://localhost:3004/api');
        try {
          return await this.http.pingCheck('productService', `${productServiceUrl}/health`);
        } catch (error) {
          this.logger.warn(`Product Service health check failed: ${error.message}`);
          return { productService: { status: 'down', message: error.message } };
        }
      },

      // Check Cart Service
      async () => {
        const cartServiceUrl = this.configService.get<string>('CART_SERVICE_URL', 'http://localhost:3005/api');
        try {
          return await this.http.pingCheck('cartService', `${cartServiceUrl}/health`);
        } catch (error) {
          this.logger.warn(`Cart Service health check failed: ${error.message}`);
          return { cartService: { status: 'down', message: error.message } };
        }
      },

      // Check Order Service
      async () => {
        const orderServiceUrl = this.configService.get<string>('ORDER_SERVICE_URL', 'http://localhost:3006/api');
        try {
          return await this.http.pingCheck('orderService', `${orderServiceUrl}/health`);
        } catch (error) {
          this.logger.warn(`Order Service health check failed: ${error.message}`);
          return { orderService: { status: 'down', message: error.message } };
        }
      },
    ]);
  }

  @Get('service/:name')
  @HealthCheck()
  @ApiOperation({ summary: 'Check specific microservice health' })
  @ApiParam({ name: 'name', enum: ['user', 'store', 'product', 'cart', 'order'], description: 'Service name' })
  @ApiResponse({ status: 200, description: 'Service health status' })
  @ApiResponse({ status: 400, description: 'Invalid service name' })
  async checkService(@Param('name') name: string) {
    this.logger.log(`Service health check requested for: ${name}`);

    const serviceMap = {
      user: this.configService.get<string>('USER_SERVICE_URL', 'http://localhost:3001/api'),
      store: this.configService.get<string>('STORE_SERVICE_URL', 'http://localhost:3002/api'),
      product: this.configService.get<string>('PRODUCT_SERVICE_URL', 'http://localhost:3004/api'),
      cart: this.configService.get<string>('CART_SERVICE_URL', 'http://localhost:3005/api'),
      order: this.configService.get<string>('ORDER_SERVICE_URL', 'http://localhost:3006/api'),
    };

    const serviceUrl = serviceMap[name];

    if (!serviceUrl) {
      throw new Error(`Unknown service: ${name}`);
    }

    return this.health.check([
      async () => {
        try {
          return await this.http.pingCheck(name + 'Service', `${serviceUrl}/health`);
        } catch (error) {
          this.logger.warn(`${name} Service health check failed: ${error.message}`);
          return { [name + 'Service']: { status: 'down', message: error.message } };
        }
      },
    ]);
  }

  @Get('circuit-breaker')
  @ApiOperation({ summary: 'Get circuit breaker status' })
  @ApiResponse({ status: 200, description: 'Circuit breaker status' })
  getCircuitBreakerStatus() {
    this.logger.log('Circuit breaker status requested');

    return {
      status: 'ok',
      circuitBreakers: this.circuitBreaker.getStatus(),
    };
  }
}
