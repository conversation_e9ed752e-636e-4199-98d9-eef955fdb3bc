import { Controller, Post, Body, Get, UseGuards, Req, Logger, HttpCode, HttpStatus, UnauthorizedException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AuthenticationService } from '../services/authentication.service';
import { CreateUserDto } from '../dto/create-user.dto';
import { User } from '../entities/user.entity';
import { LocalAuthGuard } from '../guards/local-auth.guard';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { MessagePattern } from '@nestjs/microservices';

@ApiTags('authentication')
@Controller('auth')
export class AuthenticationController {
  private readonly logger = new Logger(AuthenticationController.name);

  constructor(private readonly authenticationService: AuthenticationService) {}

  @Post('register')
  @ApiOperation({ summary: 'Register a new user' })
  @ApiResponse({ status: 201, description: 'User registered successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 409, description: 'User already exists' })
  async register(@Body() createUserDto: CreateUserDto): Promise<User> {
    this.logger.log(`Registration request received for: ${createUserDto.email}`);
    return this.authenticationService.register(createUserDto);
  }

  @Post('login')
  @UseGuards(LocalAuthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Login a user' })
  @ApiResponse({ status: 200, description: 'Login successful' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async login(@Body() loginDto: { email: string; password: string }, @Req() req: any): Promise<{ accessToken: string; user: User }> {
    this.logger.log(`Login request received for: ${loginDto.email}`);

    // At this point, authentication has already been performed by the LocalAuthGuard
    // The user object is attached to the request by Passport
    if (!req.user) {
      this.logger.error('Login endpoint reached without a user object in the request');
      throw new UnauthorizedException('Authentication failed');
    }

    this.logger.log(`User authenticated successfully: ${req.user.email} (ID: ${req.user.id})`);
    return this.authenticationService.login(req.user);
  }

  @Get('profile')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user profile' })
  @ApiResponse({ status: 200, description: 'Profile retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getProfile(@Req() req: any): Promise<User> {
    this.logger.log(`Profile request received for user ID: ${req.user.sub}`);
    return this.authenticationService.getProfile(req.user.sub);
  }

  // Microservice endpoints

  @MessagePattern('register_user')
  async registerUser(createUserDto: CreateUserDto): Promise<User> {
    this.logger.log(`Microservice registration request received for: ${createUserDto.email}`);
    return this.authenticationService.register(createUserDto);
  }

  @MessagePattern('validate_user')
  async validateUser(data: { email: string; password: string }): Promise<User> {
    this.logger.log(`Microservice validation request received for: ${data.email}`);
    return this.authenticationService.validateUser(data.email, data.password);
  }

  @MessagePattern('find_user_by_email')
  async findUserByEmail(email: string): Promise<User> {
    this.logger.log(`Microservice find user by email request received for: ${email}`);
    return this.authenticationService.findByEmail(email);
  }

  @MessagePattern('find_user_by_id')
  async findUserById(id: string): Promise<User> {
    this.logger.log(`Microservice find user by ID request received for: ${id}`);
    return this.authenticationService.getProfile(id);
  }
}
