# Order Service API Documentation

## 🎯 Overview
Complete API documentation for the Order Service, including all endpoints, request/response formats, authentication, and examples.

**Base URL**: `http://localhost:3006/api` (Direct) or `http://localhost:3000/api` (via API Gateway)
**Authentication**: Bearer JWT Token
**Content-Type**: `application/json`

## 🔐 Authentication
All endpoints require JWT authentication except health checks.

```bash
# Include JWT token in Authorization header
Authorization: Bearer <your-jwt-token>
```

## 📋 Order Management Endpoints

### **1. Create Order**
```http
POST /api/orders
```

**Description**: Create a new order from cart items or direct product selection.

**Request Body**:
```json
{
  "cartId": "uuid-string",           // Optional: Create from cart
  "items": [                         // Optional: Direct item specification
    {
      "productId": "uuid-string",
      "quantity": 2,
      "price": 29.99
    }
  ],
  "shippingAddress": {
    "fullName": "<PERSON> Doe",
    "addressLine1": "123 Main St",
    "addressLine2": "Apt 4B",
    "city": "New York",
    "state": "NY",
    "postalCode": "10001",
    "country": "USA"
  },
  "billingAddress": {               // Optional: defaults to shipping
    "fullName": "John Doe",
    "addressLine1": "123 Main St",
    "city": "New York",
    "state": "NY",
    "postalCode": "10001",
    "country": "USA"
  },
  "paymentMethod": "credit_card",
  "notes": "Please deliver after 5 PM"
}
```

**Response** (201 Created):
```json
{
  "id": "order-uuid",
  "orderNumber": "ORD-2025-001234",
  "userId": "user-uuid",
  "status": "PENDING",
  "paymentStatus": "PENDING",
  "items": [
    {
      "id": "item-uuid",
      "productId": "product-uuid",
      "quantity": 2,
      "price": 29.99,
      "total": 59.98,
      "productTitle": "Sample Product",
      "productImage": "https://example.com/image.jpg",
      "storeId": "store-uuid",
      "storeName": "Sample Store"
    }
  ],
  "subtotal": 59.98,
  "shipping": 5.99,
  "tax": 6.60,
  "total": 72.57,
  "discount": 0.00,
  "shippingAddress": { /* address object */ },
  "billingAddress": { /* address object */ },
  "paymentMethod": "credit_card",
  "notes": "Please deliver after 5 PM",
  "createdAt": "2025-05-30T17:00:00.000Z",
  "updatedAt": "2025-05-30T17:00:00.000Z"
}
```

### **2. Get User Orders**
```http
GET /api/orders
```

**Description**: Get all orders for the authenticated user.

**Query Parameters**:
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `status` (optional): Filter by order status

**Response** (200 OK):
```json
{
  "orders": [
    {
      "id": "order-uuid",
      "orderNumber": "ORD-2025-001234",
      "status": "PENDING",
      "paymentStatus": "PENDING",
      "total": 72.57,
      "itemCount": 2,
      "createdAt": "2025-05-30T17:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 1,
    "totalPages": 1
  }
}
```

### **3. Get Order by ID**
```http
GET /api/orders/:id
```

**Description**: Get detailed information about a specific order.

**Path Parameters**:
- `id`: Order UUID

**Response** (200 OK):
```json
{
  "id": "order-uuid",
  "orderNumber": "ORD-2025-001234",
  "userId": "user-uuid",
  "status": "PENDING",
  "paymentStatus": "PENDING",
  "items": [
    {
      "id": "item-uuid",
      "productId": "product-uuid",
      "quantity": 2,
      "price": 29.99,
      "total": 59.98,
      "productTitle": "Sample Product",
      "productImage": "https://example.com/image.jpg",
      "storeId": "store-uuid",
      "storeName": "Sample Store"
    }
  ],
  "subtotal": 59.98,
  "shipping": 5.99,
  "tax": 6.60,
  "total": 72.57,
  "discount": 0.00,
  "shippingAddress": { /* address object */ },
  "billingAddress": { /* address object */ },
  "paymentMethod": "credit_card",
  "notes": "Please deliver after 5 PM",
  "createdAt": "2025-05-30T17:00:00.000Z",
  "updatedAt": "2025-05-30T17:00:00.000Z"
}
```

### **4. Get Order by Order Number**
```http
GET /api/orders/number/:orderNumber
```

**Description**: Get order details using the human-readable order number.

**Path Parameters**:
- `orderNumber`: Order number (e.g., "ORD-2025-001234")

**Response**: Same as Get Order by ID

### **5. Update Order**
```http
PATCH /api/orders/:id
```

**Description**: Update order details (status, payment status, addresses, etc.).

**Request Body**:
```json
{
  "status": "CONFIRMED",
  "paymentStatus": "PAID",
  "shippingAddress": {
    "fullName": "John Doe",
    "addressLine1": "456 New St",
    "city": "Boston",
    "state": "MA",
    "postalCode": "02101",
    "country": "USA"
  },
  "notes": "Updated delivery instructions"
}
```

**Response** (200 OK): Updated order object

### **6. Cancel Order**
```http
POST /api/orders/:id/cancel
```

**Description**: Cancel an order (only if status allows cancellation).

**Response** (200 OK):
```json
{
  "id": "order-uuid",
  "status": "CANCELLED",
  "message": "Order cancelled successfully"
}
```

### **7. Delete Order**
```http
DELETE /api/orders/:id
```

**Description**: Delete an order (admin only, or user for pending orders).

**Response** (200 OK):
```json
{
  "message": "Order deleted successfully"
}
```

## 🏪 Store-Specific Endpoints

### **8. Get Orders by Store**
```http
GET /api/orders/store/:storeId
```

**Description**: Get all orders for a specific store (store owner only).

**Path Parameters**:
- `storeId`: Store UUID

**Query Parameters**:
- `page`, `limit`, `status`: Same as user orders

**Response**: List of orders for the store

## 👑 Admin Endpoints

### **9. Get All Orders (Admin)**
```http
GET /api/orders/all
```

**Description**: Get all orders in the system (admin only).

**Query Parameters**:
- `page`, `limit`, `status`: Pagination and filtering
- `userId`: Filter by user
- `storeId`: Filter by store

**Response**: Paginated list of all orders

## 🏥 Health Check Endpoints

### **10. Comprehensive Health Check**
```http
GET /api/health
```

**Description**: Detailed health status including database and dependencies.

**Response** (200 OK):
```json
{
  "status": "ok",
  "info": {
    "database": {"status": "up"},
    "memory_heap": {"status": "up"},
    "memory_rss": {"status": "up"},
    "storage": {"status": "up"}
  },
  "error": {},
  "details": {
    "database": {"status": "up"},
    "memory_heap": {"status": "up"},
    "memory_rss": {"status": "up"},
    "storage": {"status": "up"}
  }
}
```

### **11. Simple Health Check**
```http
GET /api/health/simple
```

**Description**: Basic health status.

**Response** (200 OK):
```json
{
  "status": "ok",
  "timestamp": "2025-05-30T17:00:00.000Z",
  "service": "order-service",
  "version": "1.0.0"
}
```

## 📊 Data Models

### **Order Status Values**
- `PENDING`: Order created, awaiting confirmation
- `CONFIRMED`: Order confirmed, processing
- `SHIPPED`: Order shipped to customer
- `DELIVERED`: Order delivered successfully
- `CANCELLED`: Order cancelled
- `RETURNED`: Order returned by customer

### **Payment Status Values**
- `PENDING`: Payment not processed
- `PAID`: Payment completed successfully
- `FAILED`: Payment failed
- `REFUNDED`: Payment refunded
- `PARTIAL_REFUND`: Partial refund issued

### **Address Object**
```json
{
  "fullName": "string",
  "addressLine1": "string",
  "addressLine2": "string (optional)",
  "city": "string",
  "state": "string",
  "postalCode": "string",
  "country": "string"
}
```

## ❌ Error Responses

### **Common Error Codes**
- `400`: Bad Request - Invalid input data
- `401`: Unauthorized - Missing or invalid JWT token
- `403`: Forbidden - Insufficient permissions
- `404`: Not Found - Order not found
- `409`: Conflict - Invalid status transition
- `500`: Internal Server Error - Server error

### **Error Response Format**
```json
{
  "statusCode": 404,
  "timestamp": "2025-05-30T17:00:00.000Z",
  "path": "/api/orders/invalid-id",
  "error": "Not Found",
  "message": "Order not found"
}
```

## 🧪 Testing Examples

### **Create Order Test**
```bash
curl -X POST http://localhost:3000/api/orders \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "items": [
      {
        "productId": "product-uuid",
        "quantity": 1,
        "price": 29.99
      }
    ],
    "shippingAddress": {
      "fullName": "Test User",
      "addressLine1": "123 Test St",
      "city": "Test City",
      "state": "TS",
      "postalCode": "12345",
      "country": "US"
    },
    "paymentMethod": "credit_card"
  }'
```

### **Get Orders Test**
```bash
curl -X GET http://localhost:3000/api/orders \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

---

**API Version**: 1.0
**Last Updated**: May 30, 2025
**Swagger Documentation**: Available at `/api/docs`
