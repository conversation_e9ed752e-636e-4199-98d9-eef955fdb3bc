import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { VerificationController } from './controllers/verification.controller';
import { VerificationService } from './services/verification.service';
import { VerificationRepository } from './repositories/verification.repository';
import { VerificationToken } from './entities/verification-token.entity';
import { AuthenticationModule } from '../authentication/authentication.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([VerificationToken]),
    AuthenticationModule,
  ],
  controllers: [VerificationController],
  providers: [
    VerificationService,
    VerificationRepository,
  ],
  exports: [
    VerificationService,
    VerificationRepository,
  ],
})
export class VerificationModule {}
