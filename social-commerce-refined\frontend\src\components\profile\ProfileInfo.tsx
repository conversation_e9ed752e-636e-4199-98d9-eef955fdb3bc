import React from 'react';
import {
  Box,
  Heading,
  Text,
  SimpleGrid,
  Divider,
  useColorModeValue,
  Icon,
  Flex,
} from '@chakra-ui/react';
import { FiMail, FiPhone, FiMapPin, FiCalendar, FiGlobe } from 'react-icons/fi';

interface ProfileInfoProps {
  personalInfo: {
    email: string;
    phone?: string;
    address?: string;
    dateOfBirth?: string;
    website?: string;
    bio?: string;
  };
}

const ProfileInfo: React.FC<ProfileInfoProps> = ({ personalInfo }) => {
  const bgColor = useColorModeValue('white', 'gray.700');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  return (
    <Box
      bg={bgColor}
      borderRadius="lg"
      borderWidth="1px"
      borderColor={borderColor}
      p={6}
      mb={6}
    >
      <Heading size="md" mb={4}>
        Personal Information
      </Heading>
      
      <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
        <InfoItem
          icon={FiMail}
          label="Email"
          value={personalInfo.email}
        />
        
        {personalInfo.phone && (
          <InfoItem
            icon={FiPhone}
            label="Phone"
            value={personalInfo.phone}
          />
        )}
        
        {personalInfo.address && (
          <InfoItem
            icon={FiMapPin}
            label="Address"
            value={personalInfo.address}
          />
        )}
        
        {personalInfo.dateOfBirth && (
          <InfoItem
            icon={FiCalendar}
            label="Date of Birth"
            value={personalInfo.dateOfBirth}
          />
        )}
        
        {personalInfo.website && (
          <InfoItem
            icon={FiGlobe}
            label="Website"
            value={personalInfo.website}
            isLink
          />
        )}
      </SimpleGrid>
      
      {personalInfo.bio && (
        <>
          <Divider my={4} />
          <Box>
            <Text fontWeight="medium" mb={2}>
              Bio
            </Text>
            <Text color="gray.600">
              {personalInfo.bio}
            </Text>
          </Box>
        </>
      )}
    </Box>
  );
};

interface InfoItemProps {
  icon: React.ElementType;
  label: string;
  value: string;
  isLink?: boolean;
}

const InfoItem: React.FC<InfoItemProps> = ({ icon, label, value, isLink }) => {
  return (
    <Flex align="center" mb={2}>
      <Icon as={icon} mr={2} color="brand.500" />
      <Box>
        <Text fontSize="sm" color="gray.500">
          {label}
        </Text>
        {isLink ? (
          <Text
            as="a"
            href={value.startsWith('http') ? value : `https://${value}`}
            target="_blank"
            rel="noopener noreferrer"
            color="brand.500"
            fontWeight="medium"
          >
            {value}
          </Text>
        ) : (
          <Text fontWeight="medium">{value}</Text>
        )}
      </Box>
    </Flex>
  );
};

export default ProfileInfo;
