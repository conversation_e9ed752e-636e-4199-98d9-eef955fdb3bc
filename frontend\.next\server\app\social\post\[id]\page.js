(()=>{var e={};e.id=1485,e.ids=[1485],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},12208:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var a=t(67096),r=t(16132),o=t(37284),n=t.n(o),i=t(32564),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(s,l);let d=["",{children:["social",{children:["post",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,95751)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\social\\post\\[id]\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9291,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\social\\post\\[id]\\page.tsx"],m="/social/post/[id]/page",x={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/social/post/[id]/page",pathname:"/social/post/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},72241:(e,s,t)=>{Promise.resolve().then(t.bind(t,34158))},34158:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>PostPage});var a=t(30784),r=t(9885),o=t(27870),n=t(57114),i=t(51020),l=t(40446),d=t(52451),c=t.n(d),m=t(11440),x=t.n(m),u=t(73531),p=t(19923);let social_CommentItem=({comment:e,className:s=""})=>{let{t}=(0,o.$G)("social"),[n,l]=(0,r.useState)(!1),{data:d}=(0,p.Mx)(),[m,{isLoading:h}]=(0,i.so)(),[g]=(0,i.iN)(),y=d?.id===e.userId,handleDelete=async()=>{if(window.confirm(t("social.confirmDeleteComment","Are you sure you want to delete this comment?")))try{await m({postId:e.postId,commentId:e.id}).unwrap()}catch(e){console.error("Failed to delete comment:",e)}},handleReaction=async s=>{try{await g({postId:e.postId,commentId:e.id,type:s}).unwrap()}catch(e){console.error("Failed to react to comment:",e)}},v=e.createdAt?(0,u.Z)(new Date(e.createdAt),{addSuffix:!0}):"";return(0,a.jsxs)("div",{className:`flex items-start ${s}`,children:[a.jsx("div",{className:"flex-shrink-0 mr-3",children:a.jsx(x(),{href:`/profile/${e.user.username}`,children:a.jsx("div",{className:"w-8 h-8 relative rounded-full overflow-hidden",children:a.jsx(c(),{src:e.user.avatarUrl||"/images/default-avatar.png",alt:e.user.displayName||e.user.username,fill:!0,className:"object-cover"})})})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"bg-gray-100 dark:bg-gray-700 rounded-lg px-4 py-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(x(),{href:`/profile/${e.user.username}`,className:"font-medium text-gray-900 dark:text-gray-100 hover:underline",children:[e.user.displayName||e.user.username,e.user.isVerified&&a.jsx("span",{className:"ml-1 text-primary-600 dark:text-primary-400",children:"✓"})]}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx("button",{type:"button",className:"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300",onClick:()=>l(!n),children:a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"})})}),n&&a.jsx("div",{className:"absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg z-10",children:(0,a.jsxs)("div",{className:"py-1",children:[y&&a.jsx("button",{type:"button",className:"w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:handleDelete,disabled:h,children:t("social.deleteComment","Delete Comment")}),a.jsx("button",{type:"button",className:"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:()=>l(!1),children:t("social.reportComment","Report Comment")})]})})]})]}),a.jsx("p",{className:"text-gray-800 dark:text-gray-200 mt-1",children:e.content})]}),(0,a.jsxs)("div",{className:"flex items-center mt-1 text-xs text-gray-500 dark:text-gray-400",children:[a.jsx("span",{children:v}),a.jsx("span",{className:"mx-2",children:"•"}),a.jsx("button",{type:"button",className:`hover:text-gray-700 dark:hover:text-gray-300 ${e.userReacted?"text-primary-600 dark:text-primary-400":""}`,onClick:()=>handleReaction("like"),children:t("social.like","Like")}),a.jsx("span",{className:"mx-2",children:"•"}),(0,a.jsxs)("span",{children:[e.reactionCount," ",1===e.reactionCount?t("social.like","like"):t("social.likes","likes")]})]})]})]})};var h=t(59872);let social_CommentList=({postId:e,limit:s=10,className:t=""})=>{let{t:n}=(0,o.$G)("social"),[l,d]=(0,r.useState)(1),{data:c,isLoading:m,error:x}=(0,i.jF)({postId:e,page:l,limit:s}),u=c?.comments||[],p=c?.total||0,g=p>l*s;return m&&!c?a.jsx("div",{className:`space-y-4 ${t}`,children:Array.from({length:3}).map((e,s)=>a.jsx("div",{className:"animate-pulse",children:(0,a.jsxs)("div",{className:"flex items-start",children:[a.jsx("div",{className:"w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full"}),(0,a.jsxs)("div",{className:"ml-3 flex-1",children:[a.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-2"}),a.jsx("div",{className:"h-3 bg-gray-200 dark:bg-gray-700 rounded w-full"})]})]})},s))}):x?a.jsx("div",{className:`p-4 text-red-500 dark:text-red-400 ${t}`,children:n("social.errorLoadingComments","Error loading comments")}):0===u.length?a.jsx("div",{className:`p-4 text-gray-500 dark:text-gray-400 text-center ${t}`,children:n("social.noComments","No comments yet. Be the first to comment!")}):(0,a.jsxs)("div",{className:`space-y-4 ${t}`,children:[u.map(e=>a.jsx(social_CommentItem,{comment:e},e.id)),g&&a.jsx("div",{className:"flex justify-center mt-4",children:a.jsx(h.Z,{variant:"outline",size:"sm",onClick:()=>{d(l+1)},isLoading:m,children:n("social.loadMoreComments","Load More Comments")})})]})};var g=t(84694),y=t(2522);function PostPage({params:e}){let{id:s}=e,{t}=(0,o.$G)("social"),d=(0,n.useRouter)(),[c,m]=(0,r.useState)(!1),{data:x,isLoading:u,error:p}=(0,i.useGetPostByIdQuery)(s),[v,{isLoading:j}]=(0,i.sH)(),[b]=(0,i.CH)(),[f]=(0,i.useRemoveReactionFromPostMutation)(),handleCommentSubmit=async e=>{if(e.trim())try{await v({postId:s,content:e.trim()}).unwrap()}catch(e){console.error("Failed to create comment:",e)}},handleReaction=async e=>{if(x)try{let t=x.reactions.find(s=>s.type===e);t?.userReacted?await f(s).unwrap():await b({postId:s,type:e}).unwrap()}catch(e){console.error("Failed to react to post:",e)}};return u?a.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 py-8",children:a.jsx("div",{className:"max-w-3xl mx-auto px-4 sm:px-6",children:a.jsx("div",{className:"flex items-center justify-center py-12",children:a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"})})})}):p||!x?a.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 py-8",children:a.jsx("div",{className:"max-w-3xl mx-auto px-4 sm:px-6",children:a.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6",children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center py-6",children:[a.jsx("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:t("social.postNotFound","Post Not Found")}),a.jsx("p",{className:"text-gray-500 dark:text-gray-400 mb-6",children:t("social.postNotFoundDesc","The post you are looking for does not exist or has been removed.")}),a.jsx(h.Z,{onClick:()=>d.push("/social/feed"),children:t("social.backToFeed","Back to Feed")})]})})})}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 py-8",children:[(0,a.jsxs)("div",{className:"max-w-3xl mx-auto px-4 sm:px-6",children:[a.jsx("div",{className:"mb-6",children:(0,a.jsxs)(h.Z,{variant:"outline",onClick:()=>d.back(),className:"mb-4",children:[a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),t("social.back","Back")]})}),a.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden mb-6",children:a.jsx(l.Z,{post:x,onReaction:handleReaction,onShare:()=>{m(!0)},expanded:!0})}),(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4",children:[(0,a.jsxs)("h2",{className:"text-lg font-semibold mb-4",children:[t("social.comments","Comments")," (",x.commentCount||0,")"]}),a.jsx("div",{className:"mb-6",children:a.jsx(g.Z,{onSubmit:handleCommentSubmit,isLoading:j})}),a.jsx(social_CommentList,{postId:s})]})]}),c&&a.jsx(y.Z,{post:x,onClose:()=>m(!1)})]})}},95751:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>n,__esModule:()=>o,default:()=>l});var a=t(95153);let r=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\social\post\[id]\page.tsx`),{__esModule:o,$$typeof:n}=r,i=r.default,l=i}};var s=require("../../../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),t=s.X(0,[2103,2765,2763,3902,1020,2522,4694],()=>__webpack_exec__(12208));module.exports=t})();