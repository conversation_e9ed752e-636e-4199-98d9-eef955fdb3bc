import { Command } from 'commander';
import * as chalk from 'chalk';
import * as inquirer from 'inquirer';
import { getAllServices, serviceExists } from '../utils/paths';
import { installDependencies } from '../utils/npm';

export function installCommand(program: Command): void {
  program
    .command('install [service]')
    .description('Install dependencies')
    .option('-a, --all', 'Install dependencies for all services')
    .action(async (service: string | undefined, options: { all?: boolean }) => {
      try {
        // If no service is specified and --all is not set, prompt for service
        if (!service && !options.all) {
          const services = getAllServices();
          
          if (services.length === 0) {
            console.log(chalk.yellow('No services found'));
            return;
          }
          
          const answers = await inquirer.prompt([
            {
              type: 'list',
              name: 'service',
              message: 'Which service do you want to install dependencies for?',
              choices: [...services, 'all'],
            },
          ]);
          
          if (answers.service === 'all') {
            options.all = true;
          } else {
            service = answers.service;
          }
        }

        // Install dependencies for all services
        if (options.all) {
          const services = getAllServices();
          
          if (services.length === 0) {
            console.log(chalk.yellow('No services found'));
            return;
          }
          
          console.log(chalk.blue(`Installing dependencies for all services: ${services.join(', ')}...`));
          
          for (const svc of services) {
            try {
              await installDependencies(svc);
            } catch (error) {
              console.error(chalk.red(`Error installing dependencies for ${svc}-service: ${error.message}`));
            }
          }
          
          console.log(chalk.green('Dependencies installed successfully for all services'));
          return;
        }

        // Install dependencies for specific service
        if (service) {
          if (!serviceExists(service)) {
            console.error(chalk.red(`Service ${service}-service does not exist`));
            return;
          }
          
          console.log(chalk.blue(`Installing dependencies for ${service}-service...`));
          await installDependencies(service);
          console.log(chalk.green(`Dependencies installed successfully for ${service}-service`));
          return;
        }
      } catch (error) {
        console.error(chalk.red(`Error: ${error.message}`));
        process.exit(1);
      }
    });
}
