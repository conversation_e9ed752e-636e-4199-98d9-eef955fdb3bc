/**
 * Interface for order data
 */
export interface IOrder {
  /**
   * Order ID
   */
  id: string;

  /**
   * User ID
   */
  userId: string;

  /**
   * Total amount
   */
  totalAmount: number;

  /**
   * Order status
   */
  status: string;

  /**
   * Order items
   */
  items: IOrderItem[];

  /**
   * Order creation timestamp
   */
  createdAt: Date | string;

  /**
   * Order update timestamp
   */
  updatedAt: Date | string;
}

/**
 * Interface for order item data
 */
export interface IOrderItem {
  /**
   * Product ID
   */
  productId: string;

  /**
   * Product name
   */
  productName: string;

  /**
   * Quantity
   */
  quantity: number;

  /**
   * Price per unit
   */
  price: number;
}
