import { BaseEvent } from '../base-event.interface';
export declare class ProductCreatedEvent implements BaseEvent<ProductCreatedPayload> {
    id: string;
    type: string;
    version: string;
    timestamp: string;
    producer: string;
    payload: ProductCreatedPayload;
    constructor(payload: ProductCreatedPayload);
}
export interface ProductCreatedPayload {
    id: string;
    storeId: string;
    name: string;
    description: string;
    price: number;
    inventory: number;
    categories: string[];
    createdAt: string;
}
