(()=>{var e={};e.id=500,e.ids=[500],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},89574:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>n.a,__next_app__:()=>h,originalPathname:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var r=a(67096),t=a(16132),i=a(37284),n=a.n(i),d=a(32564),l={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);a.d(s,l);let o=["",{children:["wishlist",{children:["share",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,34681)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\wishlist\\share\\[id]\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,9291,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\wishlist\\share\\[id]\\page.tsx"],m="/wishlist/share/[id]/page",h={require:a,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/wishlist/share/[id]/page",pathname:"/wishlist/share/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},94358:(e,s,a)=>{Promise.resolve().then(a.bind(a,21476))},21476:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>SharedWishlistPage});var r=a(30784),t=a(9885),i=a(57114),n=a(21022),d=a(59872),l=a(86867),o=a(2280),c=a(19923),m=a(25533);function SharedWishlistPage({params:e}){let{id:s}=e,a=(0,i.useRouter)(),[h,x]=(0,t.useState)([]),{data:u}=(0,c.Mx)(),{data:p,isLoading:g}=(0,l._x)(s),{data:v,isLoading:b}=(0,l.cd)(s),[j]=(0,o.$B)(),y=g||b,w=u?.id===p?.userId,handleAddToCart=async e=>{x(s=>[...s,e]);try{await j({productId:e,quantity:1}).unwrap()}catch(e){console.error("Failed to add item to cart:",e)}finally{x(s=>s.filter(s=>s!==e))}},handleAddAllToCart=async()=>{if(v)for(let e of v)await handleAddToCart(e.productId)};return y?r.jsx("div",{className:"min-h-screen p-6",children:r.jsx("div",{className:"max-w-4xl mx-auto",children:(0,r.jsxs)("div",{className:"animate-pulse",children:[r.jsx("div",{className:"h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-6"}),r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-8"}),r.jsx("div",{className:"space-y-4",children:[void 0,void 0,void 0].map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center py-4",children:[r.jsx("div",{className:"h-20 w-20 bg-gray-200 dark:bg-gray-700 rounded-md"}),(0,r.jsxs)("div",{className:"ml-4 flex-1",children:[r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"}),r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"})]}),r.jsx("div",{className:"ml-4 h-8 w-20 bg-gray-200 dark:bg-gray-700 rounded"})]},s))})]})})}):p?p.isPublic||w?r.jsx("div",{className:"min-h-screen p-6",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[r.jsx("h1",{className:"text-2xl font-bold",children:p.name}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx(d.Z,{variant:"outline",onClick:()=>a.push("/products"),children:"Continue Shopping"}),w&&r.jsx(d.Z,{variant:"outline",onClick:()=>a.push("/wishlist"),children:"My Wishlists"})]})]}),(0,r.jsxs)("div",{className:"flex items-center mb-6",children:[r.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:w?"Your wishlist":`${p.name} by User`}),r.jsx("div",{className:"ml-4",children:r.jsx(m.Z,{url:"",title:`Check out this wishlist: ${p.name}`})})]}),(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6",children:[v&&v.length>0&&r.jsx("div",{className:"flex justify-end mb-4",children:r.jsx(d.Z,{onClick:handleAddAllToCart,variant:"primary",size:"sm",children:"Add All to Cart"})}),r.jsx(n.Z,{items:v||[],isLoading:y,onRemoveItem:async()=>{},onMoveToCart:handleAddToCart,emptyMessage:w?"Your wishlist is empty. Add products to your wishlist to save them for later.":"This wishlist is empty."})]})]})}):r.jsx("div",{className:"min-h-screen p-6",children:r.jsx("div",{className:"max-w-4xl mx-auto",children:(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center",children:[r.jsx("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:"Private Wishlist"}),r.jsx("p",{className:"mb-6",children:"This wishlist is private and can only be viewed by its owner."}),r.jsx(d.Z,{onClick:()=>a.push("/"),children:"Back to Home"})]})})}):r.jsx("div",{className:"min-h-screen p-6",children:r.jsx("div",{className:"max-w-4xl mx-auto",children:(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center",children:[r.jsx("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:"Wishlist Not Found"}),r.jsx("p",{className:"mb-6",children:"The wishlist you're looking for doesn't exist or is not public."}),r.jsx(d.Z,{onClick:()=>a.push("/"),children:"Back to Home"})]})})})}},34681:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>n,__esModule:()=>i,default:()=>l});var r=a(95153);let t=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\wishlist\share\[id]\page.tsx`),{__esModule:i,$$typeof:n}=t,d=t.default,l=d}};var s=require("../../../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),a=s.X(0,[2103,2765,1022,5533],()=>__webpack_exec__(89574));module.exports=a})();