const { Client } = require('pg');
const bcrypt = require('bcrypt');

async function testLoginDirect() {
  try {
    console.log('Connecting to PostgreSQL database...');
    const client = new Client({
      host: 'localhost',
      port: 5432,
      database: 'social_commerce',
      user: 'postgres',
      password: '1111'
    });

    await client.connect();
    console.log('Connected to PostgreSQL database');

    // Test login for simpletestuser
    const username = 'simpletestuser';
    const password = 'password123';
    
    console.log('Testing login for user:', username);
    
    // Find the user
    console.log('Finding user...');
    const findResult = await client.query(
      'SELECT id, username, email, role, "passwordHash" FROM users WHERE username = $1',
      [username]
    );
    
    if (findResult.rows.length === 0) {
      console.log('User not found');
      await client.end();
      return;
    }
    
    const user = findResult.rows[0];
    console.log('User found:', {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      passwordHash: user.passwordHash ? user.passwordHash.substring(0, 10) + '...' : 'Missing'
    });
    
    // Test password verification
    console.log('Testing password verification...');
    const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
    console.log('Password valid?', isPasswordValid);
    
    // Create a new hash for comparison
    const newHash = await bcrypt.hash(password, 10);
    console.log('New hash created:', newHash);
    
    // Compare the new hash with the stored hash
    const hashesMatch = await bcrypt.compare(password, newHash);
    console.log('New hash valid?', hashesMatch);
    
    // If the password is not valid, try to update it
    if (!isPasswordValid) {
      console.log('Password is not valid, updating it...');
      const updatedHash = await bcrypt.hash(password, 10);
      
      await client.query(
        'UPDATE users SET "passwordHash" = $1 WHERE id = $2',
        [updatedHash, user.id]
      );
      
      console.log('Password updated successfully');
      
      // Verify the updated password
      const updatedFindResult = await client.query(
        'SELECT id, username, email, role, "passwordHash" FROM users WHERE id = $1',
        [user.id]
      );
      
      const updatedUser = updatedFindResult.rows[0];
      const isUpdatedPasswordValid = await bcrypt.compare(password, updatedUser.passwordHash);
      console.log('Updated password valid?', isUpdatedPasswordValid);
    }
    
    await client.end();
    console.log('Disconnected from PostgreSQL database');
    
    console.log('\nLogin credentials for testing:');
    console.log('Username:', username);
    console.log('Password:', password);
  } catch (error) {
    console.error('Error:', error);
  }
}

testLoginDirect();
