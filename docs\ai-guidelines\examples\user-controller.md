# User Controller Example

This document provides an example of a correctly implemented User Controller following the Social Commerce Platform's patterns and best practices.

## Controller Implementation

```typescript
import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Put,
  UseGuards,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { UserService } from './user.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { User } from './entities/user.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { Public } from '@app/common';

@ApiTags('users')
@Controller('users')
export class UserController {
  private readonly logger = new Logger(UserController.name);

  constructor(private readonly userService: UserService) {}

  @Post()
  @Public()
  @ApiOperation({ summary: 'Create a new user' })
  @ApiResponse({ status: 201, description: 'User created successfully', type: User })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async create(@Body() createUserDto: CreateUserDto): Promise<User> {
    this.logger.log(`Creating new user with email: ${createUserDto.email}`);
    
    try {
      return await this.userService.create(createUserDto);
    } catch (error) {
      this.logger.error(`Error creating user: ${error.message}`, error.stack);
      
      if (error.code === '23505') { // PostgreSQL unique violation
        throw new BadRequestException('Email already exists');
      }
      
      throw error;
    }
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all users' })
  @ApiResponse({ status: 200, description: 'Return all users', type: [User] })
  async findAll(): Promise<User[]> {
    this.logger.log('Getting all users');
    
    try {
      return await this.userService.findAll();
    } catch (error) {
      this.logger.error(`Error getting all users: ${error.message}`, error.stack);
      throw error;
    }
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get a user by ID' })
  @ApiResponse({ status: 200, description: 'Return the user', type: User })
  @ApiResponse({ status: 404, description: 'User not found' })
  async findOne(@Param('id') id: string): Promise<User> {
    this.logger.log(`Getting user with ID: ${id}`);
    
    try {
      const user = await this.userService.findOne(id);
      
      if (!user) {
        throw new NotFoundException(`User with ID ${id} not found`);
      }
      
      return user;
    } catch (error) {
      this.logger.error(`Error getting user: ${error.message}`, error.stack);
      throw error;
    }
  }
}
```

## Key Points to Note

1. **Proper Imports**: All necessary imports are included
2. **API Documentation**: Swagger decorators are used for API documentation
3. **Error Handling**: Try/catch blocks with proper error logging
4. **Authentication**: JWT guards are applied to protected routes
5. **Logging**: Consistent logging throughout the controller
6. **Message Patterns**: Microservice message patterns are implemented correctly
7. **Response Types**: Return types are specified for all methods
8. **Exception Handling**: Appropriate exceptions are thrown with meaningful messages
9. **Validation**: DTOs are used for input validation

## Common Mistakes to Avoid

1. ❌ Missing try/catch blocks
2. ❌ Inconsistent logging
3. ❌ Missing API documentation
4. ❌ Not handling specific error cases (like duplicate emails)
5. ❌ Not checking if entities exist before operations
6. ❌ Not using DTOs for input validation
7. ❌ Missing authentication guards on protected routes
8. ❌ Not following the established naming conventions

By following this example, AI assistants can ensure they generate controllers that adhere to the project's standards and best practices.
