import { Module, DynamicModule } from '@nestjs/common';
import { RabbitMQModule } from './rabbitmq.module';
import { EventPublisherService } from './event-publisher.service';
import { RabbitMQOptions } from '../interfaces/rabbitmq-options.interface';

@Module({})
export class EventPublisherModule {
  static register(options?: RabbitMQOptions): DynamicModule {
    return {
      module: EventPublisherModule,
      imports: [RabbitMQModule.register(options)],
      providers: [EventPublisherService],
      exports: [EventPublisherService],
    };
  }

  static registerAsync(options: {
    useFactory: (...args: any[]) => Promise<RabbitMQOptions> | RabbitMQOptions;
    inject?: any[];
  }): DynamicModule {
    return {
      module: EventPublisherModule,
      imports: [RabbitMQModule.registerAsync(options)],
      providers: [EventPublisherService],
      exports: [EventPublisherService],
    };
  }
}
