exports.id=9336,exports.ids=[9336],exports.modules={22914:(e,r,s)=>{Promise.resolve().then(s.bind(s,66281))},66281:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>__WEBPACK_DEFAULT_EXPORT__});var a=s(30784),t=s(9885),o=s(57114),n=s(11440),i=s.n(n),l=s(27870),d=s(14379),m=s(706),g=s(59872),c=s(19923);let __WEBPACK_DEFAULT_EXPORT__=()=>{let[e,r]=(0,t.useState)("login"),[s,n]=(0,t.useState)({username:"",email:"",phone:"",password:"",confirmPassword:""}),[h,u]=(0,t.useState)({}),p=(0,o.useRouter)(),{t:w}=(0,l.$G)("auth"),{isRtl:x}=(0,d.g)(),[f,{isLoading:v}]=(0,c.YA)(),[y,{isLoading:b}]=(0,c.l4)(),handleChange=e=>{let{name:r,value:s}=e.target;n(e=>({...e,[r]:s})),h[r]&&u(e=>({...e,[r]:""}))},validateForm=()=>{let r={};return s.username.trim()||(r.username=w("validation.required",{field:w("register.username")})),s.password?s.password.length<8&&(r.password=w("validation.minLength",{field:w("login.password"),length:8})):r.password=w("validation.required",{field:w("login.password")}),"register"===e&&(s.email&&!/\S+@\S+\.\S+/.test(s.email)&&(r.email=w("validation.email")),s.phone&&!/^\+?[0-9]{10,15}$/.test(s.phone)&&(r.phone=w("validation.phone")),s.email||s.phone||(r.email=w("validation.emailOrPhone"),r.phone=w("validation.emailOrPhone")),s.password!==s.confirmPassword&&(r.confirmPassword=w("validation.passwordMatch"))),u(r),0===Object.keys(r).length},handleSubmit=async a=>{if(a.preventDefault(),console.log("Form submitted, mode:",e),!validateForm()){console.log("Form validation failed");return}try{if("login"===e){console.log("Attempting to login with credentials:",{usernameOrEmail:s.username,passwordLength:s.password.length});try{console.log("Making direct fetch call to /auth/simple-login");let e=await fetch("http://localhost:3001/auth/simple-login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({usernameOrEmail:s.username,password:s.password}),credentials:"include"});console.log("Direct fetch response status:",e.status);let r=await e.json().catch(()=>null);console.log("Direct fetch response data:",r)}catch(e){console.error("Direct fetch error:",e)}console.log("Now trying with RTK Query login method");let e=await f({usernameOrEmail:s.username,password:s.password}).unwrap();console.log("Login successful, result:",e),p.push("/dashboard")}else console.log("Attempting to register with data:",{username:s.username,email:s.email||void 0,phone:s.phone||void 0,passwordLength:s.password.length}),await y({username:s.username,email:s.email||void 0,phone:s.phone||void 0,password:s.password}).unwrap(),console.log("Registration successful"),alert(w("register.success")),r("login")}catch(r){console.error("Auth error:",r),console.error("Auth error details:",{status:r.status,data:r.data,error:r.error,message:r.message,stack:r.stack,originalError:r.originalError});let e=r.data?.message||r.error||("string"==typeof r?r:JSON.stringify(r))||w("common.error");console.log("Setting form error:",e),u({form:e})}};return(0,a.jsxs)("div",{className:`w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-md dark:bg-gray-800 ${x?"text-right":"text-left"}`,children:[(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("h1",{className:"text-3xl font-bold",children:"login"===e?w("login.title"):w("register.title")}),a.jsx("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"login"===e?w("login.subtitle"):w("register.subtitle")})]}),h.form&&a.jsx("div",{className:"p-4 text-red-700 bg-red-100 rounded-md dark:bg-red-900 dark:text-red-100",children:h.form}),(0,a.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:handleSubmit,children:[a.jsx(m.Z,{label:"login"===e?w("login.usernameOrEmail"):w("register.username"),name:"username",type:"text",value:s.username,onChange:handleChange,error:h.username,required:!0}),"register"===e&&(0,a.jsxs)(a.Fragment,{children:[a.jsx(m.Z,{label:w("register.email"),name:"email",type:"email",value:s.email,onChange:handleChange,error:h.email}),a.jsx(m.Z,{label:w("register.phone"),name:"phone",type:"tel",value:s.phone,onChange:handleChange,error:h.phone})]}),a.jsx(m.Z,{label:w("login.password"),name:"password",type:"password",value:s.password,onChange:handleChange,error:h.password,required:!0}),"register"===e&&a.jsx(m.Z,{label:w("register.confirmPassword"),name:"confirmPassword",type:"password",value:s.confirmPassword,onChange:handleChange,error:h.confirmPassword,required:!0}),"login"===e&&a.jsx("div",{className:"flex justify-end mb-4",children:a.jsx(i(),{href:"/password-reset",className:"text-sm text-primary-600 hover:text-primary-500",children:w("login.forgotPassword")})}),a.jsx(g.Z,{type:"submit",fullWidth:!0,isLoading:v||b,children:"login"===e?w("login.button"):w("register.button")})]}),a.jsx("div",{className:"text-center mt-4",children:a.jsx("button",{onClick:()=>{r(e=>"login"===e?"register":"login"),u({})},className:"text-primary-600 hover:text-primary-500",type:"button",children:"login"===e?`${w("login.noAccount")} ${w("login.register")}`:`${w("register.hasAccount")} ${w("register.login")}`})}),a.jsx("div",{className:"text-center mt-4",children:a.jsx(i(),{href:"/",className:"text-primary-600 hover:text-primary-500",children:w("common.backToHome")})})]})}},36006:(e,r,s)=>{"use strict";s.d(r,{ZP:()=>l});var a=s(95153);let t=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\components\auth\AuthForm.tsx`),{__esModule:o,$$typeof:n}=t,i=t.default,l=i},60232:(e,r,s)=>{"use strict";s.d(r,{$:()=>getServerTranslations});var a=s(47420),t=s(94386);async function getServerTranslations(e="common"){let r=(0,a.G)(),{t:s}=await (0,a.i)(r,e);return{t:s,language:r,direction:(0,t.Fp)(r)?"rtl":"ltr",isRtl:(0,t.Fp)(r)}}}};