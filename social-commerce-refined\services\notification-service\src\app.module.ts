import { Module, MiddlewareConsumer, RequestMethod, NestModule } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { ClientsModule, Transport } from '@nestjs/microservices';
// import { MessagingModule } from '@app/messaging'; // Temporarily disabled
import { AuthenticationModule } from './authentication/authentication.module';
import { ProfileManagementModule } from './profile-management/profile-management.module';
import { VerificationModule } from './verification/verification.module';
import { SharedModule } from './shared/shared.module';
import { CorrelationIdMiddleware } from './shared/middleware/correlation-id.middleware';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { LoggingInterceptor } from './shared/interceptors/logging.interceptor';
import { ClassSerializerInterceptor } from '@nestjs/common';

// Import entities explicitly
import { User } from './authentication/entities/user.entity';
import { Profile } from './profile-management/entities/profile.entity';
import { VerificationToken } from './verification/entities/verification-token.entity';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: `.env.${process.env.NODE_ENV || 'development'}`,
    }),

    // Database
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get<string>('DB_HOST', 'localhost'),
        port: configService.get<number>('DB_PORT', 5432),
        username: configService.get<string>('DB_USERNAME', 'postgres'),
        password: configService.get<string>('DB_PASSWORD', '1111'),
        database: configService.get<string>('DB_DATABASE', 'user_service'),
        entities: [User, Profile, VerificationToken],
        synchronize: configService.get<boolean>('DB_SYNCHRONIZE', true),
        logging: configService.get<boolean>('DB_LOGGING', true),
      }),
    }),

    // JWT
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET', 'your-secret-key'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN', '1h'),
        },
      }),
    }),

    // Messaging - Temporarily disabled for initial deployment
    // MessagingModule.register({
    //   rabbitmqUrl: process.env.RABBITMQ_URL || 'amqp://admin:admin@localhost:5672',
    //   serviceName: 'user-service',
    // }),

    // Microservices - Temporarily disabled for testing
    // ClientsModule.registerAsync([
    //   {
    //     name: 'NOTIFICATION_SERVICE',
    //     imports: [ConfigModule],
    //     inject: [ConfigService],
    //     useFactory: (configService: ConfigService) => ({
    //       transport: Transport.RMQ,
    //       options: {
    //         urls: [configService.get<string>('RABBITMQ_URL', 'amqp://admin:admin@localhost:5672')],
    //         queue: configService.get<string>('NOTIFICATION_QUEUE', 'notification_queue'),
    //         queueOptions: {
    //           durable: true,
    //         },
    //       },
    //     }),
    //   },
    // ]),

    // Feature modules
    AuthenticationModule,
    ProfileManagementModule,
    VerificationModule,
    SharedModule,
  ],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggingInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: ClassSerializerInterceptor,
    },
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(CorrelationIdMiddleware)
      .forRoutes({ path: '*', method: RequestMethod.ALL });
  }
}
