import { BaseEvent } from '../base-event.interface';
export declare class CartItemAddedEvent implements BaseEvent<CartItemAddedPayload> {
    id: string;
    type: string;
    version: string;
    timestamp: string;
    producer: string;
    payload: CartItemAddedPayload;
    constructor(payload: CartItemAddedPayload);
}
export interface CartItemAddedPayload {
    cartId: string;
    userId: string;
    productId: string;
    productName: string;
    quantity: number;
    price: number;
    addedAt: string;
}
