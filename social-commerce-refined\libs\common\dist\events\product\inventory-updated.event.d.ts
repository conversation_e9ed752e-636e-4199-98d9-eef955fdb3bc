import { BaseEvent } from '../base-event.interface';
export declare class InventoryUpdatedEvent implements BaseEvent<InventoryUpdatedPayload> {
    id: string;
    type: string;
    version: string;
    timestamp: string;
    producer: string;
    payload: InventoryUpdatedPayload;
    constructor(payload: InventoryUpdatedPayload);
}
export interface InventoryUpdatedPayload {
    id: string;
    storeId: string;
    previousInventory: number;
    newInventory: number;
    updatedAt: string;
}
