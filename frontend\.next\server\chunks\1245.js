"use strict";exports.id=1245,exports.ids=[1245],exports.modules={41245:(e,a,i)=>{i.d(a,{Z:()=>__WEBPACK_DEFAULT_EXPORT__});var r=i(30784),s=i(9885),t=i(57114),l=i(706),o=i(59872),n=i(34952),d=i(48042);let __WEBPACK_DEFAULT_EXPORT__=({storeId:e,storeUsername:a,product:i,isEditing:m=!1})=>{let[c,p]=(0,s.useState)({title:i?.title||"",description:i?.description||"",price:i?.price?.toString()||"",mediaUrl:i?.mediaUrls?.[0]||"",postType:i?.postType||n.h.REGULAR,isActive:i?.isActive??!0,affiliateEnabled:i?.affiliateEnabled??!1,commissionType:i?.commissionType||n.Y.PERCENTAGE,commissionValue:i?.commissionValue?.toString()||"10"}),[h,u]=(0,s.useState)({}),b=(0,t.useRouter)(),[x,{isLoading:g}]=(0,d.qX)(),[y,{isLoading:f}]=(0,d.wE)(),handleChange=e=>{let{name:a,value:i,type:r}=e.target;if("checkbox"===r){let{checked:i}=e.target;p(e=>({...e,[a]:i}))}else p(e=>({...e,[a]:i}));h[a]&&u(e=>({...e,[a]:""}))},validateForm=()=>{let e={};return c.title.trim()||(e.title="Title is required"),c.price&&isNaN(parseFloat(c.price))&&(e.price="Price must be a valid number"),c.mediaUrl&&!isValidUrl(c.mediaUrl)&&(e.mediaUrl="Please enter a valid URL"),u(e),0===Object.keys(e).length},isValidUrl=e=>{try{return new URL(e),!0}catch(e){return!1}},handleSubmit=async r=>{if(r.preventDefault(),validateForm())try{let r={title:c.title,description:c.description||void 0,price:c.price?parseFloat(c.price):void 0,mediaUrls:c.mediaUrl?[c.mediaUrl]:[],postType:c.postType,isActive:c.isActive,affiliateEnabled:c.affiliateEnabled,commissionType:c.affiliateEnabled?c.commissionType:void 0,commissionValue:c.affiliateEnabled&&c.commissionValue?parseFloat(c.commissionValue):void 0};m&&i?await y({id:i.id,data:r}).unwrap():await x({storeId:e,...r}).unwrap(),b.push(`/stores/${a}`)}catch(a){let e=a.data?.message||"An error occurred";u({form:e})}};return(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6",children:[r.jsx("h2",{className:"text-xl font-semibold mb-4",children:m?"Edit Product":"Add New Product"}),h.form&&r.jsx("div",{className:"p-4 mb-4 text-red-700 bg-red-100 rounded-md dark:bg-red-900 dark:text-red-100",children:h.form}),(0,r.jsxs)("form",{onSubmit:handleSubmit,children:[r.jsx(l.Z,{label:"Title",name:"title",value:c.title,onChange:handleChange,error:h.title,required:!0}),(0,r.jsxs)("div",{className:"mb-4",children:[r.jsx("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Description"}),r.jsx("textarea",{id:"description",name:"description",rows:4,className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 border-gray-300 dark:border-gray-700",value:c.description,onChange:handleChange})]}),r.jsx(l.Z,{label:"Price",name:"price",type:"number",step:"0.01",value:c.price,onChange:handleChange,error:h.price,placeholder:"Leave empty for 'Contact for price'"}),r.jsx(l.Z,{label:"Media URL",name:"mediaUrl",value:c.mediaUrl,onChange:handleChange,error:h.mediaUrl,placeholder:"https://example.com/image.jpg"}),(0,r.jsxs)("div",{className:"mb-4",children:[r.jsx("label",{htmlFor:"postType",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Post Type"}),(0,r.jsxs)("select",{id:"postType",name:"postType",className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 border-gray-300 dark:border-gray-700",value:c.postType,onChange:handleChange,children:[r.jsx("option",{value:n.h.REGULAR,children:"Regular"}),r.jsx("option",{value:n.h.GROUP_BUY,children:"Group Buy"})]})]}),(0,r.jsxs)("div",{className:"mb-4 flex items-center",children:[r.jsx("input",{id:"isActive",name:"isActive",type:"checkbox",className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded",checked:c.isActive,onChange:handleChange}),r.jsx("label",{htmlFor:"isActive",className:"ml-2 block text-sm text-gray-700 dark:text-gray-300",children:"Active (visible to customers)"})]}),(0,r.jsxs)("div",{className:"mt-8 mb-4 border-t border-gray-200 dark:border-gray-700 pt-4",children:[r.jsx("h3",{className:"text-lg font-medium mb-3",children:"Affiliate Options"}),(0,r.jsxs)("div",{className:"mb-4 flex items-center",children:[r.jsx("input",{id:"affiliateEnabled",name:"affiliateEnabled",type:"checkbox",className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded",checked:c.affiliateEnabled,onChange:handleChange}),r.jsx("label",{htmlFor:"affiliateEnabled",className:"ml-2 block text-sm text-gray-700 dark:text-gray-300",children:"Enable affiliate program for this product"})]}),c.affiliateEnabled&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"mb-4",children:[r.jsx("label",{htmlFor:"commissionType",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Commission Type"}),(0,r.jsxs)("select",{id:"commissionType",name:"commissionType",className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 border-gray-300 dark:border-gray-700",value:c.commissionType,onChange:handleChange,children:[r.jsx("option",{value:n.Y.FIXED,children:"Fixed Amount"}),r.jsx("option",{value:n.Y.PERCENTAGE,children:"Percentage"})]})]}),r.jsx(l.Z,{label:c.commissionType===n.Y.FIXED?"Commission Amount ($)":"Commission Percentage (%)",name:"commissionValue",type:"number",step:c.commissionType===n.Y.FIXED?"0.01":"0.1",min:"0",max:c.commissionType===n.Y.PERCENTAGE?"100":void 0,value:c.commissionValue,onChange:handleChange})]})]}),(0,r.jsxs)("div",{className:"flex justify-end gap-3 mt-6",children:[r.jsx(o.Z,{type:"button",variant:"outline",onClick:()=>b.push(`/stores/${a}`),children:"Cancel"}),r.jsx(o.Z,{type:"submit",isLoading:g||f,children:m?"Save Changes":"Create Product"})]})]})]})}},34952:(e,a,i)=>{var r,s;i.d(a,{Y:()=>s,h:()=>r}),function(e){e.REGULAR="REGULAR",e.GROUP_BUY="GROUP_BUY"}(r||(r={})),function(e){e.FIXED="FIXED",e.PERCENTAGE="PERCENTAGE"}(s||(s={}))}};