import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

/**
 * API client for integration tests
 */
export class ApiClient {
  private readonly apiGatewayUrl: string;
  private readonly userServiceUrl: string;
  private readonly storeServiceUrl: string;
  private accessToken: string | null = null;

  constructor() {
    this.apiGatewayUrl = process.env.API_GATEWAY_URL || 'http://localhost:3000/api';
    this.userServiceUrl = process.env.USER_SERVICE_URL || 'http://localhost:3001/api';
    this.storeServiceUrl = process.env.STORE_SERVICE_URL || 'http://localhost:3002/api';
  }

  /**
   * Create an axios instance with the given base URL and authorization header
   * @param baseUrl The base URL for the axios instance
   * @returns An axios instance
   */
  private createAxiosInstance(baseUrl: string): AxiosInstance {
    const config: AxiosRequestConfig = {
      baseURL: baseUrl,
      headers: {},
    };

    if (this.accessToken) {
      config.headers['Authorization'] = `Bearer ${this.accessToken}`;
    }

    return axios.create(config);
  }

  /**
   * Set the access token for authenticated requests
   * @param token The access token
   */
  setAccessToken(token: string): void {
    this.accessToken = token;
  }

  /**
   * Clear the access token
   */
  clearAccessToken(): void {
    this.accessToken = null;
  }

  /**
   * Get the API Gateway client
   * @returns An axios instance for the API Gateway
   */
  gateway(): AxiosInstance {
    return this.createAxiosInstance(this.apiGatewayUrl);
  }

  /**
   * Get the User Service client
   * @returns An axios instance for the User Service
   */
  userService(): AxiosInstance {
    return this.createAxiosInstance(this.userServiceUrl);
  }

  /**
   * Get the Store Service client
   * @returns An axios instance for the Store Service
   */
  storeService(): AxiosInstance {
    return this.createAxiosInstance(this.storeServiceUrl);
  }

  /**
   * Register a new user
   * @param email The user's email
   * @param password The user's password
   * @param profile The user's profile
   * @returns The created user
   */
  async registerUser(
    email: string,
    password: string,
    profile?: { firstName?: string; lastName?: string },
  ): Promise<any> {
    const response = await this.gateway().post('/auth/register', {
      email,
      password,
      profile,
    });

    return response.data;
  }

  /**
   * Login a user
   * @param email The user's email
   * @param password The user's password
   * @returns The login response with access token and user
   */
  async login(email: string, password: string): Promise<any> {
    const response = await this.gateway().post('/auth/login', {
      email,
      password,
    });

    this.setAccessToken(response.data.accessToken);

    return response.data;
  }

  /**
   * Get the current user's profile
   * @returns The user's profile
   */
  async getProfile(): Promise<any> {
    const response = await this.gateway().get('/auth/profile');
    return response.data;
  }

  /**
   * Create a new store
   * @param name The store's name
   * @param description The store's description
   * @returns The created store
   */
  async createStore(name: string, description?: string): Promise<any> {
    const response = await this.gateway().post('/stores', {
      name,
      description,
    });

    return response.data;
  }

  /**
   * Get a store by ID
   * @param storeId The store's ID
   * @returns The store
   */
  async getStore(storeId: string): Promise<any> {
    const response = await this.gateway().get(`/stores/${storeId}`);
    return response.data;
  }

  /**
   * Create a new product
   * @param storeId The store's ID
   * @param name The product's name
   * @param price The product's price
   * @param description The product's description
   * @returns The created product
   */
  async createProduct(
    storeId: string,
    name: string,
    price: number,
    description?: string,
  ): Promise<any> {
    const response = await this.gateway().post('/products', {
      storeId,
      name,
      price,
      description,
    });

    return response.data;
  }

  /**
   * Get a product by ID
   * @param productId The product's ID
   * @returns The product
   */
  async getProduct(productId: string): Promise<any> {
    const response = await this.gateway().get(`/products/${productId}`);
    return response.data;
  }
}
