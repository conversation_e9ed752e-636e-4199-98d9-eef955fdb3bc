(()=>{var e={};e.id=9088,e.ids=[9088],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},875:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var t=r(67096),a=r(16132),i=r(37284),n=r.n(i),o=r(32564),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(s,l);let d=["",{children:["stores",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,95803)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\stores\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9291,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\stores\\page.tsx"],m="/stores/page",x={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/stores/page",pathname:"/stores",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},69084:(e,s,r)=>{Promise.resolve().then(r.bind(r,92666))},92666:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>StoresPage});var t=r(30784);r(9885);var a=r(57114),i=r(52451),n=r.n(i),o=r(11440),l=r.n(o),d=r(59872);let store_StoreCard=({store:e,isOwner:s=!1})=>{let r=(0,a.useRouter)();return t.jsx(l(),{href:`/stores/${e.username}`,children:(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300",children:[t.jsx("div",{className:"relative h-32 bg-gradient-to-r from-primary-500 to-secondary-500",children:e.profileImageUrl&&t.jsx(n(),{src:e.profileImageUrl,alt:e.displayName||e.username,fill:!0,className:"object-cover"})}),(0,t.jsxs)("div",{className:"p-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"text-lg font-semibold",children:e.displayName||e.username}),(0,t.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:["@",e.username]})]}),s&&t.jsx(d.Z,{variant:"outline",size:"sm",onClick:s=>{s.preventDefault(),s.stopPropagation(),r.push(`/stores/${e.username}/edit`)},children:"Edit"})]}),e.bio&&t.jsx("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-300 line-clamp-2",children:e.bio}),(0,t.jsxs)("div",{className:"mt-4 flex justify-between text-sm text-gray-500 dark:text-gray-400",children:[(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:e.followerCount})," followers"]}),(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:e.postCount})," posts"]}),(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:e.starRating.toFixed(1)})," ★"]})]})]})]})})};var c=r(77783),m=r(19923);function StoresPage(){let e=(0,a.useRouter)(),{data:s,isLoading:r}=(0,c.Ci)(),{data:i}=(0,m.Mx)(),n=s?.filter(e=>e.ownerId===i?.id)||[],o=s?.filter(e=>e.ownerId!==i?.id)||[];return t.jsx("div",{className:"min-h-screen p-6 md:p-12",children:(0,t.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[t.jsx("h1",{className:"text-3xl font-bold",children:"Stores"}),t.jsx(d.Z,{onClick:()=>e.push("/stores/create"),children:"Create Store"})]}),r?(0,t.jsxs)("div",{className:"text-center py-12",children:[t.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600 mx-auto"}),t.jsx("p",{className:"mt-4 text-lg",children:"Loading stores..."})]}):(0,t.jsxs)(t.Fragment,{children:[n.length>0&&(0,t.jsxs)("div",{className:"mb-12",children:[t.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"My Stores"}),t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:n.map(e=>t.jsx(store_StoreCard,{store:e,isOwner:!0},e.id))})]}),(0,t.jsxs)("div",{children:[t.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"Discover Stores"}),o.length>0?t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:o.map(e=>t.jsx(store_StoreCard,{store:e},e.id))}):t.jsx("div",{className:"text-center py-12 bg-gray-50 dark:bg-gray-800 rounded-lg",children:t.jsx("p",{className:"text-lg text-gray-600 dark:text-gray-300",children:"No stores to discover yet."})})]})]})]})})}},95803:(e,s,r)=>{"use strict";r.r(s),r.d(s,{$$typeof:()=>n,__esModule:()=>i,default:()=>l});var t=r(95153);let a=(0,t.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\stores\page.tsx`),{__esModule:i,$$typeof:n}=a,o=a.default,l=o}};var s=require("../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),r=s.X(0,[2103,2765,7783],()=>__webpack_exec__(875));module.exports=r})();