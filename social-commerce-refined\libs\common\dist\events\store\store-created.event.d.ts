import { BaseEvent } from '../base-event.interface';
export declare class StoreCreatedEvent implements BaseEvent<StoreCreatedPayload> {
    id: string;
    type: string;
    version: string;
    timestamp: string;
    producer: string;
    payload: StoreCreatedPayload;
    constructor(payload: StoreCreatedPayload);
}
export interface StoreCreatedPayload {
    id: string;
    ownerId: string;
    name: string;
    description: string;
    createdAt: string;
}
