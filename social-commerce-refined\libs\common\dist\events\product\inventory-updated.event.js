"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InventoryUpdatedEvent = void 0;
class InventoryUpdatedEvent {
    constructor(payload) {
        this.type = 'product.inventory.updated';
        this.version = '1.0';
        this.producer = 'product-service';
        this.id = payload.id;
        this.timestamp = new Date().toISOString();
        this.payload = payload;
    }
}
exports.InventoryUpdatedEvent = InventoryUpdatedEvent;
//# sourceMappingURL=inventory-updated.event.js.map