# Contributing to Social Commerce Platform

Thank you for your interest in contributing to the Social Commerce Platform! This document outlines the development workflow, coding standards, and best practices that all contributors (human developers and AI assistants) must follow.

## Table of Contents

- [Development Workflow](#development-workflow)
- [Using the Development CLI](#using-the-development-cli)
- [Git Workflow](#git-workflow)
- [Code Style and Standards](#code-style-and-standards)
- [Testing Requirements](#testing-requirements)
- [Documentation Requirements](#documentation-requirements)
- [AI Assistant Guidelines](#ai-assistant-guidelines)
- [Review Process](#review-process)

## Development Workflow

Our development workflow is designed to ensure consistency, quality, and efficiency. All contributors must follow this workflow without exception.

### Key Principles

1. **Use the provided tools**: Always use the development CLI tool for common tasks
2. **Follow the microservices architecture**: Respect service boundaries and communication patterns
3. **Test thoroughly**: Write tests for all new features and bug fixes
4. **Document your work**: Update documentation to reflect your changes
5. **Maintain consistency**: Follow established patterns and conventions

### Required Workflow Steps

1. **Setup**: Use Docker Compose for local development environment
2. **Development**: Use the CLI tool for starting, stopping, and managing services
3. **Testing**: Run tests before submitting changes
4. **Documentation**: Update relevant documentation
5. **Review**: Submit changes for review following the Git workflow

## Using the Development CLI

The Development CLI tool (`dev-cli`) is the primary interface for development tasks. **Always use this tool instead of direct npm/Docker commands.**

### Installation

```bash
cd dev-cli
npm install
npm link
```

### Common Commands

```bash
# Start all services
dev start

# Start a specific service
dev start [service]  # e.g., dev start user

# Stop services
dev stop [service]

# Check service status
dev status

# Install dependencies
dev install [service]

# Build services
dev build [service]
```

### When to Use Each Command

| Task | Command | Notes |
|------|---------|-------|
| Starting development | `dev start` | Always check status after starting |
| Checking if services are running | `dev status` | Run before troubleshooting |
| Installing new dependencies | `dev install` | Always commit package.json changes |
| Building for testing | `dev build` | Required before running tests |

## Git Workflow

1. **Create a feature branch**:
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make small, focused commits**:
   ```bash
   git add .
   git commit -m "feat: add specific feature"
   ```

   Use conventional commit messages:
   - `feat:` for new features
   - `fix:` for bug fixes
   - `docs:` for documentation changes
   - `test:` for test additions or changes
   - `refactor:` for code refactoring
   - `style:` for formatting changes
   - `chore:` for maintenance tasks

3. **Push to the repository**:
   ```bash
   git push origin feature/your-feature-name
   ```

4. **Create a Pull Request** with a clear description of changes

5. **Address review feedback** and update your PR

6. **Merge** once approved

## Code Style and Standards

- Follow the ESLint and Prettier configurations
- Use TypeScript for type safety
- Follow the established architecture patterns
- Use dependency injection where appropriate
- Keep functions small and focused
- Write meaningful comments and documentation

## Testing Requirements

- Write unit tests for all new functionality
- Ensure existing tests pass
- Aim for at least 80% test coverage for new code
- Include integration tests for critical paths
- Test error handling and edge cases

## Documentation Requirements

- Update README.md for significant changes
- Document new API endpoints
- Update SCRIPTS.md when adding new scripts
- Add JSDoc comments to functions and classes
- Update Swagger documentation for API changes

## AI Assistant Guidelines

AI assistants (including Augment AI Code Assistant) must strictly adhere to the guidelines in the [AI Guidelines](./docs/ai-guidelines/README.md) document. These guidelines ensure that AI-generated code adheres to our project standards and best practices.

For detailed examples and patterns, refer to:
- [User Controller Example](./docs/ai-guidelines/examples/user-controller.md)
- [Error Handling Example](./docs/ai-guidelines/examples/error-handling.md)
- [Testing Example](./docs/ai-guidelines/examples/testing.md)
- [Documentation Example](./docs/ai-guidelines/examples/documentation.md)

### Key Requirements for AI Assistants

1. **Always use the dev-cli tool** for development tasks
   ```bash
   # CORRECT
   dev start user

   # INCORRECT
   cd backend && npm run start:user
   ```

2. **Always check service health** before troubleshooting
   ```bash
   # REQUIRED BEFORE TROUBLESHOOTING
   dev status
   ```

3. **Follow the established directory structure**
   - Place new files in the correct directories
   - Follow the naming conventions

4. **Use Docker Compose** for service management
   ```bash
   # CORRECT
   dev start

   # INCORRECT
   npm run start
   ```

5. **Respect service boundaries**
   - Don't bypass the API Gateway
   - Use the appropriate message patterns for service communication

6. **Implement proper error handling**
   - Use the established error handling patterns
   - Log errors appropriately

7. **Document all changes**
   - Update relevant documentation
   - Add comments to complex code

8. **Test thoroughly**
   - Write tests for all new functionality
   - Ensure existing tests pass

## Review Process

All contributions will be reviewed for:

1. Functionality: Does it work as expected?
2. Quality: Is the code well-written and maintainable?
3. Tests: Are there sufficient tests?
4. Documentation: Is it well-documented?
5. Workflow Compliance: Does it follow our workflow?

Contributions that don't follow the established workflow will be returned for revision.

## Questions and Support

If you have questions about the development workflow, please:

1. Check the documentation (README.md, SCRIPTS.md, CONTRIBUTING.md)
2. Use the `dev help` command for CLI tool guidance
3. Reach out to the team for clarification

Thank you for following these guidelines and contributing to the Social Commerce Platform!
