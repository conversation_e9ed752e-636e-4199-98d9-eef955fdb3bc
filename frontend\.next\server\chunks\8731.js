"use strict";exports.id=8731,exports.ids=[8731],exports.modules={28731:e=>{e.exports=JSON.parse('{"profile":{"title":"Profile","editProfile":"Edit Profile","saveProfile":"Save Profile","discardChanges":"Discard Changes","profileUpdated":"Profile updated successfully","errorUpdatingProfile":"Error updating profile","basicInfo":"Basic Information","username":"Username","displayName":"Display Name","bio":"Bio","bioPlaceholder":"Write a short bio about yourself","location":"Location","locationPlaceholder":"City, Country","website":"Website","websitePlaceholder":"https://example.com","socialLinks":"Social Links","facebook":"Facebook","twitter":"Twitter","instagram":"Instagram","linkedin":"LinkedIn","youtube":"YouTube","tiktok":"TikTok","interests":"Interests","interestsPlaceholder":"Add interests","privacySettings":"Privacy Settings","profileVisibility":"Profile Visibility","activityVisibility":"Activity Visibility","purchaseHistoryVisibility":"Purchase History Visibility","reviewsVisibility":"Reviews Visibility","connectionListVisibility":"Connection List Visibility","public":"Public","connections":"Connections Only","private":"Private","avatar":"Profile Picture","changeAvatar":"Change Profile Picture","coverImage":"Cover Image","changeCover":"Change Cover Image","uploadImage":"Upload Image","removeImage":"Remove Image","dragAndDrop":"Drag and drop an image here, or click to select","imageRequirements":"JPG, PNG or GIF (max. 2MB)","cropImage":"Crop Image","apply":"Apply","cancel":"Cancel","notFound":"Profile Not Found","notFoundDescription":"The profile you are looking for does not exist or has been removed.","noPurchases":"No purchases yet","noReviews":"No reviews yet","noGroups":"No groups yet"},"connections":{"title":"Connections","myConnections":"My Connections","pendingRequests":"Pending Requests","sentRequests":"Sent Requests","suggestions":"Suggested Connections","noConnections":"No connections yet","noPendingRequests":"No pending requests","noSentRequests":"No sent requests","noSuggestions":"No suggestions available","connect":"Connect","connected":"Connected","pending":"Pending","acceptRequest":"Accept","decline":"Decline","cancelRequest":"Cancel Request","removeConnection":"Remove Connection","block":"Block","unblock":"Unblock","blockedUsers":"Blocked Users","noBlockedUsers":"No blocked users","requestSent":"Connection request sent","requestAccepted":"Connection request accepted","requestDeclined":"Connection request declined","requestCancelled":"Connection request cancelled","connectionRemoved":"Connection removed","userBlocked":"User blocked","userUnblocked":"User unblocked","errorSendingRequest":"Error sending connection request","errorAcceptingRequest":"Error accepting connection request","errorDecliningRequest":"Error declining connection request","errorCancellingRequest":"Error cancelling connection request","errorRemovingConnection":"Error removing connection","errorBlockingUser":"Error blocking user","errorUnblockingUser":"Error unblocking user"},"activity":{"title":"Activity","myActivity":"My Activity","feed":"Feed","noActivity":"No activity yet","userNoActivity":"This user has no activity yet","noFeed":"No feed items available","productView":"viewed a product","productPurchase":"purchased a product","productReview":"reviewed a product","productShare":"shared a product","productLike":"liked a product","productComment":"commented on a product","storeFollow":"followed a store","userFollow":"followed a user","groupJoin":"joined a group","groupCreate":"created a group","deleteActivity":"Delete Activity","activityDeleted":"Activity deleted","errorDeletingActivity":"Error deleting activity","changeVisibility":"Change Visibility","visibilityChanged":"Visibility changed","errorChangingVisibility":"Error changing visibility","errorLoading":"Error loading activity feed"},"comments":{"title":"Comments","writeComment":"Write a comment...","reply":"Reply","replies":"Replies","showReplies":"Show Replies","hideReplies":"Hide Replies","edit":"Edit","delete":"Delete","report":"Report","commentAdded":"Comment added","commentUpdated":"Comment updated","commentDeleted":"Comment deleted","errorAddingComment":"Error adding comment","errorUpdatingComment":"Error updating comment","errorDeletingComment":"Error deleting comment"},"reactions":{"like":"Like","unlike":"Unlike","love":"Love","haha":"Haha","wow":"Wow","sad":"Sad","angry":"Angry","reactionAdded":"Reaction added","reactionRemoved":"Reaction removed","errorAddingReaction":"Error adding reaction","errorRemovingReaction":"Error removing reaction"},"notifications":{"title":"Notifications","markAllAsRead":"Mark All as Read","noNotifications":"No notifications","connectionRequest":"sent you a connection request","connectionAccepted":"accepted your connection request","productComment":"commented on your product","productLike":"liked your product","productShare":"shared your product","productReview":"reviewed your product","groupInvite":"invited you to join a group","groupJoin":"joined your group","notificationsMarkedAsRead":"All notifications marked as read","errorMarkingNotifications":"Error marking notifications as read"},"search":{"title":"Search","searchPlaceholder":"Search for people, products, stores...","people":"People","products":"Products","stores":"Stores","groups":"Groups","noPeopleFound":"No people found","noProductsFound":"No products found","noStoresFound":"No stores found","noGroupsFound":"No groups found","searchResults":"Search Results","noResults":"No results found"},"common":{"follow":"Follow","following":"Following","unfollow":"Unfollow","followers":"Followers","posts":"Posts","reviews":"Reviews","purchases":"Purchases","groups":"Groups","connect":"Connect","connected":"Connected","loading":"Loading...","loadMore":"Load More","seeAll":"See All","edit":"Edit","delete":"Delete","save":"Save","cancel":"Cancel","confirm":"Confirm","joinedOn":"Joined on {{date}}","share":"Share","report":"Report","block":"Block","unblock":"Unblock","mute":"Mute","unmute":"Unmute","success":"Success","error":"Error","warning":"Warning","info":"Information","backToHome":"Back to Home","retry":"Retry","like":"Like","comment":"Comment"}}')}};