import { Injectable, Logger } from '@nestjs/common';

export enum CircuitState {
  CLOSED = 'CLOSED',
  OPEN = 'OPEN',
  HALF_OPEN = 'HALF_OPEN',
}

export interface CircuitBreakerOptions {
  failureThreshold: number;
  resetTimeout: number;
  monitoringPeriod: number;
}

export interface CircuitBreakerStats {
  state: CircuitState;
  failures: number;
  successes: number;
  requests: number;
  lastFailureTime?: Date;
}

@Injectable()
export class CircuitBreakerService {
  private readonly logger = new Logger(CircuitBreakerService.name);
  private circuits = new Map<string, CircuitBreakerStats>();
  private readonly defaultOptions: CircuitBreakerOptions = {
    failureThreshold: 5,
    resetTimeout: 60000, // 1 minute
    monitoringPeriod: 10000, // 10 seconds
  };

  /**
   * Execute a function with circuit breaker protection
   */
  async execute<T>(
    circuitName: string,
    operation: () => Promise<T>,
    options?: Partial<CircuitBreakerOptions>,
  ): Promise<T> {
    const opts = { ...this.defaultOptions, ...options };
    const circuit = this.getOrCreateCircuit(circuitName);

    // Check if circuit is open
    if (circuit.state === CircuitState.OPEN) {
      if (this.shouldAttemptReset(circuit, opts)) {
        circuit.state = CircuitState.HALF_OPEN;
        this.logger.debug(`Circuit ${circuitName} moved to HALF_OPEN state`);
      } else {
        throw new Error(`Circuit breaker ${circuitName} is OPEN`);
      }
    }

    try {
      const result = await operation();
      this.onSuccess(circuitName);
      return result;
    } catch (error) {
      this.onFailure(circuitName, opts);
      throw error;
    }
  }

  /**
   * Execute an HTTP request with circuit breaker protection
   */
  async request(serviceName: string, requestConfig: any): Promise<any> {
    const axios = require('axios');
    return this.execute(serviceName, () => axios(requestConfig));
  }

  /**
   * Get circuit breaker statistics
   */
  getStats(circuitName: string): CircuitBreakerStats | undefined {
    return this.circuits.get(circuitName);
  }

  /**
   * Get all circuit breaker statistics
   */
  getAllStats(): Record<string, CircuitBreakerStats> {
    const stats: Record<string, CircuitBreakerStats> = {};
    this.circuits.forEach((value, key) => {
      stats[key] = value;
    });
    return stats;
  }

  /**
   * Get circuit breaker status (alias for getAllStats for backward compatibility)
   */
  getStatus(): Record<string, CircuitBreakerStats> {
    return this.getAllStats();
  }

  /**
   * Reset a specific circuit breaker
   */
  reset(circuitName: string): void {
    const circuit = this.circuits.get(circuitName);
    if (circuit) {
      circuit.state = CircuitState.CLOSED;
      circuit.failures = 0;
      circuit.successes = 0;
      circuit.requests = 0;
      circuit.lastFailureTime = undefined;
      this.logger.debug(`Circuit ${circuitName} has been reset`);
    }
  }

  /**
   * Reset all circuit breakers
   */
  resetAll(): void {
    this.circuits.forEach((_, circuitName) => {
      this.reset(circuitName);
    });
    this.logger.debug('All circuits have been reset');
  }

  private getOrCreateCircuit(circuitName: string): CircuitBreakerStats {
    if (!this.circuits.has(circuitName)) {
      this.circuits.set(circuitName, {
        state: CircuitState.CLOSED,
        failures: 0,
        successes: 0,
        requests: 0,
      });
    }
    return this.circuits.get(circuitName)!;
  }

  private shouldAttemptReset(
    circuit: CircuitBreakerStats,
    options: CircuitBreakerOptions,
  ): boolean {
    if (!circuit.lastFailureTime) {
      return true;
    }
    return Date.now() - circuit.lastFailureTime.getTime() >= options.resetTimeout;
  }

  private onSuccess(circuitName: string): void {
    const circuit = this.circuits.get(circuitName);
    if (circuit) {
      circuit.successes++;
      circuit.requests++;

      if (circuit.state === CircuitState.HALF_OPEN) {
        circuit.state = CircuitState.CLOSED;
        circuit.failures = 0;
        this.logger.debug(`Circuit ${circuitName} moved to CLOSED state after successful request`);
      }
    }
  }

  private onFailure(circuitName: string, options: CircuitBreakerOptions): void {
    const circuit = this.circuits.get(circuitName);
    if (circuit) {
      circuit.failures++;
      circuit.requests++;
      circuit.lastFailureTime = new Date();

      if (circuit.failures >= options.failureThreshold) {
        circuit.state = CircuitState.OPEN;
        this.logger.warn(`Circuit ${circuitName} moved to OPEN state after ${circuit.failures} failures`);
      }
    }
  }
}
