# Social Commerce Platform - Environment Variables Template
# Copy this file to .env and update the values for your local development

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DB_HOST=postgres
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=1111
DB_DATABASE_USER=user_service
DB_DATABASE_STORE=store_service
DB_SYNCHRONIZE=false
DB_LOGGING=true
DB_RUN_MIGRATIONS=true

# =============================================================================
# JWT AUTHENTICATION
# =============================================================================
# IMPORTANT: Generate a secure random secret for production!
# Use: openssl rand -base64 32
JWT_SECRET=your_super_secure_random_jwt_secret_key_at_least_32_characters_long
JWT_EXPIRES_IN=1h

# =============================================================================
# RABBITMQ MESSAGE BROKER
# =============================================================================
RABBITMQ_URL=amqp://admin:admin@rabbitmq:5672
RABBITMQ_DEFAULT_USER=admin
RABBITMQ_DEFAULT_PASS=admin

# Service-specific queues
RABBITMQ_USER_QUEUE=user_queue
RABBITMQ_STORE_QUEUE=store_queue
RABBITMQ_NOTIFICATION_QUEUE=notification_queue

# =============================================================================
# SERVICE PORTS
# =============================================================================
# API Gateway
API_GATEWAY_PORT=3000
HTTP_PORT=3000

# User Service
USER_SERVICE_PORT=3001
USER_SERVICE_MICROSERVICE_PORT=3001

# Store Service
STORE_SERVICE_PORT=3002
STORE_SERVICE_MICROSERVICE_PORT=3002

# Frontend
FRONTEND_PORT=3003

# =============================================================================
# SERVICE URLS (for inter-service communication)
# =============================================================================
USER_SERVICE_URL=http://user-service:3001/api
STORE_SERVICE_URL=http://store-service:3002/api
API_GATEWAY_URL=http://api-gateway:3000/api

# =============================================================================
# ENVIRONMENT SETTINGS
# =============================================================================
NODE_ENV=development

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
# Comma-separated list of allowed origins
ALLOWED_ORIGINS=http://localhost:3003,http://localhost:3000

# =============================================================================
# INTEGRATION TESTING
# =============================================================================
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=password123

# =============================================================================
# DEVELOPMENT FLAGS
# =============================================================================
# Enable/disable features for development
ENABLE_SWAGGER=true
ENABLE_CORS=true
ENABLE_RATE_LIMITING=false

# =============================================================================
# PRODUCTION OVERRIDES
# =============================================================================
# These should be overridden in production:
# - JWT_SECRET (use a secure random value)
# - DB_SYNCHRONIZE (set to false)
# - DB_PASSWORD (use a secure password)
# - RABBITMQ credentials (use secure credentials)
# - ALLOWED_ORIGINS (set to your production domains)
