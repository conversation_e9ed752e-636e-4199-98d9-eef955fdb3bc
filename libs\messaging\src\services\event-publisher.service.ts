import { Injectable, Inject, Logger } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { BaseEvent } from '../events/base.event';

@Injectable()
export class EventPublisherService {
  private readonly logger = new Logger(EventPublisherService.name);

  constructor(
    @Inject('EVENT_BUS') private readonly client: ClientProxy,
    @Inject('MESSAGING_OPTIONS') private readonly options: { serviceName: string },
  ) {}

  /**
   * Publish an event to the event bus
   * @param event The event to publish
   */
  async publish(event: BaseEvent): Promise<void> {
    this.logger.log(`Publishing event: ${event.type}`);
    
    try {
      // Add source service information to the event
      const eventWithSource = {
        ...event,
        source: this.options.serviceName,
      };
      
      // Emit the event to the event bus
      this.client.emit(event.type, eventWithSource);
      
      this.logger.log(`Event published successfully: ${event.type}`);
    } catch (error) {
      this.logger.error(`Failed to publish event: ${error.message}`, error.stack);
      throw error;
    }
  }
}
