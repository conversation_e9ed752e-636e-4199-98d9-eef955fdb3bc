import { Controller, Get, Post, Put, Delete, Patch, Body, Param, Query, Headers, Req, UseInterceptors, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Request } from 'express';
import { CacheInterceptor } from '@nestjs/cache-manager';
import { RoutingService } from '../services/routing.service';

@ApiTags('orders')
@Controller('orders')
export class OrderController {
  private readonly logger = new Logger(OrderController.name);
  private readonly SERVICE_NAME = 'order';

  constructor(private readonly routingService: RoutingService) {}

  @Post()
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new order' })
  @ApiResponse({ status: 201, description: 'Order created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  createOrder(@Body() createOrderDto: any, @Headers() headers: any): Observable<any> {
    this.logger.log('Forwarding POST /orders request');
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, '/orders', 'POST', createOrderDto, headers)
      .pipe(map((response) => response.data));
  }

  @Get()
  @UseInterceptors(CacheInterceptor)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user orders' })
  @ApiResponse({ status: 200, description: 'Return user orders' })
  getUserOrders(@Headers() headers: any, @Query() query: any): Observable<any> {
    this.logger.log('Forwarding GET /orders request');
    const queryString = new URLSearchParams(query).toString();
    const path = queryString ? `/orders?${queryString}` : '/orders';
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, path, 'GET', null, headers)
      .pipe(map((response) => response.data));
  }

  @Get('all')
  @UseInterceptors(CacheInterceptor)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all orders (admin)' })
  @ApiResponse({ status: 200, description: 'Return all orders' })
  getAllOrders(@Headers() headers: any, @Query() query: any): Observable<any> {
    this.logger.log('Forwarding GET /orders/all request');
    const queryString = new URLSearchParams(query).toString();
    const path = queryString ? `/orders/all?${queryString}` : '/orders/all';
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, path, 'GET', null, headers)
      .pipe(map((response) => response.data));
  }

  @Get('store/:storeId')
  @UseInterceptors(CacheInterceptor)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get orders by store' })
  @ApiResponse({ status: 200, description: 'Return orders for the store' })
  getOrdersByStore(@Param('storeId') storeId: string, @Headers() headers: any, @Query() query: any): Observable<any> {
    this.logger.log(`Forwarding GET /orders/store/${storeId} request`);
    const queryString = new URLSearchParams(query).toString();
    const path = queryString ? `/orders/store/${storeId}?${queryString}` : `/orders/store/${storeId}`;
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, path, 'GET', null, headers)
      .pipe(map((response) => response.data));
  }

  @Get('number/:orderNumber')
  @UseInterceptors(CacheInterceptor)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get order by order number' })
  @ApiResponse({ status: 200, description: 'Return order by order number' })
  @ApiResponse({ status: 404, description: 'Order not found' })
  getOrderByNumber(@Param('orderNumber') orderNumber: string, @Headers() headers: any): Observable<any> {
    this.logger.log(`Forwarding GET /orders/number/${orderNumber} request`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, `/orders/number/${orderNumber}`, 'GET', null, headers)
      .pipe(map((response) => response.data));
  }

  @Get(':id')
  @UseInterceptors(CacheInterceptor)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get order by ID' })
  @ApiResponse({ status: 200, description: 'Return order by ID' })
  @ApiResponse({ status: 404, description: 'Order not found' })
  getOrderById(@Param('id') id: string, @Headers() headers: any): Observable<any> {
    this.logger.log(`Forwarding GET /orders/${id} request`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, `/orders/${id}`, 'GET', null, headers)
      .pipe(map((response) => response.data));
  }

  @Patch(':id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update order' })
  @ApiResponse({ status: 200, description: 'Order updated successfully' })
  @ApiResponse({ status: 404, description: 'Order not found' })
  updateOrder(@Param('id') id: string, @Body() updateOrderDto: any, @Headers() headers: any): Observable<any> {
    this.logger.log(`Forwarding PATCH /orders/${id} request`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, `/orders/${id}`, 'PATCH', updateOrderDto, headers)
      .pipe(map((response) => response.data));
  }

  @Post(':id/cancel')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Cancel order' })
  @ApiResponse({ status: 200, description: 'Order cancelled successfully' })
  @ApiResponse({ status: 404, description: 'Order not found' })
  cancelOrder(@Param('id') id: string, @Headers() headers: any): Observable<any> {
    this.logger.log(`Forwarding POST /orders/${id}/cancel request`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, `/orders/${id}/cancel`, 'POST', null, headers)
      .pipe(map((response) => response.data));
  }

  @Delete(':id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete order' })
  @ApiResponse({ status: 200, description: 'Order deleted successfully' })
  @ApiResponse({ status: 404, description: 'Order not found' })
  deleteOrder(@Param('id') id: string, @Headers() headers: any): Observable<any> {
    this.logger.log(`Forwarding DELETE /orders/${id} request`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, `/orders/${id}`, 'DELETE', null, headers)
      .pipe(map((response) => response.data));
  }

  // Catch-all route for other order service endpoints
  @Get('*')
  @UseInterceptors(CacheInterceptor)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Forward any GET request to the order service' })
  forwardGetRequest(@Req() req: Request, @Headers() headers: any): Observable<any> {
    const path = req.url;
    this.logger.log(`Forwarding GET ${path} request to order service`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, path, 'GET', null, headers)
      .pipe(map((response) => response.data));
  }
}
