import React, { ReactElement } from 'react';
import {
  Box,
  Flex,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  useColorModeValue,
  Icon,
} from '@chakra-ui/react';

interface StatCardProps {
  title: string;
  stat: string;
  icon: ReactElement;
  helpText?: string;
  change?: string;
  isUp?: boolean;
}

const StatCard = ({ title, stat, icon, helpText, change, isUp }: StatCardProps) => {
  return (
    <Stat
      px={{ base: 4, md: 6 }}
      py="5"
      shadow="md"
      border="1px solid"
      borderColor={useColorModeValue('gray.200', 'gray.500')}
      rounded="lg"
      bg={useColorModeValue('white', 'gray.700')}
    >
      <Flex justifyContent="space-between">
        <Box pl={{ base: 2, md: 4 }}>
          <StatLabel fontWeight="medium" isTruncated>
            {title}
          </StatLabel>
          <StatNumber fontSize="2xl" fontWeight="medium">
            {stat}
          </StatNumber>
          {helpText && (
            <StatHelpText>
              {change && (
                <span
                  style={{
                    color: isUp ? 'green' : 'red',
                    marginRight: '8px',
                  }}
                >
                  {isUp ? '↑' : '↓'} {change}
                </span>
              )}
              {helpText}
            </StatHelpText>
          )}
        </Box>
        <Box
          my="auto"
          color={useColorModeValue('brand.500', 'brand.300')}
          alignContent="center"
        >
          {icon}
        </Box>
      </Flex>
    </Stat>
  );
};

export default StatCard;
