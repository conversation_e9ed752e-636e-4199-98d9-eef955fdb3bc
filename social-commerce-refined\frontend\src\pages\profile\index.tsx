import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  Heading,
  Text,
  Container,
  useColorModeValue,
  useToast,
} from '@chakra-ui/react';
import MainLayout from '@/components/layout/MainLayout';
import { useAuth } from '@/context/AuthContext';
import ProfileHeader from '@/components/profile/ProfileHeader';
import ProfileInfo from '@/components/profile/ProfileInfo';
import ProfileEditForm from '@/components/profile/ProfileEditForm';
import { userApi } from '@/services/api';

const Profile = () => {
  const { isAuthenticated, isLoading, user } = useAuth();
  const router = useRouter();
  const toast = useToast();
  const [isEditing, setIsEditing] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [profileData, setProfileData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    dateOfBirth: '',
    website: '',
    bio: '',
  });

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, isLoading, router]);

  // Load profile data when user is authenticated
  useEffect(() => {
    if (user) {
      // In a real app, we would fetch the full profile from the API
      // For now, we'll use the user data from auth context and add mock data
      setProfileData({
        name: user.name || '',
        email: user.email || '',
        phone: '+****************',
        address: '123 Main St, Anytown, USA',
        dateOfBirth: '1990-01-01',
        website: 'example.com',
        bio: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
      });
    }
  }, [user]);

  // Handle profile update
  const handleProfileUpdate = async (data: any) => {
    setIsSubmitting(true);

    try {
      // In a real app, we would call the API to update the profile
      // await userApi.updateProfile(data);

      // For now, just update the local state
      setProfileData(data);
      setIsEditing(false);

      toast({
        title: 'Profile updated',
        description: 'Your profile has been successfully updated.',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update profile. Please try again.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show loading if not authenticated
  if (isLoading || !isAuthenticated) {
    return (
      <Box textAlign="center" py={10}>
        <Text>Loading...</Text>
      </Box>
    );
  }

  return (
    <MainLayout>
      <Container maxW="container.lg" py={8}>
        <Heading as="h1" size="xl" mb={6}>
          Profile
        </Heading>

        {isEditing ? (
          <ProfileEditForm
            initialData={profileData}
            onSubmit={handleProfileUpdate}
            onCancel={() => setIsEditing(false)}
            isLoading={isSubmitting}
          />
        ) : (
          <>
            <ProfileHeader
              user={{
                id: user?.id || '',
                name: profileData.name,
                email: profileData.email,
                role: user?.role || 'User',
                isVerified: true,
                joinDate: 'January 2023',
              }}
              onEditClick={() => setIsEditing(true)}
            />

            <ProfileInfo
              personalInfo={{
                email: profileData.email,
                phone: profileData.phone,
                address: profileData.address,
                dateOfBirth: profileData.dateOfBirth,
                website: profileData.website,
                bio: profileData.bio,
              }}
            />
          </>
        )}
      </Container>
    </MainLayout>
  );
};

export default Profile;
