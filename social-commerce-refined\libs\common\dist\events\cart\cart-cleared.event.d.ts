import { BaseEvent } from '../base-event.interface';
export declare class CartClearedEvent implements BaseEvent<CartClearedPayload> {
    id: string;
    type: string;
    version: string;
    timestamp: string;
    producer: string;
    payload: CartClearedPayload;
    constructor(payload: CartClearedPayload);
}
export interface CartClearedPayload {
    cartId: string;
    userId: string;
    reason: string;
    clearedAt: string;
}
