(()=>{var e={};e.id=3523,e.ids=[3523],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},2484:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>d});var i=t(67096),a=t(16132),r=t(37284),n=t.n(r),l=t(32564),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let d=["",{children:["wishlist",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,38800)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\wishlist\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9291,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\wishlist\\[id]\\page.tsx"],m="/wishlist/[id]/page",u={require:t,loadChunk:()=>Promise.resolve()},h=new i.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/wishlist/[id]/page",pathname:"/wishlist/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},61689:(e,s,t)=>{Promise.resolve().then(t.bind(t,654))},654:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>WishlistPage});var i=t(30784);t(9885);var a=t(57114),r=t(21022),n=t(59872),l=t(86867),o=t(2280),d=t(25533);function WishlistPage({params:e}){let{id:s}=e,t=(0,a.useRouter)(),{data:c,isLoading:m}=(0,l._x)(s),{data:u,isLoading:h}=(0,l.cd)(s),[x]=(0,l.L$)(),[p]=(0,o.$B)(),g=m||h,handleRemoveItem=async e=>{await x({wishlistId:s,itemId:e}).unwrap()},handleMoveToCart=async e=>{try{await p({productId:e,quantity:1}).unwrap();let s=u?.find(s=>s.productId===e);s&&await handleRemoveItem(s.id)}catch(e){console.error("Failed to move item to cart:",e)}},handleClearWishlist=async()=>{if(u&&window.confirm("Are you sure you want to clear this wishlist?"))for(let e of u)await handleRemoveItem(e.id)};return g?i.jsx("div",{className:"min-h-screen p-6",children:i.jsx("div",{className:"max-w-4xl mx-auto",children:(0,i.jsxs)("div",{className:"animate-pulse",children:[i.jsx("div",{className:"h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-6"}),i.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-8"}),i.jsx("div",{className:"space-y-4",children:[void 0,void 0,void 0].map((e,s)=>(0,i.jsxs)("div",{className:"flex items-center py-4",children:[i.jsx("div",{className:"h-20 w-20 bg-gray-200 dark:bg-gray-700 rounded-md"}),(0,i.jsxs)("div",{className:"ml-4 flex-1",children:[i.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"}),i.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"})]}),i.jsx("div",{className:"ml-4 h-8 w-20 bg-gray-200 dark:bg-gray-700 rounded"})]},s))})]})})}):c?i.jsx("div",{className:"min-h-screen p-6",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,i.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[i.jsx("h1",{className:"text-2xl font-bold",children:c.name}),(0,i.jsxs)("div",{className:"flex space-x-2",children:[i.jsx(n.Z,{variant:"outline",onClick:()=>t.push("/wishlist"),children:"Back to Wishlists"}),i.jsx(n.Z,{variant:"outline",onClick:()=>t.push("/products"),children:"Continue Shopping"})]})]}),(0,i.jsxs)("div",{className:"flex items-center mb-6",children:[(0,i.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:[c.itemCount," ",1===c.itemCount?"item":"items"," •",c.isDefault?" Default wishlist • ":" ",c.isPublic?"Public":"Private"]}),c.isPublic&&i.jsx("div",{className:"ml-4",children:i.jsx(d.Z,{url:"",title:`Check out my wishlist: ${c.name}`})})]}),i.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6",children:i.jsx(r.Z,{items:u||[],isLoading:g,onRemoveItem:handleRemoveItem,onMoveToCart:handleMoveToCart,onClearWishlist:handleClearWishlist,emptyMessage:"This wishlist is empty. Add products to your wishlist to save them for later."})})]})}):i.jsx("div",{className:"min-h-screen p-6",children:i.jsx("div",{className:"max-w-4xl mx-auto",children:(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center",children:[i.jsx("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:"Wishlist Not Found"}),i.jsx("p",{className:"mb-6",children:"The wishlist you're looking for doesn't exist."}),i.jsx(n.Z,{onClick:()=>t.push("/wishlist"),children:"Back to My Wishlists"})]})})})}},38800:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>n,__esModule:()=>r,default:()=>o});var i=t(95153);let a=(0,i.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\wishlist\[id]\page.tsx`),{__esModule:r,$$typeof:n}=a,l=a.default,o=l}};var s=require("../../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),t=s.X(0,[2103,2765,1022,5533],()=>__webpack_exec__(2484));module.exports=t})();