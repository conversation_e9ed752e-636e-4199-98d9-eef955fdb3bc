import { Inject, Injectable, Logger } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { JwtService } from '@nestjs/jwt';
import { firstValueFrom } from 'rxjs';
import { SERVICES } from '@app/common';
import { LoginDto, RegisterDto, UserDto, UserRole } from '@app/common/dto/user.dto';
import { AuthService } from '../auth/auth.service';

/**
 * Debug Service
 *
 * This service provides debugging functionality for the authentication system.
 * It should only be used in development and testing environments.
 */
@Injectable()
export class DebugService {
  private readonly logger = new Logger(DebugService.name);

  constructor(
    @Inject(SERVICES.USER_SERVICE) private userServiceClient: ClientProxy,
    private jwtService: JwtService,
    private authService: AuthService,
  ) {
    this.logger.log('DebugService initialized');
  }

  /**
   * Creates a test user with predefined credentials
   */
  async createTestUser() {
    this.logger.debug('Creating test user');

    try {
      // Create a test user with known credentials
      const testUser: RegisterDto = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
        role: UserRole.USER
      };

      this.logger.debug('Registering test user');
      const result = await this.authService.register(testUser);

      this.logger.debug(`Test user created successfully: ${result.user.username} (ID: ${result.user.id})`);
      return {
        success: true,
        message: 'Test user created successfully',
        user: {
          id: result.user.id,
          username: result.user.username,
          email: result.user.email,
          role: result.user.role,
        },
        loginCredentials: {
          usernameOrEmail: testUser.username,
          password: testUser.password
        }
      };
    } catch (error) {
      this.logger.error(`Failed to create test user: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message || 'Failed to create test user',
        error: error.name,
      };
    }
  }

  /**
   * Creates a test user directly through the user service
   */
  async createTestUserDirect() {
    this.logger.debug('Creating test user directly through user service');

    try {
      const user = await firstValueFrom(
        this.userServiceClient.send('create_test_user', {})
      );

      if (!user) {
        this.logger.warn('User service did not return a user');
        return { success: false, message: 'Failed to create test user' };
      }

      this.logger.debug(`Test user created successfully: ${user.username} (ID: ${user.id})`);
      return {
        success: true,
        message: 'Test user created successfully',
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role,
        },
        loginCredentials: {
          usernameOrEmail: 'testuser123',
          password: 'password123'
        }
      };
    } catch (error) {
      this.logger.error(`Failed to create test user directly: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message || 'Failed to create test user',
        error: error.name,
      };
    }
  }

  /**
   * Creates a test user directly in the database
   */
  async createDirectDbUser() {
    this.logger.debug('Creating test user directly in database');

    try {
      const user = await firstValueFrom(
        this.userServiceClient.send('create_direct_test_user', {})
      );

      if (!user) {
        this.logger.warn('User service did not return a user');
        return { success: false, message: 'Failed to create direct DB test user' };
      }

      this.logger.debug(`Direct DB test user created successfully: ${user.username} (ID: ${user.id})`);
      return {
        success: true,
        message: 'Direct DB test user created successfully',
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role,
        },
        loginCredentials: {
          usernameOrEmail: 'directtestuser',
          password: 'password123'
        }
      };
    } catch (error) {
      this.logger.error(`Failed to create direct DB test user: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message || 'Failed to create direct DB test user',
        error: error.name,
      };
    }
  }

  /**
   * Creates a test user with a fixed password hash
   */
  async createFixedHashUser() {
    this.logger.debug('Creating test user with fixed password hash');

    try {
      const user = await firstValueFrom(
        this.userServiceClient.send('create_fixed_hash_user', {})
      );

      if (!user) {
        this.logger.warn('User service did not return a user');
        return { success: false, message: 'Failed to create fixed hash test user' };
      }

      this.logger.debug(`Fixed hash test user created successfully: ${user.username} (ID: ${user.id})`);
      return {
        success: true,
        message: 'Fixed hash test user created successfully',
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role,
        },
        loginCredentials: {
          usernameOrEmail: 'fixedhashuser',
          password: 'password123'
        }
      };
    } catch (error) {
      this.logger.error(`Failed to create fixed hash test user: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message || 'Failed to create fixed hash test user',
        error: error.name,
      };
    }
  }

  /**
   * Debug login functionality
   */
  async debugLogin(loginDto: LoginDto) {
    this.logger.debug(`Debug login attempt for: ${loginDto.usernameOrEmail}`);

    try {
      // Try to validate the user directly
      const user = await this.authService.validateUser(
        loginDto.usernameOrEmail,
        loginDto.password
      );

      if (!user) {
        this.logger.debug(`Debug login failed: Invalid credentials for ${loginDto.usernameOrEmail}`);
        return { success: false, message: 'Invalid credentials' };
      }

      // If validation succeeds, login the user
      const result = await this.authService.login(user);
      this.logger.debug(`Debug login successful for user: ${user.username} (ID: ${user.id})`);

      return {
        success: true,
        message: 'Login successful',
        user: {
          id: result.user.id,
          username: result.user.username,
          email: result.user.email,
          role: result.user.role,
        },
        token: result.token,
      };
    } catch (error) {
      this.logger.error(`Debug login error: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message || 'Login failed',
        error: error.name,
      };
    }
  }

  /**
   * Test password hashing for a user
   */
  async testPassword(payload: { username: string; password: string }) {
    this.logger.debug(`Testing password for user: ${payload.username}`);

    try {
      // Test password hash directly through the user service
      const result = await firstValueFrom(
        this.userServiceClient.send('test_password_hash', payload)
      );

      this.logger.debug(`Password test result: ${JSON.stringify(result)}`);
      return result;
    } catch (error) {
      this.logger.error(`Password test error: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message || 'Failed to test password',
        error: error.name,
      };
    }
  }

  /**
   * Check if a user exists and get their credentials
   */
  async checkUser(username: string) {
    this.logger.debug(`Checking user: ${username}`);

    try {
      // Get the user directly from the user service
      const user = await firstValueFrom(
        this.userServiceClient.send('find_user_by_credentials', { username })
      );

      if (!user) {
        this.logger.debug(`User not found: ${username}`);
        return { success: false, message: 'User not found' };
      }

      this.logger.debug(`User found: ${user.username} (ID: ${user.id})`);
      return {
        success: true,
        message: 'User found',
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role,
        }
      };
    } catch (error) {
      this.logger.error(`Check user error: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message || 'Failed to check user',
        error: error.name,
      };
    }
  }

  /**
   * Find a user by username or email
   */
  async findByUsername(usernameOrEmail: string) {
    this.logger.debug(`Finding user by username or email: ${usernameOrEmail}`);

    try {
      // Get the user directly from the user service using the new pattern
      const user = await firstValueFrom(
        this.userServiceClient.send('find_by_username_or_email', { usernameOrEmail })
      );

      if (!user) {
        this.logger.debug(`User not found: ${usernameOrEmail}`);
        return { success: false, message: 'User not found' };
      }

      this.logger.debug(`User found: ${user.username} (ID: ${user.id})`);
      return {
        success: true,
        message: 'User found',
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role,
        }
      };
    } catch (error) {
      this.logger.error(`Find by username error: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message || 'Failed to find user',
        error: error.name,
      };
    }
  }

  /**
   * Verify a user password
   */
  async verifyPassword(payload: { userId: string; password: string }) {
    this.logger.debug(`Verifying password for user ID: ${payload.userId}`);

    try {
      // Verify the password directly through the user service
      const isValid = await firstValueFrom(
        this.userServiceClient.send('verify_password', payload)
      );

      this.logger.debug(`Password verification result: ${isValid ? 'Valid' : 'Invalid'}`);
      return {
        success: true,
        isValid,
        message: isValid ? 'Password is valid' : 'Password is invalid'
      };
    } catch (error) {
      this.logger.error(`Verify password error: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message || 'Failed to verify password',
        error: error.name,
      };
    }
  }

  /**
   * Direct login endpoint bypassing Passport
   */
  async directLogin(loginDto: LoginDto) {
    this.logger.debug(`Direct login attempt for: ${loginDto.usernameOrEmail}`);

    try {
      // Get the user directly from the user service
      const user = await firstValueFrom(
        this.userServiceClient.send('login_user', loginDto)
      );

      if (!user || !user.user) {
        this.logger.debug(`Direct login failed: Invalid credentials for ${loginDto.usernameOrEmail}`);
        return { success: false, message: 'Invalid credentials' };
      }

      this.logger.debug(`Direct login successful for user: ${user.user.username} (ID: ${user.user.id})`);
      return {
        success: true,
        message: 'Login successful',
        user: {
          id: user.user.id,
          username: user.user.username,
          email: user.user.email,
          role: user.user.role,
        },
        token: user.token,
      };
    } catch (error) {
      this.logger.error(`Direct login error: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message || 'Login failed',
        error: error.name,
      };
    }
  }
}
