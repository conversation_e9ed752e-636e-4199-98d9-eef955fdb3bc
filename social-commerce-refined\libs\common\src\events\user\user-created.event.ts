import { BaseEvent } from '../base-event.interface';

/**
 * Event emitted when a new user is created
 */
export class UserCreatedEvent implements BaseEvent<UserCreatedPayload> {
  id: string;
  type: string = 'user.created';
  version: string = '1.0';
  timestamp: string;
  producer: string = 'user-service';
  payload: UserCreatedPayload;

  constructor(payload: UserCreatedPayload) {
    this.id = payload.id;
    this.timestamp = new Date().toISOString();
    this.payload = payload;
  }
}

/**
 * Payload for UserCreatedEvent
 */
export interface UserCreatedPayload {
  /**
   * User ID
   */
  id: string;

  /**
   * User email
   */
  email: string;

  /**
   * User name
   */
  name: string;

  /**
   * Whether the user's email is verified
   */
  isEmailVerified: boolean;

  /**
   * User creation timestamp
   */
  createdAt: string;
}
