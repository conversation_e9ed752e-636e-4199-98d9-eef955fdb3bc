import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TerminusModule } from '@nestjs/terminus';

import { ProductManagementModule } from './product-management/product-management.module';
import { HealthController } from './health/health.controller';

// Import entities explicitly (like Store Service)
import { Product } from './product-management/entities/product.entity';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get<string>('DB_HOST', 'localhost'),
        port: configService.get<number>('DB_PORT', 5432),
        username: configService.get<string>('DB_USERNAME', 'postgres'),
        password: configService.get<string>('DB_PASSWORD', '1111'),
        database: configService.get<string>('DB_DATABASE', 'product_service_db'),
        entities: [Product],
        synchronize: configService.get<boolean>('DB_SYNCHRONIZE', true),
        logging: configService.get<boolean>('DB_LOGGING', true),
        retryAttempts: 3,
        retryDelay: 3000,
      }),
    }),
    TerminusModule,
    ProductManagementModule,
  ],
  controllers: [HealthController],
})
export class AppModule {}
