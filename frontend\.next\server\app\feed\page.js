(()=>{var e={};e.id=7826,e.ids=[7826],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},7431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>g,originalPathname:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>c});var r=s(67096),a=s(16132),n=s(37284),o=s.n(n),i=s(32564),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(t,l);let c=["",{children:["feed",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,32076)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\feed\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9291,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\feed\\page.tsx"],p="/feed/page",g={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/feed/page",pathname:"/feed",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},70257:(e,t,s)=>{Promise.resolve().then(s.bind(s,18752))},18752:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>FeedPage});var r=s(30784),a=s(9885),n=s(27870),o=s(57114),i=s(95499),l=s(29850),c=s(19911),d=s(93603),p=s(51020);function FeedPage(){let{t:e}=(0,n.$G)("social"),t=(0,o.useSearchParams)(),s=t.get("userId"),g=t.get("type"),u=t.get("tag"),[m,x]=(0,a.useState)({following:!0,...s&&{userId:s},...g&&{type:g},...u&&{tags:[u]}}),[h,f]=(0,a.useState)(1),[y,_]=(0,a.useState)([]),{data:b,isLoading:v,isFetching:j,refetch:P}=(0,p.ws)({page:h,limit:10,filters:m}),[w]=(0,p.CH)(),[k]=(0,p.d6)(),[N]=(0,p.u3)();(0,a.useEffect)(()=>{b?.posts&&(1===h?_(b.posts):_(e=>[...e,...b.posts]))},[b,h]);let handleReaction=async(e,t)=>{try{let s=y.find(t=>t.id===e);if(!s)return;let r=s.reactions.some(e=>e.type===t&&e.userReacted);r?await k({postId:e,type:t}).unwrap():await w({postId:e,type:t}).unwrap(),P()}catch(e){console.error("Error handling reaction:",e)}},handleCreatePost=async e=>{try{await N({type:e.type,content:e.content,mediaUrls:e.mediaUrls,visibility:e.visibility,tags:e.tags}).unwrap(),P()}catch(e){throw console.error("Error creating post:",e),e}};return r.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 py-8",children:r.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[r.jsx("div",{className:"hidden lg:block lg:col-span-1",children:r.jsx(c.Z,{})}),(0,r.jsxs)("div",{className:"lg:col-span-2",children:[r.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6",children:e("feed","Social Feed")}),r.jsx("div",{className:"mb-6",children:r.jsx(d.Z,{onSubmit:handleCreatePost})}),r.jsx("div",{className:"mb-6",children:r.jsx(l.Z,{filters:m,onChange:e=>{x(e),f(1),_([])}})}),r.jsx(i.Z,{posts:y,isLoading:v||j,hasMore:b?.posts.length===10,onLoadMore:()=>{f(e=>e+1)},onLike:handleReaction,onComment:()=>{},onShare:()=>{}})]}),r.jsx("div",{className:"hidden lg:block lg:col-span-1",children:(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-6",children:[r.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4",children:e("trending","Trending")}),r.jsx("div",{className:"space-y-2",children:r.jsx("p",{className:"text-gray-500 dark:text-gray-400 text-sm",children:e("trendingPlaceholder","Trending content will appear here")})})]})})]})})})}},32076:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>o,__esModule:()=>n,default:()=>l});var r=s(95153);let a=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\feed\page.tsx`),{__esModule:n,$$typeof:o}=a,i=a.default,l=i}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),s=t.X(0,[2103,2765,2763,3902,1020,2522,4694,4154,5499,2928,9850,3456],()=>__webpack_exec__(7431));module.exports=s})();