import { BaseEvent } from '../base-event.interface';

/**
 * Event emitted when a user is updated
 */
export class UserUpdatedEvent implements BaseEvent<UserUpdatedPayload> {
  id: string;
  type: string = 'user.updated';
  version: string = '1.0';
  timestamp: string;
  producer: string = 'user-service';
  payload: UserUpdatedPayload;

  constructor(payload: UserUpdatedPayload) {
    this.id = payload.id;
    this.timestamp = new Date().toISOString();
    this.payload = payload;
  }
}

/**
 * Payload for UserUpdatedEvent
 */
export interface UserUpdatedPayload {
  /**
   * User ID
   */
  id: string;

  /**
   * User email
   */
  email: string;

  /**
   * User name
   */
  name: string;

  /**
   * Whether the user's email is verified
   */
  isEmailVerified: boolean;

  /**
   * User update timestamp
   */
  updatedAt: string;
}
