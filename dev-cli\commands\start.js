const chalk = require('chalk');
const spawn = require('cross-spawn');
const ora = require('ora');
const path = require('path');
const fs = require('fs');

/**
 * Start command implementation
 * @param {Object} program - Commander program instance
 */
module.exports = (program) => {
  program
    .command('start')
    .description('Start services')
    .argument('[service]', 'Service to start (all, user, main, frontend)', 'all')
    .option('-d, --detached', 'Run in detached mode', false)
    .action((service, options) => {
      const spinner = ora('Starting services...').start();
      
      try {
        const rootDir = path.resolve(__dirname, '../../');
        
        if (service === 'all') {
          // Start all services using Docker Compose
          spinner.text = 'Starting all services with Docker Compose...';
          
          const result = spawn.sync('docker-compose', ['up', options.detached ? '-d' : ''], {
            cwd: rootDir,
            stdio: 'inherit'
          });
          
          if (result.status === 0) {
            spinner.succeed(chalk.green('All services started successfully'));
          } else {
            spinner.fail(chalk.red('Failed to start services'));
            console.error(chalk.red('Error: Docker Compose failed to start services'));
          }
        } else if (service === 'user') {
          // Start only the user service
          spinner.text = 'Starting user service...';
          
          const backendDir = path.join(rootDir, 'backend');
          if (!fs.existsSync(backendDir)) {
            spinner.fail(chalk.red('Backend directory not found'));
            return;
          }
          
          const result = spawn.sync('npm', ['run', 'start:user'], {
            cwd: backendDir,
            stdio: 'inherit'
          });
          
          if (result.status === 0) {
            spinner.succeed(chalk.green('User service started successfully'));
          } else {
            spinner.fail(chalk.red('Failed to start user service'));
          }
        } else if (service === 'main') {
          // Start only the main gateway
          spinner.text = 'Starting main gateway...';
          
          const backendDir = path.join(rootDir, 'backend');
          if (!fs.existsSync(backendDir)) {
            spinner.fail(chalk.red('Backend directory not found'));
            return;
          }
          
          const result = spawn.sync('npm', ['run', 'start'], {
            cwd: backendDir,
            stdio: 'inherit'
          });
          
          if (result.status === 0) {
            spinner.succeed(chalk.green('Main gateway started successfully'));
          } else {
            spinner.fail(chalk.red('Failed to start main gateway'));
          }
        } else if (service === 'frontend') {
          // Start only the frontend
          spinner.text = 'Starting frontend...';
          
          const frontendDir = path.join(rootDir, 'frontend');
          if (!fs.existsSync(frontendDir)) {
            spinner.fail(chalk.red('Frontend directory not found'));
            return;
          }
          
          const result = spawn.sync('npm', ['run', 'dev'], {
            cwd: frontendDir,
            stdio: 'inherit'
          });
          
          if (result.status === 0) {
            spinner.succeed(chalk.green('Frontend started successfully'));
          } else {
            spinner.fail(chalk.red('Failed to start frontend'));
          }
        } else {
          spinner.fail(chalk.red(`Unknown service: ${service}`));
          console.error(chalk.yellow('Available services: all, user, main, frontend'));
        }
      } catch (error) {
        spinner.fail(chalk.red('Error starting services'));
        console.error(chalk.red(error.message));
      }
    });
};
