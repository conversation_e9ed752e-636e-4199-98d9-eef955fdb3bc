"use strict";exports.id=7245,exports.ids=[7245],exports.modules={31889:(t,r,e)=>{e.d(r,{A7:()=>E,Ev:()=>m,Lt:()=>B,M:()=>g,SS:()=>D,Vs:()=>d,Y:()=>n,Zl:()=>T,cb:()=>y,m$:()=>G});var i=e(86372);let u=i.g.injectEndpoints({endpoints:t=>({getGroupBuying:t.query({query:t=>`/group-buying/${t}`,providesTags:(t,r,e)=>[{type:"GroupBuying",id:e}],transformResponse:(t,r,e)=>{if(!t){let t=new Date,r=new Date(t);r.setDate(r.getDate()-2);let i=new Date(t);return i.setDate(i.getDate()+5),{id:e,title:`Mock Group Buy for Product ${e}`,description:"This is a mock group buying for development purposes.",productId:`product-${e}`,storeId:"store-1",creatorId:"user-1",type:GroupBuyingType.FIXED_PRICE,status:GroupBuyingStatus.ACTIVE,minParticipants:5,maxParticipants:20,currentParticipants:3,originalPrice:99.99,discountedPrice:79.99,startDate:r.toISOString(),endDate:i.toISOString(),imageUrl:`https://via.placeholder.com/500?text=Group+Buy+${e}`,tiers:[{id:"tier-1",participantsCount:5,discountPercentage:20,isActive:!0},{id:"tier-2",participantsCount:10,discountPercentage:30,isActive:!0},{id:"tier-3",participantsCount:15,discountPercentage:40,isActive:!0}],createdAt:r.toISOString(),updatedAt:t.toISOString(),product:{id:`product-${e}`,title:`Product ${e}`,slug:`product-${e}`,mediaUrls:[`https://via.placeholder.com/500?text=Product+${e}`],price:99.99},store:{id:"store-1",name:"Mock Store",slug:"mock-store",logoUrl:"https://via.placeholder.com/100?text=Store"},creator:{id:"user-1",username:"mockuser",displayName:"Mock User",avatarUrl:"https://via.placeholder.com/100?text=User"},participants:[{id:"participant-1",groupBuyingId:e,userId:"user-2",status:GroupBuyingParticipantStatus.PAID,joinedAt:r.toISOString(),paidAt:r.toISOString(),user:{id:"user-2",username:"participant1",displayName:"Participant 1",avatarUrl:"https://via.placeholder.com/100?text=User+2"}},{id:"participant-2",groupBuyingId:e,userId:"user-3",status:GroupBuyingParticipantStatus.PAID,joinedAt:r.toISOString(),paidAt:r.toISOString(),user:{id:"user-3",username:"participant2",displayName:"Participant 2",avatarUrl:"https://via.placeholder.com/100?text=User+3"}},{id:"participant-3",groupBuyingId:e,userId:"user-4",status:GroupBuyingParticipantStatus.PENDING,joinedAt:r.toISOString(),user:{id:"user-4",username:"participant3",displayName:"Participant 3",avatarUrl:"https://via.placeholder.com/100?text=User+4"}}]}}return t}}),getGroupBuyingParticipants:t.query({query:({groupBuyingId:t,page:r=1,limit:e=10})=>`/group-buying/${t}/participants?page=${r}&limit=${e}`,providesTags:(t,r,{groupBuyingId:e})=>[{type:"GroupBuyingParticipants",id:e}]}),searchGroupBuying:t.query({query:t=>{let r=new URLSearchParams;return t.query&&r.append("query",t.query),t.status&&r.append("status",t.status),t.type&&r.append("type",t.type),t.storeId&&r.append("storeId",t.storeId),t.productId&&r.append("productId",t.productId),t.creatorId&&r.append("creatorId",t.creatorId),t.minParticipants&&r.append("minParticipants",t.minParticipants.toString()),t.maxParticipants&&r.append("maxParticipants",t.maxParticipants.toString()),t.minPrice&&r.append("minPrice",t.minPrice.toString()),t.maxPrice&&r.append("maxPrice",t.maxPrice.toString()),t.startDateFrom&&r.append("startDateFrom",t.startDateFrom),t.startDateTo&&r.append("startDateTo",t.startDateTo),t.endDateFrom&&r.append("endDateFrom",t.endDateFrom),t.endDateTo&&r.append("endDateTo",t.endDateTo),t.sortBy&&r.append("sortBy",t.sortBy),t.sortOrder&&r.append("sortOrder",t.sortOrder),t.page&&r.append("page",t.page.toString()),t.limit&&r.append("limit",t.limit.toString()),`/group-buying/search?${r.toString()}`},providesTags:["GroupBuying"]}),getFeaturedGroupBuying:t.query({query:({page:t=1,limit:r=10})=>`/group-buying/featured?page=${t}&limit=${r}`,providesTags:["GroupBuying"]}),getPopularGroupBuying:t.query({query:({page:t=1,limit:r=10})=>`/group-buying/popular?page=${t}&limit=${r}`,providesTags:["GroupBuying"]}),getEndingSoonGroupBuying:t.query({query:({page:t=1,limit:r=10})=>`/group-buying/ending-soon?page=${t}&limit=${r}`,providesTags:["GroupBuying"]}),getUserCreatedGroupBuying:t.query({query:({userId:t,page:r=1,limit:e=10})=>`/users/${t}/group-buying/created?page=${r}&limit=${e}`,providesTags:(t,r,{userId:e})=>[{type:"UserGroupBuying",id:`created-${e}`}]}),getUserJoinedGroupBuying:t.query({query:({userId:t,page:r=1,limit:e=10})=>`/users/${t}/group-buying/joined?page=${r}&limit=${e}`,providesTags:(t,r,{userId:e})=>[{type:"UserGroupBuying",id:`joined-${e}`}]}),getCurrentUserCreatedGroupBuying:t.query({query:({page:t=1,limit:r=10})=>`/user/group-buying/created?page=${t}&limit=${r}`,providesTags:["UserGroupBuying"]}),getCurrentUserJoinedGroupBuying:t.query({query:({page:t=1,limit:r=10})=>`/user/group-buying/joined?page=${t}&limit=${r}`,providesTags:["UserGroupBuying"]}),createGroupBuying:t.mutation({query:t=>({url:"/group-buying",method:"POST",body:t}),invalidatesTags:["GroupBuying","UserGroupBuying"]}),updateGroupBuying:t.mutation({query:({id:t,data:r})=>({url:`/group-buying/${t}`,method:"PATCH",body:r}),invalidatesTags:(t,r,{id:e})=>[{type:"GroupBuying",id:e},"GroupBuying","UserGroupBuying"]}),deleteGroupBuying:t.mutation({query:t=>({url:`/group-buying/${t}`,method:"DELETE"}),invalidatesTags:(t,r,e)=>[{type:"GroupBuying",id:e},"GroupBuying","UserGroupBuying"]}),joinGroupBuying:t.mutation({query:t=>({url:"/group-buying/join",method:"POST",body:t}),invalidatesTags:(t,r,{groupBuyingId:e})=>[{type:"GroupBuying",id:e},{type:"GroupBuyingParticipants",id:e},"GroupBuying","UserGroupBuying"],transformResponse:(t,r,{groupBuyingId:e,quantity:i=1})=>{if(!t){let t=new Date;return{id:`participant-${Date.now()}`,groupBuyingId:e,userId:"current-user-id",status:GroupBuyingParticipantStatus.PENDING,joinedAt:t.toISOString(),quantity:i,user:{id:"current-user-id",username:"currentuser",displayName:"Current User",avatarUrl:"https://via.placeholder.com/100?text=Current+User"}}}return t}}),leaveGroupBuying:t.mutation({query:t=>({url:"/group-buying/leave",method:"POST",body:t}),invalidatesTags:(t,r,{groupBuyingId:e})=>[{type:"GroupBuying",id:e},{type:"GroupBuyingParticipants",id:e},"GroupBuying","UserGroupBuying"]}),inviteToGroupBuying:t.mutation({query:t=>({url:"/group-buying/invite",method:"POST",body:t}),invalidatesTags:(t,r,{groupBuyingId:e})=>[{type:"GroupBuying",id:e}]}),cancelGroupBuying:t.mutation({query:t=>({url:`/group-buying/${t}/cancel`,method:"POST"}),invalidatesTags:(t,r,e)=>[{type:"GroupBuying",id:e},"GroupBuying","UserGroupBuying"]}),completeGroupBuying:t.mutation({query:t=>({url:`/group-buying/${t}/complete`,method:"POST"}),invalidatesTags:(t,r,e)=>[{type:"GroupBuying",id:e},"GroupBuying","UserGroupBuying"]})})}),a=u.endpoints.getGroupBuying,{useGetGroupBuyingQuery:n,useGetGroupBuyingParticipantsQuery:p,useSearchGroupBuyingQuery:o,useLazySearchGroupBuyingQuery:s,useGetFeaturedGroupBuyingQuery:g,useGetPopularGroupBuyingQuery:d,useGetEndingSoonGroupBuyingQuery:y,useGetUserCreatedGroupBuyingQuery:c,useGetUserJoinedGroupBuyingQuery:l,useGetCurrentUserCreatedGroupBuyingQuery:m,useGetCurrentUserJoinedGroupBuyingQuery:B,useCreateGroupBuyingMutation:G,useUpdateGroupBuyingMutation:I,useDeleteGroupBuyingMutation:P,useJoinGroupBuyingMutation:D,useLeaveGroupBuyingMutation:E,useInviteToGroupBuyingMutation:T,useCancelGroupBuyingMutation:S,useCompleteGroupBuyingMutation:v}=u;a.useQuery},69302:(t,r,e)=>{var i,u,a;e.d(r,{wU:()=>a,ys:()=>i}),function(t){t.PENDING="PENDING",t.ACTIVE="ACTIVE",t.COMPLETED="COMPLETED",t.EXPIRED="EXPIRED",t.CANCELLED="CANCELLED"}(i||(i={})),function(t){t.PENDING="PENDING",t.PAID="PAID",t.CANCELLED="CANCELLED",t.REFUNDED="REFUNDED"}(u||(u={})),function(t){t.FIXED_PRICE="FIXED_PRICE",t.TIERED_DISCOUNT="TIERED_DISCOUNT",t.TIME_LIMITED="TIME_LIMITED"}(a||(a={}))}};