#!/bin/bash

# Template Consistency Verification Script
# Purpose: Verify that services follow User Service template exactly
# Usage: ./verify-template-consistency.sh [service-name]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
TEMPLATE_SERVICE="user-service"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

echo -e "${BLUE}🔍 Template Consistency Verification Script${NC}"
echo -e "${BLUE}===========================================${NC}"

# Check if service name provided
if [ -z "$1" ]; then
    echo -e "${RED}❌ Error: Service name required${NC}"
    echo "Usage: $0 [service-name]"
    echo "Example: $0 store-service"
    exit 1
fi

SERVICE_NAME="$1"
TEMPLATE_PATH="$PROJECT_ROOT/services/$TEMPLATE_SERVICE"
SERVICE_PATH="$PROJECT_ROOT/services/$SERVICE_NAME"

echo -e "${BLUE}Template Service:${NC} $TEMPLATE_SERVICE"
echo -e "${BLUE}Target Service:${NC} $SERVICE_NAME"
echo ""

# Check if template service exists
if [ ! -d "$TEMPLATE_PATH" ]; then
    echo -e "${RED}❌ Template service not found: $TEMPLATE_PATH${NC}"
    exit 1
fi

# Check if target service exists
if [ ! -d "$SERVICE_PATH" ]; then
    echo -e "${RED}❌ Target service not found: $SERVICE_PATH${NC}"
    exit 1
fi

# Function to compare files
compare_config_file() {
    local file_name="$1"
    local template_file="$TEMPLATE_PATH/$file_name"
    local service_file="$SERVICE_PATH/$file_name"
    
    echo -e "${YELLOW}Checking $file_name...${NC}"
    
    if [ ! -f "$template_file" ]; then
        echo -e "${RED}  ❌ Template file missing: $file_name${NC}"
        return 1
    fi
    
    if [ ! -f "$service_file" ]; then
        echo -e "${RED}  ❌ Service file missing: $file_name${NC}"
        return 1
    fi
    
    # For tsconfig.json, check critical settings
    if [ "$file_name" = "tsconfig.json" ]; then
        local template_target=$(grep '"target"' "$template_file" | sed 's/.*"target": *"\([^"]*\)".*/\1/')
        local service_target=$(grep '"target"' "$service_file" | sed 's/.*"target": *"\([^"]*\)".*/\1/')
        
        if [ "$template_target" != "$service_target" ]; then
            echo -e "${RED}  ❌ TypeScript target mismatch: template=$template_target, service=$service_target${NC}"
            return 1
        fi
        
        # Check if exclude patterns exist
        if ! grep -q '"exclude"' "$service_file"; then
            echo -e "${RED}  ❌ Missing exclude patterns in $file_name${NC}"
            return 1
        fi
        
        echo -e "${GREEN}  ✅ TypeScript configuration consistent${NC}"
        return 0
    fi
    
    # For nest-cli.json, check webpack setting
    if [ "$file_name" = "nest-cli.json" ]; then
        if ! grep -q '"webpack": true' "$service_file"; then
            echo -e "${RED}  ❌ Missing webpack configuration in $file_name${NC}"
            return 1
        fi
        
        echo -e "${GREEN}  ✅ NestJS CLI configuration consistent${NC}"
        return 0
    fi
    
    # For Dockerfile, check CMD
    if [ "$file_name" = "Dockerfile" ]; then
        if ! grep -q 'CMD \["node", "dist/main.js"\]' "$service_file"; then
            echo -e "${RED}  ❌ Incorrect CMD in Dockerfile (should be: CMD [\"node\", \"dist/main.js\"])${NC}"
            return 1
        fi
        
        echo -e "${GREEN}  ✅ Dockerfile CMD consistent${NC}"
        return 0
    fi
    
    echo -e "${GREEN}  ✅ File exists${NC}"
    return 0
}

# Verification results
ERRORS=0

echo -e "${BLUE}🔍 Verifying Configuration Files...${NC}"
echo ""

# Check critical configuration files
CONFIG_FILES=("tsconfig.json" "nest-cli.json" "Dockerfile")

for file in "${CONFIG_FILES[@]}"; do
    if ! compare_config_file "$file"; then
        ((ERRORS++))
    fi
    echo ""
done

# Check package.json structure (basic check)
echo -e "${YELLOW}Checking package.json structure...${NC}"
if [ -f "$SERVICE_PATH/package.json" ]; then
    if grep -q '"name"' "$SERVICE_PATH/package.json"; then
        echo -e "${GREEN}  ✅ Package.json exists with name field${NC}"
    else
        echo -e "${RED}  ❌ Package.json missing name field${NC}"
        ((ERRORS++))
    fi
else
    echo -e "${RED}  ❌ Package.json missing${NC}"
    ((ERRORS++))
fi

echo ""

# Summary
echo -e "${BLUE}📊 Verification Summary${NC}"
echo -e "${BLUE}=====================${NC}"

if [ $ERRORS -eq 0 ]; then
    echo -e "${GREEN}✅ SUCCESS: $SERVICE_NAME follows template consistency rules${NC}"
    echo -e "${GREEN}   All configuration files are properly configured${NC}"
    echo -e "${GREEN}   Service is ready for build and deployment${NC}"
    exit 0
else
    echo -e "${RED}❌ FAILURE: $SERVICE_NAME has $ERRORS template consistency violations${NC}"
    echo -e "${RED}   Please fix the issues above before proceeding${NC}"
    echo ""
    echo -e "${YELLOW}💡 Quick Fix Commands:${NC}"
    echo -e "${YELLOW}   1. Copy template files:${NC}"
    echo "      cp $TEMPLATE_PATH/tsconfig.json $SERVICE_PATH/"
    echo "      cp $TEMPLATE_PATH/nest-cli.json $SERVICE_PATH/"
    echo ""
    echo -e "${YELLOW}   2. Fix Dockerfile CMD:${NC}"
    echo '      sed -i "s|CMD \[\"node\", \"dist/src/main.js\"\]|CMD [\"node\", \"dist/main.js\"]|" '"$SERVICE_PATH/Dockerfile"
    echo ""
    echo -e "${YELLOW}   3. Re-run verification:${NC}"
    echo "      $0 $SERVICE_NAME"
    exit 1
fi
