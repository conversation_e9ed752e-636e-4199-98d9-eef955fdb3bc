import React from 'react';
import {
  Box,
  Flex,
  Text,
  Avatar,
  Heading,
  Badge,
  useColorModeValue,
  Button,
  Icon,
} from '@chakra-ui/react';
import { FiEdit2 } from 'react-icons/fi';

interface ProfileHeaderProps {
  user: {
    id: string;
    name?: string;
    email: string;
    role?: string;
    avatarUrl?: string;
    joinDate?: string;
    isVerified?: boolean;
  };
  onEditClick: () => void;
}

const ProfileHeader: React.FC<ProfileHeaderProps> = ({ user, onEditClick }) => {
  const bgColor = useColorModeValue('white', 'gray.700');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  return (
    <Box
      bg={bgColor}
      borderRadius="lg"
      borderWidth="1px"
      borderColor={borderColor}
      overflow="hidden"
      mb={6}
    >
      {/* Cover Image */}
      <Box
        h="150px"
        bg="brand.500"
        position="relative"
      />

      {/* Profile Info */}
      <Flex
        direction={{ base: 'column', md: 'row' }}
        justify="space-between"
        p={6}
        pt={{ base: 16, md: 6 }}
        position="relative"
      >
        {/* Avatar */}
        <Avatar
          size="xl"
          name={user.name || user.email}
          src={user.avatarUrl}
          position={{ base: 'absolute', md: 'relative' }}
          top={{ base: '-40px', md: 'auto' }}
          left={{ base: '50%', md: 'auto' }}
          transform={{ base: 'translateX(-50%)', md: 'none' }}
          border="4px solid"
          borderColor={bgColor}
        />

        {/* User Info */}
        <Box
          mt={{ base: 4, md: 0 }}
          ml={{ base: 0, md: 4 }}
          textAlign={{ base: 'center', md: 'left' }}
          flex="1"
        >
          <Flex
            direction={{ base: 'column', md: 'row' }}
            align={{ base: 'center', md: 'flex-start' }}
            justify="space-between"
          >
            <Box>
              <Heading size="lg">{user.name || 'User'}</Heading>
              <Text color="gray.500">{user.email}</Text>
              
              <Flex mt={2} align="center" justify={{ base: 'center', md: 'flex-start' }}>
                {user.role && (
                  <Badge colorScheme="brand" mr={2}>
                    {user.role}
                  </Badge>
                )}
                
                {user.isVerified && (
                  <Badge colorScheme="green">
                    Verified
                  </Badge>
                )}
              </Flex>
            </Box>
            
            <Button
              leftIcon={<Icon as={FiEdit2} />}
              variant="outline"
              size="sm"
              onClick={onEditClick}
              mt={{ base: 4, md: 0 }}
            >
              Edit Profile
            </Button>
          </Flex>
          
          {user.joinDate && (
            <Text fontSize="sm" color="gray.500" mt={2}>
              Member since {user.joinDate}
            </Text>
          )}
        </Box>
      </Flex>
    </Box>
  );
};

export default ProfileHeader;
