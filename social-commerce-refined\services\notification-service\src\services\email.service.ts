import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';
import { SendEmailDto } from '../dto/send-email.dto';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private transporter: nodemailer.Transporter;

  constructor(private readonly configService: ConfigService) {
    this.initializeTransporter();
  }

  private initializeTransporter() {
    const emailConfig = {
      host: this.configService.get<string>('SMTP_HOST', 'localhost'),
      port: this.configService.get<number>('SMTP_PORT', 587),
      secure: this.configService.get<boolean>('SMTP_SECURE', false),
      auth: {
        user: this.configService.get<string>('SMTP_USER'),
        pass: this.configService.get<string>('SMTP_PASS'),
      },
    };

    // For development, use console logging if no SMTP config
    if (!emailConfig.auth.user) {
      this.logger.warn('No SMTP configuration found, using console logging for emails');
      this.transporter = nodemailer.createTransporter({
        streamTransport: true,
        newline: 'unix',
        buffer: true,
      });
    } else {
      this.transporter = nodemailer.createTransporter(emailConfig);
    }
  }

  async sendEmail(emailData: SendEmailDto): Promise<{ messageId: string; success: boolean }> {
    const mailOptions = {
      from: this.configService.get<string>('SMTP_FROM', '<EMAIL>'),
      to: emailData.to,
      subject: emailData.subject,
      html: emailData.message,
    };

    try {
      const info = await this.transporter.sendMail(mailOptions);

      // For development without real SMTP, log the email
      if (!this.configService.get<string>('SMTP_USER')) {
        this.logger.log('📧 Email would be sent:');
        this.logger.log(`To: ${mailOptions.to}`);
        this.logger.log(`Subject: ${mailOptions.subject}`);
        this.logger.log(`Content: ${mailOptions.html}`);
      }

      return { messageId: info.messageId, success: true };
    } catch (error) {
      this.logger.error(`Failed to send email: ${error.message}`);
      throw error;
    }
  }

  async verifyConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      this.logger.log('Email service connection verified');
      return true;
    } catch (error) {
      this.logger.error(`Email service connection failed: ${error.message}`);
      return false;
    }
  }
}
