{"version": 3, "file": "stop.js", "sourceRoot": "", "sources": ["../../src/commands/stop.ts"], "names": [], "mappings": ";;AAMA,kCAoEC;AAzED,+BAA+B;AAC/B,qCAAqC;AACrC,0CAA+D;AAC/D,4CAA+C;AAE/C,SAAgB,WAAW,CAAC,OAAgB;IAC1C,OAAO;SACJ,OAAO,CAAC,gBAAgB,CAAC;SACzB,WAAW,CAAC,eAAe,CAAC;SAC5B,MAAM,CAAC,WAAW,EAAE,mBAAmB,CAAC;SACxC,MAAM,CAAC,sBAAsB,EAAE,mCAAmC,CAAC;SACnE,MAAM,CAAC,KAAK,EAAE,OAA2B,EAAE,OAAoD,EAAE,EAAE;QAClG,IAAI,CAAC;YAEH,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;gBACxD,MAAM,QAAQ,GAAG,IAAA,sBAAc,GAAE,CAAC;gBAElC,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC1B,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAAC;oBAC/C,OAAO;gBACT,CAAC;gBAED,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,MAAM,CAAC;oBACpC;wBACE,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,SAAS;wBACf,OAAO,EAAE,oCAAoC;wBAC7C,OAAO,EAAE,CAAC,GAAG,QAAQ,EAAE,KAAK,EAAE,gBAAgB,CAAC;qBAChD;iBACF,CAAC,CAAC;gBAEH,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;oBAC9B,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC;gBACrB,CAAC;qBAAM,IAAI,OAAO,CAAC,OAAO,KAAK,gBAAgB,EAAE,CAAC;oBAChD,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC;gBAChC,CAAC;qBAAM,CAAC;oBACN,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;gBAC5B,CAAC;YACH,CAAC;YAGD,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;gBAChB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC;gBACpD,MAAM,IAAA,qBAAY,GAAE,CAAC;gBACrB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC,CAAC;gBAC9D,OAAO;YACT,CAAC;YAGD,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;gBAC3B,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC,CAAC;gBAC/D,MAAM,IAAA,qBAAY,GAAE,CAAC;gBACrB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC,CAAC;gBACzE,OAAO;YACT,CAAC;YAGD,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,IAAA,qBAAa,EAAC,OAAO,CAAC,EAAE,CAAC;oBAC5B,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,OAAO,yBAAyB,CAAC,CAAC,CAAC;oBACtE,OAAO;gBACT,CAAC;gBAED,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,OAAO,aAAa,CAAC,CAAC,CAAC;gBAC1D,MAAM,IAAA,qBAAY,EAAC,CAAC,GAAG,OAAO,UAAU,CAAC,CAAC,CAAC;gBAC3C,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,OAAO,+BAA+B,CAAC,CAAC,CAAC;gBACpE,OAAO;YACT,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YACpD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;IACH,CAAC,CAAC,CAAC;AACP,CAAC"}