import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';
import {
  AppError,
  ErrorCode
} from '@app/common/errors';

@Injectable()
export class EmailService {
  private transporter: nodemailer.Transporter;
  private readonly logger = new Logger(EmailService.name);

  constructor(private configService: ConfigService) {
    try {
      const host = this.configService.get<string>('EMAIL_HOST');
      const port = this.configService.get<number>('EMAIL_PORT');
      const secure = this.configService.get<boolean>('EMAIL_SECURE');
      const user = this.configService.get<string>('EMAIL_USER');
      const pass = this.configService.get<string>('EMAIL_PASSWORD');

      if (!host || !port || !user || !pass) {
        this.logger.warn('Missing email configuration, using test account');
        this.setupTestAccount();
      } else {
        this.transporter = nodemailer.createTransport({
          host,
          port,
          secure,
          auth: {
            user,
            pass,
          },
        });
        this.logger.log('Email service initialized with configured account');
      }
    } catch (error) {
      this.logger.error(`Failed to initialize email service: ${error.message}`, error.stack);
      this.setupTestAccount();
    }
  }

  /**
   * Set up a test email account for development
   */
  private async setupTestAccount() {
    try {
      this.logger.log('Setting up test email account');
      const testAccount = await nodemailer.createTestAccount();
      
      this.transporter = nodemailer.createTransport({
        host: 'smtp.ethereal.email',
        port: 587,
        secure: false,
        auth: {
          user: testAccount.user,
          pass: testAccount.pass,
        },
      });
      
      this.logger.log(`Test email account created: ${testAccount.user}`);
    } catch (error) {
      this.logger.error(`Failed to create test email account: ${error.message}`, error.stack);
    }
  }

  /**
   * Send email verification
   */
  async sendEmailVerification(email: string, verificationLink: string): Promise<void> {
    this.logger.log(`Sending email verification to: ${email}`);

    const subject = 'Verify Your Email Address';
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Verify Your Email Address</h2>
        <p>Thank you for registering! Please click the link below to verify your email address:</p>
        <p>
          <a 
            href="${verificationLink}" 
            style="display: inline-block; background-color: #4F46E5; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;"
          >
            Verify Email
          </a>
        </p>
        <p>Or copy and paste this link in your browser:</p>
        <p>${verificationLink}</p>
        <p>This link will expire in 24 hours.</p>
        <p>If you did not create an account, please ignore this email.</p>
      </div>
    `;

    try {
      const info = await this.transporter.sendMail({
        from: `"Social Commerce" <${this.configService.get<string>('EMAIL_FROM') || '<EMAIL>'}>`,
        to: email,
        subject,
        html,
      });

      this.logger.log(`Email verification sent successfully to: ${email}`);
      
      // If using a test account, log the preview URL
      if (info.messageId && this.transporter.options.host === 'smtp.ethereal.email') {
        this.logger.log(`Email preview URL: ${nodemailer.getTestMessageUrl(info)}`);
      }
    } catch (error) {
      this.logger.error(`Failed to send email verification: ${error.message}`, error.stack);
      throw new AppError({
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        message: 'Failed to send email verification',
        translationKey: 'errors.email.sendVerificationFailed',
        details: { email, error: error.message }
      });
    }
  }

  /**
   * Send password reset email
   */
  async sendPasswordResetEmail(email: string, resetLink: string): Promise<void> {
    this.logger.log(`Sending password reset email to: ${email}`);

    const subject = 'Reset Your Password';
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Reset Your Password</h2>
        <p>We received a request to reset your password. Click the link below to create a new password:</p>
        <p>
          <a 
            href="${resetLink}" 
            style="display: inline-block; background-color: #4F46E5; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;"
          >
            Reset Password
          </a>
        </p>
        <p>Or copy and paste this link in your browser:</p>
        <p>${resetLink}</p>
        <p>This link will expire in 24 hours.</p>
        <p>If you did not request a password reset, please ignore this email.</p>
      </div>
    `;

    try {
      const info = await this.transporter.sendMail({
        from: `"Social Commerce" <${this.configService.get<string>('EMAIL_FROM') || '<EMAIL>'}>`,
        to: email,
        subject,
        html,
      });

      this.logger.log(`Password reset email sent successfully to: ${email}`);
      
      // If using a test account, log the preview URL
      if (info.messageId && this.transporter.options.host === 'smtp.ethereal.email') {
        this.logger.log(`Email preview URL: ${nodemailer.getTestMessageUrl(info)}`);
      }
    } catch (error) {
      this.logger.error(`Failed to send password reset email: ${error.message}`, error.stack);
      throw new AppError({
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        message: 'Failed to send password reset email',
        translationKey: 'errors.email.sendPasswordResetFailed',
        details: { email, error: error.message }
      });
    }
  }
}
