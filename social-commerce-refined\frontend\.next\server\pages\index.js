"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "__barrel_optimize__?names=FiHeart,FiHome,FiPackage,FiSettings,FiShoppingBag,FiShoppingCart,FiUser!=!./node_modules/react-icons/fi/index.mjs":
/*!***************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=FiHeart,FiHome,FiPackage,FiSettings,FiShoppingBag,FiShoppingCart,FiUser!=!./node_modules/react-icons/fi/index.mjs ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_rz_Documents_augment_social_commerce_social_commerce_refined_frontend_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/fi/index.mjs */ "./node_modules/react-icons/fi/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_rz_Documents_augment_social_commerce_social_commerce_refined_frontend_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_rz_Documents_augment_social_commerce_social_commerce_refined_frontend_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src\\pages\\index.tsx */ \"./src/pages/index.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/components/layout/MainLayout.tsx":
/*!**********************************************!*\
  !*** ./src/components/layout/MainLayout.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @chakra-ui/react */ \"@chakra-ui/react\");\n/* harmony import */ var _Navbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Navbar */ \"./src/components/layout/Navbar.tsx\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Sidebar */ \"./src/components/layout/Sidebar.tsx\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/context/AuthContext */ \"./src/context/AuthContext.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__, _Navbar__WEBPACK_IMPORTED_MODULE_3__, _Sidebar__WEBPACK_IMPORTED_MODULE_4__, _context_AuthContext__WEBPACK_IMPORTED_MODULE_5__]);\n([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__, _Navbar__WEBPACK_IMPORTED_MODULE_3__, _Sidebar__WEBPACK_IMPORTED_MODULE_4__, _context_AuthContext__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nconst MainLayout = ({ children })=>{\n    const { isAuthenticated } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Flex, {\n        direction: \"column\",\n        minH: \"100vh\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\MainLayout.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Flex, {\n                flex: \"1\",\n                children: [\n                    isAuthenticated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box, {\n                        as: \"aside\",\n                        w: \"250px\",\n                        minW: \"250px\",\n                        h: \"calc(100vh - 60px)\",\n                        borderRightWidth: \"1px\",\n                        display: {\n                            base: \"none\",\n                            md: \"block\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\MainLayout.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\MainLayout.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box, {\n                        as: \"main\",\n                        flex: \"1\",\n                        p: 4,\n                        overflowY: \"auto\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\MainLayout.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\MainLayout.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\MainLayout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MainLayout);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/layout/MainLayout.tsx\n");

/***/ }),

/***/ "./src/components/layout/Navbar.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Navbar.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @chakra-ui/react */ \"@chakra-ui/react\");\n/* harmony import */ var _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @chakra-ui/icons */ \"@chakra-ui/icons\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/context/AuthContext */ \"./src/context/AuthContext.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__, _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_3__, _context_AuthContext__WEBPACK_IMPORTED_MODULE_5__]);\n([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__, _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_3__, _context_AuthContext__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nconst Navbar = ()=>{\n    const { isOpen, onToggle } = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useDisclosure)();\n    const { user, isAuthenticated, logout } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Flex, {\n                bg: (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue)(\"white\", \"gray.800\"),\n                color: (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue)(\"gray.600\", \"white\"),\n                minH: \"60px\",\n                py: {\n                    base: 2\n                },\n                px: {\n                    base: 4\n                },\n                borderBottom: 1,\n                borderStyle: \"solid\",\n                borderColor: (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue)(\"gray.200\", \"gray.900\"),\n                align: \"center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Flex, {\n                        flex: {\n                            base: 1,\n                            md: \"auto\"\n                        },\n                        ml: {\n                            base: -2\n                        },\n                        display: {\n                            base: \"flex\",\n                            md: \"none\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.IconButton, {\n                            onClick: onToggle,\n                            icon: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_3__.CloseIcon, {\n                                w: 3,\n                                h: 3\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 24\n                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_3__.HamburgerIcon, {\n                                w: 5,\n                                h: 5\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 52\n                            }, void 0),\n                            variant: \"ghost\",\n                            \"aria-label\": \"Toggle Navigation\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Flex, {\n                        flex: {\n                            base: 1\n                        },\n                        justify: {\n                            base: \"center\",\n                            md: \"start\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                href: \"/\",\n                                passHref: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                    as: \"a\",\n                                    textAlign: (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useBreakpointValue)({\n                                        base: \"center\",\n                                        md: \"left\"\n                                    }),\n                                    fontFamily: \"heading\",\n                                    color: (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue)(\"brand.500\", \"white\"),\n                                    fontWeight: \"bold\",\n                                    fontSize: \"xl\",\n                                    children: \"Social Commerce\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Flex, {\n                                display: {\n                                    base: \"none\",\n                                    md: \"flex\"\n                                },\n                                ml: 10,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DesktopNav, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                        flex: {\n                            base: 1,\n                            md: 0\n                        },\n                        justify: \"flex-end\",\n                        direction: \"row\",\n                        spacing: 6,\n                        children: isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Menu, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.MenuButton, {\n                                    as: _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Button,\n                                    rounded: \"full\",\n                                    variant: \"link\",\n                                    cursor: \"pointer\",\n                                    minW: 0,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Avatar, {\n                                        size: \"sm\",\n                                        name: user?.name || user?.email\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.MenuList, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                            href: \"/profile\",\n                                            passHref: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.MenuItem, {\n                                                as: \"a\",\n                                                children: \"Profile\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                            href: \"/dashboard\",\n                                            passHref: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.MenuItem, {\n                                                as: \"a\",\n                                                children: \"Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.MenuDivider, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.MenuItem, {\n                                            onClick: logout,\n                                            children: \"Sign Out\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    href: \"/login\",\n                                    passHref: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        as: \"a\",\n                                        fontSize: \"sm\",\n                                        fontWeight: 400,\n                                        variant: \"link\",\n                                        children: \"Sign In\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    href: \"/register\",\n                                    passHref: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        as: \"a\",\n                                        display: {\n                                            base: \"none\",\n                                            md: \"inline-flex\"\n                                        },\n                                        fontSize: \"sm\",\n                                        fontWeight: 600,\n                                        color: \"white\",\n                                        bg: \"brand.500\",\n                                        _hover: {\n                                            bg: \"brand.600\"\n                                        },\n                                        children: \"Sign Up\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Collapse, {\n                in: isOpen,\n                animateOpacity: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MobileNav, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, undefined);\n};\nconst DesktopNav = ()=>{\n    const linkColor = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue)(\"gray.600\", \"gray.200\");\n    const linkHoverColor = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue)(\"brand.500\", \"white\");\n    const popoverContentBgColor = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue)(\"white\", \"gray.800\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n        direction: \"row\",\n        spacing: 4,\n        children: NAV_ITEMS.map((navItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Popover, {\n                    trigger: \"hover\",\n                    placement: \"bottom-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.PopoverTrigger, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Link, {\n                                as: (next_link__WEBPACK_IMPORTED_MODULE_4___default()),\n                                p: 2,\n                                href: navItem.href ?? \"#\",\n                                fontSize: \"sm\",\n                                fontWeight: 500,\n                                color: linkColor,\n                                _hover: {\n                                    textDecoration: \"none\",\n                                    color: linkHoverColor\n                                },\n                                children: navItem.label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 13\n                        }, undefined),\n                        navItem.children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.PopoverContent, {\n                            border: 0,\n                            boxShadow: \"xl\",\n                            bg: popoverContentBgColor,\n                            p: 4,\n                            rounded: \"xl\",\n                            minW: \"sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                                children: navItem.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DesktopSubNav, {\n                                        ...child\n                                    }, child.label, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 21\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 11\n                }, undefined)\n            }, navItem.label, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                lineNumber: 157,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, undefined);\n};\nconst DesktopSubNav = ({ label, href, subLabel })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Link, {\n        as: (next_link__WEBPACK_IMPORTED_MODULE_4___default()),\n        href: href,\n        role: \"group\",\n        display: \"block\",\n        p: 2,\n        rounded: \"md\",\n        _hover: {\n            bg: (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue)(\"brand.50\", \"gray.900\")\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n            direction: \"row\",\n            align: \"center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                            transition: \"all .3s ease\",\n                            _groupHover: {\n                                color: \"brand.500\"\n                            },\n                            fontWeight: 500,\n                            children: label\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                            fontSize: \"sm\",\n                            children: subLabel\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Flex, {\n                    transition: \"all .3s ease\",\n                    transform: \"translateX(-10px)\",\n                    opacity: 0,\n                    _groupHover: {\n                        opacity: \"100%\",\n                        transform: \"translateX(0)\"\n                    },\n                    justify: \"flex-end\",\n                    align: \"center\",\n                    flex: 1,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Icon, {\n                        color: \"brand.500\",\n                        w: 5,\n                        h: 5,\n                        as: _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_3__.ChevronRightIcon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n            lineNumber: 210,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n        lineNumber: 201,\n        columnNumber: 5\n    }, undefined);\n};\nconst MobileNav = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n        bg: (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue)(\"white\", \"gray.800\"),\n        p: 4,\n        display: {\n            md: \"none\"\n        },\n        children: NAV_ITEMS.map((navItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MobileNavItem, {\n                ...navItem\n            }, navItem.label, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                lineNumber: 245,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n        lineNumber: 239,\n        columnNumber: 5\n    }, undefined);\n};\nconst MobileNavItem = ({ label, children, href })=>{\n    const { isOpen, onToggle } = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useDisclosure)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n        spacing: 4,\n        onClick: children && onToggle,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Flex, {\n                py: 2,\n                as: _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Link,\n                href: href ?? \"#\",\n                justify: \"space-between\",\n                align: \"center\",\n                _hover: {\n                    textDecoration: \"none\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                        fontWeight: 600,\n                        color: (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue)(\"gray.600\", \"gray.200\"),\n                        children: label\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, undefined),\n                    children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Icon, {\n                        as: _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_3__.ChevronDownIcon,\n                        transition: \"all .25s ease-in-out\",\n                        transform: isOpen ? \"rotate(180deg)\" : \"\",\n                        w: 6,\n                        h: 6\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Collapse, {\n                in: isOpen,\n                animateOpacity: true,\n                style: {\n                    marginTop: \"0!important\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                    mt: 2,\n                    pl: 4,\n                    borderLeft: 1,\n                    borderStyle: \"solid\",\n                    borderColor: (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue)(\"gray.200\", \"gray.700\"),\n                    align: \"start\",\n                    children: children && children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Link, {\n                            py: 2,\n                            href: child.href,\n                            children: child.label\n                        }, child.label, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 15\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                lineNumber: 283,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n        lineNumber: 255,\n        columnNumber: 5\n    }, undefined);\n};\nconst NAV_ITEMS = [\n    {\n        label: \"Explore\",\n        children: [\n            {\n                label: \"Stores\",\n                subLabel: \"Browse all stores\",\n                href: \"/stores\"\n            },\n            {\n                label: \"Products\",\n                subLabel: \"Find products\",\n                href: \"/products\"\n            }\n        ]\n    },\n    {\n        label: \"How It Works\",\n        href: \"/how-it-works\"\n    },\n    {\n        label: \"About\",\n        href: \"/about\"\n    }\n];\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navbar);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/layout/Navbar.tsx\n");

/***/ }),

/***/ "./src/components/layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @chakra-ui/react */ \"@chakra-ui/react\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_FiHeart_FiHome_FiPackage_FiSettings_FiShoppingBag_FiShoppingCart_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FiHeart,FiHome,FiPackage,FiSettings,FiShoppingBag,FiShoppingCart,FiUser!=!react-icons/fi */ \"__barrel_optimize__?names=FiHeart,FiHome,FiPackage,FiSettings,FiShoppingBag,FiShoppingCart,FiUser!=!./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/context/AuthContext */ \"./src/context/AuthContext.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__, _context_AuthContext__WEBPACK_IMPORTED_MODULE_5__]);\n([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__, _context_AuthContext__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nconst NavItem = ({ icon, children, href, isActive })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Link, {\n        as: (next_link__WEBPACK_IMPORTED_MODULE_3___default()),\n        href: href,\n        style: {\n            textDecoration: \"none\"\n        },\n        _focus: {\n            boxShadow: \"none\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Flex, {\n            align: \"center\",\n            p: \"3\",\n            mx: \"2\",\n            borderRadius: \"lg\",\n            role: \"group\",\n            cursor: \"pointer\",\n            bg: isActive ? \"brand.50\" : \"transparent\",\n            color: isActive ? \"brand.500\" : \"gray.600\",\n            _hover: {\n                bg: \"brand.50\",\n                color: \"brand.500\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Icon, {\n                    mr: \"4\",\n                    fontSize: \"16\",\n                    as: icon,\n                    color: isActive ? \"brand.500\" : \"gray.500\",\n                    _groupHover: {\n                        color: \"brand.500\"\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, undefined),\n                children\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, undefined);\n};\nconst Sidebar = ()=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { user } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const isActive = (path)=>{\n        return router.pathname === path || router.pathname.startsWith(`${path}/`);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box, {\n        h: \"full\",\n        bg: (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue)(\"white\", \"gray.900\"),\n        overflowY: \"auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Flex, {\n            direction: \"column\",\n            h: \"full\",\n            py: 5,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                    spacing: 1,\n                    flex: \"1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavItem, {\n                            icon: _barrel_optimize_names_FiHeart_FiHome_FiPackage_FiSettings_FiShoppingBag_FiShoppingCart_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiHome,\n                            href: \"/dashboard\",\n                            isActive: isActive(\"/dashboard\"),\n                            children: \"Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavItem, {\n                            icon: _barrel_optimize_names_FiHeart_FiHome_FiPackage_FiSettings_FiShoppingBag_FiShoppingCart_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiShoppingBag,\n                            href: \"/stores\",\n                            isActive: isActive(\"/stores\"),\n                            children: \"Stores\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavItem, {\n                            icon: _barrel_optimize_names_FiHeart_FiHome_FiPackage_FiSettings_FiShoppingBag_FiShoppingCart_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiPackage,\n                            href: \"/products\",\n                            isActive: isActive(\"/products\"),\n                            children: \"Products\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavItem, {\n                            icon: _barrel_optimize_names_FiHeart_FiHome_FiPackage_FiSettings_FiShoppingBag_FiShoppingCart_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiShoppingCart,\n                            href: \"/orders\",\n                            isActive: isActive(\"/orders\"),\n                            children: \"Orders\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavItem, {\n                            icon: _barrel_optimize_names_FiHeart_FiHome_FiPackage_FiSettings_FiShoppingBag_FiShoppingCart_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiHeart,\n                            href: \"/wishlist\",\n                            isActive: isActive(\"/wishlist\"),\n                            children: \"Wishlist\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Divider, {\n                            my: 3\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavItem, {\n                            icon: _barrel_optimize_names_FiHeart_FiHome_FiPackage_FiSettings_FiShoppingBag_FiShoppingCart_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiUser,\n                            href: \"/profile\",\n                            isActive: isActive(\"/profile\"),\n                            children: \"Profile\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavItem, {\n                            icon: _barrel_optimize_names_FiHeart_FiHome_FiPackage_FiSettings_FiShoppingBag_FiShoppingCart_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiSettings,\n                            href: \"/settings\",\n                            isActive: isActive(\"/settings\"),\n                            children: \"Settings\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box, {\n                    p: 4,\n                    mt: 4,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                            fontSize: \"sm\",\n                            color: \"gray.500\",\n                            children: \"Logged in as:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                            fontWeight: \"medium\",\n                            fontSize: \"sm\",\n                            children: user?.name || user?.email\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/layout/Sidebar.tsx\n");

/***/ }),

/***/ "./src/context/AuthContext.tsx":
/*!*************************************!*\
  !*** ./src/context/AuthContext.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/api */ \"./src/services/api.ts\");\n/* harmony import */ var jwt_decode__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! jwt-decode */ \"jwt-decode\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_services_api__WEBPACK_IMPORTED_MODULE_3__, jwt_decode__WEBPACK_IMPORTED_MODULE_4__]);\n([_services_api__WEBPACK_IMPORTED_MODULE_3__, jwt_decode__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check if user is logged in on initial load\n        const token = localStorage.getItem(\"token\");\n        if (token) {\n            try {\n                // Decode token to get user info\n                const decoded = (0,jwt_decode__WEBPACK_IMPORTED_MODULE_4__.jwtDecode)(token);\n                // Check if token is expired\n                const currentTime = Date.now() / 1000;\n                if (decoded.exp && decoded.exp < currentTime) {\n                    // Token is expired\n                    localStorage.removeItem(\"token\");\n                    setUser(null);\n                    setIsLoading(false);\n                    return;\n                }\n                // Fetch user profile\n                fetchUserProfile();\n            } catch (error) {\n                console.error(\"Invalid token:\", error);\n                localStorage.removeItem(\"token\");\n                setUser(null);\n                setIsLoading(false);\n            }\n        } else {\n            setIsLoading(false);\n        }\n    }, []);\n    const fetchUserProfile = async ()=>{\n        try {\n            const userData = await _services_api__WEBPACK_IMPORTED_MODULE_3__.userApi.getProfile();\n            setUser(userData);\n        } catch (error) {\n            console.error(\"Error fetching user profile:\", error);\n            localStorage.removeItem(\"token\");\n            setUser(null);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const login = async (email, password)=>{\n        console.log(\"AuthContext: Login attempt started\", {\n            email\n        });\n        console.log(\"AuthContext: Password details\", {\n            length: password.length,\n            firstChar: password.charAt(0),\n            lastChar: password.charAt(password.length - 1),\n            hasUppercase: /[A-Z]/.test(password),\n            hasLowercase: /[a-z]/.test(password),\n            hasNumbers: /[0-9]/.test(password),\n            hasSpecialChars: /[!@#$%^&*]/.test(password)\n        });\n        setIsLoading(true);\n        try {\n            console.log(\"AuthContext: Calling authApi.login\");\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__.authApi.login(email, password);\n            console.log(\"AuthContext: Login response received\", response);\n            const token = response.accessToken || response.token; // Handle both formats\n            console.log(\"AuthContext: Token extracted\", {\n                hasToken: !!token\n            });\n            localStorage.setItem(\"token\", token);\n            // Decode token to get user info\n            const decoded = (0,jwt_decode__WEBPACK_IMPORTED_MODULE_4__.jwtDecode)(token);\n            console.log(\"AuthContext: Token decoded\", decoded);\n            setUser({\n                id: decoded.sub,\n                email: decoded.email,\n                name: decoded.name || \"\",\n                role: decoded.role || \"user\"\n            });\n            console.log(\"AuthContext: User set, redirecting to dashboard\");\n            router.push(\"/dashboard\");\n        } catch (error) {\n            console.error(\"AuthContext: Login error:\", error);\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const register = async (userData)=>{\n        setIsLoading(true);\n        try {\n            await _services_api__WEBPACK_IMPORTED_MODULE_3__.authApi.register(userData);\n            // After registration, redirect to login\n            router.push(\"/login?registered=true\");\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = ()=>{\n        localStorage.removeItem(\"token\");\n        setUser(null);\n        router.push(\"/login\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            isLoading,\n            isAuthenticated: !!user,\n            login,\n            register,\n            logout\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\context\\\\AuthContext.tsx\",\n        lineNumber: 137,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuthContext);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/context/AuthContext.tsx\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @chakra-ui/react */ \"@chakra-ui/react\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/AuthContext */ \"./src/context/AuthContext.tsx\");\n/* harmony import */ var _styles_theme__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/styles/theme */ \"./src/styles/theme.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__, _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__, _styles_theme__WEBPACK_IMPORTED_MODULE_3__]);\n([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__, _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__, _styles_theme__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.ChakraProvider, {\n        theme: _styles_theme__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 10,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUNrRDtBQUNHO0FBQ2xCO0FBRXBCLFNBQVNHLElBQUksRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQVk7SUFDNUQscUJBQ0UsOERBQUNMLDREQUFjQTtRQUFDRSxPQUFPQSxxREFBS0E7a0JBQzFCLDRFQUFDRCw4REFBWUE7c0JBQ1gsNEVBQUNHO2dCQUFXLEdBQUdDLFNBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJaEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zb2NpYWwtY29tbWVyY2UtZnJvbnRlbmQvLi9zcmMvcGFnZXMvX2FwcC50c3g/ZjlkNiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IEFwcFByb3BzIH0gZnJvbSAnbmV4dC9hcHAnO1xuaW1wb3J0IHsgQ2hha3JhUHJvdmlkZXIgfSBmcm9tICdAY2hha3JhLXVpL3JlYWN0JztcbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gJ0AvY29udGV4dC9BdXRoQ29udGV4dCc7XG5pbXBvcnQgdGhlbWUgZnJvbSAnQC9zdHlsZXMvdGhlbWUnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHAoeyBDb21wb25lbnQsIHBhZ2VQcm9wcyB9OiBBcHBQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxDaGFrcmFQcm92aWRlciB0aGVtZT17dGhlbWV9PlxuICAgICAgPEF1dGhQcm92aWRlcj5cbiAgICAgICAgPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxuICAgICAgPC9BdXRoUHJvdmlkZXI+XG4gICAgPC9DaGFrcmFQcm92aWRlcj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJDaGFrcmFQcm92aWRlciIsIkF1dGhQcm92aWRlciIsInRoZW1lIiwiQXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @chakra-ui/react */ \"@chakra-ui/react\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/context/AuthContext */ \"./src/context/AuthContext.tsx\");\n/* harmony import */ var _components_layout_MainLayout__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/layout/MainLayout */ \"./src/components/layout/MainLayout.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__, _context_AuthContext__WEBPACK_IMPORTED_MODULE_5__, _components_layout_MainLayout__WEBPACK_IMPORTED_MODULE_6__]);\n([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__, _context_AuthContext__WEBPACK_IMPORTED_MODULE_5__, _components_layout_MainLayout__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nconst Home = ()=>{\n    const { isAuthenticated, isLoading } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Redirect to dashboard if authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoading && isAuthenticated) {\n            router.push(\"/dashboard\");\n        }\n    }, [\n        isAuthenticated,\n        isLoading,\n        router\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_MainLayout__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Container, {\n            maxW: \"7xl\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Stack, {\n                align: \"center\",\n                spacing: {\n                    base: 8,\n                    md: 10\n                },\n                py: {\n                    base: 20,\n                    md: 28\n                },\n                direction: {\n                    base: \"column\",\n                    md: \"row\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Stack, {\n                        flex: 1,\n                        spacing: {\n                            base: 5,\n                            md: 10\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Heading, {\n                                lineHeight: 1.1,\n                                fontWeight: 600,\n                                fontSize: {\n                                    base: \"3xl\",\n                                    sm: \"4xl\",\n                                    lg: \"6xl\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        as: \"span\",\n                                        position: \"relative\",\n                                        _after: {\n                                            content: \"''\",\n                                            width: \"full\",\n                                            height: \"30%\",\n                                            position: \"absolute\",\n                                            bottom: 1,\n                                            left: 0,\n                                            bg: \"brand.400\",\n                                            zIndex: -1\n                                        },\n                                        children: \"Social Commerce\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        as: \"span\",\n                                        color: \"brand.500\",\n                                        children: \"Platform\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                color: \"gray.500\",\n                                children: \"Connect, sell, and grow your business with our social commerce platform. Create your own store, connect with customers, and boost your sales through social interactions and group buying features.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Stack, {\n                                spacing: {\n                                    base: 4,\n                                    sm: 6\n                                },\n                                direction: {\n                                    base: \"column\",\n                                    sm: \"row\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        as: (next_link__WEBPACK_IMPORTED_MODULE_4___default()),\n                                        href: \"/register\",\n                                        rounded: \"full\",\n                                        size: \"lg\",\n                                        fontWeight: \"normal\",\n                                        px: 6,\n                                        colorScheme: \"brand\",\n                                        bg: \"brand.500\",\n                                        _hover: {\n                                            bg: \"brand.600\"\n                                        },\n                                        children: \"Get started\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        as: (next_link__WEBPACK_IMPORTED_MODULE_4___default()),\n                                        href: \"/login\",\n                                        rounded: \"full\",\n                                        size: \"lg\",\n                                        fontWeight: \"normal\",\n                                        px: 6,\n                                        leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayIcon, {\n                                            h: 4,\n                                            w: 4,\n                                            color: \"gray.300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 27\n                                        }, void 0),\n                                        children: \"Sign in\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Flex, {\n                        flex: 1,\n                        justify: \"center\",\n                        align: \"center\",\n                        position: \"relative\",\n                        w: \"full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                            position: \"relative\",\n                            height: \"300px\",\n                            rounded: \"2xl\",\n                            boxShadow: \"2xl\",\n                            width: \"full\",\n                            overflow: \"hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Image, {\n                                alt: \"Hero Image\",\n                                fit: \"cover\",\n                                align: \"center\",\n                                w: \"100%\",\n                                h: \"100%\",\n                                src: \"https://images.unsplash.com/photo-1499951360447-b19be8fe80f5?ixlib=rb-1.2.1&ixid=MXwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHw%3D&auto=format&fit=crop&w=800&q=80\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, undefined);\n};\n// Play icon component\nconst PlayIcon = (props)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        height: props.h,\n        width: props.w,\n        viewBox: \"0 0 58 58\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"29\",\n                cy: \"29\",\n                r: \"29\",\n                fill: \"currentColor\",\n                fillOpacity: \"0.2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M23 36V22L37 29L23 36Z\",\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment\\\\social-commerce\\\\social-commerce-refined\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n        lineNumber: 136,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Home);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/index.tsx\n");

/***/ }),

/***/ "./src/services/api.ts":
/*!*****************************!*\
  !*** ./src/services/api.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   productApi: () => (/* binding */ productApi),\n/* harmony export */   storeApi: () => (/* binding */ storeApi),\n/* harmony export */   userApi: () => (/* binding */ userApi)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"axios\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_0__]);\naxios__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n// Create axios instance\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: \"http://localhost:3001\",\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// Request interceptor for adding the auth token\napi.interceptors.request.use((config)=>{\n    const token = localStorage.getItem(\"token\");\n    if (token) {\n        config.headers[\"Authorization\"] = `Bearer ${token}`;\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor for handling errors\napi.interceptors.response.use((response)=>response, (error)=>{\n    // Handle 401 Unauthorized errors (token expired)\n    if (error.response?.status === 401) {\n        // Clear token and redirect to login\n        localStorage.removeItem(\"token\");\n        window.location.href = \"/login\";\n    }\n    return Promise.reject(error);\n});\n// Generic request function\nconst request = async (config)=>{\n    try {\n        const response = await api(config);\n        return response.data;\n    } catch (error) {\n        if (axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].isAxiosError(error)) {\n            // Handle specific error cases\n            const errorMessage = error.response?.data?.message || error.message;\n            console.error(`API Error: ${errorMessage}`);\n            throw new Error(errorMessage);\n        }\n        throw error;\n    }\n};\n// API endpoints\nconst authApi = {\n    login: (email, password)=>request({\n            url: \"/api/auth/login\",\n            method: \"POST\",\n            data: {\n                email,\n                password\n            }\n        }),\n    register: (userData)=>request({\n            url: \"/api/auth/register\",\n            method: \"POST\",\n            data: userData\n        }),\n    verifyEmail: (token)=>request({\n            url: `/api/auth/verify-email/${token}`,\n            method: \"GET\"\n        }),\n    forgotPassword: (email)=>request({\n            url: \"/api/auth/forgot-password\",\n            method: \"POST\",\n            data: {\n                email\n            }\n        }),\n    resetPassword: (token, password)=>request({\n            url: \"/api/auth/reset-password\",\n            method: \"POST\",\n            data: {\n                token,\n                password\n            }\n        })\n};\nconst userApi = {\n    getProfile: ()=>request({\n            url: \"/users/profile\",\n            method: \"GET\"\n        }),\n    updateProfile: (profileData)=>request({\n            url: \"/users/profile\",\n            method: \"PUT\",\n            data: profileData\n        })\n};\nconst storeApi = {\n    getAllStores: ()=>request({\n            url: \"/stores\",\n            method: \"GET\"\n        }),\n    getStoreById: (id)=>request({\n            url: `/stores/${id}`,\n            method: \"GET\"\n        }),\n    createStore: (storeData)=>request({\n            url: \"/stores\",\n            method: \"POST\",\n            data: storeData\n        }),\n    updateStore: (id, storeData)=>request({\n            url: `/stores/${id}`,\n            method: \"PUT\",\n            data: storeData\n        }),\n    deleteStore: (id)=>request({\n            url: `/stores/${id}`,\n            method: \"DELETE\"\n        })\n};\nconst productApi = {\n    getProductsByStore: (storeId)=>request({\n            url: `/stores/${storeId}/products`,\n            method: \"GET\"\n        }),\n    getProductById: (storeId, productId)=>request({\n            url: `/stores/${storeId}/products/${productId}`,\n            method: \"GET\"\n        }),\n    createProduct: (storeId, productData)=>request({\n            url: `/stores/${storeId}/products`,\n            method: \"POST\",\n            data: productData\n        }),\n    updateProduct: (storeId, productId, productData)=>request({\n            url: `/stores/${storeId}/products/${productId}`,\n            method: \"PUT\",\n            data: productData\n        }),\n    deleteProduct: (storeId, productId)=>request({\n            url: `/stores/${storeId}/products/${productId}`,\n            method: \"DELETE\"\n        })\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/services/api.ts\n");

/***/ }),

/***/ "./src/styles/theme.ts":
/*!*****************************!*\
  !*** ./src/styles/theme.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @chakra-ui/react */ \"@chakra-ui/react\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_0__]);\n_chakra_ui_react__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n// Color mode config\nconst config = {\n    initialColorMode: \"light\",\n    useSystemColorMode: false\n};\n// Custom colors\nconst colors = {\n    brand: {\n        50: \"#e6f7ff\",\n        100: \"#b3e0ff\",\n        200: \"#80caff\",\n        300: \"#4db3ff\",\n        400: \"#1a9dff\",\n        500: \"#0080ff\",\n        600: \"#0066cc\",\n        700: \"#004d99\",\n        800: \"#003366\",\n        900: \"#001a33\"\n    },\n    accent: {\n        50: \"#fff0e6\",\n        100: \"#ffd6b3\",\n        200: \"#ffbd80\",\n        300: \"#ffa34d\",\n        400: \"#ff8a1a\",\n        500: \"#ff7000\",\n        600: \"#cc5a00\",\n        700: \"#994300\",\n        800: \"#662d00\",\n        900: \"#331600\"\n    }\n};\n// Font configuration\nconst fonts = {\n    heading: 'Inter, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Helvetica, Arial, sans-serif',\n    body: 'Inter, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Helvetica, Arial, sans-serif'\n};\n// Component style overrides\nconst components = {\n    Button: {\n        baseStyle: {\n            fontWeight: \"semibold\",\n            borderRadius: \"md\"\n        },\n        variants: {\n            solid: {\n                bg: \"brand.500\",\n                color: \"white\",\n                _hover: {\n                    bg: \"brand.600\"\n                }\n            },\n            outline: {\n                borderColor: \"brand.500\",\n                color: \"brand.500\",\n                _hover: {\n                    bg: \"brand.50\"\n                }\n            },\n            ghost: {\n                color: \"brand.500\",\n                _hover: {\n                    bg: \"brand.50\"\n                }\n            }\n        }\n    },\n    Card: {\n        baseStyle: {\n            container: {\n                borderRadius: \"lg\",\n                boxShadow: \"md\"\n            }\n        }\n    },\n    Input: {\n        variants: {\n            outline: {\n                field: {\n                    borderColor: \"gray.300\",\n                    _focus: {\n                        borderColor: \"brand.500\",\n                        boxShadow: \"0 0 0 1px var(--chakra-colors-brand-500)\"\n                    }\n                }\n            }\n        }\n    }\n};\n// Create the theme\nconst theme = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_0__.extendTheme)({\n    config,\n    colors,\n    fonts,\n    components,\n    styles: {\n        global: {\n            body: {\n                bg: \"gray.50\"\n            }\n        }\n    }\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (theme);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/styles/theme.ts\n");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "@chakra-ui/icons":
/*!***********************************!*\
  !*** external "@chakra-ui/icons" ***!
  \***********************************/
/***/ ((module) => {

module.exports = import("@chakra-ui/icons");;

/***/ }),

/***/ "@chakra-ui/react":
/*!***********************************!*\
  !*** external "@chakra-ui/react" ***!
  \***********************************/
/***/ ((module) => {

module.exports = import("@chakra-ui/react");;

/***/ }),

/***/ "axios":
/*!************************!*\
  !*** external "axios" ***!
  \************************/
/***/ ((module) => {

module.exports = import("axios");;

/***/ }),

/***/ "jwt-decode":
/*!*****************************!*\
  !*** external "jwt-decode" ***!
  \*****************************/
/***/ ((module) => {

module.exports = import("jwt-decode");;

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/react-icons"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();