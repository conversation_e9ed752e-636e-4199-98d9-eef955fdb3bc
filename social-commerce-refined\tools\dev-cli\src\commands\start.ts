import { Command } from 'commander';
import * as chalk from 'chalk';
import * as inquirer from 'inquirer';
import { getAllServices, serviceExists } from '../utils/paths';
import { startServices } from '../utils/docker';
import { startService } from '../utils/npm';

export function startCommand(program: Command): void {
  program
    .command('start [service]')
    .description('Start services')
    .option('-a, --all', 'Start all services')
    .option('-i, --infrastructure', 'Start infrastructure services only')
    .action(async (service: string | undefined, options: { all?: boolean; infrastructure?: boolean }) => {
      try {
        // Start infrastructure services
        console.log(chalk.blue('Starting infrastructure services...'));
        await startServices();
        console.log(chalk.green('Infrastructure services started successfully'));

        // If only infrastructure services are requested, exit
        if (options.infrastructure) {
          return;
        }

        // If no service is specified and --all is not set, prompt for service
        if (!service && !options.all) {
          const services = getAllServices();
          
          if (services.length === 0) {
            console.log(chalk.yellow('No services found'));
            return;
          }
          
          const answers = await inquirer.prompt([
            {
              type: 'list',
              name: 'service',
              message: 'Which service do you want to start?',
              choices: [...services, 'all'],
            },
          ]);
          
          if (answers.service === 'all') {
            options.all = true;
          } else {
            service = answers.service;
          }
        }

        // Start all services
        if (options.all) {
          const services = getAllServices();
          
          if (services.length === 0) {
            console.log(chalk.yellow('No services found'));
            return;
          }
          
          console.log(chalk.blue(`Starting all services: ${services.join(', ')}...`));
          
          for (const svc of services) {
            try {
              await startService(svc);
            } catch (error) {
              console.error(chalk.red(`Error starting ${svc}-service: ${error.message}`));
            }
          }
          
          console.log(chalk.green('All services started successfully'));
          return;
        }

        // Start specific service
        if (service) {
          if (!serviceExists(service)) {
            console.error(chalk.red(`Service ${service}-service does not exist`));
            return;
          }
          
          console.log(chalk.blue(`Starting ${service}-service...`));
          await startService(service);
          console.log(chalk.green(`${service}-service started successfully`));
          return;
        }
      } catch (error) {
        console.error(chalk.red(`Error: ${error.message}`));
        process.exit(1);
      }
    });
}
