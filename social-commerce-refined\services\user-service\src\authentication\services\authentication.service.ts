import { Injectable, Logger, UnauthorizedException, ConflictException, NotFoundException, Inject } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ClientProxy } from '@nestjs/microservices';
import { UserRepository } from '../repositories/user.repository';
import { CreateUserDto } from '../dto/create-user.dto';
import { User } from '../entities/user.entity';
// import { EventPublisherService, UserCreatedEvent } from '@app/messaging'; // Temporarily disabled

@Injectable()
export class AuthenticationService {
  private readonly logger = new Logger(AuthenticationService.name);

  constructor(
    private readonly userRepository: UserRepository,
    private readonly jwtService: JwtService,
    // @Inject('NOTIFICATION_SERVICE') private readonly notificationClient: ClientProxy, // Temporarily disabled for testing
    // private readonly eventPublisher: EventPublisherService, // Temporarily disabled
  ) {}

  async register(createUserDto: CreateUserDto): Promise<User> {
    this.logger.log(`Registering user with email: ${createUserDto.email}`);

    // Check if user already exists
    const existingUser = await this.userRepository.findByEmail(createUserDto.email);

    if (existingUser) {
      throw new ConflictException(`User with email ${createUserDto.email} already exists`);
    }

    // Create user
    const user = await this.userRepository.create(createUserDto);

    // TODO: Add event publishing and notifications when messaging is enabled
    // For now, just return the user without profile to avoid circular reference

    return user;
  }

  async validateUser(email: string, password: string): Promise<User> {
    this.logger.log(`Validating user with email: ${email}`);
    this.logger.debug(`Password length: ${password?.length}, Password starts with: ${password?.substring(0, 3)}...`);

    const user = await this.userRepository.findByEmail(email);

    if (!user) {
      this.logger.error(`User not found with email: ${email}`);
      throw new UnauthorizedException('Invalid credentials');
    }

    this.logger.debug(`User found: ${user.email}, Password hash starts with: ${user.password?.substring(0, 10)}...`);

    const isPasswordValid = await user.validatePassword(password);
    this.logger.debug(`Password validation result: ${isPasswordValid}`);

    if (!isPasswordValid) {
      this.logger.error(`Password validation failed for user: ${email}`);
      throw new UnauthorizedException('Invalid credentials');
    }

    // Update last login
    await this.userRepository.updateLastLogin(user.id);

    return user;
  }

  async login(user: User): Promise<{ accessToken: string; user: User }> {
    this.logger.log(`Generating JWT token for user: ${user.email}`);

    const payload = {
      sub: user.id,
      email: user.email,
      role: user.role,
    };

    const accessToken = this.jwtService.sign(payload);

    return {
      accessToken,
      user,
    };
  }

  async getProfile(userId: string): Promise<User> {
    this.logger.log(`Getting profile for user with ID: ${userId}`);

    return this.userRepository.findOne(userId);
  }

  async findByEmail(email: string): Promise<User> {
    this.logger.log(`Finding user with email: ${email}`);

    const user = await this.userRepository.findByEmail(email);

    if (!user) {
      throw new NotFoundException(`User with email ${email} not found`);
    }

    return user;
  }
}
