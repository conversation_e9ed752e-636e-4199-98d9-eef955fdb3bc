(()=>{var e={};e.id=7009,e.ids=[7009],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},18354:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>g,tree:()=>o});var r=t(67096),s=t(16132),d=t(37284),n=t.n(d),i=t(32564),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(a,l);let o=["",{children:["affiliate",{children:["dashboard",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,42569)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\affiliate\\dashboard\\settings\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9291,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\affiliate\\dashboard\\settings\\page.tsx"],m="/affiliate/dashboard/settings/page",u={require:t,loadChunk:()=>Promise.resolve()},g=new r.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/affiliate/dashboard/settings/page",pathname:"/affiliate/dashboard/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},88092:(e,a,t)=>{Promise.resolve().then(t.bind(t,88844))},88844:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>AffiliateSettingsPage});var r=t(30784),s=t(9885),d=t(27870),n=t(14379),i=t(11440),l=t.n(i),o=t(59872),c=t(94820),m=t(26352);let affiliate_AffiliateSettingsForm=({account:e,onSubmit:a,isLoading:t=!1,className:i=""})=>{let{t:l}=(0,d.$G)("affiliate"),{isRtl:c}=(0,n.g)(),[u,g]=(0,s.useState)(e.preferredPaymentMethod||m.sI.BANK_TRANSFER),[x,y]=(0,s.useState)({bankName:e.paymentDetails?.bankName||"",accountNumber:e.paymentDetails?.accountNumber||"",accountName:e.paymentDetails?.accountName||"",swiftCode:e.paymentDetails?.swiftCode||"",paypalEmail:e.paymentDetails?.paypalEmail||"",cryptoAddress:e.paymentDetails?.cryptoAddress||"",cryptoCurrency:e.paymentDetails?.cryptoCurrency||""}),handleInputChange=e=>{let{name:a,value:t}=e.target;y(e=>({...e,[a]:t}))};return(0,r.jsxs)("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 ${i}`,children:[r.jsx("h2",{className:"text-xl font-bold text-gray-900 dark:text-gray-100 mb-4",children:l("account.paymentDetails","Payment Details")}),(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),a({preferredPaymentMethod:u,paymentDetails:x})},children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("label",{htmlFor:"preferredPaymentMethod",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[l("account.preferredPaymentMethod","Preferred Payment Method")," *"]}),r.jsx("select",{id:"preferredPaymentMethod",value:u,onChange:e=>g(e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100",required:!0,disabled:t,children:e.program?.availablePaymentMethods.map(e=>r.jsx("option",{value:e,children:l(`payments.${e.toLowerCase()}`,e)},e))})]}),u===m.sI.BANK_TRANSFER&&(0,r.jsxs)("div",{className:"mb-6",children:[r.jsx("h3",{className:"text-md font-medium text-gray-900 dark:text-gray-100 mb-3",children:l("account.bankDetails","Bank Details")}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"bankName",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[l("account.bankName","Bank Name")," *"]}),r.jsx("input",{id:"bankName",name:"bankName",type:"text",value:x.bankName,onChange:handleInputChange,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100",required:u===m.sI.BANK_TRANSFER,disabled:t})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"accountName",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[l("account.accountName","Account Name")," *"]}),r.jsx("input",{id:"accountName",name:"accountName",type:"text",value:x.accountName,onChange:handleInputChange,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100",required:u===m.sI.BANK_TRANSFER,disabled:t})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"accountNumber",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[l("account.accountNumber","Account Number")," *"]}),r.jsx("input",{id:"accountNumber",name:"accountNumber",type:"text",value:x.accountNumber,onChange:handleInputChange,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100",required:u===m.sI.BANK_TRANSFER,disabled:t})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"swiftCode",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:l("account.swiftCode","SWIFT/BIC Code")}),r.jsx("input",{id:"swiftCode",name:"swiftCode",type:"text",value:x.swiftCode,onChange:handleInputChange,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100",disabled:t})]})]})]}),u===m.sI.PAYPAL&&(0,r.jsxs)("div",{className:"mb-6",children:[r.jsx("h3",{className:"text-md font-medium text-gray-900 dark:text-gray-100 mb-3",children:l("account.paypalDetails","PayPal Details")}),r.jsx("div",{className:"space-y-4",children:(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"paypalEmail",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[l("account.paypalEmail","PayPal Email")," *"]}),r.jsx("input",{id:"paypalEmail",name:"paypalEmail",type:"email",value:x.paypalEmail,onChange:handleInputChange,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100",required:u===m.sI.PAYPAL,disabled:t})]})})]}),u===m.sI.CRYPTO&&(0,r.jsxs)("div",{className:"mb-6",children:[r.jsx("h3",{className:"text-md font-medium text-gray-900 dark:text-gray-100 mb-3",children:l("account.cryptoDetails","Cryptocurrency Details")}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"cryptoCurrency",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[l("account.cryptoCurrency","Cryptocurrency")," *"]}),(0,r.jsxs)("select",{id:"cryptoCurrency",name:"cryptoCurrency",value:x.cryptoCurrency,onChange:e=>y({...x,cryptoCurrency:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100",required:u===m.sI.CRYPTO,disabled:t,children:[r.jsx("option",{value:"",children:"Select a cryptocurrency"}),r.jsx("option",{value:"BTC",children:"Bitcoin (BTC)"}),r.jsx("option",{value:"ETH",children:"Ethereum (ETH)"}),r.jsx("option",{value:"USDT",children:"Tether (USDT)"}),r.jsx("option",{value:"USDC",children:"USD Coin (USDC)"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"cryptoAddress",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[l("account.cryptoAddress","Wallet Address")," *"]}),r.jsx("input",{id:"cryptoAddress",name:"cryptoAddress",type:"text",value:x.cryptoAddress,onChange:handleInputChange,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100",required:u===m.sI.CRYPTO,disabled:t})]})]})]}),u===m.sI.STORE_CREDIT&&(0,r.jsxs)("div",{className:"mb-6",children:[r.jsx("h3",{className:"text-md font-medium text-gray-900 dark:text-gray-100 mb-3",children:l("account.storeCreditDetails","Store Credit Details")}),r.jsx("div",{className:"bg-gray-50 dark:bg-gray-900 p-4 rounded-lg",children:r.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:l("account.storeCreditInfo","Payments will be added to your store credit balance. No additional information is required.")})})]}),r.jsx("div",{className:"flex justify-end mt-6",children:r.jsx(o.Z,{type:"submit",isLoading:t,disabled:t,children:l("account.saveChanges","Save Changes")})})]})]})};var u=t(3619);function AffiliateSettingsPage(){let{t:e}=(0,d.$G)("affiliate"),{isRtl:a}=(0,n.g)(),{data:t,isLoading:s}=(0,u.nK)({}),i=t?.accounts?.[0],[m,{isLoading:g}]=(0,u.Gx)(),handleUpdateSettings=async e=>{if(i)try{await m({id:i.id,data:{preferredPaymentMethod:e.preferredPaymentMethod,paymentDetails:e.paymentDetails}}).unwrap(),console.log("Settings saved successfully")}catch(e){console.error("Failed to update settings:",e)}};return r.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2",children:e("account.title","Account Settings")}),r.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:e("account.description","Manage your affiliate account settings")})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[r.jsx("div",{className:"lg:col-span-1",children:r.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4",children:r.jsx(c.Z,{})})}),r.jsx("div",{className:"lg:col-span-3",children:i&&i?r.jsx(affiliate_AffiliateSettingsForm,{account:i,onSubmit:handleUpdateSettings,isLoading:g}):r.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6",children:(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-16 w-16 text-gray-400 dark:text-gray-600 mx-auto mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]}),r.jsx("h3",{className:"text-xl font-medium text-gray-900 dark:text-gray-100 mb-2",children:e("noAffiliateAccount","No Affiliate Account")}),r.jsx("p",{className:"text-gray-500 dark:text-gray-400 mb-6 max-w-md mx-auto",children:e("joinProgramFirst","Join an affiliate program to manage your account settings")}),r.jsx(l(),{href:"/affiliate/programs",children:r.jsx(o.Z,{variant:"primary",children:e("browsePrograms","Browse Programs")})})]})})})]})]})})}},42569:(e,a,t)=>{"use strict";t.r(a),t.d(a,{$$typeof:()=>n,__esModule:()=>d,default:()=>l});var r=t(95153);let s=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\affiliate\dashboard\settings\page.tsx`),{__esModule:d,$$typeof:n}=s,i=s.default,l=i}};var a=require("../../../../webpack-runtime.js");a.C(e);var __webpack_exec__=e=>a(a.s=e),t=a.X(0,[2103,2765,3619,9522,6352],()=>__webpack_exec__(18354));module.exports=t})();