#!/bin/bash

# Order Service Integration Test Script
# Tests the complete Order Service integration with API Gateway

set -e

echo "🧪 Order Service Integration Testing"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counter
TESTS_PASSED=0
TESTS_FAILED=0

# Function to run test
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_pattern="$3"
    
    echo -e "\n${BLUE}🔍 Testing: $test_name${NC}"
    echo "Command: $test_command"
    
    if response=$(eval "$test_command" 2>&1); then
        if [[ -z "$expected_pattern" ]] || echo "$response" | grep -q "$expected_pattern"; then
            echo -e "${GREEN}✅ PASS${NC}: $test_name"
            echo "Response: $response"
            ((TESTS_PASSED++))
            return 0
        else
            echo -e "${RED}❌ FAIL${NC}: $test_name - Response doesn't match expected pattern"
            echo "Expected pattern: $expected_pattern"
            echo "Actual response: $response"
            ((TESTS_FAILED++))
            return 1
        fi
    else
        echo -e "${RED}❌ FAIL${NC}: $test_name - Command failed"
        echo "Error: $response"
        ((TESTS_FAILED++))
        return 1
    fi
}

# Function to check service status
check_service_status() {
    local service_name="$1"
    local port="$2"
    
    echo -e "\n${YELLOW}📋 Checking $service_name status...${NC}"
    
    if curl -s --connect-timeout 5 "http://localhost:$port/api/health" > /dev/null; then
        echo -e "${GREEN}✅ $service_name is running on port $port${NC}"
        return 0
    else
        echo -e "${RED}❌ $service_name is not responding on port $port${NC}"
        return 1
    fi
}

echo -e "\n${YELLOW}📋 Step 1: Service Status Check${NC}"
echo "================================"

# Check if services are running
check_service_status "Order Service" "3006"
ORDER_SERVICE_STATUS=$?

check_service_status "API Gateway" "3000"
API_GATEWAY_STATUS=$?

if [ $ORDER_SERVICE_STATUS -ne 0 ]; then
    echo -e "\n${RED}❌ Order Service is not running. Please start it first:${NC}"
    echo "docker-compose up -d order-service"
    exit 1
fi

if [ $API_GATEWAY_STATUS -ne 0 ]; then
    echo -e "\n${RED}❌ API Gateway is not running. Please start it first:${NC}"
    echo "docker-compose up -d api-gateway"
    exit 1
fi

echo -e "\n${YELLOW}📋 Step 2: Order Service Direct Tests${NC}"
echo "====================================="

# Test 1: Order Service Health Check
run_test "Order Service Health Check" \
    "curl -s http://localhost:3006/api/health" \
    "status.*ok"

# Test 2: Order Service Simple Health
run_test "Order Service Simple Health" \
    "curl -s http://localhost:3006/api/health/simple" \
    "status.*ok"

echo -e "\n${YELLOW}📋 Step 3: API Gateway Integration Tests${NC}"
echo "========================================"

# Test 3: API Gateway Health (should include Order Service)
run_test "API Gateway Health Check" \
    "curl -s http://localhost:3000/api/health" \
    "orderService"

# Test 4: Order Service through API Gateway
run_test "Order Service via API Gateway" \
    "curl -s http://localhost:3000/api/orders/health" \
    "status"

echo -e "\n${YELLOW}📋 Step 4: Order Service API Tests${NC}"
echo "=================================="

# Test 5: Get Orders (should return empty array or require auth)
run_test "Get Orders Endpoint" \
    "curl -s -w '%{http_code}' http://localhost:3000/api/orders" \
    "401\|200\|\[\]"

# Test 6: Get Orders via Direct Service
run_test "Get Orders Direct Service" \
    "curl -s -w '%{http_code}' http://localhost:3006/api/orders" \
    "401\|200\|\[\]"

echo -e "\n${YELLOW}📋 Step 5: Database Connection Test${NC}"
echo "==================================="

# Test 7: Database connectivity (via health check)
run_test "Database Connection Check" \
    "curl -s http://localhost:3006/api/health | jq -r '.details.database.status'" \
    "up"

echo -e "\n${YELLOW}📋 Step 6: Service Discovery Test${NC}"
echo "================================="

# Test 8: Check if Order Service is registered in API Gateway
run_test "Service Discovery Check" \
    "curl -s http://localhost:3000/api/health | jq -r '.orderService.status'" \
    "up"

echo -e "\n${YELLOW}📋 Test Summary${NC}"
echo "==============="

TOTAL_TESTS=$((TESTS_PASSED + TESTS_FAILED))

echo -e "Total Tests: $TOTAL_TESTS"
echo -e "${GREEN}Passed: $TESTS_PASSED${NC}"
echo -e "${RED}Failed: $TESTS_FAILED${NC}"

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "\n${GREEN}🎉 All tests passed! Order Service integration is working correctly.${NC}"
    
    echo -e "\n${BLUE}📋 Next Steps:${NC}"
    echo "1. ✅ Order Service is running and healthy"
    echo "2. ✅ API Gateway integration is working"
    echo "3. ✅ Database connection is established"
    echo "4. 🚀 Ready for end-to-end order flow testing"
    echo "5. 🚀 Ready to implement next service in roadmap"
    
    exit 0
else
    echo -e "\n${RED}❌ Some tests failed. Please check the issues above.${NC}"
    
    echo -e "\n${YELLOW}🔧 Troubleshooting Tips:${NC}"
    echo "1. Check if all services are running: docker-compose ps"
    echo "2. Check service logs: docker-compose logs order-service"
    echo "3. Verify database exists: docker exec social-commerce-postgres psql -U postgres -c '\l'"
    echo "4. Restart services if needed: docker-compose restart order-service api-gateway"
    
    exit 1
fi
