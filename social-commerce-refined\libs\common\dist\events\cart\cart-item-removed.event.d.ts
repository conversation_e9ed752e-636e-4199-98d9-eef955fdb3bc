import { BaseEvent } from '../base-event.interface';
export declare class CartItemRemovedEvent implements BaseEvent<CartItemRemovedPayload> {
    id: string;
    type: string;
    version: string;
    timestamp: string;
    producer: string;
    payload: CartItemRemovedPayload;
    constructor(payload: CartItemRemovedPayload);
}
export interface CartItemRemovedPayload {
    cartId: string;
    userId: string;
    productId: string;
    removedAt: string;
}
