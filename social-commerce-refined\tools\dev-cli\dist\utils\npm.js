"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.npmRun = npmRun;
exports.installDependencies = installDependencies;
exports.buildService = buildService;
exports.startService = startService;
exports.testService = testService;
const childProcess = require("child_process");
const chalk = require("chalk");
const paths_1 = require("./paths");
function npmRun(service, command, args = []) {
    return new Promise((resolve, reject) => {
        var _a, _b;
        const serviceDir = (0, paths_1.getServiceDir)(service);
        const npmCommand = `npm ${command} ${args.join(' ')}`;
        console.log(chalk.blue(`Running in ${service}-service: ${npmCommand}`));
        const process = childProcess.exec(npmCommand, { cwd: serviceDir });
        (_a = process.stdout) === null || _a === void 0 ? void 0 : _a.on('data', (data) => {
            console.log(data.toString().trim());
        });
        (_b = process.stderr) === null || _b === void 0 ? void 0 : _b.on('data', (data) => {
            console.error(chalk.red(data.toString().trim()));
        });
        process.on('close', (code) => {
            if (code === 0) {
                resolve();
            }
            else {
                reject(new Error(`npm command failed with code ${code}`));
            }
        });
    });
}
function installDependencies(service) {
    return npmRun(service, 'install');
}
function buildService(service) {
    return npmRun(service, 'run', ['build']);
}
function startService(service) {
    return npmRun(service, 'run', ['start:dev']);
}
function testService(service, watch = false) {
    const command = watch ? 'run test:watch' : 'test';
    return npmRun(service, command);
}
//# sourceMappingURL=npm.js.map