import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Inject,
  Logger,
} from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { StoreRepository } from '../repositories/store.repository';
import { Store, StoreStatus } from '../entities/store.entity';
import { CreateStoreDto, UpdateStoreDto } from '../dto';
import { firstValueFrom, timeout } from 'rxjs';

@Injectable()
export class StoreService {
  private readonly logger = new Logger(StoreService.name);
  private readonly MAX_STORES_PER_USER = 5; // Business rule: Maximum stores per user

  constructor(
    private readonly storeRepository: StoreRepository,
    @Inject('USER_SERVICE') private readonly userService: ClientProxy, // Re-enabled for production
  ) {}

  /**
   * Create a new store
   */
  async create(createStoreDto: CreateStoreDto, ownerId: string): Promise<Store> {
    this.logger.log(`Creating store for user ${ownerId}`);

    // Verify user exists
    await this.verifyUserExists(ownerId);

    // Business Rule: Check maximum stores per user limit
    const userStoreCount = await this.storeRepository.countByOwnerId(ownerId);
    if (userStoreCount >= this.MAX_STORES_PER_USER) {
      this.logger.warn(`User ${ownerId} attempted to create store but has reached limit (${userStoreCount}/${this.MAX_STORES_PER_USER})`);
      throw new BadRequestException(
        `Maximum ${this.MAX_STORES_PER_USER} stores allowed per user. You currently have ${userStoreCount} stores.`
      );
    }

    // Check if user already has a store with the same name
    const existingStore = await this.storeRepository.findOne({
      where: { name: createStoreDto.name, ownerId },
    });

    if (existingStore) {
      throw new BadRequestException('You already have a store with this name');
    }

    const store = this.storeRepository.create({
      ...createStoreDto,
      ownerId,
      status: StoreStatus.ACTIVE,
    });

    const savedStore = await this.storeRepository.save(store);
    this.logger.log(`Store created successfully: ${savedStore.id}`);

    return savedStore;
  }

  /**
   * Find all stores (optionally filtered by owner)
   */
  async findAll(ownerId?: string, page: number = 1, limit: number = 10): Promise<{
    stores: Store[];
    total: number;
    page: number;
    limit: number;
  }> {
    const { stores, total } = await this.storeRepository.findWithPagination(
      page,
      limit,
      ownerId,
    );

    return { stores, total, page, limit };
  }

  /**
   * Find stores by owner ID
   */
  async findByOwnerId(ownerId: string): Promise<Store[]> {
    await this.verifyUserExists(ownerId);
    return this.storeRepository.findByOwnerId(ownerId);
  }

  /**
   * Get store limits and usage information for a user
   */
  async getStoreLimits(ownerId: string): Promise<{
    maxStores: number;
    currentStores: number;
    remainingStores: number;
    canCreateMore: boolean;
  }> {
    const currentStores = await this.storeRepository.countByOwnerId(ownerId);
    const remainingStores = Math.max(0, this.MAX_STORES_PER_USER - currentStores);

    return {
      maxStores: this.MAX_STORES_PER_USER,
      currentStores,
      remainingStores,
      canCreateMore: currentStores < this.MAX_STORES_PER_USER,
    };
  }

  /**
   * Find one store by ID
   */
  async findOne(id: string, requestingUserId?: string): Promise<Store> {
    const store = await this.storeRepository.findOne({ where: { id } });

    if (!store) {
      throw new NotFoundException('Store not found');
    }

    // If requesting user is specified, populate owner info
    if (requestingUserId) {
      try {
        const owner = await this.getUserInfo(store.ownerId);
        store.owner = owner;
      } catch (error) {
        this.logger.warn(`Failed to fetch owner info for store ${id}: ${error.message}`);
      }
    }

    return store;
  }

  /**
   * Update a store
   */
  async update(
    id: string,
    updateStoreDto: UpdateStoreDto,
    requestingUserId: string,
  ): Promise<Store> {
    const store = await this.storeRepository.findByIdAndOwnerId(id, requestingUserId);

    if (!store) {
      throw new NotFoundException('Store not found or you do not have permission to update it');
    }

    // If updating name, check for duplicates
    if (updateStoreDto.name && updateStoreDto.name !== store.name) {
      const existingStore = await this.storeRepository.findOne({
        where: { name: updateStoreDto.name, ownerId: requestingUserId },
      });

      if (existingStore && existingStore.id !== id) {
        throw new BadRequestException('You already have a store with this name');
      }
    }

    Object.assign(store, updateStoreDto);
    const updatedStore = await this.storeRepository.save(store);

    this.logger.log(`Store updated successfully: ${updatedStore.id}`);
    return updatedStore;
  }

  /**
   * Remove a store
   */
  async remove(id: string, requestingUserId: string): Promise<void> {
    const store = await this.storeRepository.findByIdAndOwnerId(id, requestingUserId);

    if (!store) {
      throw new NotFoundException('Store not found or you do not have permission to delete it');
    }

    await this.storeRepository.remove(store);
    this.logger.log(`Store deleted successfully: ${id}`);
  }

  /**
   * Verify user exists via User Service
   */
  private async verifyUserExists(userId: string): Promise<void> {
    this.logger.log(`Verifying user exists: ${userId}`);

    try {
      const response = await firstValueFrom(
        this.userService.send('find_user_by_id', userId).pipe(timeout(5000))
      );

      if (!response || !response.id) {
        this.logger.warn(`User verification failed: User ${userId} not found`);
        throw new NotFoundException('User not found');
      }

      this.logger.log(`User verification successful: ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to verify user ${userId}: ${error.message}`);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Failed to verify user - User Service unavailable');
    }
  }

  /**
   * Get user information from User Service
   */
  private async getUserInfo(userId: string): Promise<{
    id: string;
    email: string;
    firstName?: string;
    lastName?: string;
  }> {
    this.logger.log(`Getting user info: ${userId}`);

    try {
      const response = await firstValueFrom(
        this.userService.send('find_user_by_id', userId).pipe(timeout(5000))
      );

      if (!response || !response.id) {
        throw new NotFoundException('User not found');
      }

      return {
        id: response.id,
        email: response.email,
        firstName: response.firstName,
        lastName: response.lastName,
      };
    } catch (error) {
      this.logger.error(`Failed to get user info ${userId}: ${error.message}`);
      throw new BadRequestException('Failed to get user information');
    }
  }
}
