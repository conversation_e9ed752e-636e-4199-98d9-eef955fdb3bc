import { Controller, Get, Post, Put, Delete, Patch, Param, Body, Query, Headers, Req, Res, HttpStatus, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { RoutingService } from '../services/routing.service';
import { Request, Response } from 'express';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

@ApiTags('products')
@Controller('products')
export class ProductController {
  private readonly logger = new Logger(ProductController.name);
  private readonly SERVICE_NAME = 'product';

  constructor(private readonly routingService: RoutingService) {}

  @Get()
  @ApiOperation({ summary: 'Get all products' })
  @ApiResponse({ status: 200, description: 'Return all products' })
  getAllProducts(@Req() req: Request, @Headers() headers: any, @Query() query: any): Observable<any> {
    this.logger.log('Forwarding GET /products request');
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, '/products', 'GET', null, headers)
      .pipe(map((response) => response.data));
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a product by ID' })
  @ApiResponse({ status: 200, description: 'Return the product' })
  @ApiResponse({ status: 404, description: 'Product not found' })
  getProductById(@Param('id') id: string, @Headers() headers: any): Observable<any> {
    this.logger.log(`Forwarding GET /products/${id} request`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, `/products/${id}`, 'GET', null, headers)
      .pipe(map((response) => response.data));
  }

  @Post()
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new product' })
  @ApiResponse({ status: 201, description: 'Product created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  createProduct(@Body() body: any, @Headers() headers: any): Observable<any> {
    this.logger.log('Forwarding POST /products request');
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, '/products', 'POST', body, headers)
      .pipe(map((response) => response.data));
  }

  @Put(':id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update a product' })
  @ApiResponse({ status: 200, description: 'Product updated successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'Product not found' })
  updateProduct(@Param('id') id: string, @Body() body: any, @Headers() headers: any): Observable<any> {
    this.logger.log(`Forwarding PUT /products/${id} request`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, `/products/${id}`, 'PUT', body, headers)
      .pipe(map((response) => response.data));
  }

  @Delete(':id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete a product' })
  @ApiResponse({ status: 200, description: 'Product deleted successfully' })
  @ApiResponse({ status: 404, description: 'Product not found' })
  deleteProduct(@Param('id') id: string, @Headers() headers: any): Observable<any> {
    this.logger.log(`Forwarding DELETE /products/${id} request`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, `/products/${id}`, 'DELETE', null, headers)
      .pipe(map((response) => response.data));
  }

  @Get('store/:storeId')
  @ApiOperation({ summary: 'Get products by store ID' })
  @ApiResponse({ status: 200, description: 'Return the products' })
  @ApiResponse({ status: 404, description: 'Store not found' })
  getProductsByStoreId(@Param('storeId') storeId: string, @Headers() headers: any): Observable<any> {
    this.logger.log(`Forwarding GET /products/store/${storeId} request`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, `/products/store/${storeId}`, 'GET', null, headers)
      .pipe(map((response) => response.data));
  }

  // Catch-all route for other product service endpoints
  @Get('*')
  @ApiOperation({ summary: 'Forward any GET request to the product service' })
  forwardGetRequest(@Req() req: Request, @Headers() headers: any): Observable<any> {
    const path = req.url;
    this.logger.log(`Forwarding GET ${path} request to product service`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, path, 'GET', null, headers)
      .pipe(map((response) => response.data));
  }

  @Post('*')
  @ApiOperation({ summary: 'Forward any POST request to the product service' })
  forwardPostRequest(@Req() req: Request, @Body() body: any, @Headers() headers: any): Observable<any> {
    const path = req.url;
    this.logger.log(`Forwarding POST ${path} request to product service`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, path, 'POST', body, headers)
      .pipe(map((response) => response.data));
  }

  @Put('*')
  @ApiOperation({ summary: 'Forward any PUT request to the product service' })
  forwardPutRequest(@Req() req: Request, @Body() body: any, @Headers() headers: any): Observable<any> {
    const path = req.url;
    this.logger.log(`Forwarding PUT ${path} request to product service`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, path, 'PUT', body, headers)
      .pipe(map((response) => response.data));
  }

  @Delete('*')
  @ApiOperation({ summary: 'Forward any DELETE request to the product service' })
  forwardDeleteRequest(@Req() req: Request, @Headers() headers: any): Observable<any> {
    const path = req.url;
    this.logger.log(`Forwarding DELETE ${path} request to product service`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, path, 'DELETE', null, headers)
      .pipe(map((response) => response.data));
  }

  @Patch('*')
  @ApiOperation({ summary: 'Forward any PATCH request to the product service' })
  forwardPatchRequest(@Req() req: Request, @Body() body: any, @Headers() headers: any): Observable<any> {
    const path = req.url;
    this.logger.log(`Forwarding PATCH ${path} request to product service`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, path, 'PATCH', body, headers)
      .pipe(map((response) => response.data));
  }
}
