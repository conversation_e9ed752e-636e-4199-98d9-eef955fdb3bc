import { OnModuleInit } from '@nestjs/common';
import { DiscoveryService, MetadataScanner, Reflector } from '@nestjs/core';
import { RabbitMQService } from './rabbitmq.service';
export declare class EventSubscriberExplorerService implements OnModuleInit {
    private readonly discoveryService;
    private readonly metadataScanner;
    private readonly reflector;
    private readonly rabbitMQService;
    private readonly logger;
    private readonly exchange;
    constructor(discoveryService: DiscoveryService, metadataScanner: MetadataScanner, reflector: Reflector, rabbitMQService: RabbitMQService);
    onModuleInit(): Promise<void>;
    explore(): Promise<void>;
    private exploreWrapper;
    private exploreMethod;
    private registerEventSubscriber;
}
