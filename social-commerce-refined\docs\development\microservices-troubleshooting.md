# Microservices Troubleshooting Guide

## Common Issues and Solutions

### 1. Dependency Injection Errors

#### Symptom
```
ERROR [ExceptionHandler] Nest can't resolve dependencies of the ServiceName (Dependency1, ?). 
Please make sure that the argument "SERVICE_NAME" at index [X] is available in the ModuleName context.
```

#### Root Causes & Solutions

**A. Missing ClientsModule Import**
```typescript
// ❌ Problem: Service trying to inject client without ClientsModule
@Injectable()
export class MyService {
  constructor(@Inject('OTHER_SERVICE') private client: ClientProxy) {}
}

// ✅ Solution: Import ClientsModule in module
@Module({
  imports: [
    ClientsModule.registerAsync([
      {
        name: 'OTHER_SERVICE',
        // ... configuration
      },
    ]),
  ],
})
```

**B. Missing Import Statement**
```typescript
// ❌ Problem: Missing import
// import { ClientsModule, Transport } from '@nestjs/microservices'; // Commented out

// ✅ Solution: Add proper imports
import { ClientsModule, Transport } from '@nestjs/microservices';
```

**C. Service Not Implemented**
```typescript
// ❌ Problem: Trying to inject non-existent service
@Inject('NOTIFICATION_SERVICE') // Service doesn't exist

// ✅ Solution: Implement the missing service first
// 1. Create the service
// 2. Add to docker-compose.yml
// 3. Configure ClientsModule
```

### 2. Docker Build Issues

#### Missing Dependencies
```bash
# Symptom
ERROR in ./src/module.ts:X:Y
TS2307: Cannot find module '@nestjs/terminus'

# Solution
# Add missing dependency to package.json
{
  "dependencies": {
    "@nestjs/terminus": "^10.0.0"
  }
}
```

#### Incorrect Method Names
```bash
# Symptom
TS2551: Property 'createTransporter' does not exist

# Solution
# Check API documentation for correct method names
nodemailer.createTransport() // ✅ Correct
nodemailer.createTransporter() // ❌ Incorrect
```

### 3. Container Startup Issues

#### Container Not Starting
```bash
# Diagnosis
docker-compose ps service-name
# Shows: Starting for long time or not listed

# Check logs
docker-compose logs service-name

# Common causes:
# 1. Application hanging on microservice connection
# 2. Missing environment variables
# 3. Port conflicts
# 4. Database connection issues
```

#### Microservice Connection Hanging
```typescript
// ❌ Problem: Blocking connection without error handling
await app.startAllMicroservices();

// ✅ Solution: Add error handling and timeouts
try {
  const microservice = app.connectMicroservice(config);
  await Promise.race([
    app.startAllMicroservices(),
    new Promise((_, reject) => 
      setTimeout(() => reject(new Error('Timeout')), 10000)
    )
  ]);
} catch (error) {
  logger.warn('Microservice connection failed, continuing with HTTP only');
}
```

### 4. Service Communication Issues

#### RabbitMQ Connection Failures
```bash
# Symptoms
- Services start but can't communicate
- Connection timeout errors
- Queue not found errors

# Diagnosis
docker-compose logs rabbitmq
docker-compose exec rabbitmq rabbitmqctl list_queues

# Solutions
# 1. Check RabbitMQ is healthy
docker-compose ps rabbitmq

# 2. Verify queue configuration
{
  queue: 'correct_queue_name',
  queueOptions: { durable: true }
}

# 3. Check network connectivity
docker-compose exec service-name ping rabbitmq
```

#### Service Discovery Issues
```typescript
// ❌ Problem: Hardcoded localhost URLs
urls: ['amqp://admin:admin@localhost:5672']

// ✅ Solution: Use Docker service names
urls: ['amqp://admin:admin@rabbitmq:5672']
```

### 5. Database Connection Issues

#### Connection Refused
```bash
# Symptom
ECONNREFUSED 127.0.0.1:5432

# Solution
# Use Docker service name instead of localhost
host: 'postgres' // ✅ Docker service name
host: 'localhost' // ❌ Won't work in containers
```

#### Database Not Ready
```yaml
# Solution: Add proper health checks and dependencies
depends_on:
  postgres:
    condition: service_healthy
```

## Debugging Workflow

### 1. Systematic Diagnosis
```bash
# Step 1: Check container status
docker-compose ps

# Step 2: Check logs for each service
docker-compose logs service-name

# Step 3: Check network connectivity
docker-compose exec service-name ping other-service

# Step 4: Check service health
curl http://localhost:PORT/health
```

### 2. Isolation Testing
```bash
# Test services individually
docker run --rm service-image

# Test with minimal dependencies
docker-compose up -d postgres rabbitmq
docker-compose up service-name
```

### 3. Configuration Verification
```bash
# Check environment variables
docker-compose exec service-name env | grep SERVICE

# Check configuration loading
docker-compose exec service-name cat /app/dist/main.js | grep config
```

## Prevention Strategies

### 1. Systematic Service Creation
```bash
# Always follow this order:
1. Create service structure
2. Implement core functionality
3. Add Docker configuration
4. Test individually
5. Add to docker-compose
6. Test integration
```

### 2. Dependency Management
```typescript
// Always check dependencies exist before injection
@Module({
  imports: [
    // Verify all imported modules exist
    ClientsModule.registerAsync([...]),
  ],
})
```

### 3. Error Handling
```typescript
// Add comprehensive error handling
try {
  await this.client.send('pattern', data).toPromise();
} catch (error) {
  this.logger.error(`Service communication failed: ${error.message}`);
  throw new ServiceUnavailableException('External service unavailable');
}
```

### 4. Health Checks
```typescript
// Implement proper health checks
@Get('health')
async checkHealth() {
  return {
    status: 'ok',
    timestamp: new Date().toISOString(),
    services: {
      database: await this.checkDatabase(),
      rabbitmq: await this.checkRabbitMQ(),
    }
  };
}
```

## Quick Reference Commands

```bash
# Build specific service
docker-compose build service-name

# Start with logs
docker-compose up service-name

# Check service health
curl http://localhost:PORT/health

# View real-time logs
docker-compose logs -f service-name

# Restart service
docker-compose restart service-name

# Clean rebuild
docker-compose build --no-cache service-name

# Check network
docker network ls
docker network inspect network-name
```

## Related Documentation
- [Microservices Dependency Resolution](./microservices-dependency-resolution.md)
- [Docker Development Workflow](./docker-development.md)
- [Service Architecture Guidelines](../architecture/service-guidelines.md)
