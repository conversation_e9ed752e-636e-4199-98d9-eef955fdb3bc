"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CartClearedEvent = void 0;
class CartClearedEvent {
    constructor(payload) {
        this.type = 'cart.cleared';
        this.version = '1.0';
        this.producer = 'cart-service';
        this.id = `${payload.cartId}-${Date.now()}`;
        this.timestamp = new Date().toISOString();
        this.payload = payload;
    }
}
exports.CartClearedEvent = CartClearedEvent;
//# sourceMappingURL=cart-cleared.event.js.map