{"name": "api-gateway", "version": "1.0.0", "description": "API Gateway for Social Commerce Platform", "main": "dist/main.js", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "keywords": ["microservice", "api-gateway", "<PERSON><PERSON><PERSON>"], "author": "", "license": "MIT", "dependencies": {"@nestjs/axios": "^4.0.0", "@nestjs/cache-manager": "^2.2.2", "@nestjs/common": "^11.1.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.1", "@nestjs/jwt": "^11.0.0", "@nestjs/microservices": "^11.1.1", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.1", "@nestjs/swagger": "^11.2.0", "@nestjs/terminus": "^11.0.0", "axios": "^1.9.0", "cache-manager": "^5.7.6", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "nest-winston": "^1.10.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "uuid": "^11.0.3", "winston": "^3.17.0"}, "devDependencies": {"@nestjs/cli": "^11.0.7", "@nestjs/testing": "^11.1.1", "@types/cookie-parser": "^1.4.8", "@types/express": "^5.0.2", "@types/jest": "^29.5.14", "@types/node": "^22.15.21", "@types/passport-jwt": "^4.0.1", "@types/uuid": "^10.0.0", "jest": "^29.7.0", "rimraf": "^6.0.1", "supertest": "^7.1.1", "ts-jest": "^29.3.4", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}