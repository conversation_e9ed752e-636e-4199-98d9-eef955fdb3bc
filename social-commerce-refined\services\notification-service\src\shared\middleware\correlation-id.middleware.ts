import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';

/**
 * Middleware to add a correlation ID to each request
 * This helps with tracing requests across multiple services
 */
@Injectable()
export class CorrelationIdMiddleware implements NestMiddleware {
  private readonly logger = new Logger(CorrelationIdMiddleware.name);

  /**
   * Add a correlation ID to the request and response headers
   * @param req The request object
   * @param res The response object
   * @param next The next function
   */
  use(req: Request, res: Response, next: NextFunction) {
    // Get the correlation ID from the request header or generate a new one
    const correlationId = req.headers['x-correlation-id'] || uuidv4();
    
    // Add the correlation ID to the request headers
    req.headers['x-correlation-id'] = correlationId;
    
    // Add the correlation ID to the response headers
    res.setHeader('x-correlation-id', correlationId);
    
    // Log the correlation ID
    this.logger.log(`Request ${req.method} ${req.url} assigned correlation ID: ${correlationId}`);
    
    // Continue with the request
    next();
  }
}
