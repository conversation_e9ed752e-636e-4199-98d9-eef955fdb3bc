import { BaseEvent } from '../base-event.interface';

/**
 * Event emitted when a new product is created
 */
export class ProductCreatedEvent implements BaseEvent<ProductCreatedPayload> {
  id: string;
  type: string = 'product.created';
  version: string = '1.0';
  timestamp: string;
  producer: string = 'product-service';
  payload: ProductCreatedPayload;

  constructor(payload: ProductCreatedPayload) {
    this.id = payload.id;
    this.timestamp = new Date().toISOString();
    this.payload = payload;
  }
}

/**
 * Payload for ProductCreatedEvent
 */
export interface ProductCreatedPayload {
  /**
   * Product ID
   */
  id: string;

  /**
   * Store ID
   */
  storeId: string;

  /**
   * Product name
   */
  name: string;

  /**
   * Product description
   */
  description: string;

  /**
   * Product price
   */
  price: number;

  /**
   * Product inventory count
   */
  inventory: number;

  /**
   * Product categories
   */
  categories: string[];

  /**
   * Product creation timestamp
   */
  createdAt: string;
}
