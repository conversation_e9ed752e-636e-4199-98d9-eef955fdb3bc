import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Stack,
  Text,
  Link,
  FormErrorMessage,
  useColorModeValue,
  Alert,
  AlertIcon,
  AlertDescription,
  useToast,
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import NextLink from 'next/link';
import { useRouter } from 'next/router';
import { useAuth } from '@/context/AuthContext';

interface LoginFormData {
  email: string;
  password: string;
}

const Login = () => {
  const { login, isAuthenticated } = useAuth();
  const router = useRouter();
  const toast = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<any>(null);

  const {
    handleSubmit,
    register,
    formState: { errors },
  } = useForm<LoginFormData>();

  // Check if user is already logged in
  useEffect(() => {
    if (isAuthenticated) {
      router.push('/dashboard');
    }
  }, [isAuthenticated, router]);

  // Check if user just registered
  useEffect(() => {
    if (router.query.registered === 'true') {
      toast({
        title: 'Registration successful',
        description: 'Please check your email to verify your account.',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
    }
  }, [router.query, toast]);

  // Load debug info from localStorage
  useEffect(() => {
    const storedDebug = localStorage.getItem('loginDebug');
    if (storedDebug) {
      try {
        const parsed = JSON.parse(storedDebug);
        setDebugInfo(parsed);
        console.log('Loaded debug info from localStorage:', parsed);
      } catch (e) {
        console.error('Failed to parse debug info:', e);
      }
    }
  }, []);

  const onSubmit = async (data: LoginFormData) => {

    setIsLoading(true);
    setError(null);

    try {
      console.log('Login page: Calling login function');
      localStorage.setItem('loginDebug', JSON.stringify({
        timestamp: new Date().toISOString(),
        email: data.email,
        step: 'calling_login_function'
      }));

      await login(data.email, data.password);

      console.log('Login page: Login function completed successfully');
      localStorage.setItem('loginDebug', JSON.stringify({
        timestamp: new Date().toISOString(),
        email: data.email,
        step: 'login_completed_successfully'
      }));
      // Redirect is handled in the auth context
    } catch (err: any) {
      console.error('Login page: Login failed', err);

      // Store detailed error info
      const errorInfo = {
        timestamp: new Date().toISOString(),
        email: data.email,
        step: 'login_failed',
        error: {
          message: err.message,
          stack: err.stack,
          name: err.name,
          response: err.response?.data,
          status: err.response?.status
        }
      };

      localStorage.setItem('loginDebug', JSON.stringify(errorInfo));
      console.error('Detailed error stored in localStorage:', errorInfo);

      setError(err.message || 'Failed to login. Please check your credentials.');

      // Prevent any potential page refresh by stopping event propagation
      console.log('Error handled, preventing any redirects');
      return false;
    } finally {
      setIsLoading(false);
      console.log('Login page: Loading state set to false');
    }
  };

  return (
    <Flex
      minH={'100vh'}
      align={'center'}
      justify={'center'}
      bg={useColorModeValue('gray.50', 'gray.800')}
    >
      <Stack spacing={8} mx={'auto'} maxW={'lg'} py={12} px={6}>
        <Stack align={'center'}>
          <Heading fontSize={'4xl'}>Sign in to your account</Heading>
          <Text fontSize={'lg'} color={'gray.600'}>
            to enjoy all of our cool features ✌️
          </Text>
        </Stack>

        <Box
          rounded={'lg'}
          bg={useColorModeValue('white', 'gray.700')}
          boxShadow={'lg'}
          p={8}
          w={{ base: 'full', md: '400px' }}
        >
          {error && (
            <Alert status="error" mb={4} borderRadius="md">
              <AlertIcon />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {debugInfo && (
            <Alert status="info" mb={4} borderRadius="md">
              <AlertIcon />
              <Box>
                <AlertDescription>
                  <strong>Debug Info:</strong> {debugInfo.step} at {new Date(debugInfo.timestamp).toLocaleTimeString()}
                  {debugInfo.error && (
                    <Box mt={2}>
                      <strong>Error:</strong> {debugInfo.error.message}
                      <br />
                      <strong>Status:</strong> {debugInfo.error.status}
                    </Box>
                  )}
                  {debugInfo.passwordDebug && (
                    <Box mt={2}>
                      <strong>Password Debug:</strong>
                      <br />
                      Length: {debugInfo.passwordDebug.length},
                      First: "{debugInfo.passwordDebug.firstChar}",
                      Last: "{debugInfo.passwordDebug.lastChar}"
                      <br />
                      Uppercase: {debugInfo.passwordDebug.hasUppercase ? 'Yes' : 'No'},
                      Lowercase: {debugInfo.passwordDebug.hasLowercase ? 'Yes' : 'No'},
                      Numbers: {debugInfo.passwordDebug.hasNumbers ? 'Yes' : 'No'},
                      Special: {debugInfo.passwordDebug.hasSpecialChars ? 'Yes' : 'No'}
                    </Box>
                  )}
                  <Button
                    size="sm"
                    mt={2}
                    onClick={() => {
                      localStorage.removeItem('loginDebug');
                      setDebugInfo(null);
                    }}
                  >
                    Clear Debug Info
                  </Button>
                </AlertDescription>
              </Box>
            </Alert>
          )}

          <form onSubmit={(e) => {
            e.preventDefault(); // Prevent default form submission
            console.log('Form onSubmit triggered');
            localStorage.setItem('formDebug', JSON.stringify({
              timestamp: new Date().toISOString(),
              step: 'form_onSubmit_triggered'
            }));
            return handleSubmit(onSubmit)(e);
          }}>
            <Stack spacing={4}>
              <FormControl id="email" isInvalid={!!errors.email}>
                <FormLabel>Email address</FormLabel>
                <Input
                  type="email"
                  {...register('email', {
                    required: 'Email is required',
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: 'Invalid email address',
                    },
                  })}
                />
                <FormErrorMessage>{errors.email?.message}</FormErrorMessage>
              </FormControl>

              <FormControl id="password" isInvalid={!!errors.password}>
                <FormLabel>Password</FormLabel>
                <Input
                  type="password"
                  {...register('password', {
                    required: 'Password is required',
                    minLength: {
                      value: 6,
                      message: 'Password must be at least 6 characters',
                    },
                  })}
                />
                <FormErrorMessage>{errors.password?.message}</FormErrorMessage>
              </FormControl>

              <Stack spacing={10}>
                <Stack
                  direction={{ base: 'column', sm: 'row' }}
                  align={'start'}
                  justify={'space-between'}
                >
                  <Link as={NextLink} href="/forgot-password" color={'brand.500'}>
                    Forgot password?
                  </Link>
                </Stack>

                <Button
                  bg={'brand.500'}
                  color={'white'}
                  _hover={{
                    bg: 'brand.600',
                  }}
                  type="submit"
                  isLoading={isLoading}
                  onClick={() => {
                    console.log('Button clicked');
                    localStorage.setItem('buttonDebug', JSON.stringify({
                      timestamp: new Date().toISOString(),
                      step: 'button_clicked'
                    }));
                  }}
                >
                  Sign in
                </Button>
              </Stack>

              <Stack pt={6}>
                <Text align={'center'}>
                  Don't have an account?{' '}
                  <Link as={NextLink} href="/register" color={'brand.500'}>
                    Register
                  </Link>
                </Text>
              </Stack>
            </Stack>
          </form>
        </Box>
      </Stack>
    </Flex>
  );
};

export default Login;
