"use strict";exports.id=1070,exports.ids=[1070],exports.modules={75444:(t,e,r)=>{r.d(e,{Ls:()=>n,P7:()=>a,Qc:()=>u,U9:()=>C,Uu:()=>s,YD:()=>m,_G:()=>q,iD:()=>T});var o=r(86372);let p=o.g.injectEndpoints({endpoints:t=>({uploadImportFile:t.mutation({query:t=>({url:"/imports/upload",method:"POST",body:t})}),getImportPreview:t.query({query:({fileUrl:t,fileType:e,entityType:r,options:o})=>({url:"/imports/preview",method:"POST",body:{fileUrl:t,fileType:e,entityType:r,options:o}})}),createImport:t.mutation({query:t=>({url:"/imports",method:"POST",body:t}),invalidatesTags:["ImportHistory"]}),getImportStatus:t.query({query:t=>`/imports/${t}`,providesTags:(t,e,r)=>[{type:"Import",id:r}]}),cancelImport:t.mutation({query:t=>({url:`/imports/${t}/cancel`,method:"POST"}),invalidatesTags:(t,e,r)=>[{type:"Import",id:r},"ImportHistory"]}),getImportTemplates:t.query({query:({entityType:t})=>`/imports/templates?entityType=${t}`,providesTags:["ImportTemplates"]}),createImportTemplate:t.mutation({query:t=>({url:"/imports/templates",method:"POST",body:t}),invalidatesTags:["ImportTemplates"]}),updateImportTemplate:t.mutation({query:({id:t,template:e})=>({url:`/imports/templates/${t}`,method:"PATCH",body:e}),invalidatesTags:(t,e,{id:r})=>[{type:"ImportTemplates",id:r},"ImportTemplates"]}),deleteImportTemplate:t.mutation({query:t=>({url:`/imports/templates/${t}`,method:"DELETE"}),invalidatesTags:["ImportTemplates"]}),getImportHistory:t.query({query:({entityType:t,limit:e=10,page:r=1})=>{let o=`/imports/history?limit=${e}&page=${r}`;return t&&(o+=`&entityType=${t}`),o},providesTags:["ImportHistory"]}),getImportErrors:t.query({query:({importId:t,limit:e=100,page:r=1})=>`/imports/${t}/errors?limit=${e}&page=${r}`}),getProductImportFields:t.query({query:()=>"/imports/fields/product"}),downloadImportTemplate:t.query({query:({entityType:t,fileType:e})=>`/imports/templates/download?entityType=${t}&fileType=${e}`})})}),{useUploadImportFileMutation:m,useGetImportPreviewQuery:i,useLazyGetImportPreviewQuery:s,useCreateImportMutation:a,useGetImportStatusQuery:u,useCancelImportMutation:T,useGetImportTemplatesQuery:l,useCreateImportTemplateMutation:d,useUpdateImportTemplateMutation:y,useDeleteImportTemplateMutation:I,useGetImportHistoryQuery:E,useGetImportErrorsQuery:n,useGetProductImportFieldsQuery:P,useLazyGetProductImportFieldsQuery:C,useDownloadImportTemplateQuery:O,useLazyDownloadImportTemplateQuery:q}=p},93003:(t,e,r)=>{var o,p,m;r.d(e,{TS:()=>m,X2:()=>p,d2:()=>o}),function(t){t.PENDING="PENDING",t.VALIDATING="VALIDATING",t.MAPPING="MAPPING",t.PROCESSING="PROCESSING",t.COMPLETED="COMPLETED",t.FAILED="FAILED",t.PARTIALLY_COMPLETED="PARTIALLY_COMPLETED",t.CANCELLED="CANCELLED"}(o||(o={})),function(t){t.CSV="CSV",t.EXCEL="EXCEL",t.JSON="JSON"}(p||(p={})),function(t){t.PRODUCT="PRODUCT",t.CATEGORY="CATEGORY",t.CUSTOMER="CUSTOMER",t.ORDER="ORDER"}(m||(m={}))}};