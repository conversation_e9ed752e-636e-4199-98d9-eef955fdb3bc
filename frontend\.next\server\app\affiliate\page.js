(()=>{var e={};e.id=2862,e.ids=[2862],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},61092:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>m,routeModule:()=>d,tree:()=>l});var n=r(67096),s=r(16132),a=r(37284),o=r.n(a),i=r(32564),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);r.d(t,c);let l=["",{children:["affiliate",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,94163,23)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\affiliate\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9291,23)),"next/dist/client/components/not-found-error"]}],m=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\affiliate\\page.tsx"],u="/affiliate/page",x={require:r,loadChunk:()=>Promise.resolve()},d=new n.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/affiliate/page",pathname:"/affiliate",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},35303:()=>{},94163:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError: \n  \x1b[38;2;255;30;30m\xd7\x1b[0m Expected a semicolon\n    ╭─[\x1b[38;2;92;157;255;1;4mC:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\affiliate\\page.tsx\x1b[0m:72:1]\n \x1b[2m72\x1b[0m │         earnings: 146.78,\n \x1b[2m73\x1b[0m │       },\n \x1b[2m74\x1b[0m │     ],\n \x1b[2m75\x1b[0m │   });\n    \xb7 \x1b[38;2;246;87;248m   ─\x1b[0m\n \x1b[2m76\x1b[0m │ \n \x1b[2m77\x1b[0m │   // Default account if data is not loaded yet\n \x1b[2m78\x1b[0m │   const account: AffiliateAccount = (accountsData?.accounts?.[0]) || {\n    ╰────\n\n\nCaused by:\n    Syntax Error")}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),r=t.X(0,[2103,2765],()=>__webpack_exec__(61092));module.exports=r})();