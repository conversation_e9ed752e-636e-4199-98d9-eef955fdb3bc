import { IsString, <PERSON>Optional, <PERSON><PERSON><PERSON>E<PERSON>y, <PERSON><PERSON>eng<PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateStoreDto {
  @ApiProperty({
    description: 'Store name',
    example: 'Tech Electronics Store',
    maxLength: 100,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  name: string;

  @ApiPropertyOptional({
    description: 'Store description',
    example: 'Your one-stop shop for the latest electronics and gadgets',
  })
  @IsString()
  @IsOptional()
  description?: string;
}
