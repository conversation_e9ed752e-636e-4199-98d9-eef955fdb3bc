"use strict";exports.id=9529,exports.ids=[9529],exports.modules={99529:(e,t,r)=>{r.d(t,{Z:()=>__WEBPACK_DEFAULT_EXPORT__});var s=r(30784),a=r(9885),o=r(27870),c=r(14379),d=r(65716),l=r(706),i=r(59872);let __WEBPACK_DEFAULT_EXPORT__=({onSelect:e,placeholder:t,className:r="",buttonText:n,showButton:u=!0,autoFocus:m=!1,initialValue:h=""})=>{let{t:p}=(0,o.$G)("product"),{isRtl:g}=(0,c.g)(),[x,v]=(0,a.useState)(h),[S,y]=(0,a.useState)(!1),f=(0,a.useRef)(null),[k,{data:w,isLoading:$}]=(0,d.mD)(),handleProductSelect=t=>{e(t),v(t.title),y(!1)};return(0,a.useEffect)(()=>{let handleClickOutside=e=>{f.current&&!f.current.contains(e.target)&&y(!1)};return document.addEventListener("mousedown",handleClickOutside),()=>{document.removeEventListener("mousedown",handleClickOutside)}},[]),(0,s.jsxs)("div",{className:`relative ${r}`,children:[(0,s.jsxs)("div",{className:"flex",children:[s.jsx(l.Z,{label:p("search.label","Search Products"),value:x,onChange:e=>{let t=e.target.value;v(t),t.trim().length>=2?(k({query:t,limit:5}),y(!0)):y(!1)},placeholder:t||p("search.placeholder","Search for products..."),className:"flex-1",autoFocus:m}),u&&s.jsx(i.Z,{type:"button",onClick:()=>{x.trim().length>=2&&(k({query:x,limit:5}),y(!0))},className:"ml-2 self-end mb-4",children:n||p("search.button","Search")})]}),S&&s.jsx("div",{ref:f,className:"absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg max-h-60 overflow-auto",children:$?s.jsx("div",{className:"p-4 text-center text-gray-500 dark:text-gray-400",children:p("search.loading","Loading...")}):w?.products&&w.products.length>0?s.jsx("ul",{children:w.products.map(e=>s.jsx("li",{className:"cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700",onClick:()=>handleProductSelect(e),children:(0,s.jsxs)("div",{className:"flex items-center p-3",children:[e.imageUrl&&s.jsx("div",{className:"w-10 h-10 flex-shrink-0 mr-3",children:s.jsx("img",{src:e.imageUrl,alt:e.title,className:"w-full h-full object-cover rounded"})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[s.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 truncate",children:e.title}),s.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:e.storeName||p("search.store","Store")})]}),s.jsx("div",{className:"text-sm font-semibold text-gray-900 dark:text-gray-100",children:new Intl.NumberFormat(g?"fa-IR":"en-US",{style:"currency",currency:"USD"}).format(e.price)})]})},e.id))}):s.jsx("div",{className:"p-4 text-center text-gray-500 dark:text-gray-400",children:p("search.noResults","No products found")})})]})}},65716:(e,t,r)=>{r.d(t,{lZ:()=>c,mD:()=>o});var s=r(86372);let a=s.g.injectEndpoints({endpoints:e=>({searchProducts:e.query({query:({query:e,limit:t=10,page:r=1})=>`/products/search?q=${encodeURIComponent(e)}&limit=${t}&page=${r}`,providesTags:["Product"],transformResponse:e=>{if(!e){let e=[];for(let t=1;t<=5;t++)e.push({id:`mock-search-${t}`,storeId:`mock-store-${t%3+1}`,title:`${query} Product ${t}`,description:`This is a mock search result ${t} for "${query}".`,price:19.99+10*t,mediaUrls:[`https://via.placeholder.com/300?text=Search+${t}`],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),isActive:!0,store:{id:`mock-store-${t%3+1}`,name:`Store ${t%3+1}`,ownerId:`mock-user-${t%3+1}`,description:`Mock store ${t%3+1}`,logoUrl:`https://via.placeholder.com/100?text=Store+${t%3+1}`,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}});return{products:e,total:25,page,limit}}return e}}),getProduct:e.query({query:e=>`/products/${e}`,providesTags:["Product"],transformResponse:(e,t,r)=>e||{id:r,storeId:"mock-store-1",title:`Product ${r}`,description:`This is a mock product with ID ${r} for development purposes.`,price:29.99,mediaUrls:[`https://via.placeholder.com/300?text=Product+${r}`],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),isActive:!0,store:{id:"mock-store-1",name:"Mock Store",ownerId:"mock-user-1",description:"A mock store for development",logoUrl:"https://via.placeholder.com/100?text=Store",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}}})}),overrideExisting:!0}),{useLazySearchProductsQuery:o,useGetProductQuery:c}=a}};