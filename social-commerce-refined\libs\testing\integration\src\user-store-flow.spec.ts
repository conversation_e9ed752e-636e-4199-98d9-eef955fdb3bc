import { ApiClient } from './helpers/api.helper';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

describe('User and Store Integration Flow', () => {
  const apiClient = new ApiClient();
  
  // Test data
  const testUser = {
    email: `test-${Date.now()}@example.com`,
    password: 'password123',
    profile: {
      firstName: 'Test',
      lastName: 'User',
    },
  };
  
  const testStore = {
    name: `Test Store ${Date.now()}`,
    description: 'This is a test store',
  };
  
  const testProduct = {
    name: `Test Product ${Date.now()}`,
    price: 99.99,
    description: 'This is a test product',
  };
  
  // User data
  let userId: string;
  let storeId: string;
  let productId: string;
  
  // Increase timeout for integration tests
  jest.setTimeout(30000);
  
  it('should register a new user', async () => {
    // Register a new user
    const user = await apiClient.registerUser(
      testUser.email,
      testUser.password,
      testUser.profile,
    );
    
    // Verify the user was created
    expect(user).toBeDefined();
    expect(user.id).toBeDefined();
    expect(user.email).toBe(testUser.email);
    expect(user.profile).toBeDefined();
    expect(user.profile.firstName).toBe(testUser.profile.firstName);
    expect(user.profile.lastName).toBe(testUser.profile.lastName);
    
    // Save the user ID for later tests
    userId = user.id;
  });
  
  it('should login the user', async () => {
    // Login the user
    const loginResponse = await apiClient.login(
      testUser.email,
      testUser.password,
    );
    
    // Verify the login response
    expect(loginResponse).toBeDefined();
    expect(loginResponse.accessToken).toBeDefined();
    expect(loginResponse.user).toBeDefined();
    expect(loginResponse.user.id).toBe(userId);
    expect(loginResponse.user.email).toBe(testUser.email);
  });
  
  it('should get the user profile', async () => {
    // Get the user profile
    const profile = await apiClient.getProfile();
    
    // Verify the profile
    expect(profile).toBeDefined();
    expect(profile.id).toBe(userId);
    expect(profile.email).toBe(testUser.email);
    expect(profile.profile).toBeDefined();
    expect(profile.profile.firstName).toBe(testUser.profile.firstName);
    expect(profile.profile.lastName).toBe(testUser.profile.lastName);
  });
  
  it('should create a store', async () => {
    // Create a store
    const store = await apiClient.createStore(
      testStore.name,
      testStore.description,
    );
    
    // Verify the store was created
    expect(store).toBeDefined();
    expect(store.id).toBeDefined();
    expect(store.name).toBe(testStore.name);
    expect(store.description).toBe(testStore.description);
    expect(store.ownerId).toBe(userId);
    
    // Save the store ID for later tests
    storeId = store.id;
  });
  
  it('should get the store', async () => {
    // Get the store
    const store = await apiClient.getStore(storeId);
    
    // Verify the store
    expect(store).toBeDefined();
    expect(store.id).toBe(storeId);
    expect(store.name).toBe(testStore.name);
    expect(store.description).toBe(testStore.description);
    expect(store.ownerId).toBe(userId);
  });
  
  it('should create a product', async () => {
    // Create a product
    const product = await apiClient.createProduct(
      storeId,
      testProduct.name,
      testProduct.price,
      testProduct.description,
    );
    
    // Verify the product was created
    expect(product).toBeDefined();
    expect(product.id).toBeDefined();
    expect(product.name).toBe(testProduct.name);
    expect(product.price).toBe(testProduct.price);
    expect(product.description).toBe(testProduct.description);
    expect(product.storeId).toBe(storeId);
    
    // Save the product ID for later tests
    productId = product.id;
  });
  
  it('should get the product', async () => {
    // Get the product
    const product = await apiClient.getProduct(productId);
    
    // Verify the product
    expect(product).toBeDefined();
    expect(product.id).toBe(productId);
    expect(product.name).toBe(testProduct.name);
    expect(product.price).toBe(testProduct.price);
    expect(product.description).toBe(testProduct.description);
    expect(product.storeId).toBe(storeId);
  });
});
