import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>al, <PERSON><PERSON><PERSON>ber, IsBoolean, IsArray, IsObject, Min } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class UpdateProductDto {
  @ApiPropertyOptional({
    description: 'The name of the product',
    example: 'Updated Product',
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional({
    description: 'The description of the product',
    example: 'This is an updated product description',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'The price of the product',
    example: 89.99,
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  @Type(() => Number)
  price?: number;

  @ApiPropertyOptional({
    description: 'The quantity of the product',
    example: 150,
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  @Type(() => Number)
  quantity?: number;

  @ApiPropertyOptional({
    description: 'Whether the product is active',
    example: false,
  })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'The images of the product',
    example: ['https://example.com/updated-image1.jpg', 'https://example.com/updated-image2.jpg'],
  })
  @IsArray()
  @IsOptional()
  images?: string[];

  @ApiPropertyOptional({
    description: 'The categories of the product',
    example: ['Updated Category', 'New Category'],
  })
  @IsArray()
  @IsOptional()
  categories?: string[];

  @ApiPropertyOptional({
    description: 'The tags of the product',
    example: ['updated', 'new-arrival'],
  })
  @IsArray()
  @IsOptional()
  tags?: string[];

  @ApiPropertyOptional({
    description: 'The attributes of the product',
    example: {
      color: 'Blue',
      size: 'Medium',
      material: 'Polyester',
    },
  })
  @IsObject()
  @IsOptional()
  attributes?: Record<string, string>;

  @ApiPropertyOptional({
    description: 'The variants of the product',
    example: [
      {
        id: '1',
        name: 'Blue Medium',
        price: 89.99,
        quantity: 75,
        attributes: {
          color: 'Blue',
          size: 'Medium',
        },
      },
      {
        id: '2',
        name: 'Green Small',
        price: 79.99,
        quantity: 50,
        attributes: {
          color: 'Green',
          size: 'Small',
        },
      },
    ],
  })
  @IsArray()
  @IsOptional()
  variants?: {
    id: string;
    name: string;
    price: number;
    quantity: number;
    attributes: Record<string, string>;
  }[];

  @ApiPropertyOptional({
    description: 'The SKU of the product',
    example: 'SKU-654321',
  })
  @IsString()
  @IsOptional()
  sku?: string;

  @ApiPropertyOptional({
    description: 'The barcode of the product',
    example: '210987654321',
  })
  @IsString()
  @IsOptional()
  barcode?: string;

  @ApiPropertyOptional({
    description: 'The weight of the product',
    example: 2.0,
  })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  weight?: number;

  @ApiPropertyOptional({
    description: 'The dimensions of the product',
    example: '15x25x35',
  })
  @IsString()
  @IsOptional()
  dimensions?: string;

  @ApiPropertyOptional({
    description: 'The shipping information of the product',
    example: {
      dimensions: {
        length: 15,
        width: 25,
        height: 35,
        unit: 'cm',
      },
      weight: {
        value: 2.0,
        unit: 'kg',
      },
      freeShipping: false,
      shippingClass: 'express',
    },
  })
  @IsObject()
  @IsOptional()
  shipping?: {
    dimensions?: {
      length: number;
      width: number;
      height: number;
      unit: string;
    };
    weight?: {
      value: number;
      unit: string;
    };
    freeShipping?: boolean;
    shippingClass?: string;
  };

  @ApiPropertyOptional({
    description: 'The SEO information of the product',
    example: {
      title: 'Updated Product - Best Deal',
      description: 'This is an updated product with new features',
      keywords: ['updated', 'product', 'new'],
    },
  })
  @IsObject()
  @IsOptional()
  seo?: {
    title?: string;
    description?: string;
    keywords?: string[];
  };
}
