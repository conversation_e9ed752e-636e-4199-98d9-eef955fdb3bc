(()=>{var e={};e.id=2142,e.ids=[2142],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},81733:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>d.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>o});var s=r(67096),a=r(16132),i=r(37284),d=r.n(i),l=r(32564),n={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);r.d(t,n);let o=["",{children:["dashboard",{children:["products",{children:["import",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,55909)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\dashboard\\products\\import\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,68182)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9291,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\dashboard\\products\\import\\page.tsx"],m="/dashboard/products/import/page",x={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/dashboard/products/import/page",pathname:"/dashboard/products/import",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},19546:(e,t,r)=>{Promise.resolve().then(r.bind(r,46567))},46567:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>ProductImportPage});var s,a=r(30784),i=r(9885),d=r(27870),l=r(14379),n=r(57114),o=r(11440),c=r.n(o),m=r(59872),x=r(93003);let import_FileUpload=({onFileSelected:e,acceptedFileTypes:t=[x.X2.CSV,x.X2.EXCEL],maxFileSize:r=10485760,isLoading:s=!1,error:n,className:o=""})=>{let{t:c}=(0,d.$G)("import"),{isRtl:p}=(0,l.g)(),u=(0,i.useRef)(null),[g,h]=(0,i.useState)(!1),[y,f]=(0,i.useState)(null),[j,b]=(0,i.useState)(null),validateAndSetFile=s=>{if(b(null),s.size>r){b(c("errors.fileTooLarge","File is too large. Maximum size is {{size}}MB.",{size:r/1048576}));return}let a=s.name.split(".").pop()?.toLowerCase(),i="csv"===a&&t.includes(x.X2.CSV)||("xlsx"===a||"xls"===a)&&t.includes(x.X2.EXCEL)||"json"===a&&t.includes(x.X2.JSON);if(!i){b(c("errors.invalidFileType","Invalid file type. Accepted types: {{types}}",{types:t.join(", ")}));return}f(s),e(s)},getDisplayFileSize=e=>e<1024?`${e} B`:e<1048576?`${(e/1024).toFixed(2)} KB`:`${(e/1048576).toFixed(2)} MB`;return(0,a.jsxs)("div",{className:`${o}`,children:[a.jsx("input",{type:"file",ref:u,className:"hidden",accept:(()=>{let e=[];return t.includes(x.X2.CSV)&&e.push(".csv"),t.includes(x.X2.EXCEL)&&e.push(".xlsx",".xls"),t.includes(x.X2.JSON)&&e.push(".json"),e.join(",")})(),onChange:e=>{let t=e.target.files;t&&t.length>0&&validateAndSetFile(t[0])},disabled:s}),a.jsx("div",{className:`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors
          ${g?"border-primary-500 bg-primary-50 dark:bg-primary-900/20":"border-gray-300 dark:border-gray-700 hover:border-primary-400 dark:hover:border-primary-600"}
          ${s?"opacity-50 cursor-not-allowed":""}
        `,onDragEnter:e=>{e.preventDefault(),e.stopPropagation(),h(!0)},onDragLeave:e=>{e.preventDefault(),e.stopPropagation(),h(!1)},onDragOver:e=>{e.preventDefault(),e.stopPropagation()},onDrop:e=>{e.preventDefault(),e.stopPropagation(),h(!1);let t=e.dataTransfer.files;t&&t.length>0&&validateAndSetFile(t[0])},onClick:()=>{u.current&&u.current.click()},children:s?(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center py-4",children:[a.jsx("div",{className:"animate-spin rounded-full h-10 w-10 border-b-2 border-primary-500 mb-2"}),a.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:c("uploading","Uploading...")})]}):y?(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center py-4",children:[a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-10 w-10 text-green-500 mb-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),a.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-1",children:y.name}),a.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:getDisplayFileSize(y.size)}),a.jsx(m.Z,{variant:"text",size:"sm",className:"mt-2",onClick:e=>{e.stopPropagation(),f(null),u.current&&(u.current.value="")},children:c("changeFile","Change File")})]}):(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center py-8",children:[a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-12 w-12 text-gray-400 mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})}),a.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-1",children:c("dragAndDrop","Drag and drop your file here")}),a.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mb-4",children:c("or","or")}),a.jsx(m.Z,{size:"sm",children:c("browseFiles","Browse Files")}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-4",children:[c("acceptedFileTypes","Accepted file types"),": ",t.join(", ")]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:[c("maxFileSize","Maximum file size"),": ",getDisplayFileSize(r)]})]})}),(n||j)&&a.jsx("div",{className:"mt-2 text-sm text-red-600 dark:text-red-400",children:n||j})]})};var p=r(75444);let import_ImportFileUploader=({entityType:e,acceptedFileTypes:t=[x.X2.CSV,x.X2.EXCEL],onFileUploaded:r,onCancel:s,className:n=""})=>{let{t:o}=(0,d.$G)("import"),{isRtl:c}=(0,l.g)(),[u,g]=(0,i.useState)(null),[h,{isLoading:y,error:f}]=(0,p.YD)(),handleUpload=async()=>{if(u)try{let t=new FormData;t.append("file",u),t.append("entityType",e);let s=await h(t).unwrap();r(s.fileUrl,s.fileName,s.fileSize)}catch(e){console.error("Error uploading file:",e)}},getEntityTypeDisplayName=e=>{switch(e){case x.TS.PRODUCT:return o("entityTypes.product","Product");case x.TS.CATEGORY:return o("entityTypes.category","Category");case x.TS.CUSTOMER:return o("entityTypes.customer","Customer");case x.TS.ORDER:return o("entityTypes.order","Order");default:return e}};return a.jsx("div",{className:`${n}`,children:(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6",children:[a.jsx("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:o("uploadFile","Upload {{entityType}} File",{entityType:getEntityTypeDisplayName(e)})}),a.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-6",children:o("uploadInstructions","Upload a file containing {{entityType}} data. Accepted file types: {{types}}",{entityType:getEntityTypeDisplayName(e).toLowerCase(),types:t.join(", ")})}),a.jsx(import_FileUpload,{onFileSelected:e=>{g(e)},acceptedFileTypes:t,isLoading:y,error:f?o("errors.uploadFailed","File upload failed. Please try again."):void 0,className:"mb-6"}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:[s&&a.jsx(m.Z,{variant:"outline",onClick:s,disabled:y,children:o("cancel","Cancel")}),a.jsx(m.Z,{onClick:handleUpload,disabled:!u||y,isLoading:y,children:o("upload","Upload")})]})]})})},import_ImportPreview=({fileUrl:e,fileName:t,fileType:r,entityType:s,onContinue:n,onBack:o,className:c=""})=>{let{t:u}=(0,d.$G)("import"),{isRtl:g}=(0,l.g)(),[h,y]=(0,i.useState)(!0),[f,{data:j,isLoading:b,error:v}]=(0,p.Uu)();(0,i.useEffect)(()=>{loadPreview()},[e,h]);let loadPreview=()=>{f({fileUrl:e,fileType:r,entityType:s,options:{skipFirstRow:h}})};return a.jsx("div",{className:`${c}`,children:(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6",children:[a.jsx("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:u("previewFile","Preview {{entityType}} File",{entityType:(e=>{switch(e){case x.TS.PRODUCT:return u("entityTypes.product","Product");case x.TS.CATEGORY:return u("entityTypes.category","Category");case x.TS.CUSTOMER:return u("entityTypes.customer","Customer");case x.TS.ORDER:return u("entityTypes.order","Order");default:return e}})(s)})}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:t}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:[u("totalRows","Total rows"),": ",j?.totalRows||0]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("input",{id:"skip-first-row",type:"checkbox",className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded",checked:h,onChange:e=>y(e.target.checked),disabled:b}),a.jsx("label",{htmlFor:"skip-first-row",className:"ml-2 block text-sm text-gray-700 dark:text-gray-300",children:u("skipFirstRow","First row contains headers")})]})]}),a.jsx("div",{className:"border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden mb-6",children:a.jsx("div",{className:"overflow-x-auto",children:b?a.jsx("div",{className:"flex items-center justify-center py-12",children:a.jsx("div",{className:"animate-spin rounded-full h-10 w-10 border-b-2 border-primary-500"})}):v?a.jsx("div",{className:"flex items-center justify-center py-12 text-red-500",children:a.jsx("p",{children:u("errors.previewFailed","Failed to load preview. Please try again.")})}):j&&j.headers.length>0?(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[a.jsx("thead",{className:"bg-gray-50 dark:bg-gray-800",children:a.jsx("tr",{children:j.headers.map((e,t)=>a.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e},t))})}),a.jsx("tbody",{className:"bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700",children:j.rows.slice(0,5).map((e,t)=>a.jsx("tr",{className:t%2==0?"bg-white dark:bg-gray-900":"bg-gray-50 dark:bg-gray-800",children:e.map((e,t)=>a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:e},t))},t))})]}):a.jsx("div",{className:"flex items-center justify-center py-12 text-gray-500 dark:text-gray-400",children:a.jsx("p",{children:u("noData","No data available")})})})}),j&&j.rows.length>5&&a.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mb-6",children:u("showingPreview","Showing preview of first 5 rows out of {{total}}",{total:j.rows.length})}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx(m.Z,{variant:"outline",onClick:o,disabled:b,children:u("back","Back")}),a.jsx(m.Z,{onClick:()=>{j&&n(j)},disabled:b||!j||0===j.headers.length,children:u("continue","Continue")})]})]})})},import_ColumnMapping=({preview:e,entityType:t,initialMappings:r,onSave:s,onBack:n,className:o=""})=>{let{t:c}=(0,d.$G)("import"),{isRtl:u}=(0,l.g)(),[g,h]=(0,i.useState)([]),[y,f]=(0,i.useState)([]),[j,{data:b,isLoading:v}]=(0,p.U9)();(0,i.useEffect)(()=>{t===x.TS.PRODUCT&&j()},[t]),(0,i.useEffect)(()=>{if(e&&e.headers.length>0){if(r&&r.length>0)h(r);else if(e.suggestedMappings&&e.suggestedMappings.length>0)h(e.suggestedMappings);else{let t=e.headers.map(e=>{let t=y.find(t=>t.name.toLowerCase()===e.toLowerCase());return{sourceColumn:e,targetField:t?t.name:"",isRequired:!!t&&t.isRequired,isValid:!!t}});h(t)}}},[e,y,r]),(0,i.useEffect)(()=>{b&&f(b.fields)},[b]);let handleMappingChange=(e,t)=>{h(r=>r.map(r=>{if(r.sourceColumn===e){let e=y.find(e=>e.name===t);return{...r,targetField:t,isRequired:!!e&&e.isRequired,isValid:!!t}}return r}))},areRequiredFieldsMapped=()=>{let e=y.filter(e=>e.isRequired);return e.every(e=>g.some(t=>t.targetField===e.name&&t.isValid))},getFieldDescription=e=>{let t=y.find(t=>t.name===e);return t?t.description:""},isFieldAlreadyMapped=(e,t)=>g.some(r=>r.targetField===e&&r.sourceColumn!==t);return a.jsx("div",{className:`${o}`,children:(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6",children:[a.jsx("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:c("mapColumns","Map Columns")}),a.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-6",children:c("mapColumnsDescription","Match the columns in your file to the appropriate fields in our system.")}),v?a.jsx("div",{className:"flex items-center justify-center py-12",children:a.jsx("div",{className:"animate-spin rounded-full h-10 w-10 border-b-2 border-primary-500"})}):(0,a.jsxs)("div",{children:[a.jsx("div",{className:"border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden mb-6",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[a.jsx("thead",{className:"bg-gray-50 dark:bg-gray-800",children:(0,a.jsxs)("tr",{children:[a.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:c("fileColumn","File Column")}),a.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:c("systemField","System Field")}),a.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:c("preview","Preview")})]})}),a.jsx("tbody",{className:"bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700",children:g.map((t,r)=>(0,a.jsxs)("tr",{className:r%2==0?"bg-white dark:bg-gray-900":"bg-gray-50 dark:bg-gray-800",children:[a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100",children:t.sourceColumn}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("select",{value:t.targetField,onChange:e=>handleMappingChange(t.sourceColumn,e.target.value),className:`block w-full pl-3 pr-10 py-2 text-base border rounded-md focus:outline-none focus:ring-1 sm:text-sm
                              ${t.isRequired&&!t.isValid?"border-red-300 text-red-900 focus:ring-red-500 focus:border-red-500 dark:border-red-700 dark:text-red-400":"border-gray-300 text-gray-900 focus:ring-primary-500 focus:border-primary-500 dark:border-gray-700 dark:text-gray-100"}
                            `,children:[a.jsx("option",{value:"",children:c("doNotImport","-- Do not import --")}),y.map(e=>(0,a.jsxs)("option",{value:e.name,disabled:isFieldAlreadyMapped(e.name,t.sourceColumn),children:[e.label," ",e.isRequired?"*":""]},e.name))]}),t.targetField&&a.jsx("div",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:getFieldDescription(t.targetField)}),t.isRequired&&!t.isValid&&a.jsx("div",{className:"mt-1 text-xs text-red-500",children:c("requiredField","This field is required")})]})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:e.rows[0]&&e.rows[0][e.headers.indexOf(t.sourceColumn)]})]},r))})]})}),!areRequiredFieldsMapped()&&a.jsx("div",{className:"mb-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-md",children:(0,a.jsxs)("div",{className:"flex",children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx("svg",{className:"h-5 w-5 text-yellow-400",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:a.jsx("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,a.jsxs)("div",{className:"ml-3",children:[a.jsx("h3",{className:"text-sm font-medium text-yellow-800 dark:text-yellow-200",children:c("requiredFieldsWarning","Required fields not mapped")}),a.jsx("div",{className:"mt-2 text-sm text-yellow-700 dark:text-yellow-300",children:a.jsx("p",{children:c("requiredFieldsWarningDescription","Please map all required fields (marked with *) before continuing.")})})]})]})}),(0,a.jsxs)("div",{className:"flex justify-between mt-6",children:[a.jsx(m.Z,{variant:"outline",onClick:n,children:c("back","Back")}),a.jsx(m.Z,{onClick:()=>{s(g)},disabled:!areRequiredFieldsMapped(),children:c("continue","Continue")})]})]})]})})},import_ImportOptionsAdvanced=({entityType:e,initialOptions:t,onSave:r,onBack:s,className:n=""})=>{let{t:o}=(0,d.$G)("import"),{isRtl:c}=(0,l.g)(),[p,u]=(0,i.useState)(t||{skipFirstRow:!0,updateExisting:!0,identifierField:"sku",createMissing:!0,batchSize:100,validateOnly:!1,importImages:!0,defaultStatus:"draft"}),handleCheckboxChange=e=>{u(t=>({...t,[e]:!t[e]}))},handleSelectChange=(e,t)=>{u(r=>({...r,[e]:t}))},handleNumberChange=(e,t)=>{let r=parseInt(t,10);isNaN(r)||u(t=>({...t,[e]:r}))};return a.jsx("div",{className:`${n}`,children:(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6",children:[a.jsx("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:o("importOptions","Import Options")}),a.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-6",children:o("importOptionsDescription","Configure how the {{entityType}} data should be imported.",{entityType:(e=>{switch(e){case x.TS.PRODUCT:return o("entityTypes.product","Product");case x.TS.CATEGORY:return o("entityTypes.category","Category");case x.TS.CUSTOMER:return o("entityTypes.customer","Customer");case x.TS.ORDER:return o("entityTypes.order","Order");default:return e}})(e).toLowerCase()})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:o("identifierOptions","Identifier Options")}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start",children:[a.jsx("div",{className:"flex items-center h-5",children:a.jsx("input",{id:"update-existing",type:"checkbox",checked:p.updateExisting,onChange:()=>handleCheckboxChange("updateExisting"),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"})}),(0,a.jsxs)("div",{className:"ml-3 text-sm",children:[a.jsx("label",{htmlFor:"update-existing",className:"font-medium text-gray-700 dark:text-gray-300",children:o("updateExisting","Update Existing Records")}),a.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:o("updateExistingDescription","If a record with the same identifier exists, update it instead of creating a new one.")})]})]}),p.updateExisting&&(0,a.jsxs)("div",{className:"ml-7",children:[a.jsx("label",{htmlFor:"identifier-field",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:o("identifierField","Identifier Field")}),(0,a.jsxs)("select",{id:"identifier-field",value:p.identifierField,onChange:e=>handleSelectChange("identifierField",e.target.value),className:"block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-700 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md",children:[a.jsx("option",{value:"id",children:o("fields.id","ID")}),a.jsx("option",{value:"sku",children:o("fields.sku","SKU")}),a.jsx("option",{value:"slug",children:o("fields.slug","Slug")})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[a.jsx("div",{className:"flex items-center h-5",children:a.jsx("input",{id:"create-missing",type:"checkbox",checked:p.createMissing,onChange:()=>handleCheckboxChange("createMissing"),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"})}),(0,a.jsxs)("div",{className:"ml-3 text-sm",children:[a.jsx("label",{htmlFor:"create-missing",className:"font-medium text-gray-700 dark:text-gray-300",children:o("createMissing","Create Missing Records")}),a.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:o("createMissingDescription","Create new records for items that don't exist in the system.")})]})]})]})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:o("processingOptions","Processing Options")}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start",children:[a.jsx("div",{className:"flex items-center h-5",children:a.jsx("input",{id:"validate-only",type:"checkbox",checked:p.validateOnly,onChange:()=>handleCheckboxChange("validateOnly"),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"})}),(0,a.jsxs)("div",{className:"ml-3 text-sm",children:[a.jsx("label",{htmlFor:"validate-only",className:"font-medium text-gray-700 dark:text-gray-300",children:o("validateOnly","Validate Only")}),a.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:o("validateOnlyDescription","Check the file for errors without actually importing the data.")})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"batch-size",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:o("batchSize","Batch Size")}),a.jsx("input",{type:"number",id:"batch-size",value:p.batchSize,onChange:e=>handleNumberChange("batchSize",e.target.value),min:"1",max:"1000",className:"block w-full pl-3 pr-3 py-2 text-base border-gray-300 dark:border-gray-700 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"}),a.jsx("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:o("batchSizeDescription","Number of records to process in each batch. Higher values may be faster but use more memory.")})]})]})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:o("contentOptions","Content Options")}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start",children:[a.jsx("div",{className:"flex items-center h-5",children:a.jsx("input",{id:"import-images",type:"checkbox",checked:p.importImages,onChange:()=>handleCheckboxChange("importImages"),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"})}),(0,a.jsxs)("div",{className:"ml-3 text-sm",children:[a.jsx("label",{htmlFor:"import-images",className:"font-medium text-gray-700 dark:text-gray-300",children:o("importImages","Import Images")}),a.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:o("importImagesDescription","Download and import images from URLs in the file.")})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"default-status",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:o("defaultStatus","Default Status")}),(0,a.jsxs)("select",{id:"default-status",value:p.defaultStatus,onChange:e=>handleSelectChange("defaultStatus",e.target.value),className:"block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-700 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md",children:[a.jsx("option",{value:"draft",children:o("status.draft","Draft")}),a.jsx("option",{value:"active",children:o("status.active","Active")}),a.jsx("option",{value:"inactive",children:o("status.inactive","Inactive")})]}),a.jsx("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:o("defaultStatusDescription","Default status for new records if not specified in the import file.")})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-between pt-4",children:[a.jsx(m.Z,{variant:"outline",onClick:s,children:o("back","Back")}),a.jsx(m.Z,{onClick:()=>{r(p)},children:o("continue","Continue")})]})]})]})})},import_ImportProcessingStatus=({importId:e,onComplete:t,onViewErrors:r,onCancel:s,className:n=""})=>{let{t:o}=(0,d.$G)("import"),{isRtl:c}=(0,l.g)(),{data:u,isLoading:g,error:h}=(0,p.Qc)(e,{pollingInterval:2e3}),[y,{isLoading:f}]=(0,p.iD)();(0,i.useEffect)(()=>{u&&isImportComplete(u.status)&&t&&t()},[u,t]);let isImportComplete=e=>[x.d2.COMPLETED,x.d2.FAILED,x.d2.PARTIALLY_COMPLETED,x.d2.CANCELLED].includes(e),handleCancel=async()=>{if(e)try{await y(e).unwrap()}catch(e){console.error("Error cancelling import:",e)}s&&s()};return g&&!u?a.jsx("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 ${n}`,children:a.jsx("div",{className:"flex items-center justify-center py-12",children:a.jsx("div",{className:"animate-spin rounded-full h-10 w-10 border-b-2 border-primary-500"})})}):h?a.jsx("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 ${n}`,children:a.jsx("div",{className:"flex items-center justify-center py-12 text-red-500",children:a.jsx("p",{children:o("errors.statusFailed","Failed to load import status. Please try again.")})})}):u?(0,a.jsxs)("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 ${n}`,children:[a.jsx("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:o("importStatus","Import Status")}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[a.jsx("div",{className:"flex items-center",children:a.jsx("span",{className:`font-medium ${(e=>{switch(e){case x.d2.COMPLETED:return"text-green-500";case x.d2.FAILED:return"text-red-500";case x.d2.PARTIALLY_COMPLETED:return"text-yellow-500";case x.d2.CANCELLED:return"text-gray-500";default:return"text-blue-500"}})(u.status)}`,children:(e=>{switch(e){case x.d2.PENDING:return o("status.pending","Pending");case x.d2.VALIDATING:return o("status.validating","Validating");case x.d2.MAPPING:return o("status.mapping","Mapping");case x.d2.PROCESSING:return o("status.processing","Processing");case x.d2.COMPLETED:return o("status.completed","Completed");case x.d2.FAILED:return o("status.failed","Failed");case x.d2.PARTIALLY_COMPLETED:return o("status.partiallyCompleted","Partially Completed");case x.d2.CANCELLED:return o("status.cancelled","Cancelled");default:return e}})(u.status)})}),(0,a.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:[u.processedRows," / ",u.totalRows," ",o("rowsProcessed","rows processed")]})]}),a.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5",children:a.jsx("div",{className:"bg-blue-500 h-2.5 rounded-full",style:{width:`${u.processedRows/Math.max(u.totalRows,1)*100}%`}})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"bg-green-50 dark:bg-green-900/20 p-4 rounded-lg",children:[a.jsx("div",{className:"text-2xl font-bold text-green-500",children:u.successRows}),a.jsx("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:o("successfulRows","Successful")})]}),(0,a.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 p-4 rounded-lg",children:[a.jsx("div",{className:"text-2xl font-bold text-red-500",children:u.errorRows}),a.jsx("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:o("errorRows","Errors")})]}),(0,a.jsxs)("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg",children:[a.jsx("div",{className:"text-2xl font-bold text-yellow-500",children:u.warningRows||0}),a.jsx("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:o("warningRows","Warnings")})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:[!isImportComplete(u.status)&&a.jsx(m.Z,{variant:"outline",onClick:handleCancel,isLoading:f,disabled:f,children:o("cancel","Cancel")}),u.errorRows>0&&a.jsx(m.Z,{variant:"outline",onClick:()=>{r&&e&&r(e)},children:o("viewErrors","View Errors")}),isImportComplete(u.status)&&a.jsx(m.Z,{onClick:t,children:o("done","Done")})]})]}):a.jsx("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 ${n}`,children:a.jsx("div",{className:"flex items-center justify-center py-12 text-gray-500 dark:text-gray-400",children:a.jsx("p",{children:o("noImportStatus","No import status available")})})})},import_ImportErrorList=({importId:e,onClose:t,className:r=""})=>{let{t:s}=(0,d.$G)("import"),{isRtl:n}=(0,l.g)(),[o,c]=(0,i.useState)(1),[x,u]=(0,i.useState)(100),{data:g,isLoading:h,error:y}=(0,p.Ls)({importId:e,page:o,limit:x});return h&&!g?a.jsx("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 ${r}`,children:a.jsx("div",{className:"flex items-center justify-center py-12",children:a.jsx("div",{className:"animate-spin rounded-full h-10 w-10 border-b-2 border-primary-500"})})}):y?a.jsx("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 ${r}`,children:a.jsx("div",{className:"flex items-center justify-center py-12 text-red-500",children:a.jsx("p",{children:s("errors.errorsFailed","Failed to load import errors. Please try again.")})})}):g&&0!==g.errors.length?(0,a.jsxs)("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 ${r}`,children:[a.jsx("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:s("importErrors","Import Errors")}),a.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-6",children:s("errorsDescription","The following errors occurred during the import process:")}),a.jsx("div",{className:"border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden mb-6",children:a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[a.jsx("thead",{className:"bg-gray-50 dark:bg-gray-800",children:(0,a.jsxs)("tr",{children:[a.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:s("row","Row")}),a.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:s("column","Column")}),a.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:s("value","Value")}),a.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:s("error","Error")})]})}),a.jsx("tbody",{className:"bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700",children:g.errors.map((e,t)=>(0,a.jsxs)("tr",{className:t%2==0?"bg-white dark:bg-gray-900":"bg-gray-50 dark:bg-gray-800",children:[a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:e.row}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:e.column}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:e.value}),a.jsx("td",{className:"px-6 py-4 text-sm text-red-500",children:e.message})]},t))})]})})}),(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:[s("showing","Showing")," ",(o-1)*x+1," - ",(o-1)*x+g.errors.length," ",s("of","of")," ",g.totalErrors]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx(m.Z,{variant:"outline",onClick:()=>{o>1&&c(o-1)},disabled:1===o,size:"sm",children:s("previous","Previous")}),a.jsx(m.Z,{variant:"outline",onClick:()=>{g&&g.errors.length===x&&c(o+1)},disabled:g.errors.length<x,size:"sm",children:s("next","Next")})]})]}),a.jsx("div",{className:"flex justify-end",children:a.jsx(m.Z,{onClick:t,children:s("close","Close")})})]}):(0,a.jsxs)("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 ${r}`,children:[a.jsx("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:s("importErrors","Import Errors")}),a.jsx("div",{className:"flex items-center justify-center py-12 text-gray-500 dark:text-gray-400",children:a.jsx("p",{children:s("noErrors","No errors found")})}),a.jsx("div",{className:"flex justify-end",children:a.jsx(m.Z,{onClick:t,children:s("close","Close")})})]})};!function(e){e.UPLOAD="UPLOAD",e.PREVIEW="PREVIEW",e.MAPPING="MAPPING",e.OPTIONS="OPTIONS",e.PROCESSING="PROCESSING",e.COMPLETE="COMPLETE",e.ERROR="ERROR"}(s||(s={}));let import_ImportWizard=({entityType:e,acceptedFileTypes:t=[x.X2.CSV,x.X2.EXCEL],onComplete:r,onCancel:s,className:n=""})=>{let{t:o}=(0,d.$G)("import"),{isRtl:c}=(0,l.g)(),[m,u]=(0,i.useState)("UPLOAD"),[g,h]=(0,i.useState)(""),[y,f]=(0,i.useState)(""),[j,b]=(0,i.useState)(0),[v,N]=(0,i.useState)(x.X2.CSV),[w,k]=(0,i.useState)(null),[C,E]=(0,i.useState)([]),[S,P]=(0,i.useState)({skipFirstRow:!0,updateExisting:!0,identifierField:"sku",createMissing:!0,batchSize:100,validateOnly:!1,importImages:!0,defaultStatus:"draft"}),[T,F]=(0,i.useState)(""),[I,R]=(0,i.useState)(!1),[O,{isLoading:D}]=(0,p.P7)(),{data:L,isLoading:M}=(0,p.Qc)(T,{skip:!T,pollingInterval:2e3}),handleFileUploaded=(e,t,r)=>{h(e),f(t),b(r);let s=t.split(".").pop()?.toLowerCase();"csv"===s?N(x.X2.CSV):"xlsx"===s||"xls"===s?N(x.X2.EXCEL):"json"===s&&N(x.X2.JSON),u("PREVIEW")},handlePreviewContinue=e=>{k(e),u("MAPPING")},handleMappingSave=e=>{E(e),u("OPTIONS")},handleOptionsSave=async t=>{P(t);try{let r=await O({entityType:e,fileType:v,fileName:y,fileSize:j,fileUrl:g,columnMappings:C,options:t}).unwrap();F(r.id),u("PROCESSING")}catch(e){console.error("Error creating import:",e),u("ERROR")}},handleBack=()=>{switch(m){case"PREVIEW":u("UPLOAD");break;case"MAPPING":u("PREVIEW");break;case"OPTIONS":u("MAPPING")}},handleCancel=()=>{s&&s()},handleComplete=()=>{r&&T&&r(T)};return a.jsx("div",{className:`${n}`,children:(()=>{switch(m){case"UPLOAD":return a.jsx(import_ImportFileUploader,{entityType:e,acceptedFileTypes:t,onFileUploaded:handleFileUploaded,onCancel:handleCancel});case"PREVIEW":return a.jsx(import_ImportPreview,{fileUrl:g,fileName:y,fileType:v,entityType:e,onContinue:handlePreviewContinue,onBack:handleBack});case"MAPPING":return w?a.jsx(import_ColumnMapping,{preview:w,entityType:e,initialMappings:C,onSave:handleMappingSave,onBack:handleBack}):null;case"OPTIONS":return a.jsx(import_ImportOptionsAdvanced,{entityType:e,initialOptions:S,onSave:handleOptionsSave,onBack:handleBack});case"PROCESSING":if(I)return a.jsx(import_ImportErrorList,{importId:T,onClose:()=>R(!1)});return a.jsx(import_ImportProcessingStatus,{importId:T,onComplete:handleComplete,onViewErrors:()=>R(!0),onCancel:handleCancel});case"COMPLETE":return a.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-16 w-16 text-green-500 mx-auto mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),a.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2",children:o("importComplete","Import Complete")}),a.jsx("p",{className:"text-gray-500 dark:text-gray-400 mb-6",children:o("importCompleteDescription","Your import has been successfully completed.")}),a.jsx("button",{onClick:handleComplete,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500",children:o("done","Done")})]})});case"ERROR":return a.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-16 w-16 text-red-500 mx-auto mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),a.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2",children:o("importError","Import Error")}),a.jsx("p",{className:"text-gray-500 dark:text-gray-400 mb-6",children:o("importErrorDescription","There was an error processing your import. Please try again.")}),a.jsx("button",{onClick:handleCancel,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500",children:o("close","Close")})]})});default:return null}})()})};function ProductImportPage(){let{t:e}=(0,d.$G)("import"),{isRtl:t}=(0,l.g)(),r=(0,n.useRouter)();return a.jsx("div",{className:`min-h-screen p-6 ${t?"text-right":"text-left"}`,children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsxs)("div",{className:`flex flex-col md:flex-row md:items-center md:justify-between mb-8 gap-4 ${t?"md:flex-row-reverse":""}`,children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2",children:e("importProducts","Import Products")}),a.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:e("uploadInstructions","Upload a file containing {{entityType}} data. Accepted file types: {{types}}",{entityType:e("entityTypes.product","product").toLowerCase(),types:"CSV, Excel"})})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2",children:[a.jsx(c(),{href:"/dashboard/products",children:a.jsx(m.Z,{variant:"outline",children:e("cancel","Cancel")})}),a.jsx(c(),{href:"/dashboard/products/import/template",children:a.jsx(m.Z,{variant:"outline",children:e("downloadTemplate","Download Template")})})]})]}),a.jsx(import_ImportWizard,{entityType:x.TS.PRODUCT,acceptedFileTypes:[x.X2.CSV,x.X2.EXCEL],onComplete:e=>{r.push("/dashboard/products")},onCancel:()=>{r.push("/dashboard/products")}})]})})}},55909:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>d,__esModule:()=>i,default:()=>n});var s=r(95153);let a=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\dashboard\products\import\page.tsx`),{__esModule:i,$$typeof:d}=a,l=a.default,n=l}};var t=require("../../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),r=t.X(0,[2103,2765,8576,1070],()=>__webpack_exec__(81733));module.exports=r})();