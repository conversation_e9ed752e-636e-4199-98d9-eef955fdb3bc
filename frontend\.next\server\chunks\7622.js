"use strict";exports.id=7622,exports.ids=[7622],exports.modules={5387:(e,t,d)=>{d.d(t,{Z:()=>__WEBPACK_DEFAULT_EXPORT__});var a=d(30784);d(9885);let __WEBPACK_DEFAULT_EXPORT__=({className:e=""})=>(0,a.jsxs)("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden animate-pulse ${e}`,children:[a.jsx("div",{className:"aspect-square w-full bg-gray-200 dark:bg-gray-700"}),(0,a.jsxs)("div",{className:"p-4",children:[a.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"}),a.jsx("div",{className:"h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-3"}),a.jsx("div",{className:"h-5 bg-gray-200 dark:bg-gray-700 rounded w-1/3"})]})]})},78779:(e,t,d)=>{d.d(t,{it:()=>y,mW:()=>g});var a=d(86372),r=d(77354);let o=a.g.injectEndpoints({endpoints:e=>({getRelatedItems:e.query({query:({productId:e,relationshipType:t})=>{let d=`/products/${e}/related`;return t&&(d+=`?type=${t}`),d},providesTags:(e,t,{productId:d})=>[{type:"RelatedItem",id:d},...e?e.map(e=>({type:"RelatedItem",id:e.id})):[]]}),createRelatedItem:e.mutation({query:e=>({url:`/products/${e.sourceProductId}/related`,method:"POST",body:e}),invalidatesTags:(e,t,{sourceProductId:d})=>[{type:"RelatedItem",id:d},{type:"Product",id:d}]}),updateRelatedItem:e.mutation({query:({productId:e,relatedItemId:t,data:d})=>({url:`/products/${e}/related/${t}`,method:"PATCH",body:d}),invalidatesTags:(e,t,{productId:d,relatedItemId:a})=>[{type:"RelatedItem",id:d},{type:"RelatedItem",id:a}]}),deleteRelatedItem:e.mutation({query:({productId:e,relatedItemId:t})=>({url:`/products/${e}/related/${t}`,method:"DELETE"}),invalidatesTags:(e,t,{productId:d})=>[{type:"RelatedItem",id:d}]}),getRecommendationRules:e.query({query:()=>"/recommendations/rules",providesTags:e=>[{type:"RecommendationRule",id:"LIST"},...e?e.map(e=>({type:"RecommendationRule",id:e.id})):[]]}),getRecommendationRule:e.query({query:e=>`/recommendations/rules/${e}`,providesTags:(e,t,d)=>[{type:"RecommendationRule",id:d}]}),createRecommendationRule:e.mutation({query:e=>({url:"/recommendations/rules",method:"POST",body:e}),invalidatesTags:[{type:"RecommendationRule",id:"LIST"}]}),updateRecommendationRule:e.mutation({query:({ruleId:e,data:t})=>({url:`/recommendations/rules/${e}`,method:"PATCH",body:t}),invalidatesTags:(e,t,{ruleId:d})=>[{type:"RecommendationRule",id:d},{type:"RecommendationRule",id:"LIST"}]}),deleteRecommendationRule:e.mutation({query:e=>({url:`/recommendations/rules/${e}`,method:"DELETE"}),invalidatesTags:[{type:"RecommendationRule",id:"LIST"}]}),generateRecommendations:e.mutation({query:e=>({url:`/products/${e.productId}/recommendations/generate`,method:"POST",body:e}),invalidatesTags:(e,t,{productId:d})=>[{type:"RelatedItem",id:d}]}),getRecommendations:e.query({query:({productId:e,relationshipType:t,limit:d=10})=>{let a=`/products/${e}/recommendations?limit=${d}`;return t&&(a+=`&type=${t}`),a},providesTags:(e,t,{productId:d})=>[{type:"Recommendation",id:d}]}),getPersonalizedRecommendations:e.query({query:({userId:e,limit:t=10,categoryId:d})=>{let a=`/recommendations/personalized?limit=${t}`;return e&&(a+=`&userId=${e}`),d&&(a+=`&categoryId=${d}`),a},providesTags:(e,t,{userId:d})=>[{type:"Recommendation",id:`personalized-${d||"guest"}`}],transformResponse:e=>{if(!e){let e=Array.from({length:8}).map((e,t)=>({id:`product-${t+1}`,name:`Recommended Product ${t+1}`,description:"This is a personalized recommendation based on your browsing history and preferences.",price:19.99+10*t,compareAtPrice:t%2==0?29.99+10*t:void 0,images:[`https://source.unsplash.com/random/300x300?product&sig=${t}`],rating:4+Math.random(),reviewCount:10+Math.floor(90*Math.random()),isInStock:!0,slug:`recommended-product-${t+1}`,categoryId:"category-1",storeId:"store-1",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}));return{items:e.map((e,t)=>({id:`related-${t+1}`,sourceProductId:"source-product",targetProductId:e.id,relationshipType:r.nm.RELATED,source:"ALGORITHM",position:t+1,createdAt:new Date,updatedAt:new Date,targetProduct:e})),total:e.length}}return e}})})}),{useGetRelatedItemsQuery:i,useCreateRelatedItemMutation:n,useUpdateRelatedItemMutation:s,useDeleteRelatedItemMutation:m,useGetRecommendationRulesQuery:u,useGetRecommendationRuleQuery:l,useCreateRecommendationRuleMutation:c,useUpdateRecommendationRuleMutation:R,useDeleteRecommendationRuleMutation:T,useGenerateRecommendationsMutation:p,useGetRecommendationsQuery:y,useGetPersonalizedRecommendationsQuery:g}=o},77354:(e,t,d)=>{var a,r,o;d.d(t,{nm:()=>a}),function(e){e.RELATED="RELATED",e.UPSELL="UPSELL",e.CROSS_SELL="CROSS_SELL",e.ACCESSORY="ACCESSORY",e.ALTERNATIVE="ALTERNATIVE",e.BUNDLE="BUNDLE"}(a||(a={})),function(e){e.MANUAL="MANUAL",e.ALGORITHM="ALGORITHM",e.PURCHASE_HISTORY="PURCHASE_HISTORY",e.VIEWED_TOGETHER="VIEWED_TOGETHER",e.BOUGHT_TOGETHER="BOUGHT_TOGETHER"}(r||(r={})),function(e){e.EQUALS="EQUALS",e.NOT_EQUALS="NOT_EQUALS",e.CONTAINS="CONTAINS",e.NOT_CONTAINS="NOT_CONTAINS",e.GREATER_THAN="GREATER_THAN",e.LESS_THAN="LESS_THAN",e.IN="IN",e.NOT_IN="NOT_IN"}(o||(o={}))}};