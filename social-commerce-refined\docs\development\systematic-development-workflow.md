# Systematic Development Workflow

## Overview

This document outlines the systematic approach for developing microservices in the Social Commerce Platform, based on lessons learned from successful implementations.

## Core Principles

### 1. Implementation-First Approach
- **Build working code first**, then extract guidelines
- Avoid over-planning without practical validation
- Test integration early to validate architecture

### 2. Systematic Problem Solving
- **Identify root causes** before implementing solutions
- Avoid temporary workarounds that compromise architecture
- Document issues and solutions for future reference

### 3. Incremental Development
- Break complex tasks into micro-steps with verification points
- Test each component individually before integration
- Maintain working state at each step

## Service Development Workflow

### Phase 1: Analysis and Planning

#### 1.1 Requirement Analysis
```bash
# Questions to answer:
- What is the core functionality needed?
- Which services does this depend on?
- Which services will depend on this?
- What are the data requirements?
- What are the communication patterns?
```

#### 1.2 Dependency Mapping
```bash
# Create dependency diagram:
Service A → Service B (via RabbitMQ)
Service A → Database (direct connection)
Service A → External API (HTTP)
```

#### 1.3 Architecture Validation
```bash
# Verify against platform guidelines:
- Follows naming conventions?
- Fits microservices patterns?
- Maintains service boundaries?
- Supports independent deployment?
```

### Phase 2: Service Implementation

#### 2.1 Service Structure Creation
```bash
# Create from scratch (DO NOT copy existing services)
mkdir services/new-service
cd services/new-service

# Use existing services as REFERENCE ONLY
# Implement service-specific functionality
```

#### 2.2 Core Implementation Order
```bash
1. package.json (dependencies)
2. tsconfig.json (TypeScript config)
3. main.ts (application entry)
4. app.module.ts (root module)
5. Feature modules (domain-specific)
6. DTOs and entities
7. Services and controllers
8. Health checks
```

#### 2.3 Naming Convention Application
```bash
# Database
DB_NAME: social_commerce_[service]_db

# Docker Image
IMAGE: social-commerce-refined-[service]-service

# Container
CONTAINER: social-commerce-[service]-service

# Port (increment from 3000)
PORT: 300X

# Queue
QUEUE: [service]_queue
```

### Phase 3: Docker Integration

#### 3.1 Dockerfile Creation
```dockerfile
# Multi-stage build pattern
FROM node:18-alpine AS build
WORKDIR /app
COPY services/[service]/package.json ./
RUN npm install
COPY services/[service]/src ./src
COPY services/[service]/tsconfig.json ./
COPY services/[service]/nest-cli.json ./
RUN npm run build

FROM node:18-alpine
WORKDIR /app
COPY services/[service]/package.json ./
RUN npm install --only=production
COPY --from=build /app/dist ./dist
EXPOSE [PORT]
CMD ["node", "dist/main.js"]
```

#### 3.2 Docker Compose Integration
```yaml
[service]-service:
  build:
    context: .
    dockerfile: services/[service]/Dockerfile
  container_name: social-commerce-[service]-service
  environment:
    NODE_ENV: development
    HTTP_PORT: [PORT]
    RABBITMQ_URL: amqp://admin:admin@rabbitmq:5672
    [SERVICE]_QUEUE: [service]_queue
  ports:
    - "[PORT]:[PORT]"
  depends_on:
    postgres:
      condition: service_healthy
    rabbitmq:
      condition: service_healthy
  networks:
    - social-commerce-network
  healthcheck:
    test: ["CMD", "curl", "-f", "http://localhost:[PORT]/health"]
    interval: 30s
    timeout: 10s
    retries: 3
    start_period: 30s
```

### Phase 4: Testing and Validation

#### 4.1 Individual Service Testing
```bash
# Build test
docker-compose build [service]-service

# Startup test
docker-compose up -d [service]-service

# Health check test
curl http://localhost:[PORT]/health

# Logs verification
docker-compose logs [service]-service
```

#### 4.2 Integration Testing
```bash
# Start dependencies first
docker-compose up -d postgres rabbitmq

# Start service
docker-compose up -d [service]-service

# Test service communication
# (Use appropriate test methods)
```

#### 4.3 End-to-End Testing
```bash
# Start all related services
docker-compose up -d [service1] [service2] [service3]

# Test complete user journeys
# Verify data flow between services
# Check error handling and recovery
```

## Microservices Communication Setup

### 1. ClientsModule Configuration
```typescript
// In app.module.ts
import { ClientsModule, Transport } from '@nestjs/microservices';

@Module({
  imports: [
    ClientsModule.registerAsync([
      {
        name: 'TARGET_SERVICE',
        imports: [ConfigModule],
        inject: [ConfigService],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.RMQ,
          options: {
            urls: [configService.get<string>('RABBITMQ_URL')],
            queue: configService.get<string>('TARGET_QUEUE'),
            queueOptions: { durable: true },
          },
        }),
      },
    ]),
  ],
})
```

### 2. Service Injection
```typescript
// In service class
import { Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';

@Injectable()
export class MyService {
  constructor(
    @Inject('TARGET_SERVICE') private readonly targetService: ClientProxy,
  ) {}

  async callOtherService(data: any) {
    return firstValueFrom(
      this.targetService.send('pattern.name', data).pipe(timeout(5000))
    );
  }
}
```

### 3. Message Pattern Implementation
```typescript
// In target service
import { MessagePattern } from '@nestjs/microservices';

@Injectable()
export class TargetService {
  @MessagePattern('pattern.name')
  async handleMessage(data: any) {
    // Process the message
    return { success: true, result: data };
  }
}
```

## Error Handling and Recovery

### 1. Build Error Resolution
```bash
# Common build errors and solutions:

# Missing dependencies
ERROR: Cannot find module '@nestjs/terminus'
SOLUTION: Add to package.json dependencies

# Incorrect imports
ERROR: Cannot find name 'ClientsModule'
SOLUTION: Uncomment import statement

# Wrong method names
ERROR: Property 'createTransporter' does not exist
SOLUTION: Check API documentation for correct names
```

### 2. Runtime Error Handling
```typescript
// Service communication with error handling
async callExternalService(data: any) {
  try {
    return await firstValueFrom(
      this.client.send('pattern', data).pipe(timeout(5000))
    );
  } catch (error) {
    this.logger.error(`Service call failed: ${error.message}`);
    
    // Implement fallback strategy
    if (error.name === 'TimeoutError') {
      throw new ServiceUnavailableException('Service timeout');
    }
    
    throw new BadRequestException('Service communication failed');
  }
}
```

### 3. Graceful Degradation
```typescript
// Continue operation when optional services are unavailable
async sendNotification(data: NotificationData) {
  try {
    await this.notificationService.send('notify', data);
  } catch (error) {
    this.logger.warn('Notification service unavailable, logging instead');
    this.logger.log(`Notification: ${JSON.stringify(data)}`);
  }
}
```

## Quality Assurance Checklist

### Pre-Implementation
- [ ] Requirements clearly defined
- [ ] Dependencies identified and available
- [ ] Architecture validated against guidelines
- [ ] Naming conventions planned

### During Implementation
- [ ] Code follows established patterns
- [ ] Error handling implemented
- [ ] Logging added for debugging
- [ ] Health checks included

### Pre-Deployment
- [ ] Individual service tests pass
- [ ] Integration tests pass
- [ ] Docker build successful
- [ ] Documentation updated

### Post-Deployment
- [ ] Service health verified
- [ ] Communication patterns working
- [ ] Performance acceptable
- [ ] Monitoring configured

## Related Documentation
- [Microservices Dependency Resolution](./microservices-dependency-resolution.md)
- [Microservices Troubleshooting](./microservices-troubleshooting.md)
- [Service Architecture Guidelines](../architecture/service-guidelines.md)
- [Docker Development Workflow](./docker-development.md)
