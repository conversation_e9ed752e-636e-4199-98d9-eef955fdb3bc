{"name": "@app/messaging", "version": "1.0.0", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@nestjs/common": "^11.1.1", "@nestjs/core": "^11.1.1", "@nestjs/microservices": "^11.1.1", "amqplib": "^0.10.8", "rxjs": "^7.8.2", "uuid": "^11.1.0"}, "devDependencies": {"@types/amqplib": "^0.10.7", "@types/node": "^22.15.21", "typescript": "^5.8.3"}}