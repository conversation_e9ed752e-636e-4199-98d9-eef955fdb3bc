"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var MessagingModule_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessagingModule = void 0;
const common_1 = require("@nestjs/common");
const rabbitmq_module_1 = require("./rabbitmq/rabbitmq.module");
const event_publisher_module_1 = require("./rabbitmq/event-publisher.module");
const event_subscriber_module_1 = require("./rabbitmq/event-subscriber.module");
let MessagingModule = MessagingModule_1 = class MessagingModule {
    static register(options) {
        return {
            module: MessagingModule_1,
            imports: [
                rabbitmq_module_1.RabbitMQModule.register({
                    url: (options === null || options === void 0 ? void 0 : options.rabbitmqUrl) || 'amqp://admin:admin@localhost:5672',
                }),
                event_publisher_module_1.EventPublisherModule,
                event_subscriber_module_1.EventSubscriberModule,
            ],
            exports: [
                rabbitmq_module_1.RabbitMQModule,
                event_publisher_module_1.EventPublisherModule,
                event_subscriber_module_1.EventSubscriberModule,
            ],
        };
    }
    static registerAsync(options) {
        return {
            module: MessagingModule_1,
            imports: [
                rabbitmq_module_1.RabbitMQModule.registerAsync({
                    useFactory: async (...args) => {
                        const config = await options.useFactory(...args);
                        return {
                            url: config.rabbitmqUrl || 'amqp://admin:admin@localhost:5672',
                        };
                    },
                    inject: options.inject || [],
                }),
                event_publisher_module_1.EventPublisherModule,
                event_subscriber_module_1.EventSubscriberModule,
            ],
            exports: [
                rabbitmq_module_1.RabbitMQModule,
                event_publisher_module_1.EventPublisherModule,
                event_subscriber_module_1.EventSubscriberModule,
            ],
        };
    }
};
exports.MessagingModule = MessagingModule;
exports.MessagingModule = MessagingModule = MessagingModule_1 = __decorate([
    (0, common_1.Module)({})
], MessagingModule);
//# sourceMappingURL=messaging.module.js.map