(()=>{var e={};e.id=3443,e.ids=[3443],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},7145:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>l.a,__next_app__:()=>g,originalPathname:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>n});var a=s(67096),t=s(16132),i=s(37284),l=s.n(i),o=s(32564),d={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);s.d(r,d);let n=["",{children:["profile",{children:["[username]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,69613)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\profile\\[username]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9291,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\profile\\[username]\\page.tsx"],m="/profile/[username]/page",g={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/profile/[username]/page",pathname:"/profile/[username]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},84689:(e,r,s)=>{Promise.resolve().then(s.bind(s,55825))},55825:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>ProfilePage});var a=s(30784),t=s(9885),i=s(57114),l=s(27870),o=s(52451),d=s.n(o),n=s(59872);let profile_ProfileHeader=({profile:e,onFollow:r,onUnfollow:s,isFollowing:t=!1,isLoading:i=!1})=>(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden",children:[a.jsx("div",{className:"relative h-48 bg-gray-200 dark:bg-gray-700",children:e.coverImageUrl?a.jsx(d(),{src:e.coverImageUrl,alt:`${e.displayName||e.username}'s cover`,fill:!0,className:"object-cover"}):a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-primary-500 to-secondary-500 opacity-75"})}),(0,a.jsxs)("div",{className:"px-6 py-4",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-end -mt-16 mb-4",children:[a.jsx("div",{className:"relative h-24 w-24 rounded-full border-4 border-white dark:border-gray-800 overflow-hidden bg-gray-200 dark:bg-gray-700",children:e.profileImageUrl?a.jsx(d(),{src:e.profileImageUrl,alt:e.displayName||e.username,fill:!0,className:"object-cover"}):a.jsx("div",{className:"flex items-center justify-center h-full text-2xl font-bold text-gray-500",children:(e.displayName||e.username).charAt(0).toUpperCase()})}),a.jsx("div",{className:"mt-4 sm:mt-0 sm:ml-4 flex-1",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-2xl font-bold flex items-center",children:[e.displayName||e.username,e.isVerified&&a.jsx("svg",{className:"ml-1 h-5 w-5 text-primary-500",fill:"currentColor",viewBox:"0 0 20 20",children:a.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})]}),(0,a.jsxs)("p",{className:"text-gray-500 dark:text-gray-400",children:["@",e.username]})]}),!e.isCurrentUser&&(r||s)&&a.jsx("div",{className:"mt-3 sm:mt-0",children:a.jsx(n.Z,{onClick:()=>{t?s?.():r?.()},variant:t?"outline":"primary",isLoading:i,children:t?"Unfollow":"Follow"})})]})})]}),e.bio&&a.jsx("p",{className:"text-gray-700 dark:text-gray-300 mt-2",children:e.bio}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-4 mt-4",children:[(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-bold",children:e.followersCount})," ",a.jsx("span",{className:"text-gray-500 dark:text-gray-400",children:"Followers"})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-bold",children:e.followingCount})," ",a.jsx("span",{className:"text-gray-500 dark:text-gray-400",children:"Following"})]}),e.stats&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-bold",children:e.stats.productsCount})," ",a.jsx("span",{className:"text-gray-500 dark:text-gray-400",children:"Products"})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-bold",children:e.stats.storesCount})," ",a.jsx("span",{className:"text-gray-500 dark:text-gray-400",children:"Stores"})]})]})]}),e.socialLinks&&Object.values(e.socialLinks).some(e=>!!e)&&a.jsx("div",{className:"flex gap-3 mt-4",children:e.socialLinks.website&&a.jsx("a",{href:e.socialLinks.website,target:"_blank",rel:"noopener noreferrer",className:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300",children:a.jsx("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 24 24",children:a.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"})})})})]})]}),profile_ProfileTabs=({activeTab:e,onTabChange:r,tabs:s=["activity","products","stores","connections"],counts:t})=>{let getTabLabel=e=>{switch(e){case"activity":return`Activity${t?.activity?` (${t.activity})`:""}`;case"products":return`Products${t?.products?` (${t.products})`:""}`;case"stores":return`Stores${t?.stores?` (${t.stores})`:""}`;case"followers":return`Followers${t?.followers?` (${t.followers})`:""}`;case"following":return`Following${t?.following?` (${t.following})`:""}`;case"likes":return`Likes${t?.likes?` (${t.likes})`:""}`;case"connections":return`Connections${t?.followers&&t?.following?` (${t.followers+t.following})`:""}`;default:return String(e).charAt(0).toUpperCase()+String(e).slice(1)}};return a.jsx("div",{className:"border-b border-gray-200 dark:border-gray-700",children:a.jsx("nav",{className:"flex overflow-x-auto",children:s.map(s=>a.jsx("button",{onClick:()=>r(s),className:`py-4 px-6 text-center border-b-2 font-medium text-sm whitespace-nowrap ${e===s?"border-primary-500 text-primary-600 dark:text-primary-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"}`,children:getTabLabel(s)},s))})})},profile_ProfileAchievements=({profile:e,achievements:r=[],isLoading:s=!1,className:t=""})=>{let{t:i}=(0,l.$G)("profile"),o=r.length>0?r:[{id:"1",title:"Early Adopter",description:"Joined during the platform's first year",iconUrl:"/images/achievements/early-adopter.svg",earnedAt:new Date(e.createdAt).getFullYear()<=new Date().getFullYear()-1?e.createdAt:void 0},{id:"2",title:"Social Butterfly",description:"Connected with 10+ users",iconUrl:"/images/achievements/social-butterfly.svg",progress:Math.min(e.followersCount,10),maxProgress:10,earnedAt:e.followersCount>=10?new Date().toISOString():void 0},{id:"3",title:"Content Creator",description:"Created 5+ products",iconUrl:"/images/achievements/content-creator.svg",progress:Math.min(e.stats?.productsCount||0,5),maxProgress:5,earnedAt:(e.stats?.productsCount||0)>=5?new Date().toISOString():void 0},{id:"4",title:"Trendsetter",description:"Received 50+ likes on products",iconUrl:"/images/achievements/trendsetter.svg",progress:Math.min(e.stats?.likesCount||0,50),maxProgress:50,earnedAt:(e.stats?.likesCount||0)>=50?new Date().toISOString():void 0},{id:"5",title:"Store Owner",description:"Created a store",iconUrl:"/images/achievements/store-owner.svg",earnedAt:(e.stats?.storesCount||0)>0?new Date().toISOString():void 0}];return s?(0,a.jsxs)("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 ${t}`,children:[a.jsx("div",{className:"h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-6 animate-pulse"}),a.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[...Array(6)].map((e,r)=>(0,a.jsxs)("div",{className:"bg-gray-100 dark:bg-gray-700 rounded-lg p-4 animate-pulse",children:[a.jsx("div",{className:"h-12 w-12 bg-gray-200 dark:bg-gray-600 rounded-full mx-auto mb-3"}),a.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-600 rounded w-3/4 mx-auto mb-2"}),a.jsx("div",{className:"h-3 bg-gray-200 dark:bg-gray-600 rounded w-full mx-auto"})]},r))})]}):(0,a.jsxs)("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 ${t}`,children:[a.jsx("h2",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4",children:i("achievements","Achievements")}),a.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:o.map(e=>{let r=!!e.earnedAt,s=e.progress&&e.maxProgress?e.progress/e.maxProgress*100:0;return(0,a.jsxs)("div",{className:`rounded-lg p-4 text-center ${r?"bg-primary-50 dark:bg-primary-900/20":"bg-gray-100 dark:bg-gray-700"}`,children:[a.jsx("div",{className:"relative h-16 w-16 mx-auto mb-3",children:a.jsx("div",{className:`absolute inset-0 rounded-full ${r?"opacity-100":"opacity-40 grayscale"}`,children:a.jsx(d(),{src:e.iconUrl,alt:e.title,fill:!0,className:"object-contain",onError:e=>{e.target.src="/images/achievements/default.svg"}})})}),a.jsx("h3",{className:`font-medium ${r?"text-primary-700 dark:text-primary-300":"text-gray-500 dark:text-gray-400"}`,children:e.title}),a.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:e.description}),void 0!==e.progress&&void 0!==e.maxProgress&&(0,a.jsxs)("div",{className:"mt-2",children:[a.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-1.5",children:a.jsx("div",{className:"bg-primary-500 h-1.5 rounded-full",style:{width:`${s}%`}})}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:[e.progress,"/",e.maxProgress]})]}),r&&(0,a.jsxs)("p",{className:"text-xs text-primary-600 dark:text-primary-400 mt-2",children:[i("earned","Earned"),": ",new Date(e.earnedAt).toLocaleDateString()]})]},e.id)})})]})},profile_ProfileInterests=({profile:e,interests:r,isCurrentUser:s=!1,onUpdateInterests:i,isLoading:o=!1,className:d=""})=>{let{t:c}=(0,l.$G)("profile"),[m,g]=(0,t.useState)(!1),[x,u]=(0,t.useState)(!1),y=[{id:"1",name:"Technology",category:"general"},{id:"2",name:"Fashion",category:"general"},{id:"3",name:"Home Decor",category:"general"},{id:"4",name:"Fitness",category:"general"},{id:"5",name:"Books",category:"general"}],p=[...y,{id:"6",name:"Gaming",category:"entertainment"},{id:"7",name:"Cooking",category:"lifestyle"},{id:"8",name:"Travel",category:"lifestyle"},{id:"9",name:"Photography",category:"arts"},{id:"10",name:"Music",category:"entertainment"},{id:"11",name:"Movies",category:"entertainment"},{id:"12",name:"Sports",category:"lifestyle"},{id:"13",name:"Art",category:"arts"},{id:"14",name:"DIY",category:"lifestyle"},{id:"15",name:"Beauty",category:"lifestyle"}],[h,f]=(0,t.useState)(r?r.map(e=>e.id):y.slice(0,3).map(e=>e.id)),v=p.reduce((e,r)=>(e[r.category]||(e[r.category]=[]),e[r.category].push(r),e),{}),handleInterestToggle=e=>{f(r=>r.includes(e)?r.filter(r=>r!==e):[...r,e])},handleSave=async()=>{if(i){u(!0);try{await i(h),g(!1)}catch(e){console.error("Failed to update interests:",e)}finally{u(!1)}}},j=r||y.filter(e=>h.includes(e.id));return o?(0,a.jsxs)("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 ${d}`,children:[a.jsx("div",{className:"h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-6 animate-pulse"}),a.jsx("div",{className:"flex flex-wrap gap-2",children:[void 0,void 0,void 0,void 0,void 0].map((e,r)=>a.jsx("div",{className:"h-8 bg-gray-200 dark:bg-gray-700 rounded-full px-4 animate-pulse"},r))})]}):(0,a.jsxs)("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 ${d}`,children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[a.jsx("h2",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100",children:c("interests","Interests")}),s&&!m&&a.jsx(n.Z,{variant:"outline",size:"sm",onClick:()=>g(!0),children:c("edit","Edit")})]}),m?(0,a.jsxs)("div",{children:[Object.entries(v).map(([e,r])=>(0,a.jsxs)("div",{className:"mb-4",children:[a.jsx("h3",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 capitalize mb-2",children:c(`categories.${e}`,e)}),a.jsx("div",{className:"flex flex-wrap gap-2",children:r.map(e=>a.jsx("button",{onClick:()=>handleInterestToggle(e.id),className:`px-3 py-1 rounded-full text-sm font-medium transition-colors ${h.includes(e.id)?"bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600"}`,children:e.name},e.id))})]},e)),(0,a.jsxs)("div",{className:"flex justify-end mt-4 space-x-2",children:[a.jsx(n.Z,{variant:"outline",size:"sm",onClick:()=>g(!1),disabled:x,children:c("cancel","Cancel")}),a.jsx(n.Z,{size:"sm",onClick:handleSave,isLoading:x,disabled:x,children:c("save","Save")})]})]}):a.jsx("div",{className:"flex flex-wrap gap-2",children:j.length>0?j.map(e=>a.jsx("span",{className:"px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-full text-sm",children:e.name},e.id)):a.jsx("p",{className:"text-gray-500 dark:text-gray-400 text-sm",children:s?c("noInterestsYet","You haven't added any interests yet."):c("userNoInterests","{{username}} hasn't added any interests yet.",{username:e.displayName||e.username})})})]})};var c=s(11440),m=s.n(c);let profile_UserList=({users:e,emptyMessage:r="No users found",isLoading:s=!1,onFollow:t,onUnfollow:i,loadingFollowIds:l=[]})=>s?a.jsx("div",{className:"animate-pulse space-y-4",children:[void 0,void 0,void 0,void 0,void 0].map((e,r)=>(0,a.jsxs)("div",{className:"flex items-center p-4 border-b border-gray-200 dark:border-gray-700",children:[a.jsx("div",{className:"h-12 w-12 rounded-full bg-gray-200 dark:bg-gray-700"}),(0,a.jsxs)("div",{className:"ml-4 flex-1",children:[a.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-2"}),a.jsx("div",{className:"h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/3"})]}),a.jsx("div",{className:"h-8 w-20 bg-gray-200 dark:bg-gray-700 rounded"})]},r))}):e&&0!==e.length?a.jsx("div",{className:"divide-y divide-gray-200 dark:divide-gray-700",children:e.map(e=>(0,a.jsxs)("div",{className:"flex items-center p-4",children:[a.jsx(m(),{href:`/profile/${e.username}`,className:"flex-shrink-0",children:a.jsx("div",{className:"relative h-12 w-12 rounded-full overflow-hidden bg-gray-200 dark:bg-gray-700",children:e.profileImageUrl?a.jsx(d(),{src:e.profileImageUrl,alt:e.displayName||e.username,fill:!0,className:"object-cover"}):a.jsx("div",{className:"flex items-center justify-center h-full text-lg font-medium text-gray-500",children:(e.displayName||e.username).charAt(0).toUpperCase()})})}),(0,a.jsxs)("div",{className:"ml-4 flex-1",children:[(0,a.jsxs)(m(),{href:`/profile/${e.username}`,className:"flex items-center",children:[a.jsx("h3",{className:"font-medium",children:e.displayName||e.username}),e.isVerified&&a.jsx("svg",{className:"ml-1 h-4 w-4 text-primary-500",fill:"currentColor",viewBox:"0 0 20 20",children:a.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:["@",e.username]})]}),(t||i)&&a.jsx(n.Z,{size:"sm",variant:e.isFollowing?"outline":"primary",onClick:()=>{e.isFollowing?i?.(e.id):t?.(e.id)},isLoading:l.includes(e.id),disabled:l.includes(e.id),children:e.isFollowing?"Unfollow":"Follow"})]},e.id))}):a.jsx("div",{className:"text-center py-8 text-gray-500 dark:text-gray-400",children:r});var g=s(64564),x=s(14379),u=s(3902),y=s(53838);let social_UserActivityFeed=({userId:e,isCurrentUser:r=!1,className:s=""})=>{let{t:i}=(0,l.$G)("social"),{isRtl:o}=(0,x.g)(),[d,c]=(0,t.useState)(1),getActivityText=e=>{switch(e.type){case u.T8.PRODUCT_VIEW:return i("activity.productView","viewed a product");case u.T8.PRODUCT_PURCHASE:return i("activity.productPurchase","purchased a product");case u.T8.PRODUCT_REVIEW:return i("activity.productReview","reviewed a product");case u.T8.PRODUCT_SHARE:return i("activity.productShare","shared a product");case u.T8.PRODUCT_LIKE:return i("activity.productLike","liked a product");case u.T8.PRODUCT_COMMENT:return i("activity.productComment","commented on a product");case u.T8.STORE_FOLLOW:return i("activity.storeFollow","followed a store");case u.T8.USER_FOLLOW:return i("activity.userFollow","followed a user");case u.T8.GROUP_JOIN:return i("activity.groupJoin","joined a group");case u.T8.GROUP_CREATE:return i("activity.groupCreate","created a group");default:return""}},{data:m,isLoading:g,error:p,isFetching:h}=(0,y.Bh)({userId:e,page:d,limit:10});return g&&!m?a.jsx("div",{className:`flex items-center justify-center py-12 ${s}`,children:a.jsx("div",{className:"animate-spin rounded-full h-10 w-10 border-b-2 border-primary-500"})}):p?(0,a.jsxs)("div",{className:`flex flex-col items-center justify-center py-12 ${s}`,children:[a.jsx("p",{className:"text-red-500 dark:text-red-400 mb-4",children:i("activity.errorLoading","Error loading activity feed")}),a.jsx(n.Z,{variant:"outline",onClick:()=>c(1),children:i("common.retry","Retry")})]}):m&&0!==m.activities.length?(0,a.jsxs)("div",{className:`${s}`,children:[a.jsx("ul",{className:"space-y-6",children:m.activities.map(e=>a.jsx("li",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4",children:(0,a.jsxs)("div",{className:"flex",children:[a.jsx("div",{className:"flex-shrink-0 mr-4",children:a.jsx("div",{className:"h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-700 overflow-hidden",children:e.user?.avatarUrl&&a.jsx("img",{src:e.user.avatarUrl,alt:e.user.displayName,className:"h-full w-full object-cover"})})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center mb-1",children:[a.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e.user?.displayName||"Unknown User"}),a.jsx("span",{className:"mx-1 text-gray-500 dark:text-gray-400",children:"•"}),a.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:new Date(e.createdAt).toLocaleDateString(void 0,{year:"numeric",month:"short",day:"numeric"})})]}),a.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:getActivityText(e)}),"product"===e.entityType&&a.jsx("div",{className:"mt-3 p-3 border border-gray-200 dark:border-gray-700 rounded-md",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"flex-shrink-0 mr-3",children:a.jsx("div",{className:"h-12 w-12 bg-gray-200 dark:bg-gray-700 rounded-md"})}),(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:"Product Name"}),a.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"$XX.XX"})]})]})}),(0,a.jsxs)("div",{className:"mt-3 flex items-center space-x-4",children:[a.jsx("button",{className:"text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300",children:i("common.like","Like")}),a.jsx("button",{className:"text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300",children:i("common.comment","Comment")}),a.jsx("button",{className:"text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300",children:i("common.share","Share")}),r&&a.jsx("button",{className:"text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 ml-auto",children:i("common.delete","Delete")})]})]})]})},e.id))}),m.activities.length<m.total&&a.jsx("div",{className:"flex justify-center mt-6",children:a.jsx(n.Z,{variant:"outline",onClick:()=>{c(d+1)},isLoading:h,disabled:h,children:i("common.loadMore","Load More")})})]}):a.jsx("div",{className:`flex flex-col items-center justify-center py-12 ${s}`,children:a.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:r?i("activity.noActivity","No activity yet"):i("activity.userNoActivity","This user has no activity yet")})})},social_UserFollowButton=({userId:e,status:r,onFollow:s,onUnfollow:t,isLoading:i=!1,size:o="md",className:d=""})=>{let{t:c}=(0,l.$G)("social"),{isRtl:m}=(0,x.g)();return a.jsx(n.Z,{variant:(()=>{switch(r){case u.QK.FOLLOWING:case u.QK.PENDING:return"outline";default:return"primary"}})(),className:`${(()=>{switch(o){case"sm":return"text-xs px-2 py-1";case"lg":return"text-base px-4 py-2";default:return"text-sm px-3 py-1.5"}})()} ${d}`,onClick:r?()=>{t(e)}:()=>{s(e)},isLoading:i,disabled:i,children:(()=>{switch(r){case u.QK.FOLLOWING:return c("following","Following");case u.QK.PENDING:return c("requested","Requested");default:return c("follow","Follow")}})()})},social_UserConnectionList=({connections:e,currentUserId:r,type:s,isLoading:t=!1,hasMore:i=!1,onLoadMore:o,onFollow:c,onUnfollow:g,className:u=""})=>{let{t:y}=(0,l.$G)("social"),{isRtl:p}=(0,x.g)();return t&&0===e.length?a.jsx("div",{className:`space-y-4 ${u}`,children:Array.from({length:5}).map((e,r)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm animate-pulse",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full"}),(0,a.jsxs)("div",{className:"ml-3",children:[a.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-24 mb-2"}),a.jsx("div",{className:"h-3 bg-gray-200 dark:bg-gray-700 rounded w-16"})]})]}),a.jsx("div",{className:"w-20 h-8 bg-gray-200 dark:bg-gray-700 rounded"})]},r))}):0===e.length?(0,a.jsxs)("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 text-center ${u}`,children:[a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-12 w-12 text-gray-400 dark:text-gray-600 mx-auto mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"})}),a.jsx("p",{className:"text-gray-500 dark:text-gray-400 mb-4",children:"followers"===s?y("noFollowers","No followers yet"):y("notFollowingAnyone","Not following anyone yet")})]}):(0,a.jsxs)("div",{className:u,children:[a.jsx("div",{className:"space-y-4",children:e.map(e=>{let t="followers"===s?e.follower:e.following;if(!t)return null;let i=t.id===r,l=e.status||null;return(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm",children:[(0,a.jsxs)(m(),{href:`/profile/${t.username}`,className:"flex items-center",children:[a.jsx("div",{className:"w-10 h-10 relative rounded-full overflow-hidden",children:a.jsx(d(),{src:t.avatarUrl||"/images/default-avatar.png",alt:t.displayName||t.username,fill:!0,className:"object-cover"})}),(0,a.jsxs)("div",{className:"ml-3",children:[a.jsx("h3",{className:"font-medium text-gray-900 dark:text-gray-100",children:t.displayName||t.username}),(0,a.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:["@",t.username]})]})]}),!i&&c&&g&&a.jsx(social_UserFollowButton,{userId:t.id,status:l,onFollow:c,onUnfollow:g,size:"sm"})]},e.id)})}),i&&a.jsx("div",{className:"flex justify-center mt-6",children:a.jsx(n.Z,{variant:"outline",onClick:o,isLoading:t,disabled:t,children:y("loadMore","Load More")})})]})};var p=s(51020);let social_UserProfileConnections=({userId:e,currentUserId:r,className:s=""})=>{let{t:i}=(0,l.$G)("social"),{isRtl:o}=(0,x.g)(),[d,n]=(0,t.useState)("followers"),[c,m]=(0,t.useState)(1),{data:g,isLoading:y}=(0,p.xG)({userId:e,status:"followers"===d?u.QK.FOLLOWER:u.QK.FOLLOWING,page:c,limit:10}),[h,{isLoading:f}]=(0,p.Mk)(),[v,{isLoading:j}]=(0,p.$i)(),handleFollow=async e=>{try{await h(e).unwrap()}catch(e){console.error("Failed to follow user:",e)}},handleUnfollow=async e=>{try{await v(e).unwrap()}catch(e){console.error("Failed to unfollow user:",e)}};return(0,a.jsxs)("div",{className:s,children:[(0,a.jsxs)("div",{className:"flex border-b border-gray-200 dark:border-gray-700 mb-6",children:[(0,a.jsxs)("button",{className:`py-4 px-6 text-center border-b-2 font-medium text-sm ${"followers"===d?"border-primary-500 text-primary-600 dark:text-primary-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600"}`,onClick:()=>{n("followers"),m(1)},children:[i("followers","Followers"),g?.total&&"followers"===d&&` (${g.total})`]}),(0,a.jsxs)("button",{className:`py-4 px-6 text-center border-b-2 font-medium text-sm ${"following"===d?"border-primary-500 text-primary-600 dark:text-primary-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600"}`,onClick:()=>{n("following"),m(1)},children:[i("following","Following"),g?.total&&"following"===d&&` (${g.total})`]})]}),a.jsx(social_UserConnectionList,{connections:g?.connections||[],currentUserId:r,type:d,isLoading:y,hasMore:!!g&&g.total>10*c,onLoadMore:()=>{m(e=>e+1)},onFollow:handleFollow,onUnfollow:handleUnfollow})]})};var h=s(19923),f=s(86372);let v=f.g.injectEndpoints({endpoints:e=>({getCurrentUserProfile:e.query({query:()=>"/user/profile",providesTags:["UserProfile"]}),getUserProfile:e.query({query:e=>`/users/${e}/profile`,providesTags:(e,r,s)=>[{type:"UserProfile",id:s}]}),getUserProfileByUsername:e.query({query:e=>`/users/username/${e}/profile`,providesTags:(e,r,s)=>[{type:"UserProfile",id:`username-${s}`}]}),updateUserProfile:e.mutation({query:e=>({url:"/user/profile",method:"PATCH",body:e}),invalidatesTags:["UserProfile"]}),uploadProfileAvatar:e.mutation({query:e=>({url:"/user/profile/avatar",method:"POST",body:e}),invalidatesTags:["UserProfile"]}),uploadProfileCoverImage:e.mutation({query:e=>({url:"/user/profile/cover",method:"POST",body:e}),invalidatesTags:["UserProfile"]}),searchUserProfiles:e.query({query:({query:e,page:r=1,limit:s=10})=>`/users/search?query=${encodeURIComponent(e)}&page=${r}&limit=${s}`,providesTags:["UserProfile"]}),getSuggestedProfiles:e.query({query:({page:e=1,limit:r=10})=>`/users/suggested?page=${e}&limit=${r}`,providesTags:["UserProfile"]})})}),{useGetCurrentUserProfileQuery:j,useGetUserProfileQuery:w,useGetUserProfileByUsernameQuery:b,useUpdateUserProfileMutation:N,useUploadProfileAvatarMutation:k,useUploadProfileCoverImageMutation:U,useSearchUserProfilesQuery:C,useLazySearchUserProfilesQuery:P,useGetSuggestedProfilesQuery:$}=v;function ProfilePage({params:e}){let{username:r}=e,s=(0,i.useRouter)(),{t:o}=(0,l.$G)("social"),[d,c]=(0,t.useState)("activity"),m="current-user-username"===r,{data:x,isLoading:u}=(0,h.PF)(r),{data:y,isLoading:p}=(0,h.Fb)(r,{skip:"followers"!==d}),{data:f,isLoading:v}=(0,h.QC)(r,{skip:"following"!==d}),{data:j,isLoading:w}=b(r,{skip:"activity"!==d}),[N,{isLoading:k}]=(0,h.Mk)(),[U,{isLoading:C}]=(0,h.$i)(),[P,{isLoading:$}]=(0,h.useUpdateUserInterestsMutation)(),handleFollow=async()=>{if(x)try{await N(r).unwrap()}catch(e){console.error("Failed to follow user:",e)}},handleUnfollow=async()=>{if(x)try{await U(r).unwrap()}catch(e){console.error("Failed to unfollow user:",e)}},handleUpdateInterests=async e=>{if(x)try{await P({username:r,interests:e}).unwrap()}catch(e){throw console.error("Failed to update interests:",e),e}},L=[{id:"1",title:"Wireless Earbuds",price:49.99,mediaUrls:["https://images.unsplash.com/photo-1572569511254-d8f925fe2cbb?ixlib=rb-4.0.3&auto=format&fit=crop&w=1400&q=80"],likeCount:24,commentCount:5,storeId:"1",postType:"product",isActive:!0,affiliateEnabled:!1,description:"Wireless earbuds with noise cancellation",slug:"wireless-earbuds",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{id:"2",title:"Smart Watch",price:89.99,mediaUrls:["https://images.unsplash.com/photo-1579586337278-3befd40fd17a?ixlib=rb-4.0.3&auto=format&fit=crop&w=1400&q=80"],likeCount:18,commentCount:3,storeId:"1",postType:"product",isActive:!0,affiliateEnabled:!1,description:"Smart watch with health tracking features",slug:"smart-watch",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}],_=[{id:"1",name:"Tech Gadgets",imageUrl:"https://images.unsplash.com/photo-1531297484001-80022131f5a1?ixlib=rb-4.0.3&auto=format&fit=crop&w=1400&q=80",productsCount:12,followersCount:45},{id:"2",name:"Home Decor",imageUrl:"https://images.unsplash.com/photo-1513694203232-719a280e022f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1400&q=80",productsCount:8,followersCount:32}];return u?a.jsx("div",{className:"min-h-screen p-6 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600 mx-auto"}),a.jsx("p",{className:"mt-4 text-lg",children:"Loading profile..."})]})}):x?a.jsx("div",{className:"min-h-screen p-6",children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[a.jsx(profile_ProfileHeader,{profile:x,onFollow:handleFollow,onUnfollow:handleUnfollow,isLoading:k||C}),a.jsx("div",{className:"mt-6",children:a.jsx(profile_ProfileInterests,{profile:x,isCurrentUser:m,onUpdateInterests:handleUpdateInterests,isLoading:$})}),a.jsx("div",{className:"mt-6",children:a.jsx(profile_ProfileAchievements,{profile:x,isLoading:!1})}),(0,a.jsxs)("div",{className:"mt-6 bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden",children:[a.jsx(profile_ProfileTabs,{activeTab:d,onTabChange:c,counts:{products:L.length,stores:_.length,followers:x.followersCount,following:x.followingCount}}),(0,a.jsxs)("div",{className:"p-6",children:["activity"===d&&a.jsx(a.Fragment,{children:w?a.jsx("div",{className:"flex items-center justify-center py-12",children:a.jsx("div",{className:"animate-spin rounded-full h-10 w-10 border-b-2 border-primary-500"})}):j?a.jsx(social_UserActivityFeed,{userId:j.userId,isCurrentUser:m}):a.jsx("div",{className:"text-center py-12 text-gray-500 dark:text-gray-400",children:o("activity.noActivity","No activity yet")})}),"products"===d&&a.jsx(g.Z,{products:L,emptyMessage:`${x.displayName||x.username} hasn't added any products yet.`}),"stores"===d&&a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:_.map(e=>(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm overflow-hidden",children:[a.jsx("div",{className:"h-32 bg-cover bg-center",style:{backgroundImage:`url(${e.imageUrl})`}}),(0,a.jsxs)("div",{className:"p-4",children:[a.jsx("h3",{className:"text-lg font-semibold",children:e.name}),(0,a.jsxs)("div",{className:"mt-2 flex text-sm text-gray-500 dark:text-gray-400",children:[(0,a.jsxs)("span",{className:"mr-4",children:[e.productsCount," products"]}),(0,a.jsxs)("span",{children:[e.followersCount," followers"]})]}),a.jsx(n.Z,{className:"mt-3",size:"sm",fullWidth:!0,onClick:()=>s.push(`/stores/${e.id}`),children:"View Store"})]})]},e.id))}),"followers"===d&&a.jsx(profile_UserList,{users:y?.map(e=>({id:e.followerId,username:e.follower?.username||"",displayName:e.follower?.displayName,profileImageUrl:e.follower?.profileImageUrl,isVerified:e.follower?.isVerified||!1}))||[],isLoading:p,emptyMessage:`${x.displayName||x.username} doesn't have any followers yet.`}),"following"===d&&a.jsx(profile_UserList,{users:f?.map(e=>({id:e.followingId,username:e.following?.username||"",displayName:e.following?.displayName,profileImageUrl:e.following?.profileImageUrl,isVerified:e.following?.isVerified||!1}))||[],isLoading:v,emptyMessage:`${x.displayName||x.username} isn't following anyone yet.`}),"connections"===d&&a.jsx(social_UserProfileConnections,{userId:x.id,currentUserId:"current-user-id"})]})]})]})}):a.jsx("div",{className:"min-h-screen p-6 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:"User Not Found"}),a.jsx("p",{className:"mb-6",children:"The user you're looking for doesn't exist."}),a.jsx(n.Z,{onClick:()=>s.push("/"),children:"Back to Home"})]})})}},69613:(e,r,s)=>{"use strict";s.r(r),s.d(r,{$$typeof:()=>l,__esModule:()=>i,default:()=>d});var a=s(95153);let t=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\profile\[username]\page.tsx`),{__esModule:i,$$typeof:l}=t,o=t.default,d=o}};var r=require("../../../webpack-runtime.js");r.C(e);var __webpack_exec__=e=>r(r.s=e),s=r.X(0,[2103,2765,3902,1020,8845,3838,4564],()=>__webpack_exec__(7145));module.exports=s})();