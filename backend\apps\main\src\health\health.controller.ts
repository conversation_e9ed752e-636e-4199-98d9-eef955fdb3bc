import { Controller, Get, Inject } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom, timeout, catchError } from 'rxjs';
import { of } from 'rxjs';
import { Public } from '../decorators/public.decorator';
import { SERVICES } from '@app/common';

@ApiTags('health')
@Controller('health')
export class HealthController {
  constructor(
    @Inject(SERVICES.USER_SERVICE) private userServiceClient: ClientProxy,
  ) {}

  @Get()
  @Public()
  @ApiOperation({ summary: 'Check gateway health' })
  @ApiResponse({ status: 200, description: 'Gateway is healthy' })
  @ApiResponse({ status: 503, description: 'Gateway is unhealthy' })
  async check() {
    const gatewayStatus = {
      status: 'ok',
      service: 'main-gateway',
      timestamp: new Date().toISOString(),
      services: {},
    };

    // Check User Service
    try {
      const userServiceHealth = await firstValueFrom(
        this.userServiceClient.send('health_check', {}).pipe(
          timeout(3000),
          catchError(error => {
            return of({
              status: 'error',
              error: error.message || 'Service timeout',
            });
          })
        )
      );

      gatewayStatus.services['user-service'] = userServiceHealth.status === 'ok' 
        ? { status: 'ok' } 
        : { status: 'error', error: userServiceHealth.error };
    } catch (error) {
      gatewayStatus.services['user-service'] = { 
        status: 'error', 
        error: error.message || 'Failed to check service health' 
      };
    }

    // Determine overall status
    const hasErrors = Object.values(gatewayStatus.services).some(
      (service: any) => service.status === 'error'
    );

    if (hasErrors) {
      gatewayStatus.status = 'degraded';
    }

    return gatewayStatus;
  }
}
