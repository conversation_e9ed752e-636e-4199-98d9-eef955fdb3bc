# Build & Deployment Issues & Solutions Documentation

## Overview
This document details build and deployment issues encountered during the social commerce platform development, including Docker build problems, service startup sequence issues, port conflicts, and environment variable configuration challenges.

## Summary of Critical Issues
- **Store Service Missing Dockerfile** - Blocks complete stack deployment
- **API Proxy Circular Reference** - Frontend cannot communicate with backend
- **Weak JWT Secret** - Security vulnerability in production
- **DB Auto-Sync in Production** - Risk of data loss
- **Missing Environment Configuration** - No local development setup guide

## Impact Assessment
- **High Impact:** Store service deployment blocked, frontend-backend communication broken
- **Medium Impact:** Security vulnerabilities, missing development setup
- **Low Impact:** Build inconsistencies, messaging library disabled

## Critical Issues & Solutions

### 1. **Store Service Missing Dockerfile** ⭐ **DEPLOYMENT BLOCKER**

**Problem:** Store service referenced in Docker Compose but Dockerfile doesn't exist, preventing complete stack deployment.

**Symptoms:**
- Docker Compose build fails for store service
- Cannot start complete microservices stack
- Integration tests cannot run

**Root Cause:** 
Store service Dockerfile not created during service setup.

**Code Location (Problematic):**
```yaml
# File: docker-compose.yml (Lines 114-117)
store-service:
  build:
    context: .
    dockerfile: services/store-service/Dockerfile  # File doesn't exist
```

**Solution:**
```dockerfile
# Create: services/store-service/Dockerfile
FROM node:18-alpine AS build
WORKDIR /app
COPY services/store-service/package*.json ./
COPY libs/common ./libs/common
RUN npm install --legacy-peer-deps
COPY services/store-service/src ./src
COPY services/store-service/tsconfig.json ./
RUN npm run build

FROM node:18-alpine
WORKDIR /app
COPY services/store-service/package*.json ./
RUN npm install --only=production --legacy-peer-deps
COPY --from=build /app/dist ./dist
COPY --from=build /app/libs ./libs
ENV NODE_ENV=production
EXPOSE 3002
CMD ["node", "dist/main"]
```

**Impact:** 
- ✅ Enables complete stack deployment
- ✅ Allows integration testing
- ✅ Completes microservices architecture

**Status:** ⚠️ **MISSING FILE** - Blocks deployment

### 2. **API Proxy Circular Reference** ⭐ **FRONTEND BLOCKER**

**Problem:** Frontend API proxy points to same port as frontend, creating circular reference.

**Symptoms:**
- Frontend API calls fail or loop infinitely
- Cannot communicate with backend services
- Development workflow broken

**Code Location (Problematic):**
```javascript
// File: frontend/next.config.js (Line 9)
async rewrites() {
  return [
    {
      source: '/api/:path*',
      destination: 'http://localhost:3000/api/:path*', // Same port as frontend!
    },
  ];
},
```

**Solution:**
```javascript
// Fix: Point to API Gateway (frontend should use different port)
async rewrites() {
  return [
    {
      source: '/api/:path*',
      destination: 'http://localhost:3000/api/:path*', // API Gateway port
    },
  ];
},
```

**Additional Fix - Frontend Port:**
```json
// Update frontend package.json scripts
{
  "scripts": {
    "dev": "next dev -p 3003",  // Use different port
    "start": "next start -p 3003"
  }
}
```

**Impact:** 
- ✅ Enables frontend-backend communication
- ✅ Fixes development workflow
- ✅ Prevents infinite loops

**Status:** ⚠️ **CIRCULAR REFERENCE** - Blocks frontend

### 3. **Weak JWT Secret in Production** ⭐ **SECURITY RISK**

**Problem:** Default JWT secret used in all environments including production.

**Code Location (Problematic):**
```yaml
# File: docker-compose.yml (Lines 88, 130)
environment:
  JWT_SECRET: your_jwt_secret_key_here  # Weak default secret
```

**Solution:**
```yaml
# Use environment-specific secrets
environment:
  JWT_SECRET: ${JWT_SECRET:-your_jwt_secret_key_here}
```

```bash
# Create .env file
JWT_SECRET=your_super_secure_random_jwt_secret_key_at_least_32_characters_long
```

**Impact:** 
- ✅ Improves security
- ✅ Prevents token forgery
- ✅ Production-ready authentication

**Status:** ⚠️ **SECURITY VULNERABILITY**

### 4. **Database Auto-Sync in Production** ⭐ **DATA RISK**

**Problem:** DB_SYNCHRONIZE set to "true" in all environments, dangerous for production.

**Code Location (Problematic):**
```yaml
# File: docker-compose.yml (Lines 86, 128)
environment:
  DB_SYNCHRONIZE: "true"  # Dangerous in production
```

**Solution:**
```yaml
# Environment-based synchronization
environment:
  DB_SYNCHRONIZE: ${DB_SYNCHRONIZE:-false}
  DB_RUN_MIGRATIONS: ${DB_RUN_MIGRATIONS:-true}
```

**Impact:** 
- ✅ Prevents data loss in production
- ✅ Enables controlled schema changes
- ✅ Production-safe database management

**Status:** ⚠️ **PRODUCTION RISK**

## Working Build & Deployment Features

### ✅ **Health Check Configuration**
- **Comprehensive Health Checks:** All services have proper health monitoring
- **Retry Logic:** 30s interval, 10s timeout, 3 retries, 30s start period
- **Dependency Management:** Services wait for health conditions before starting
- **Health Endpoints:** `/api/health` endpoints configured for all services

### ✅ **Port Separation and Network Configuration**
- **Clear Port Assignment:** No conflicts between backend services
  - API Gateway: 3000, User Service: 3001, Store Service: 3002
  - PostgreSQL: 5432, RabbitMQ: 5672, 15672
- **Docker Network:** Proper bridge network configuration
- **Service Discovery:** Services can communicate via container names

### ✅ **Multi-Stage Docker Builds**
- **Build Optimization:** Separate build and production stages
- **Size Reduction:** Production images only contain necessary files
- **Dependency Separation:** Development vs production dependencies properly handled
- **Alpine Base:** Lightweight Node.js Alpine images used

### ✅ **Volume Configuration**
- **Development Volumes:** Source code mounted for hot reload
- **Node Modules:** Proper node_modules volume handling
- **Shared Libraries:** Libs directory properly mounted
- **Data Persistence:** PostgreSQL data volume configured

## Files Modified

### Docker Configuration:
1. **`services/store-service/Dockerfile`** - ⚠️ **MISSING** - Needs creation
2. **`services/user-service/Dockerfile`** - Line 11: Messaging library disabled
3. **`services/api-gateway/Dockerfile`** - Lines 10, 27: Inconsistent dependency flags
4. **`docker-compose.yml`** - Lines 57-58, 86, 117, 128: Multiple configuration issues

### Frontend Configuration:
1. **`frontend/next.config.js`** - Line 9: API proxy circular reference
2. **`frontend/package.json`** - Scripts need port configuration

### Environment Configuration:
1. **`.env`** - ⚠️ **MISSING** - Needs creation for local development
2. **`.env.example`** - ⚠️ **MISSING** - Needs creation for team setup

## Immediate Actions Required

### Phase 1: Critical Fixes (High Priority)
1. **Create Store Service Dockerfile** - Copy from user-service pattern
2. **Fix Frontend API Proxy** - Point to correct API Gateway port
3. **Create Environment Files** - Add .env and .env.example
4. **Update JWT Secret** - Use secure random secret

### Phase 2: Security & Production (Medium Priority)
1. **Disable DB Auto-Sync** - Use migrations in production
2. **Add Health Conditions** - Fix API Gateway dependencies
3. **Standardize Docker Builds** - Consistent patterns across services
4. **Re-enable Messaging** - Resolve circular dependencies

## Recommended Environment Variables

### Required .env file:
```bash
# Database
DB_PASSWORD=1111
DB_SYNCHRONIZE=false
DB_RUN_MIGRATIONS=true

# JWT
JWT_SECRET=your_super_secure_random_jwt_secret_key_at_least_32_characters_long
JWT_EXPIRES_IN=1h

# RabbitMQ
RABBITMQ_DEFAULT_USER=admin
RABBITMQ_DEFAULT_PASS=admin

# Services
NODE_ENV=development
API_GATEWAY_PORT=3000
USER_SERVICE_PORT=3001
STORE_SERVICE_PORT=3002
FRONTEND_PORT=3003
```

---

**Status:** ⚠️ **CRITICAL ISSUES FOUND** - Deployment blocked until fixes applied
**Date:** 2025-05-26
**Impact:** High - Cannot deploy complete stack or run frontend
