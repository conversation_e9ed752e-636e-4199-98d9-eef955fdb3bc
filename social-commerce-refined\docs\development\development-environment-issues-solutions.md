# Development Environment Issues & Solutions Documentation

## Overview
This document details development environment setup issues encountered during the social commerce platform development, including Docker configuration problems, directory/path issues, and package management challenges.

## Critical Issues & Solutions

### 1. **Terminal Directory Switching Problem** ⭐ **CRITICAL FIX**

**Problem:** Each `launch-process` command starts a NEW terminal session defaulting to parent directory instead of maintaining working directory context.

**Symptoms:**
- Commands failing because they run from wrong directory
- Build failures and service startup issues
- File not found errors during development

**Root Cause:** 
Terminal sessions don't persist working directory between `launch-process` calls.

**Code Before (Problematic):**
```bash
# Commands run from wrong directory
docker-compose ps
npm run start
```

**Code After (Fixed):**
```bash
# MANDATORY: Always prefix with directory change
cd /c/Users/<USER>/Documents/augment/social-commerce/social-commerce-refined && docker-compose ps
cd /c/Users/<USER>/Documents/augment/social-commerce/social-commerce-refined && npm run start
```

**Solution Details:**
1. **EVERY `launch-process` command MUST use this pattern:**
   ```bash
   cd /c/Users/<USER>/Documents/augment/social-commerce/social-commerce-refined && [actual command]
   ```

2. **Verification command (run first):**
   ```bash
   cd /c/Users/<USER>/Documents/augment/social-commerce/social-commerce-refined && pwd && echo "✅ Correct directory confirmed"
   ```

**Impact:** 
- ✅ Eliminates directory switching errors
- ✅ Ensures commands run from correct location
- ✅ Prevents build and deployment failures

### 2. **Docker Configuration Issues**

#### **2.1: Commented Out Messaging Library**

**Problem:** Messaging library disabled in Dockerfile causing incomplete microservices communication.

**Code Location:**
```dockerfile
# File: services/user-service/Dockerfile (Line 11)
# COPY libs/messaging ./libs/messaging  # Temporarily disabled
```

**Root Cause:** Circular dependency or build issues with messaging library.

**Solution:** 
1. Resolve messaging library dependencies
2. Re-enable messaging library copying
3. Test microservices communication

**Status:** ⚠️ **NEEDS RESOLUTION**

#### **2.2: Docker Compose vs Manual Commands Mismatch**

**Problem:** Two different deployment methods in use causing configuration drift.

**Details:**
- Docker Compose defines complete stack (postgres, rabbitmq, services)
- Manual Docker commands used different container names
- Environment variables differ between approaches

**Solution:**
1. Standardize on Docker Compose for development
2. Update documentation to use consistent approach
3. Remove manual Docker command references

**Status:** ⚠️ **NEEDS STANDARDIZATION**

#### **2.3: Legacy Peer Dependencies Flag Required**

**Problem:** `--legacy-peer-deps` flag required for npm install in all services.

**Code Location:**
```dockerfile
# File: services/user-service/Dockerfile (Lines 14, 33)
RUN npm install --legacy-peer-deps
RUN npm install --only=production --legacy-peer-deps
```

**Root Cause:** NestJS ecosystem package version incompatibilities.

**Solution Applied:** Added flag as workaround.

**Recommended Fix:**
1. Audit all package dependencies
2. Update to compatible versions
3. Remove legacy flag requirement

**Status:** ✅ **WORKAROUND APPLIED** - Needs proper dependency audit

### 3. **Package Management Issues**

#### **3.1: Missing Frontend Containerization**

**Problem:** No Dockerfile for frontend service causing inconsistent development environment.

**Impact:**
- Frontend development done outside Docker
- Inconsistent environment between team members
- Missing from Docker Compose stack

**Solution:**
```dockerfile
# Recommended frontend/Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
EXPOSE 3000
CMD ["npm", "run", "dev"]
```

**Status:** ⚠️ **MISSING CONTAINERIZATION**

#### **3.2: Shared Libraries Dependency Management**

**Problem:** Shared libs (common, messaging) require special handling in Docker builds.

**Solution Applied:**
```dockerfile
# File: services/user-service/Dockerfile (Lines 10-11)
COPY libs/common ./libs/common
# COPY libs/messaging ./libs/messaging  # Temporarily disabled
```

**Impact:** Enables proper monorepo dependency resolution.

**Status:** ✅ **RESOLVED** - Proper lib copying implemented

### 4. **Path Resolution Issues**

#### **4.1: Incorrect Working Directory Reference**

**Problem:** Confusion between parent directory and project directory.

**Incorrect Path:** `/c/Users/<USER>/Documents/augment/social-commerce`
**Correct Path:** `/c/Users/<USER>/Documents/augment/social-commerce/social-commerce-refined`

**Solution:** Always use correct project root path.

**Status:** ✅ **RESOLVED**

#### **4.2: Docker Build Context Issues**

**Problem:** Docker builds failing due to incorrect context paths.

**Solution:**
```bash
# Correct Docker build command
cd /c/Users/<USER>/Documents/augment/social-commerce/social-commerce-refined && docker build -f services/user-service/Dockerfile -t user-service .
```

**Status:** ✅ **RESOLVED**

## Files Modified

### Docker Configuration:
1. **`services/user-service/Dockerfile`**
   - Lines 14, 33: Added `--legacy-peer-deps` flag
   - Line 11: Messaging library temporarily disabled

2. **`docker-compose.yml`**
   - Complete stack configuration with health checks
   - Proper service dependencies and networking

### Development Scripts:
1. **All launch-process commands**
   - Prefixed with directory change command
   - Ensures correct working directory

## Environment Setup Verification

### ✅ **Working Correctly:**
- Docker Compose stack configuration
- Service health checks and dependencies
- Multi-stage Docker builds for production optimization
- Node modules volume mounting

### ⚠️ **Needs Attention:**
- Messaging library integration
- Frontend containerization
- Package dependency audit
- Deployment method standardization

## Recommended Environment Setup Workflow

### 1. **Initial Setup:**
```bash
cd /c/Users/<USER>/Documents/augment/social-commerce/social-commerce-refined
docker-compose up -d postgres rabbitmq
```

### 2. **Service Startup:**
```bash
cd /c/Users/<USER>/Documents/augment/social-commerce/social-commerce-refined
docker-compose up -d user-service
docker-compose up -d store-service
docker-compose up -d api-gateway
```

### 3. **Health Verification:**
```bash
cd /c/Users/<USER>/Documents/augment/social-commerce/social-commerce-refined
docker-compose ps
curl http://localhost:3000/api/health
curl http://localhost:3001/api/health
```

## Future Improvements

### High Priority:
1. **Resolve messaging library dependencies** and re-enable
2. **Create frontend Dockerfile** for complete containerization
3. **Audit and update package dependencies** to remove legacy flag
4. **Standardize on Docker Compose** for all development

### Medium Priority:
1. **Add development environment validation script**
2. **Implement automated dependency management**
3. **Create environment setup documentation**
4. **Add Docker health check improvements**

### Low Priority:
1. **Optimize Docker build times** with better caching
2. **Add development vs production environment separation**
3. **Implement automated environment testing**

---

**Status:** ✅ **DOCUMENTED** - Environment issues identified and solutions provided
**Date:** 2025-05-26
**Impact:** High - Critical for consistent development environment and team onboarding
