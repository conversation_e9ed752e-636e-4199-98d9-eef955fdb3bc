/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ([
/* 0 */,
/* 1 */
/***/ ((module) => {

module.exports = require("@nestjs/core");

/***/ }),
/* 2 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AppModule = void 0;
const common_1 = __webpack_require__(3);
const config_1 = __webpack_require__(4);
const jwt_1 = __webpack_require__(5);
const terminus_1 = __webpack_require__(6);
const axios_1 = __webpack_require__(7);
const routing_module_1 = __webpack_require__(8);
const shared_module_1 = __webpack_require__(23);
const core_1 = __webpack_require__(1);
const all_exceptions_filter_1 = __webpack_require__(25);
const logging_interceptor_1 = __webpack_require__(26);
const nest_winston_1 = __webpack_require__(29);
const winston = __webpack_require__(30);
const correlation_id_middleware_1 = __webpack_require__(31);
const cache_manager_1 = __webpack_require__(20);
let AppModule = class AppModule {
    configure(consumer) {
        consumer
            .apply(correlation_id_middleware_1.CorrelationIdMiddleware)
            .forRoutes({ path: '*', method: common_1.RequestMethod.ALL });
    }
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: `.env.${process.env.NODE_ENV || 'development'}`,
            }),
            nest_winston_1.WinstonModule.forRootAsync({
                imports: [config_1.ConfigModule],
                inject: [config_1.ConfigService],
                useFactory: (configService) => ({
                    transports: [
                        new winston.transports.Console({
                            format: winston.format.combine(winston.format.timestamp(), winston.format.ms(), winston.format.colorize(), winston.format.printf((info) => `${info.timestamp} ${info.level}: ${info.message}`)),
                        }),
                        new winston.transports.File({
                            filename: 'logs/error.log',
                            level: 'error',
                            format: winston.format.combine(winston.format.timestamp(), winston.format.json()),
                        }),
                        new winston.transports.File({
                            filename: 'logs/combined.log',
                            format: winston.format.combine(winston.format.timestamp(), winston.format.json()),
                        }),
                    ],
                }),
            }),
            jwt_1.JwtModule.registerAsync({
                imports: [config_1.ConfigModule],
                inject: [config_1.ConfigService],
                useFactory: (configService) => ({
                    secret: configService.get('JWT_SECRET', 'your-secret-key'),
                    signOptions: {
                        expiresIn: configService.get('JWT_EXPIRES_IN', '1h'),
                    },
                }),
            }),
            terminus_1.TerminusModule,
            axios_1.HttpModule,
            cache_manager_1.CacheModule.register({
                isGlobal: true,
                ttl: 60 * 1000,
                max: 100,
            }),
            routing_module_1.RoutingModule,
            shared_module_1.SharedModule,
        ],
        providers: [
            {
                provide: core_1.APP_FILTER,
                useClass: all_exceptions_filter_1.AllExceptionsFilter,
            },
            {
                provide: core_1.APP_INTERCEPTOR,
                useClass: logging_interceptor_1.LoggingInterceptor,
            },
        ],
    })
], AppModule);


/***/ }),
/* 3 */
/***/ ((module) => {

module.exports = require("@nestjs/common");

/***/ }),
/* 4 */
/***/ ((module) => {

module.exports = require("@nestjs/config");

/***/ }),
/* 5 */
/***/ ((module) => {

module.exports = require("@nestjs/jwt");

/***/ }),
/* 6 */
/***/ ((module) => {

module.exports = require("@nestjs/terminus");

/***/ }),
/* 7 */
/***/ ((module) => {

module.exports = require("@nestjs/axios");

/***/ }),
/* 8 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.RoutingModule = void 0;
const common_1 = __webpack_require__(3);
const axios_1 = __webpack_require__(7);
const config_1 = __webpack_require__(4);
const user_controller_1 = __webpack_require__(9);
const store_controller_1 = __webpack_require__(17);
const product_controller_1 = __webpack_require__(22);
const routing_service_1 = __webpack_require__(11);
let RoutingModule = class RoutingModule {
};
exports.RoutingModule = RoutingModule;
exports.RoutingModule = RoutingModule = __decorate([
    (0, common_1.Module)({
        imports: [
            axios_1.HttpModule,
            config_1.ConfigModule,
        ],
        controllers: [
            user_controller_1.UserController,
            store_controller_1.StoreController,
            product_controller_1.ProductController,
        ],
        providers: [
            routing_service_1.RoutingService,
        ],
        exports: [
            routing_service_1.RoutingService,
        ],
    })
], RoutingModule);


/***/ }),
/* 9 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var UserController_1;
var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.UserController = void 0;
const common_1 = __webpack_require__(3);
const swagger_1 = __webpack_require__(10);
const routing_service_1 = __webpack_require__(11);
const express_1 = __webpack_require__(15);
const rxjs_1 = __webpack_require__(12);
const operators_1 = __webpack_require__(16);
let UserController = UserController_1 = class UserController {
    constructor(routingService) {
        this.routingService = routingService;
        this.logger = new common_1.Logger(UserController_1.name);
        this.SERVICE_NAME = 'user';
    }
    getAllUsers(req, headers, query) {
        this.logger.log('Forwarding GET /users request');
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, '/users', 'GET', null, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
    getUserById(id, headers) {
        this.logger.log(`Forwarding GET /users/${id} request`);
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, `/users/${id}`, 'GET', null, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
    createUser(body, headers) {
        this.logger.log('Forwarding POST /users request');
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, '/users', 'POST', body, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
    updateUser(id, body, headers) {
        this.logger.log(`Forwarding PUT /users/${id} request`);
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, `/users/${id}`, 'PUT', body, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
    deleteUser(id, headers) {
        this.logger.log(`Forwarding DELETE /users/${id} request`);
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, `/users/${id}`, 'DELETE', null, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
    register(body, headers) {
        this.logger.log('Forwarding POST /auth/register request');
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, '/auth/register', 'POST', body, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
    login(body, headers) {
        this.logger.log('Forwarding POST /auth/login request');
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, '/auth/login', 'POST', body, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
    getProfile(headers) {
        this.logger.log('Forwarding GET /auth/profile request');
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, '/auth/profile', 'GET', null, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
    forwardGetRequest(req, headers) {
        const path = req.url;
        this.logger.log(`Forwarding GET ${path} request to user service`);
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, path, 'GET', null, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
    forwardPostRequest(req, body, headers) {
        const path = req.url;
        this.logger.log(`Forwarding POST ${path} request to user service`);
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, path, 'POST', body, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
    forwardPutRequest(req, body, headers) {
        const path = req.url;
        this.logger.log(`Forwarding PUT ${path} request to user service`);
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, path, 'PUT', body, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
    forwardDeleteRequest(req, headers) {
        const path = req.url;
        this.logger.log(`Forwarding DELETE ${path} request to user service`);
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, path, 'DELETE', null, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
    forwardPatchRequest(req, body, headers) {
        const path = req.url;
        this.logger.log(`Forwarding PATCH ${path} request to user service`);
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, path, 'PATCH', body, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
};
exports.UserController = UserController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all users' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Return all users' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Headers)()),
    __param(2, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_b = typeof express_1.Request !== "undefined" && express_1.Request) === "function" ? _b : Object, Object, Object]),
    __metadata("design:returntype", typeof (_c = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _c : Object)
], UserController.prototype, "getAllUsers", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get a user by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Return the user' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'User not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", typeof (_d = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _d : Object)
], UserController.prototype, "getUserById", null);
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new user' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'User created successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", typeof (_e = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _e : Object)
], UserController.prototype, "createUser", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Update a user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'User updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'User not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", typeof (_f = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _f : Object)
], UserController.prototype, "updateUser", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'User deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'User not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", typeof (_g = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _g : Object)
], UserController.prototype, "deleteUser", null);
__decorate([
    (0, common_1.Post)('auth/register'),
    (0, swagger_1.ApiOperation)({ summary: 'Register a new user' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'User registered successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 429, description: 'Too many requests' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", typeof (_h = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _h : Object)
], UserController.prototype, "register", null);
__decorate([
    (0, common_1.Post)('auth/login'),
    (0, swagger_1.ApiOperation)({ summary: 'Login a user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Login successful' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 429, description: 'Too many requests' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", typeof (_j = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _j : Object)
], UserController.prototype, "login", null);
__decorate([
    (0, common_1.Get)('auth/profile'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get user profile' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Profile retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", typeof (_k = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _k : Object)
], UserController.prototype, "getProfile", null);
__decorate([
    (0, common_1.Get)('*'),
    (0, swagger_1.ApiOperation)({ summary: 'Forward any GET request to the user service' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_l = typeof express_1.Request !== "undefined" && express_1.Request) === "function" ? _l : Object, Object]),
    __metadata("design:returntype", typeof (_m = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _m : Object)
], UserController.prototype, "forwardGetRequest", null);
__decorate([
    (0, common_1.Post)('*'),
    (0, swagger_1.ApiOperation)({ summary: 'Forward any POST request to the user service' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_o = typeof express_1.Request !== "undefined" && express_1.Request) === "function" ? _o : Object, Object, Object]),
    __metadata("design:returntype", typeof (_p = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _p : Object)
], UserController.prototype, "forwardPostRequest", null);
__decorate([
    (0, common_1.Put)('*'),
    (0, swagger_1.ApiOperation)({ summary: 'Forward any PUT request to the user service' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_q = typeof express_1.Request !== "undefined" && express_1.Request) === "function" ? _q : Object, Object, Object]),
    __metadata("design:returntype", typeof (_r = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _r : Object)
], UserController.prototype, "forwardPutRequest", null);
__decorate([
    (0, common_1.Delete)('*'),
    (0, swagger_1.ApiOperation)({ summary: 'Forward any DELETE request to the user service' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_s = typeof express_1.Request !== "undefined" && express_1.Request) === "function" ? _s : Object, Object]),
    __metadata("design:returntype", typeof (_t = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _t : Object)
], UserController.prototype, "forwardDeleteRequest", null);
__decorate([
    (0, common_1.Patch)('*'),
    (0, swagger_1.ApiOperation)({ summary: 'Forward any PATCH request to the user service' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_u = typeof express_1.Request !== "undefined" && express_1.Request) === "function" ? _u : Object, Object, Object]),
    __metadata("design:returntype", typeof (_v = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _v : Object)
], UserController.prototype, "forwardPatchRequest", null);
exports.UserController = UserController = UserController_1 = __decorate([
    (0, swagger_1.ApiTags)('users'),
    (0, common_1.Controller)('users'),
    __metadata("design:paramtypes", [typeof (_a = typeof routing_service_1.RoutingService !== "undefined" && routing_service_1.RoutingService) === "function" ? _a : Object])
], UserController);


/***/ }),
/* 10 */
/***/ ((module) => {

module.exports = require("@nestjs/swagger");

/***/ }),
/* 11 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RoutingService_1;
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.RoutingService = void 0;
const common_1 = __webpack_require__(3);
const axios_1 = __webpack_require__(7);
const config_1 = __webpack_require__(4);
const rxjs_1 = __webpack_require__(12);
const circuit_breaker_service_1 = __webpack_require__(13);
let RoutingService = RoutingService_1 = class RoutingService {
    constructor(httpService, configService, circuitBreakerService) {
        this.httpService = httpService;
        this.configService = configService;
        this.circuitBreakerService = circuitBreakerService;
        this.logger = new common_1.Logger(RoutingService_1.name);
    }
    forwardRequest(serviceName, path, method, data, headers) {
        const serviceUrl = this.getServiceUrl(serviceName);
        const url = `${serviceUrl}${path}`;
        this.logger.log(`Forwarding ${method} request to ${url}`);
        const config = {
            headers: Object.assign(Object.assign({}, headers), { 'x-correlation-id': headers['x-correlation-id'] || `api-gateway-${Date.now()}` }),
        };
        const requestConfig = {
            url,
            method: method.toUpperCase(),
            headers: config.headers,
            data,
        };
        return (0, rxjs_1.from)(this.circuitBreakerService.request(serviceName, requestConfig)).pipe((0, rxjs_1.map)((response) => {
            this.logger.log(`Response from ${url}: ${response.status}`);
            return response;
        }), (0, rxjs_1.catchError)((error) => {
            var _a, _b, _c;
            this.logger.error(`Error forwarding request to ${url}: ${error.message}`, error.stack);
            const status = ((_a = error.response) === null || _a === void 0 ? void 0 : _a.status) || common_1.HttpStatus.INTERNAL_SERVER_ERROR;
            const message = ((_c = (_b = error.response) === null || _b === void 0 ? void 0 : _b.data) === null || _c === void 0 ? void 0 : _c.message) || error.message || 'Internal Server Error';
            return (0, rxjs_1.throwError)(() => new common_1.HttpException(message, status));
        }));
    }
    getServiceUrl(serviceName) {
        const serviceMap = {
            user: this.configService.get('USER_SERVICE_URL', 'http://localhost:3001/api'),
            store: this.configService.get('STORE_SERVICE_URL', 'http://localhost:3002/api'),
            product: this.configService.get('PRODUCT_SERVICE_URL', 'http://localhost:3003/api'),
            order: this.configService.get('ORDER_SERVICE_URL', 'http://localhost:3004/api'),
            cart: this.configService.get('CART_SERVICE_URL', 'http://localhost:3005/api'),
            social: this.configService.get('SOCIAL_SERVICE_URL', 'http://localhost:3006/api'),
            notification: this.configService.get('NOTIFICATION_SERVICE_URL', 'http://localhost:3007/api'),
            search: this.configService.get('SEARCH_SERVICE_URL', 'http://localhost:3008/api'),
            analytics: this.configService.get('ANALYTICS_SERVICE_URL', 'http://localhost:3009/api'),
        };
        const serviceUrl = serviceMap[serviceName];
        if (!serviceUrl) {
            throw new common_1.HttpException(`Unknown service: ${serviceName}`, common_1.HttpStatus.BAD_REQUEST);
        }
        return serviceUrl;
    }
};
exports.RoutingService = RoutingService;
exports.RoutingService = RoutingService = RoutingService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof axios_1.HttpService !== "undefined" && axios_1.HttpService) === "function" ? _a : Object, typeof (_b = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _b : Object, typeof (_c = typeof circuit_breaker_service_1.CircuitBreakerService !== "undefined" && circuit_breaker_service_1.CircuitBreakerService) === "function" ? _c : Object])
], RoutingService);


/***/ }),
/* 12 */
/***/ ((module) => {

module.exports = require("rxjs");

/***/ }),
/* 13 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var CircuitBreakerService_1;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.CircuitBreakerService = exports.CircuitState = void 0;
const common_1 = __webpack_require__(3);
var CircuitState;
(function (CircuitState) {
    CircuitState["CLOSED"] = "CLOSED";
    CircuitState["OPEN"] = "OPEN";
    CircuitState["HALF_OPEN"] = "HALF_OPEN";
})(CircuitState || (exports.CircuitState = CircuitState = {}));
let CircuitBreakerService = CircuitBreakerService_1 = class CircuitBreakerService {
    constructor() {
        this.logger = new common_1.Logger(CircuitBreakerService_1.name);
        this.circuits = new Map();
        this.defaultOptions = {
            failureThreshold: 5,
            resetTimeout: 60000,
            monitoringPeriod: 10000,
        };
    }
    async execute(circuitName, operation, options) {
        const opts = Object.assign(Object.assign({}, this.defaultOptions), options);
        const circuit = this.getOrCreateCircuit(circuitName);
        if (circuit.state === CircuitState.OPEN) {
            if (this.shouldAttemptReset(circuit, opts)) {
                circuit.state = CircuitState.HALF_OPEN;
                this.logger.debug(`Circuit ${circuitName} moved to HALF_OPEN state`);
            }
            else {
                throw new Error(`Circuit breaker ${circuitName} is OPEN`);
            }
        }
        try {
            const result = await operation();
            this.onSuccess(circuitName);
            return result;
        }
        catch (error) {
            this.onFailure(circuitName, opts);
            throw error;
        }
    }
    async request(serviceName, requestConfig) {
        const axios = __webpack_require__(14);
        return this.execute(serviceName, () => axios(requestConfig));
    }
    getStats(circuitName) {
        return this.circuits.get(circuitName);
    }
    getAllStats() {
        const stats = {};
        this.circuits.forEach((value, key) => {
            stats[key] = value;
        });
        return stats;
    }
    getStatus() {
        return this.getAllStats();
    }
    reset(circuitName) {
        const circuit = this.circuits.get(circuitName);
        if (circuit) {
            circuit.state = CircuitState.CLOSED;
            circuit.failures = 0;
            circuit.successes = 0;
            circuit.requests = 0;
            circuit.lastFailureTime = undefined;
            this.logger.debug(`Circuit ${circuitName} has been reset`);
        }
    }
    resetAll() {
        this.circuits.forEach((_, circuitName) => {
            this.reset(circuitName);
        });
        this.logger.debug('All circuits have been reset');
    }
    getOrCreateCircuit(circuitName) {
        if (!this.circuits.has(circuitName)) {
            this.circuits.set(circuitName, {
                state: CircuitState.CLOSED,
                failures: 0,
                successes: 0,
                requests: 0,
            });
        }
        return this.circuits.get(circuitName);
    }
    shouldAttemptReset(circuit, options) {
        if (!circuit.lastFailureTime) {
            return true;
        }
        return Date.now() - circuit.lastFailureTime.getTime() >= options.resetTimeout;
    }
    onSuccess(circuitName) {
        const circuit = this.circuits.get(circuitName);
        if (circuit) {
            circuit.successes++;
            circuit.requests++;
            if (circuit.state === CircuitState.HALF_OPEN) {
                circuit.state = CircuitState.CLOSED;
                circuit.failures = 0;
                this.logger.debug(`Circuit ${circuitName} moved to CLOSED state after successful request`);
            }
        }
    }
    onFailure(circuitName, options) {
        const circuit = this.circuits.get(circuitName);
        if (circuit) {
            circuit.failures++;
            circuit.requests++;
            circuit.lastFailureTime = new Date();
            if (circuit.failures >= options.failureThreshold) {
                circuit.state = CircuitState.OPEN;
                this.logger.warn(`Circuit ${circuitName} moved to OPEN state after ${circuit.failures} failures`);
            }
        }
    }
};
exports.CircuitBreakerService = CircuitBreakerService;
exports.CircuitBreakerService = CircuitBreakerService = CircuitBreakerService_1 = __decorate([
    (0, common_1.Injectable)()
], CircuitBreakerService);


/***/ }),
/* 14 */
/***/ ((module) => {

module.exports = require("axios");

/***/ }),
/* 15 */
/***/ ((module) => {

module.exports = require("express");

/***/ }),
/* 16 */
/***/ ((module) => {

module.exports = require("rxjs/operators");

/***/ }),
/* 17 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var StoreController_1;
var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.StoreController = void 0;
const common_1 = __webpack_require__(3);
const swagger_1 = __webpack_require__(10);
const routing_service_1 = __webpack_require__(11);
const express_1 = __webpack_require__(15);
const rxjs_1 = __webpack_require__(12);
const operators_1 = __webpack_require__(16);
const cache_interceptor_1 = __webpack_require__(18);
let StoreController = StoreController_1 = class StoreController {
    constructor(routingService) {
        this.routingService = routingService;
        this.logger = new common_1.Logger(StoreController_1.name);
        this.SERVICE_NAME = 'store';
    }
    getAllStores(req, headers, query) {
        this.logger.log('Forwarding GET /stores request');
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, '/stores', 'GET', null, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
    getStoreById(id, headers) {
        this.logger.log(`Forwarding GET /stores/${id} request`);
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, `/stores/${id}`, 'GET', null, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
    createStore(body, headers) {
        this.logger.log('Forwarding POST /stores request');
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, '/stores', 'POST', body, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
    updateStore(id, body, headers) {
        this.logger.log(`Forwarding PUT /stores/${id} request`);
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, `/stores/${id}`, 'PUT', body, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
    deleteStore(id, headers) {
        this.logger.log(`Forwarding DELETE /stores/${id} request`);
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, `/stores/${id}`, 'DELETE', null, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
    getStoreProducts(storeId, headers) {
        this.logger.log(`Forwarding GET /stores/${storeId}/products request`);
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, `/stores/${storeId}/products`, 'GET', null, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
    createStoreProduct(storeId, body, headers) {
        this.logger.log(`Forwarding POST /stores/${storeId}/products request`);
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, `/stores/${storeId}/products`, 'POST', body, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
    forwardGetRequest(req, headers) {
        const path = req.url;
        this.logger.log(`Forwarding GET ${path} request to store service`);
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, path, 'GET', null, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
    forwardPostRequest(req, body, headers) {
        const path = req.url;
        this.logger.log(`Forwarding POST ${path} request to store service`);
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, path, 'POST', body, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
    forwardPutRequest(req, body, headers) {
        const path = req.url;
        this.logger.log(`Forwarding PUT ${path} request to store service`);
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, path, 'PUT', body, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
    forwardDeleteRequest(req, headers) {
        const path = req.url;
        this.logger.log(`Forwarding DELETE ${path} request to store service`);
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, path, 'DELETE', null, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
    forwardPatchRequest(req, body, headers) {
        const path = req.url;
        this.logger.log(`Forwarding PATCH ${path} request to store service`);
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, path, 'PATCH', body, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
};
exports.StoreController = StoreController;
__decorate([
    (0, common_1.Get)(),
    (0, common_1.UseInterceptors)(cache_interceptor_1.CacheInterceptor),
    (0, swagger_1.ApiOperation)({ summary: 'Get all stores' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Return all stores' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Headers)()),
    __param(2, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_b = typeof express_1.Request !== "undefined" && express_1.Request) === "function" ? _b : Object, Object, Object]),
    __metadata("design:returntype", typeof (_c = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _c : Object)
], StoreController.prototype, "getAllStores", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, common_1.UseInterceptors)(cache_interceptor_1.CacheInterceptor),
    (0, swagger_1.ApiOperation)({ summary: 'Get a store by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Return the store' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Store not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", typeof (_d = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _d : Object)
], StoreController.prototype, "getStoreById", null);
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new store' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Store created successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", typeof (_e = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _e : Object)
], StoreController.prototype, "createStore", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Update a store' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Store updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Store not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", typeof (_f = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _f : Object)
], StoreController.prototype, "updateStore", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a store' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Store deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Store not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", typeof (_g = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _g : Object)
], StoreController.prototype, "deleteStore", null);
__decorate([
    (0, common_1.Get)(':storeId/products'),
    (0, common_1.UseInterceptors)(cache_interceptor_1.CacheInterceptor),
    (0, swagger_1.ApiOperation)({ summary: 'Get all products for a store' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Return all products for the store' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Store not found' }),
    __param(0, (0, common_1.Param)('storeId')),
    __param(1, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", typeof (_h = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _h : Object)
], StoreController.prototype, "getStoreProducts", null);
__decorate([
    (0, common_1.Post)(':storeId/products'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new product for a store' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Product created successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Store not found' }),
    __param(0, (0, common_1.Param)('storeId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", typeof (_j = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _j : Object)
], StoreController.prototype, "createStoreProduct", null);
__decorate([
    (0, common_1.Get)('*'),
    (0, common_1.UseInterceptors)(cache_interceptor_1.CacheInterceptor),
    (0, swagger_1.ApiOperation)({ summary: 'Forward any GET request to the store service' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_k = typeof express_1.Request !== "undefined" && express_1.Request) === "function" ? _k : Object, Object]),
    __metadata("design:returntype", typeof (_l = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _l : Object)
], StoreController.prototype, "forwardGetRequest", null);
__decorate([
    (0, common_1.Post)('*'),
    (0, swagger_1.ApiOperation)({ summary: 'Forward any POST request to the store service' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_m = typeof express_1.Request !== "undefined" && express_1.Request) === "function" ? _m : Object, Object, Object]),
    __metadata("design:returntype", typeof (_o = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _o : Object)
], StoreController.prototype, "forwardPostRequest", null);
__decorate([
    (0, common_1.Put)('*'),
    (0, swagger_1.ApiOperation)({ summary: 'Forward any PUT request to the store service' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_p = typeof express_1.Request !== "undefined" && express_1.Request) === "function" ? _p : Object, Object, Object]),
    __metadata("design:returntype", typeof (_q = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _q : Object)
], StoreController.prototype, "forwardPutRequest", null);
__decorate([
    (0, common_1.Delete)('*'),
    (0, swagger_1.ApiOperation)({ summary: 'Forward any DELETE request to the store service' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_r = typeof express_1.Request !== "undefined" && express_1.Request) === "function" ? _r : Object, Object]),
    __metadata("design:returntype", typeof (_s = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _s : Object)
], StoreController.prototype, "forwardDeleteRequest", null);
__decorate([
    (0, common_1.Patch)('*'),
    (0, swagger_1.ApiOperation)({ summary: 'Forward any PATCH request to the store service' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_t = typeof express_1.Request !== "undefined" && express_1.Request) === "function" ? _t : Object, Object, Object]),
    __metadata("design:returntype", typeof (_u = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _u : Object)
], StoreController.prototype, "forwardPatchRequest", null);
exports.StoreController = StoreController = StoreController_1 = __decorate([
    (0, swagger_1.ApiTags)('stores'),
    (0, common_1.Controller)('stores'),
    __metadata("design:paramtypes", [typeof (_a = typeof routing_service_1.RoutingService !== "undefined" && routing_service_1.RoutingService) === "function" ? _a : Object])
], StoreController);


/***/ }),
/* 18 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CacheInterceptor_1;
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.CacheInterceptor = void 0;
const common_1 = __webpack_require__(3);
const rxjs_1 = __webpack_require__(12);
const operators_1 = __webpack_require__(16);
const cache_service_1 = __webpack_require__(19);
let CacheInterceptor = CacheInterceptor_1 = class CacheInterceptor {
    constructor(cacheService) {
        this.cacheService = cacheService;
        this.logger = new common_1.Logger(CacheInterceptor_1.name);
        this.cacheTtl = 60 * 1000;
        this.excludedPaths = [
            '/api/health',
            '/api/users/auth/login',
            '/api/users/auth/register',
        ];
    }
    async intercept(context, next) {
        const request = context.switchToHttp().getRequest();
        if (request.method !== 'GET') {
            return next.handle();
        }
        if (this.isExcluded(request.path)) {
            return next.handle();
        }
        const cacheKey = this.generateCacheKey(request);
        const cachedResponse = await this.cacheService.get(cacheKey);
        if (cachedResponse) {
            this.logger.debug(`Returning cached response for ${request.method} ${request.url}`);
            return (0, rxjs_1.of)(cachedResponse);
        }
        return next.handle().pipe((0, operators_1.tap)(response => {
            this.logger.debug(`Caching response for ${request.method} ${request.url}`);
            this.cacheService.set(cacheKey, response, this.cacheTtl);
        }));
    }
    generateCacheKey(request) {
        const queryString = Object.keys(request.query)
            .sort()
            .map(key => `${key}=${request.query[key]}`)
            .join('&');
        return `${request.path}${queryString ? `?${queryString}` : ''}`;
    }
    isExcluded(path) {
        return this.excludedPaths.some(excludedPath => path.startsWith(excludedPath));
    }
};
exports.CacheInterceptor = CacheInterceptor;
exports.CacheInterceptor = CacheInterceptor = CacheInterceptor_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof cache_service_1.CacheService !== "undefined" && cache_service_1.CacheService) === "function" ? _a : Object])
], CacheInterceptor);


/***/ }),
/* 19 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var CacheService_1;
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.CacheService = void 0;
const common_1 = __webpack_require__(3);
const cache_manager_1 = __webpack_require__(20);
const cache_manager_2 = __webpack_require__(21);
let CacheService = CacheService_1 = class CacheService {
    constructor(cacheManager) {
        this.cacheManager = cacheManager;
        this.logger = new common_1.Logger(CacheService_1.name);
        this.defaultTtl = 60 * 1000;
    }
    async get(key) {
        try {
            const value = await this.cacheManager.get(key);
            if (value) {
                this.logger.debug(`Cache hit for key: ${key}`);
                return value;
            }
            this.logger.debug(`Cache miss for key: ${key}`);
            return null;
        }
        catch (error) {
            this.logger.error(`Error getting value from cache for key ${key}: ${error.message}`);
            return null;
        }
    }
    async set(key, value, ttl) {
        try {
            await this.cacheManager.set(key, value, ttl || this.defaultTtl);
            this.logger.debug(`Cached value for key: ${key} with TTL: ${ttl || this.defaultTtl}ms`);
        }
        catch (error) {
            this.logger.error(`Error setting value in cache for key ${key}: ${error.message}`);
        }
    }
    async delete(key) {
        try {
            await this.cacheManager.del(key);
            this.logger.debug(`Deleted cache for key: ${key}`);
        }
        catch (error) {
            this.logger.error(`Error deleting value from cache for key ${key}: ${error.message}`);
        }
    }
    async clear() {
        try {
            await this.cacheManager.clear();
            this.logger.debug('Cleared entire cache');
        }
        catch (error) {
            this.logger.error(`Error clearing cache: ${error.message}`);
        }
    }
    async getOrSet(key, factory, ttl) {
        const cachedValue = await this.get(key);
        if (cachedValue !== null) {
            return cachedValue;
        }
        const value = await factory();
        await this.set(key, value, ttl);
        return value;
    }
};
exports.CacheService = CacheService;
exports.CacheService = CacheService = CacheService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(cache_manager_1.CACHE_MANAGER)),
    __metadata("design:paramtypes", [typeof (_a = typeof cache_manager_2.Cache !== "undefined" && cache_manager_2.Cache) === "function" ? _a : Object])
], CacheService);


/***/ }),
/* 20 */
/***/ ((module) => {

module.exports = require("@nestjs/cache-manager");

/***/ }),
/* 21 */
/***/ ((module) => {

module.exports = require("cache-manager");

/***/ }),
/* 22 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ProductController_1;
var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ProductController = void 0;
const common_1 = __webpack_require__(3);
const swagger_1 = __webpack_require__(10);
const routing_service_1 = __webpack_require__(11);
const express_1 = __webpack_require__(15);
const rxjs_1 = __webpack_require__(12);
const operators_1 = __webpack_require__(16);
let ProductController = ProductController_1 = class ProductController {
    constructor(routingService) {
        this.routingService = routingService;
        this.logger = new common_1.Logger(ProductController_1.name);
        this.SERVICE_NAME = 'store';
    }
    getAllProducts(req, headers, query) {
        this.logger.log('Forwarding GET /products request');
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, '/products', 'GET', null, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
    getProductById(id, headers) {
        this.logger.log(`Forwarding GET /products/${id} request`);
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, `/products/${id}`, 'GET', null, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
    createProduct(body, headers) {
        this.logger.log('Forwarding POST /products request');
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, '/products', 'POST', body, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
    updateProduct(id, body, headers) {
        this.logger.log(`Forwarding PUT /products/${id} request`);
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, `/products/${id}`, 'PUT', body, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
    deleteProduct(id, headers) {
        this.logger.log(`Forwarding DELETE /products/${id} request`);
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, `/products/${id}`, 'DELETE', null, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
    getProductsByStoreId(storeId, headers) {
        this.logger.log(`Forwarding GET /products/store/${storeId} request`);
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, `/products/store/${storeId}`, 'GET', null, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
    forwardGetRequest(req, headers) {
        const path = req.url;
        this.logger.log(`Forwarding GET ${path} request to product service`);
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, path, 'GET', null, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
    forwardPostRequest(req, body, headers) {
        const path = req.url;
        this.logger.log(`Forwarding POST ${path} request to product service`);
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, path, 'POST', body, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
    forwardPutRequest(req, body, headers) {
        const path = req.url;
        this.logger.log(`Forwarding PUT ${path} request to product service`);
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, path, 'PUT', body, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
    forwardDeleteRequest(req, headers) {
        const path = req.url;
        this.logger.log(`Forwarding DELETE ${path} request to product service`);
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, path, 'DELETE', null, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
    forwardPatchRequest(req, body, headers) {
        const path = req.url;
        this.logger.log(`Forwarding PATCH ${path} request to product service`);
        return this.routingService
            .forwardRequest(this.SERVICE_NAME, path, 'PATCH', body, headers)
            .pipe((0, operators_1.map)((response) => response.data));
    }
};
exports.ProductController = ProductController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all products' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Return all products' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Headers)()),
    __param(2, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_b = typeof express_1.Request !== "undefined" && express_1.Request) === "function" ? _b : Object, Object, Object]),
    __metadata("design:returntype", typeof (_c = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _c : Object)
], ProductController.prototype, "getAllProducts", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a product by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Return the product' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Product not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", typeof (_d = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _d : Object)
], ProductController.prototype, "getProductById", null);
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new product' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Product created successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", typeof (_e = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _e : Object)
], ProductController.prototype, "createProduct", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Update a product' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Product updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Product not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", typeof (_f = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _f : Object)
], ProductController.prototype, "updateProduct", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a product' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Product deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Product not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", typeof (_g = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _g : Object)
], ProductController.prototype, "deleteProduct", null);
__decorate([
    (0, common_1.Get)('store/:storeId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get products by store ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Return the products' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Store not found' }),
    __param(0, (0, common_1.Param)('storeId')),
    __param(1, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", typeof (_h = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _h : Object)
], ProductController.prototype, "getProductsByStoreId", null);
__decorate([
    (0, common_1.Get)('*'),
    (0, swagger_1.ApiOperation)({ summary: 'Forward any GET request to the product service' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_j = typeof express_1.Request !== "undefined" && express_1.Request) === "function" ? _j : Object, Object]),
    __metadata("design:returntype", typeof (_k = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _k : Object)
], ProductController.prototype, "forwardGetRequest", null);
__decorate([
    (0, common_1.Post)('*'),
    (0, swagger_1.ApiOperation)({ summary: 'Forward any POST request to the product service' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_l = typeof express_1.Request !== "undefined" && express_1.Request) === "function" ? _l : Object, Object, Object]),
    __metadata("design:returntype", typeof (_m = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _m : Object)
], ProductController.prototype, "forwardPostRequest", null);
__decorate([
    (0, common_1.Put)('*'),
    (0, swagger_1.ApiOperation)({ summary: 'Forward any PUT request to the product service' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_o = typeof express_1.Request !== "undefined" && express_1.Request) === "function" ? _o : Object, Object, Object]),
    __metadata("design:returntype", typeof (_p = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _p : Object)
], ProductController.prototype, "forwardPutRequest", null);
__decorate([
    (0, common_1.Delete)('*'),
    (0, swagger_1.ApiOperation)({ summary: 'Forward any DELETE request to the product service' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_q = typeof express_1.Request !== "undefined" && express_1.Request) === "function" ? _q : Object, Object]),
    __metadata("design:returntype", typeof (_r = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _r : Object)
], ProductController.prototype, "forwardDeleteRequest", null);
__decorate([
    (0, common_1.Patch)('*'),
    (0, swagger_1.ApiOperation)({ summary: 'Forward any PATCH request to the product service' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_s = typeof express_1.Request !== "undefined" && express_1.Request) === "function" ? _s : Object, Object, Object]),
    __metadata("design:returntype", typeof (_t = typeof rxjs_1.Observable !== "undefined" && rxjs_1.Observable) === "function" ? _t : Object)
], ProductController.prototype, "forwardPatchRequest", null);
exports.ProductController = ProductController = ProductController_1 = __decorate([
    (0, swagger_1.ApiTags)('products'),
    (0, common_1.Controller)('products'),
    __metadata("design:paramtypes", [typeof (_a = typeof routing_service_1.RoutingService !== "undefined" && routing_service_1.RoutingService) === "function" ? _a : Object])
], ProductController);


/***/ }),
/* 23 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.SharedModule = void 0;
const common_1 = __webpack_require__(3);
const config_1 = __webpack_require__(4);
const jwt_1 = __webpack_require__(5);
const config_2 = __webpack_require__(4);
const terminus_1 = __webpack_require__(6);
const axios_1 = __webpack_require__(7);
const health_controller_1 = __webpack_require__(24);
const all_exceptions_filter_1 = __webpack_require__(25);
const logging_interceptor_1 = __webpack_require__(26);
const circuit_breaker_service_1 = __webpack_require__(13);
const rate_limit_factory_1 = __webpack_require__(27);
const cache_service_1 = __webpack_require__(19);
const cache_interceptor_1 = __webpack_require__(18);
let SharedModule = class SharedModule {
};
exports.SharedModule = SharedModule;
exports.SharedModule = SharedModule = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({
        imports: [
            jwt_1.JwtModule.registerAsync({
                imports: [config_1.ConfigModule],
                inject: [config_2.ConfigService],
                useFactory: (configService) => ({
                    secret: configService.get('JWT_SECRET', 'your-secret-key'),
                    signOptions: {
                        expiresIn: configService.get('JWT_EXPIRES_IN', '1h'),
                    },
                }),
            }),
            terminus_1.TerminusModule,
            axios_1.HttpModule,
        ],
        controllers: [health_controller_1.HealthController],
        providers: [
            all_exceptions_filter_1.AllExceptionsFilter,
            logging_interceptor_1.LoggingInterceptor,
            circuit_breaker_service_1.CircuitBreakerService,
            cache_service_1.CacheService,
            cache_interceptor_1.CacheInterceptor,
            {
                provide: 'AUTH_RATE_LIMIT_GUARD',
                useFactory: () => rate_limit_factory_1.RateLimitFactory.createAuthRateLimitGuard(),
            },
            {
                provide: 'API_RATE_LIMIT_GUARD',
                useFactory: () => rate_limit_factory_1.RateLimitFactory.createApiRateLimitGuard(),
            },
            {
                provide: 'STORE_RATE_LIMIT_GUARD',
                useFactory: () => rate_limit_factory_1.RateLimitFactory.createStoreRateLimitGuard(),
            },
            {
                provide: 'PRODUCT_RATE_LIMIT_GUARD',
                useFactory: () => rate_limit_factory_1.RateLimitFactory.createProductRateLimitGuard(),
            },
        ],
        exports: [
            jwt_1.JwtModule,
            all_exceptions_filter_1.AllExceptionsFilter,
            logging_interceptor_1.LoggingInterceptor,
            circuit_breaker_service_1.CircuitBreakerService,
            cache_service_1.CacheService,
            cache_interceptor_1.CacheInterceptor,
            'AUTH_RATE_LIMIT_GUARD',
            'API_RATE_LIMIT_GUARD',
            'STORE_RATE_LIMIT_GUARD',
            'PRODUCT_RATE_LIMIT_GUARD',
        ],
    })
], SharedModule);


/***/ }),
/* 24 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var HealthController_1;
var _a, _b, _c, _d, _e, _f;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.HealthController = void 0;
const common_1 = __webpack_require__(3);
const swagger_1 = __webpack_require__(10);
const terminus_1 = __webpack_require__(6);
const config_1 = __webpack_require__(4);
const circuit_breaker_service_1 = __webpack_require__(13);
let HealthController = HealthController_1 = class HealthController {
    constructor(health, http, disk, memory, configService, circuitBreaker) {
        this.health = health;
        this.http = http;
        this.disk = disk;
        this.memory = memory;
        this.configService = configService;
        this.circuitBreaker = circuitBreaker;
        this.logger = new common_1.Logger(HealthController_1.name);
    }
    async check() {
        this.logger.log('Overall health check requested');
        return this.health.check([
            () => Promise.resolve({ apiGateway: { status: 'up' } }),
            async () => this.disk.checkStorage('storage', { path: '/', thresholdPercent: 0.9 }),
            async () => this.memory.checkHeap('memory_heap', 300 * 1024 * 1024),
            async () => this.memory.checkRSS('memory_rss', 300 * 1024 * 1024),
            async () => {
                const userServiceUrl = this.configService.get('USER_SERVICE_URL', 'http://localhost:3001/api');
                try {
                    return await this.http.pingCheck('userService', `${userServiceUrl}/health`);
                }
                catch (error) {
                    this.logger.warn(`User Service health check failed: ${error.message}`);
                    return { userService: { status: 'down', message: error.message } };
                }
            },
            async () => {
                const storeServiceUrl = this.configService.get('STORE_SERVICE_URL', 'http://localhost:3002/api');
                try {
                    return await this.http.pingCheck('storeService', `${storeServiceUrl}/health`);
                }
                catch (error) {
                    this.logger.warn(`Store Service health check failed: ${error.message}`);
                    return { storeService: { status: 'down', message: error.message } };
                }
            },
        ]);
    }
    async checkServices() {
        this.logger.log('Services health check requested');
        return this.health.check([
            async () => {
                const userServiceUrl = this.configService.get('USER_SERVICE_URL', 'http://localhost:3001/api');
                try {
                    return await this.http.pingCheck('userService', `${userServiceUrl}/health`);
                }
                catch (error) {
                    this.logger.warn(`User Service health check failed: ${error.message}`);
                    return { userService: { status: 'down', message: error.message } };
                }
            },
            async () => {
                const storeServiceUrl = this.configService.get('STORE_SERVICE_URL', 'http://localhost:3002/api');
                try {
                    return await this.http.pingCheck('storeService', `${storeServiceUrl}/health`);
                }
                catch (error) {
                    this.logger.warn(`Store Service health check failed: ${error.message}`);
                    return { storeService: { status: 'down', message: error.message } };
                }
            },
        ]);
    }
    async checkService(name) {
        this.logger.log(`Service health check requested for: ${name}`);
        const serviceMap = {
            user: this.configService.get('USER_SERVICE_URL', 'http://localhost:3001/api'),
            store: this.configService.get('STORE_SERVICE_URL', 'http://localhost:3002/api'),
        };
        const serviceUrl = serviceMap[name];
        if (!serviceUrl) {
            throw new Error(`Unknown service: ${name}`);
        }
        return this.health.check([
            async () => {
                try {
                    return await this.http.pingCheck(name + 'Service', `${serviceUrl}/health`);
                }
                catch (error) {
                    this.logger.warn(`${name} Service health check failed: ${error.message}`);
                    return { [name + 'Service']: { status: 'down', message: error.message } };
                }
            },
        ]);
    }
    getCircuitBreakerStatus() {
        this.logger.log('Circuit breaker status requested');
        return {
            status: 'ok',
            circuitBreakers: this.circuitBreaker.getStatus(),
        };
    }
};
exports.HealthController = HealthController;
__decorate([
    (0, common_1.Get)(),
    (0, terminus_1.HealthCheck)(),
    (0, swagger_1.ApiOperation)({ summary: 'Check overall system health' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'System is healthy' }),
    (0, swagger_1.ApiResponse)({ status: 503, description: 'System is unhealthy' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], HealthController.prototype, "check", null);
__decorate([
    (0, common_1.Get)('services'),
    (0, terminus_1.HealthCheck)(),
    (0, swagger_1.ApiOperation)({ summary: 'Check all microservices health' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'All services health status' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], HealthController.prototype, "checkServices", null);
__decorate([
    (0, common_1.Get)('service/:name'),
    (0, terminus_1.HealthCheck)(),
    (0, swagger_1.ApiOperation)({ summary: 'Check specific microservice health' }),
    (0, swagger_1.ApiParam)({ name: 'name', enum: ['user', 'store'], description: 'Service name' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Service health status' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid service name' }),
    __param(0, (0, common_1.Param)('name')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], HealthController.prototype, "checkService", null);
__decorate([
    (0, common_1.Get)('circuit-breaker'),
    (0, swagger_1.ApiOperation)({ summary: 'Get circuit breaker status' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Circuit breaker status' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], HealthController.prototype, "getCircuitBreakerStatus", null);
exports.HealthController = HealthController = HealthController_1 = __decorate([
    (0, swagger_1.ApiTags)('health'),
    (0, common_1.Controller)('health'),
    __metadata("design:paramtypes", [typeof (_a = typeof terminus_1.HealthCheckService !== "undefined" && terminus_1.HealthCheckService) === "function" ? _a : Object, typeof (_b = typeof terminus_1.HttpHealthIndicator !== "undefined" && terminus_1.HttpHealthIndicator) === "function" ? _b : Object, typeof (_c = typeof terminus_1.DiskHealthIndicator !== "undefined" && terminus_1.DiskHealthIndicator) === "function" ? _c : Object, typeof (_d = typeof terminus_1.MemoryHealthIndicator !== "undefined" && terminus_1.MemoryHealthIndicator) === "function" ? _d : Object, typeof (_e = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _e : Object, typeof (_f = typeof circuit_breaker_service_1.CircuitBreakerService !== "undefined" && circuit_breaker_service_1.CircuitBreakerService) === "function" ? _f : Object])
], HealthController);


/***/ }),
/* 25 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var AllExceptionsFilter_1;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AllExceptionsFilter = void 0;
const common_1 = __webpack_require__(3);
let AllExceptionsFilter = AllExceptionsFilter_1 = class AllExceptionsFilter {
    constructor() {
        this.logger = new common_1.Logger(AllExceptionsFilter_1.name);
    }
    catch(exception, host) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse();
        const request = ctx.getRequest();
        let status = common_1.HttpStatus.INTERNAL_SERVER_ERROR;
        let message = 'Internal Server Error';
        let error = 'Internal Server Error';
        if (exception instanceof common_1.HttpException) {
            status = exception.getStatus();
            const exceptionResponse = exception.getResponse();
            if (typeof exceptionResponse === 'object' && exceptionResponse !== null) {
                message = exceptionResponse.message || exception.message;
                error = exceptionResponse.error || 'Error';
            }
            else {
                message = exception.message;
            }
        }
        else if (exception instanceof Error) {
            message = exception.message;
        }
        this.logger.error(`${request.method} ${request.url} ${status} - ${message}`, exception instanceof Error ? exception.stack : '');
        response.status(status).json({
            statusCode: status,
            timestamp: new Date().toISOString(),
            path: request.url,
            error,
            message,
        });
    }
};
exports.AllExceptionsFilter = AllExceptionsFilter;
exports.AllExceptionsFilter = AllExceptionsFilter = AllExceptionsFilter_1 = __decorate([
    (0, common_1.Catch)()
], AllExceptionsFilter);


/***/ }),
/* 26 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var LoggingInterceptor_1;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.LoggingInterceptor = void 0;
const common_1 = __webpack_require__(3);
const operators_1 = __webpack_require__(16);
let LoggingInterceptor = LoggingInterceptor_1 = class LoggingInterceptor {
    constructor() {
        this.logger = new common_1.Logger(LoggingInterceptor_1.name);
    }
    intercept(context, next) {
        const ctx = context.switchToHttp();
        const request = ctx.getRequest();
        const response = ctx.getResponse();
        const { method, url, body, headers } = request;
        const userAgent = headers['user-agent'] || 'unknown';
        const contentLength = headers['content-length'] || 0;
        const requestId = headers['x-request-id'] || this.generateRequestId();
        response.setHeader('x-request-id', requestId);
        const correlationId = headers['x-correlation-id'] || 'unknown';
        const startTime = Date.now();
        this.logger.log(`[${requestId}] [${correlationId}] ${method} ${url} - User-Agent: ${userAgent} - Content-Length: ${contentLength}`);
        if (Object.keys(body || {}).length > 0) {
            this.logger.debug(`[${requestId}] [${correlationId}] Request Body: ${this.sanitizeBody(body)}`);
        }
        return next.handle().pipe((0, operators_1.tap)({
            next: (data) => {
                const endTime = Date.now();
                const duration = endTime - startTime;
                this.logger.log(`[${requestId}] [${correlationId}] ${method} ${url} ${response.statusCode} - ${duration}ms`);
                if (data && process.env.NODE_ENV === 'development') {
                    this.logger.debug(`[${requestId}] [${correlationId}] Response Body: ${this.sanitizeResponse(data)}`);
                }
            },
            error: (error) => {
                const endTime = Date.now();
                const duration = endTime - startTime;
                this.logger.error(`[${requestId}] [${correlationId}] ${method} ${url} ${error.status || 500} - ${duration}ms - ${error.message}`);
            },
        }));
    }
    generateRequestId() {
        return `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
    sanitizeBody(body) {
        if (!body)
            return 'empty';
        const sanitized = Object.assign({}, body);
        if (sanitized.password)
            sanitized.password = '***';
        if (sanitized.passwordConfirmation)
            sanitized.passwordConfirmation = '***';
        if (sanitized.token)
            sanitized.token = '***';
        if (sanitized.accessToken)
            sanitized.accessToken = '***';
        if (sanitized.refreshToken)
            sanitized.refreshToken = '***';
        return JSON.stringify(sanitized);
    }
    sanitizeResponse(data) {
        if (!data)
            return 'empty';
        const sanitized = Object.assign({}, data);
        if (sanitized.password)
            sanitized.password = '***';
        if (sanitized.token)
            sanitized.token = '***';
        if (sanitized.accessToken)
            sanitized.accessToken = '***';
        if (sanitized.refreshToken)
            sanitized.refreshToken = '***';
        return JSON.stringify(sanitized);
    }
};
exports.LoggingInterceptor = LoggingInterceptor;
exports.LoggingInterceptor = LoggingInterceptor = LoggingInterceptor_1 = __decorate([
    (0, common_1.Injectable)()
], LoggingInterceptor);


/***/ }),
/* 27 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.RateLimitFactory = void 0;
const rate_limit_guard_1 = __webpack_require__(28);
class RateLimitFactory {
    static createAuthRateLimitGuard() {
        return new rate_limit_guard_1.RateLimitGuard({
            windowMs: 15 * 60 * 1000,
            max: 10,
            message: 'Too many authentication attempts, please try again later.',
        });
    }
    static createApiRateLimitGuard() {
        return new rate_limit_guard_1.RateLimitGuard({
            windowMs: 60 * 1000,
            max: 100,
            message: 'Too many API requests, please try again later.',
        });
    }
    static createStoreRateLimitGuard() {
        return new rate_limit_guard_1.RateLimitGuard({
            windowMs: 60 * 1000,
            max: 50,
            message: 'Too many store requests, please try again later.',
        });
    }
    static createProductRateLimitGuard() {
        return new rate_limit_guard_1.RateLimitGuard({
            windowMs: 60 * 1000,
            max: 200,
            message: 'Too many product requests, please try again later.',
        });
    }
}
exports.RateLimitFactory = RateLimitFactory;


/***/ }),
/* 28 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RateLimitGuard_1;
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.RateLimitGuard = void 0;
const common_1 = __webpack_require__(3);
let RateLimitGuard = RateLimitGuard_1 = class RateLimitGuard {
    constructor(options) {
        this.logger = new common_1.Logger(RateLimitGuard_1.name);
        this.requestMap = new Map();
        this.options = {
            windowMs: (options === null || options === void 0 ? void 0 : options.windowMs) || 60 * 1000,
            max: (options === null || options === void 0 ? void 0 : options.max) || 100,
            message: (options === null || options === void 0 ? void 0 : options.message) || 'Too many requests, please try again later.',
        };
        setInterval(() => this.cleanupOldRequests(), 60 * 1000);
    }
    canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const key = this.generateKey(request);
        const now = Date.now();
        const requests = this.requestMap.get(key) || [];
        const recentRequests = requests.filter(time => time > now - this.options.windowMs);
        if (recentRequests.length >= this.options.max) {
            this.logger.warn(`Rate limit exceeded for ${key}`);
            throw new common_1.HttpException(this.options.message, common_1.HttpStatus.TOO_MANY_REQUESTS);
        }
        recentRequests.push(now);
        this.requestMap.set(key, recentRequests);
        return true;
    }
    generateKey(request) {
        const ip = request.ip ||
            request.connection.remoteAddress ||
            request.headers['x-forwarded-for'] ||
            'unknown';
        return `${ip}:${request.path}`;
    }
    cleanupOldRequests() {
        const now = Date.now();
        for (const [key, requests] of this.requestMap.entries()) {
            const recentRequests = requests.filter(time => time > now - this.options.windowMs);
            if (recentRequests.length === 0) {
                this.requestMap.delete(key);
            }
            else {
                this.requestMap.set(key, recentRequests);
            }
        }
    }
};
exports.RateLimitGuard = RateLimitGuard;
exports.RateLimitGuard = RateLimitGuard = RateLimitGuard_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof Partial !== "undefined" && Partial) === "function" ? _a : Object])
], RateLimitGuard);


/***/ }),
/* 29 */
/***/ ((module) => {

module.exports = require("nest-winston");

/***/ }),
/* 30 */
/***/ ((module) => {

module.exports = require("winston");

/***/ }),
/* 31 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var CorrelationIdMiddleware_1;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.CorrelationIdMiddleware = void 0;
const common_1 = __webpack_require__(3);
const uuid_1 = __webpack_require__(32);
let CorrelationIdMiddleware = CorrelationIdMiddleware_1 = class CorrelationIdMiddleware {
    constructor() {
        this.logger = new common_1.Logger(CorrelationIdMiddleware_1.name);
    }
    use(req, res, next) {
        const correlationId = req.headers['x-correlation-id'] || (0, uuid_1.v4)();
        req.headers['x-correlation-id'] = correlationId;
        res.setHeader('x-correlation-id', correlationId);
        this.logger.log(`Request ${req.method} ${req.url} assigned correlation ID: ${correlationId}`);
        next();
    }
};
exports.CorrelationIdMiddleware = CorrelationIdMiddleware;
exports.CorrelationIdMiddleware = CorrelationIdMiddleware = CorrelationIdMiddleware_1 = __decorate([
    (0, common_1.Injectable)()
], CorrelationIdMiddleware);


/***/ }),
/* 32 */
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NIL: () => (/* reexport safe */ _nil_js__WEBPACK_IMPORTED_MODULE_4__["default"]),
/* harmony export */   parse: () => (/* reexport safe */ _parse_js__WEBPACK_IMPORTED_MODULE_8__["default"]),
/* harmony export */   stringify: () => (/* reexport safe */ _stringify_js__WEBPACK_IMPORTED_MODULE_7__["default"]),
/* harmony export */   v1: () => (/* reexport safe */ _v1_js__WEBPACK_IMPORTED_MODULE_0__["default"]),
/* harmony export */   v3: () => (/* reexport safe */ _v3_js__WEBPACK_IMPORTED_MODULE_1__["default"]),
/* harmony export */   v4: () => (/* reexport safe */ _v4_js__WEBPACK_IMPORTED_MODULE_2__["default"]),
/* harmony export */   v5: () => (/* reexport safe */ _v5_js__WEBPACK_IMPORTED_MODULE_3__["default"]),
/* harmony export */   validate: () => (/* reexport safe */ _validate_js__WEBPACK_IMPORTED_MODULE_6__["default"]),
/* harmony export */   version: () => (/* reexport safe */ _version_js__WEBPACK_IMPORTED_MODULE_5__["default"])
/* harmony export */ });
/* harmony import */ var _v1_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(33);
/* harmony import */ var _v3_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(39);
/* harmony import */ var _v4_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(43);
/* harmony import */ var _v5_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(45);
/* harmony import */ var _nil_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(47);
/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(48);
/* harmony import */ var _validate_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(37);
/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(36);
/* harmony import */ var _parse_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(41);










/***/ }),
/* 33 */
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _rng_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(34);
/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(36);

 // **`v1()` - Generate time-based UUID**
//
// Inspired by https://github.com/LiosK/UUID.js
// and http://docs.python.org/library/uuid.html

let _nodeId;

let _clockseq; // Previous uuid creation time


let _lastMSecs = 0;
let _lastNSecs = 0; // See https://github.com/uuidjs/uuid for API details

function v1(options, buf, offset) {
  let i = buf && offset || 0;
  const b = buf || new Array(16);
  options = options || {};
  let node = options.node || _nodeId;
  let clockseq = options.clockseq !== undefined ? options.clockseq : _clockseq; // node and clockseq need to be initialized to random values if they're not
  // specified.  We do this lazily to minimize issues related to insufficient
  // system entropy.  See #189

  if (node == null || clockseq == null) {
    const seedBytes = options.random || (options.rng || _rng_js__WEBPACK_IMPORTED_MODULE_0__["default"])();

    if (node == null) {
      // Per 4.5, create and 48-bit node id, (47 random bits + multicast bit = 1)
      node = _nodeId = [seedBytes[0] | 0x01, seedBytes[1], seedBytes[2], seedBytes[3], seedBytes[4], seedBytes[5]];
    }

    if (clockseq == null) {
      // Per 4.2.2, randomize (14 bit) clockseq
      clockseq = _clockseq = (seedBytes[6] << 8 | seedBytes[7]) & 0x3fff;
    }
  } // UUID timestamps are 100 nano-second units since the Gregorian epoch,
  // (1582-10-15 00:00).  JSNumbers aren't precise enough for this, so
  // time is handled internally as 'msecs' (integer milliseconds) and 'nsecs'
  // (100-nanoseconds offset from msecs) since unix epoch, 1970-01-01 00:00.


  let msecs = options.msecs !== undefined ? options.msecs : Date.now(); // Per 4.2.1.2, use count of uuid's generated during the current clock
  // cycle to simulate higher resolution clock

  let nsecs = options.nsecs !== undefined ? options.nsecs : _lastNSecs + 1; // Time since last uuid creation (in msecs)

  const dt = msecs - _lastMSecs + (nsecs - _lastNSecs) / 10000; // Per 4.2.1.2, Bump clockseq on clock regression

  if (dt < 0 && options.clockseq === undefined) {
    clockseq = clockseq + 1 & 0x3fff;
  } // Reset nsecs if clock regresses (new clockseq) or we've moved onto a new
  // time interval


  if ((dt < 0 || msecs > _lastMSecs) && options.nsecs === undefined) {
    nsecs = 0;
  } // Per 4.2.1.2 Throw error if too many uuids are requested


  if (nsecs >= 10000) {
    throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");
  }

  _lastMSecs = msecs;
  _lastNSecs = nsecs;
  _clockseq = clockseq; // Per 4.1.4 - Convert from unix epoch to Gregorian epoch

  msecs += 12219292800000; // `time_low`

  const tl = ((msecs & 0xfffffff) * 10000 + nsecs) % 0x100000000;
  b[i++] = tl >>> 24 & 0xff;
  b[i++] = tl >>> 16 & 0xff;
  b[i++] = tl >>> 8 & 0xff;
  b[i++] = tl & 0xff; // `time_mid`

  const tmh = msecs / 0x100000000 * 10000 & 0xfffffff;
  b[i++] = tmh >>> 8 & 0xff;
  b[i++] = tmh & 0xff; // `time_high_and_version`

  b[i++] = tmh >>> 24 & 0xf | 0x10; // include version

  b[i++] = tmh >>> 16 & 0xff; // `clock_seq_hi_and_reserved` (Per 4.2.2 - include variant)

  b[i++] = clockseq >>> 8 | 0x80; // `clock_seq_low`

  b[i++] = clockseq & 0xff; // `node`

  for (let n = 0; n < 6; ++n) {
    b[i + n] = node[n];
  }

  return buf || (0,_stringify_js__WEBPACK_IMPORTED_MODULE_1__.unsafeStringify)(b);
}

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (v1);

/***/ }),
/* 34 */
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ rng)
/* harmony export */ });
/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(35);
/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);

const rnds8Pool = new Uint8Array(256); // # of random values to pre-allocate

let poolPtr = rnds8Pool.length;
function rng() {
  if (poolPtr > rnds8Pool.length - 16) {
    crypto__WEBPACK_IMPORTED_MODULE_0___default().randomFillSync(rnds8Pool);
    poolPtr = 0;
  }

  return rnds8Pool.slice(poolPtr, poolPtr += 16);
}

/***/ }),
/* 35 */
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),
/* 36 */
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   unsafeStringify: () => (/* binding */ unsafeStringify)
/* harmony export */ });
/* harmony import */ var _validate_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(37);

/**
 * Convert array of 16 byte values to UUID string format of the form:
 * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX
 */

const byteToHex = [];

for (let i = 0; i < 256; ++i) {
  byteToHex.push((i + 0x100).toString(16).slice(1));
}

function unsafeStringify(arr, offset = 0) {
  // Note: Be careful editing this code!  It's been tuned for performance
  // and works in ways you may not expect. See https://github.com/uuidjs/uuid/pull/434
  return byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + '-' + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + '-' + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + '-' + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + '-' + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]];
}

function stringify(arr, offset = 0) {
  const uuid = unsafeStringify(arr, offset); // Consistency check for valid UUID.  If this throws, it's likely due to one
  // of the following:
  // - One or more input array values don't map to a hex octet (leading to
  // "undefined" in the uuid)
  // - Invalid input values for the RFC `version` or `variant` fields

  if (!(0,_validate_js__WEBPACK_IMPORTED_MODULE_0__["default"])(uuid)) {
    throw TypeError('Stringified UUID is invalid');
  }

  return uuid;
}

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (stringify);

/***/ }),
/* 37 */
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _regex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(38);


function validate(uuid) {
  return typeof uuid === 'string' && _regex_js__WEBPACK_IMPORTED_MODULE_0__["default"].test(uuid);
}

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (validate);

/***/ }),
/* 38 */
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i);

/***/ }),
/* 39 */
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _v35_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(40);
/* harmony import */ var _md5_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(42);


const v3 = (0,_v35_js__WEBPACK_IMPORTED_MODULE_0__["default"])('v3', 0x30, _md5_js__WEBPACK_IMPORTED_MODULE_1__["default"]);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (v3);

/***/ }),
/* 40 */
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DNS: () => (/* binding */ DNS),
/* harmony export */   URL: () => (/* binding */ URL),
/* harmony export */   "default": () => (/* binding */ v35)
/* harmony export */ });
/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(36);
/* harmony import */ var _parse_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(41);



function stringToBytes(str) {
  str = unescape(encodeURIComponent(str)); // UTF8 escape

  const bytes = [];

  for (let i = 0; i < str.length; ++i) {
    bytes.push(str.charCodeAt(i));
  }

  return bytes;
}

const DNS = '6ba7b810-9dad-11d1-80b4-00c04fd430c8';
const URL = '6ba7b811-9dad-11d1-80b4-00c04fd430c8';
function v35(name, version, hashfunc) {
  function generateUUID(value, namespace, buf, offset) {
    var _namespace;

    if (typeof value === 'string') {
      value = stringToBytes(value);
    }

    if (typeof namespace === 'string') {
      namespace = (0,_parse_js__WEBPACK_IMPORTED_MODULE_0__["default"])(namespace);
    }

    if (((_namespace = namespace) === null || _namespace === void 0 ? void 0 : _namespace.length) !== 16) {
      throw TypeError('Namespace must be array-like (16 iterable integer values, 0-255)');
    } // Compute hash of namespace and value, Per 4.3
    // Future: Use spread syntax when supported on all platforms, e.g. `bytes =
    // hashfunc([...namespace, ... value])`


    let bytes = new Uint8Array(16 + value.length);
    bytes.set(namespace);
    bytes.set(value, namespace.length);
    bytes = hashfunc(bytes);
    bytes[6] = bytes[6] & 0x0f | version;
    bytes[8] = bytes[8] & 0x3f | 0x80;

    if (buf) {
      offset = offset || 0;

      for (let i = 0; i < 16; ++i) {
        buf[offset + i] = bytes[i];
      }

      return buf;
    }

    return (0,_stringify_js__WEBPACK_IMPORTED_MODULE_1__.unsafeStringify)(bytes);
  } // Function#name is not settable on some platforms (#270)


  try {
    generateUUID.name = name; // eslint-disable-next-line no-empty
  } catch (err) {} // For CommonJS default export support


  generateUUID.DNS = DNS;
  generateUUID.URL = URL;
  return generateUUID;
}

/***/ }),
/* 41 */
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _validate_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(37);


function parse(uuid) {
  if (!(0,_validate_js__WEBPACK_IMPORTED_MODULE_0__["default"])(uuid)) {
    throw TypeError('Invalid UUID');
  }

  let v;
  const arr = new Uint8Array(16); // Parse ########-....-....-....-............

  arr[0] = (v = parseInt(uuid.slice(0, 8), 16)) >>> 24;
  arr[1] = v >>> 16 & 0xff;
  arr[2] = v >>> 8 & 0xff;
  arr[3] = v & 0xff; // Parse ........-####-....-....-............

  arr[4] = (v = parseInt(uuid.slice(9, 13), 16)) >>> 8;
  arr[5] = v & 0xff; // Parse ........-....-####-....-............

  arr[6] = (v = parseInt(uuid.slice(14, 18), 16)) >>> 8;
  arr[7] = v & 0xff; // Parse ........-....-....-####-............

  arr[8] = (v = parseInt(uuid.slice(19, 23), 16)) >>> 8;
  arr[9] = v & 0xff; // Parse ........-....-....-....-############
  // (Use "/" to avoid 32-bit truncation when bit-shifting high-order bytes)

  arr[10] = (v = parseInt(uuid.slice(24, 36), 16)) / 0x10000000000 & 0xff;
  arr[11] = v / 0x100000000 & 0xff;
  arr[12] = v >>> 24 & 0xff;
  arr[13] = v >>> 16 & 0xff;
  arr[14] = v >>> 8 & 0xff;
  arr[15] = v & 0xff;
  return arr;
}

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (parse);

/***/ }),
/* 42 */
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(35);
/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);


function md5(bytes) {
  if (Array.isArray(bytes)) {
    bytes = Buffer.from(bytes);
  } else if (typeof bytes === 'string') {
    bytes = Buffer.from(bytes, 'utf8');
  }

  return crypto__WEBPACK_IMPORTED_MODULE_0___default().createHash('md5').update(bytes).digest();
}

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (md5);

/***/ }),
/* 43 */
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _native_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(44);
/* harmony import */ var _rng_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(34);
/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(36);




function v4(options, buf, offset) {
  if (_native_js__WEBPACK_IMPORTED_MODULE_0__["default"].randomUUID && !buf && !options) {
    return _native_js__WEBPACK_IMPORTED_MODULE_0__["default"].randomUUID();
  }

  options = options || {};
  const rnds = options.random || (options.rng || _rng_js__WEBPACK_IMPORTED_MODULE_1__["default"])(); // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`

  rnds[6] = rnds[6] & 0x0f | 0x40;
  rnds[8] = rnds[8] & 0x3f | 0x80; // Copy bytes to buffer, if provided

  if (buf) {
    offset = offset || 0;

    for (let i = 0; i < 16; ++i) {
      buf[offset + i] = rnds[i];
    }

    return buf;
  }

  return (0,_stringify_js__WEBPACK_IMPORTED_MODULE_2__.unsafeStringify)(rnds);
}

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (v4);

/***/ }),
/* 44 */
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(35);
/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
  randomUUID: (crypto__WEBPACK_IMPORTED_MODULE_0___default().randomUUID)
});

/***/ }),
/* 45 */
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _v35_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(40);
/* harmony import */ var _sha1_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(46);


const v5 = (0,_v35_js__WEBPACK_IMPORTED_MODULE_0__["default"])('v5', 0x50, _sha1_js__WEBPACK_IMPORTED_MODULE_1__["default"]);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (v5);

/***/ }),
/* 46 */
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(35);
/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);


function sha1(bytes) {
  if (Array.isArray(bytes)) {
    bytes = Buffer.from(bytes);
  } else if (typeof bytes === 'string') {
    bytes = Buffer.from(bytes, 'utf8');
  }

  return crypto__WEBPACK_IMPORTED_MODULE_0___default().createHash('sha1').update(bytes).digest();
}

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (sha1);

/***/ }),
/* 47 */
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ('00000000-0000-0000-0000-000000000000');

/***/ }),
/* 48 */
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _validate_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(37);


function version(uuid) {
  if (!(0,_validate_js__WEBPACK_IMPORTED_MODULE_0__["default"])(uuid)) {
    throw TypeError('Invalid UUID');
  }

  return parseInt(uuid.slice(14, 15), 16);
}

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (version);

/***/ }),
/* 49 */
/***/ ((module) => {

module.exports = require("helmet");

/***/ }),
/* 50 */
/***/ ((module) => {

module.exports = require("compression");

/***/ }),
/* 51 */
/***/ ((module) => {

module.exports = require("cookie-parser");

/***/ }),
/* 52 */
/***/ ((module) => {

module.exports = require("express-rate-limit");

/***/ }),
/* 53 */
/***/ ((module) => {

module.exports = require("path");

/***/ })
/******/ 	]);
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other modules in the chunk.
(() => {
var exports = __webpack_exports__;

Object.defineProperty(exports, "__esModule", ({ value: true }));
const core_1 = __webpack_require__(1);
const app_module_1 = __webpack_require__(2);
const common_1 = __webpack_require__(3);
const swagger_1 = __webpack_require__(10);
const helmet_1 = __webpack_require__(49);
const compression = __webpack_require__(50);
const cookieParser = __webpack_require__(51);
const config_1 = __webpack_require__(4);
const common_2 = __webpack_require__(3);
const nest_winston_1 = __webpack_require__(29);
const winston = __webpack_require__(30);
const express_rate_limit_1 = __webpack_require__(52);
const path_1 = __webpack_require__(53);
async function bootstrap() {
    const logger = nest_winston_1.WinstonModule.createLogger({
        transports: [
            new winston.transports.Console({
                format: winston.format.combine(winston.format.timestamp(), winston.format.ms(), winston.format.colorize(), winston.format.printf((info) => `${info.timestamp} ${info.level}: ${info.message}`)),
            }),
            new winston.transports.File({
                filename: 'logs/error.log',
                level: 'error',
                format: winston.format.combine(winston.format.timestamp(), winston.format.json()),
            }),
            new winston.transports.File({
                filename: 'logs/combined.log',
                format: winston.format.combine(winston.format.timestamp(), winston.format.json()),
            }),
        ],
    });
    const app = await core_1.NestFactory.create(app_module_1.AppModule, {
        logger: logger,
    });
    const configService = app.get(config_1.ConfigService);
    app.setGlobalPrefix('api');
    app.enableCors();
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true,
        transform: true,
        forbidNonWhitelisted: true,
        transformOptions: {
            enableImplicitConversion: true,
        },
    }));
    app.use((0, helmet_1.default)());
    app.use(compression());
    app.use(cookieParser());
    app.use((0, express_rate_limit_1.default)({
        windowMs: 15 * 60 * 1000,
        max: 100,
    }));
    app.useStaticAssets((0, path_1.join)(__dirname, '..', 'public'), {
        prefix: '/dashboard',
    });
    const config = new swagger_1.DocumentBuilder()
        .setTitle('API Gateway')
        .setDescription('The API Gateway for Social Commerce Platform')
        .setVersion('1.0')
        .addBearerAuth()
        .build();
    const document = swagger_1.SwaggerModule.createDocument(app, config);
    swagger_1.SwaggerModule.setup('api/docs', app, document);
    const port = configService.get('HTTP_PORT', 3000);
    await app.listen(port);
    common_2.Logger.log(`API Gateway is running on: http://localhost:${port}`);
    common_2.Logger.log(`Swagger documentation is available at: http://localhost:${port}/api/docs`);
    common_2.Logger.log(`Health dashboard is available at: http://localhost:${port}/dashboard/health-dashboard.html`);
}
bootstrap();

})();

/******/ })()
;