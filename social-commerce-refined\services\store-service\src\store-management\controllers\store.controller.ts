import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  Query,
  ParseIntPipe,
  DefaultValuePipe,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { StoreService } from '../services/store.service';
import { CreateStoreDto, UpdateStoreDto } from '../dto';
import { Store } from '../entities/store.entity';
import { JwtAuthGuard } from '../../shared/guards/jwt-auth.guard';

@ApiTags('stores')
@Controller('stores')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class StoreController {
  private readonly logger = new Logger(StoreController.name);

  constructor(private readonly storeService: StoreService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new store' })
  @ApiResponse({
    status: 201,
    description: 'Store created successfully',
    type: Store,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async create(
    @Body() createStoreDto: CreateStoreDto,
    @Request() req: any,
  ): Promise<Store> {
    this.logger.log(`Creating store for user ${req.user.id}`);
    return this.storeService.create(createStoreDto, req.user.id);
  }

  @Get()
  @ApiOperation({ summary: 'Get all stores with pagination' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiQuery({ name: 'ownerId', required: false, type: String, description: 'Filter by owner ID' })
  @ApiResponse({
    status: 200,
    description: 'Stores retrieved successfully',
  })
  async findAll(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('ownerId') ownerId?: string,
  ) {
    return this.storeService.findAll(ownerId, page, limit);
  }

  @Get('limits')
  @ApiOperation({ summary: 'Get store creation limits for current user' })
  @ApiResponse({
    status: 200,
    description: 'Store limits retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        maxStores: { type: 'number', example: 5 },
        currentStores: { type: 'number', example: 2 },
        remainingStores: { type: 'number', example: 3 },
        canCreateMore: { type: 'boolean', example: true },
      },
    },
  })
  async getStoreLimits(@Request() req: any) {
    return this.storeService.getStoreLimits(req.user.id);
  }

  @Get('my-stores')
  @ApiOperation({ summary: 'Get current user\'s stores' })
  @ApiResponse({
    status: 200,
    description: 'User stores retrieved successfully',
    type: [Store],
  })
  async getMyStores(@Request() req: any): Promise<Store[]> {
    return this.storeService.findByOwnerId(req.user.id);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get store by ID' })
  @ApiParam({ name: 'id', type: 'string', description: 'Store ID' })
  @ApiResponse({
    status: 200,
    description: 'Store retrieved successfully',
    type: Store,
  })
  @ApiResponse({ status: 404, description: 'Store not found' })
  async findOne(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<Store> {
    return this.storeService.findOne(id, req.user.id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update store' })
  @ApiParam({ name: 'id', type: 'string', description: 'Store ID' })
  @ApiResponse({
    status: 200,
    description: 'Store updated successfully',
    type: Store,
  })
  @ApiResponse({ status: 404, description: 'Store not found' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async update(
    @Param('id') id: string,
    @Body() updateStoreDto: UpdateStoreDto,
    @Request() req: any,
  ): Promise<Store> {
    return this.storeService.update(id, updateStoreDto, req.user.id);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete store' })
  @ApiParam({ name: 'id', type: 'string', description: 'Store ID' })
  @ApiResponse({
    status: 200,
    description: 'Store deleted successfully',
  })
  @ApiResponse({ status: 404, description: 'Store not found' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async remove(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<{ message: string }> {
    await this.storeService.remove(id, req.user.id);
    return { message: 'Store deleted successfully' };
  }
}
