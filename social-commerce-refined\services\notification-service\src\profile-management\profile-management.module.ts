import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProfileController } from './controllers/profile.controller';
import { ProfileService } from './services/profile.service';
import { ProfileRepository } from './repositories/profile.repository';
import { Profile } from './entities/profile.entity';
import { AuthenticationModule } from '../authentication/authentication.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Profile]),
    AuthenticationModule,
  ],
  controllers: [ProfileController],
  providers: [
    ProfileService,
    ProfileRepository,
  ],
  exports: [
    ProfileService,
    ProfileRepository,
  ],
})
export class ProfileManagementModule {}
