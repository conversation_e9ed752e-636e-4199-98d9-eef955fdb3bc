import { Injectable } from '@nestjs/common';
import { Repository, DataSource, SelectQueryBuilder } from 'typeorm';
import { Product, ProductStatus } from '../entities/product.entity';

export interface ProductFilters {
  storeId?: string;
  categoryId?: string;
  status?: ProductStatus;
  minPrice?: number;
  maxPrice?: number;
  search?: string;
  inStock?: boolean;
}

export interface ProductSearchOptions {
  page?: number;
  limit?: number;
  sortBy?: 'name' | 'price' | 'createdAt' | 'rating' | 'salesCount';
  sortOrder?: 'ASC' | 'DESC';
}

@Injectable()
export class ProductRepository extends Repository<Product> {
  constructor(private dataSource: DataSource) {
    super(Product, dataSource.createEntityManager());
  }

  /**
   * Find products by store ID
   */
  async findByStoreId(storeId: string): Promise<Product[]> {
    return this.find({
      where: { storeId, isActive: true },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Find product by ID and store ID (for ownership validation)
   */
  async findByIdAndStoreId(id: string, storeId: string): Promise<Product | null> {
    return this.findOne({
      where: { id, storeId, isActive: true },
    });
  }

  /**
   * Find active products by store ID
   */
  async findActiveByStoreId(storeId: string): Promise<Product[]> {
    return this.find({
      where: { 
        storeId, 
        isActive: true,
        status: ProductStatus.ACTIVE 
      },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Count products by store ID
   */
  async countByStoreId(storeId: string): Promise<number> {
    return this.count({
      where: { storeId, isActive: true },
    });
  }

  /**
   * Search products with filters and pagination
   */
  async searchProducts(
    filters: ProductFilters = {},
    options: ProductSearchOptions = {},
  ): Promise<{ products: Product[]; total: number }> {
    const {
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
    } = options;

    const queryBuilder = this.createQueryBuilder('product');

    // Apply filters
    this.applyFilters(queryBuilder, filters);

    // Apply search
    if (filters.search) {
      queryBuilder.andWhere(
        '(product.name ILIKE :search OR product.description ILIKE :search)',
        { search: `%${filters.search}%` }
      );
    }

    // Apply sorting
    queryBuilder.orderBy(`product.${sortBy}`, sortOrder);

    // Apply pagination
    queryBuilder
      .skip((page - 1) * limit)
      .take(limit);

    const [products, total] = await queryBuilder.getManyAndCount();

    return { products, total };
  }

  /**
   * Find products by category
   */
  async findByCategory(categoryId: string, options: ProductSearchOptions = {}): Promise<Product[]> {
    const { limit = 10, sortBy = 'createdAt', sortOrder = 'DESC' } = options;

    return this.find({
      where: { 
        categoryId, 
        isActive: true,
        status: ProductStatus.ACTIVE 
      },
      order: { [sortBy]: sortOrder },
      take: limit,
    });
  }

  /**
   * Find low stock products for a store
   */
  async findLowStockProducts(storeId: string, threshold: number = 5): Promise<Product[]> {
    return this.find({
      where: { 
        storeId, 
        isActive: true,
        status: ProductStatus.ACTIVE 
      },
      order: { stock: 'ASC' },
    }).then(products => products.filter(product => product.stock <= threshold));
  }

  /**
   * Update product view count
   */
  async incrementViewCount(id: string): Promise<void> {
    await this.increment({ id }, 'viewCount', 1);
  }

  /**
   * Update product sales count
   */
  async incrementSalesCount(id: string, quantity: number = 1): Promise<void> {
    await this.increment({ id }, 'salesCount', quantity);
  }

  /**
   * Apply filters to query builder
   */
  private applyFilters(queryBuilder: SelectQueryBuilder<Product>, filters: ProductFilters): void {
    queryBuilder.where('product.isActive = :isActive', { isActive: true });

    if (filters.storeId) {
      queryBuilder.andWhere('product.storeId = :storeId', { storeId: filters.storeId });
    }

    if (filters.categoryId) {
      queryBuilder.andWhere('product.categoryId = :categoryId', { categoryId: filters.categoryId });
    }

    if (filters.status) {
      queryBuilder.andWhere('product.status = :status', { status: filters.status });
    }

    if (filters.minPrice !== undefined) {
      queryBuilder.andWhere('product.price >= :minPrice', { minPrice: filters.minPrice });
    }

    if (filters.maxPrice !== undefined) {
      queryBuilder.andWhere('product.price <= :maxPrice', { maxPrice: filters.maxPrice });
    }

    if (filters.inStock) {
      queryBuilder.andWhere('product.stock > 0');
    }
  }
}
