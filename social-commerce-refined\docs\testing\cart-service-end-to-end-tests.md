# Cart Service End-to-End Test Plan

## 🎯 **Test Overview**

This document outlines comprehensive end-to-end tests for the Cart Service to validate all functionality from basic operations to complex user journeys.

## 📋 **Test Categories**

### **1. Service Health & Infrastructure**
### **2. Guest Cart Operations**
### **3. Authenticated User Cart Operations**
### **4. Cart Item Management**
### **5. Product Integration**
### **6. Error Handling & Edge Cases**
### **7. Performance & Load Testing**

---

## 🏥 **1. Service Health & Infrastructure Tests**

### **Test 1.1: Service Health Check**
```bash
# Test direct Cart Service health
curl http://localhost:3005/api/health/simple

# Expected: {"status":"ok","service":"cart-service","timestamp":"..."}
```

### **Test 1.2: Database Connectivity**
```bash
# Test full health check with database
curl http://localhost:3005/api/health

# Expected: All components healthy including database
```

### **Test 1.3: API Gateway Integration**
```bash
# Test Cart Service through API Gateway
curl http://localhost:3000/api/health/service/cart

# Expected: {"status":"ok","info":{"cartService":{"status":"up"}}}
```

### **Test 1.4: Swagger Documentation**
```bash
# Verify API documentation is accessible
curl http://localhost:3005/api/docs

# Expected: Swagger UI HTML response
```

---

## 🛒 **2. Guest Cart Operations**

### **Test 2.1: Create Guest Cart**
```bash
# Create a new guest cart
curl -X POST "http://localhost:3000/api/carts" \
  -H "Content-Type: application/json" \
  -d '{"sessionId":"test-guest-001","isGuestCart":true}'

# Expected: New cart with guest properties
```

### **Test 2.2: Get or Create Guest Cart**
```bash
# Get existing or create new guest cart
curl "http://localhost:3000/api/carts/guest?sessionId=test-guest-002"

# Expected: Cart for session (new or existing)
```

### **Test 2.3: Retrieve Guest Cart by ID**
```bash
# Get cart by specific ID
curl "http://localhost:3000/api/carts/{CART_ID}"

# Expected: Complete cart details
```

### **Test 2.4: Guest Cart Persistence**
```bash
# Test 1: Create cart with session
curl "http://localhost:3000/api/carts/guest?sessionId=persistence-test"

# Test 2: Retrieve same cart with same session
curl "http://localhost:3000/api/carts/guest?sessionId=persistence-test"

# Expected: Same cart ID returned both times
```

---

## 👤 **3. Authenticated User Cart Operations**

### **Test 3.1: User Authentication**
```bash
# First, authenticate user to get JWT token
curl -X POST "http://localhost:3000/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Expected: JWT token in response
```

### **Test 3.2: Get Current User Cart**
```bash
# Get authenticated user's cart
curl "http://localhost:3000/api/carts/current" \
  -H "Authorization: Bearer {JWT_TOKEN}"

# Expected: User's cart or new cart created
```

### **Test 3.3: User Cart Creation**
```bash
# Create cart for authenticated user
curl -X POST "http://localhost:3000/api/carts" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {JWT_TOKEN}" \
  -d '{"userId":"{USER_ID}"}'

# Expected: Cart associated with user
```

---

## 📦 **4. Cart Item Management**

### **Test 4.1: Add Item to Cart**
```bash
# Add product to cart
curl -X POST "http://localhost:3000/api/carts/{CART_ID}/items" \
  -H "Content-Type: application/json" \
  -d '{
    "productId": "{PRODUCT_ID}",
    "quantity": 2,
    "selectedOptions": {"size": "L", "color": "blue"}
  }'

# Expected: Updated cart with new item
```

### **Test 4.2: Update Cart Item Quantity**
```bash
# Update item quantity
curl -X PUT "http://localhost:3000/api/carts/{CART_ID}/items/{ITEM_ID}" \
  -H "Content-Type: application/json" \
  -d '{"quantity": 5}'

# Expected: Updated cart with new quantity
```

### **Test 4.3: Update Cart Item Options**
```bash
# Update item options
curl -X PUT "http://localhost:3000/api/carts/{CART_ID}/items/{ITEM_ID}" \
  -H "Content-Type: application/json" \
  -d '{
    "quantity": 3,
    "selectedOptions": {"size": "XL", "color": "red"}
  }'

# Expected: Updated cart with new options
```

### **Test 4.4: Remove Single Cart Item**
```bash
# Remove specific item from cart
curl -X DELETE "http://localhost:3000/api/carts/{CART_ID}/items/{ITEM_ID}"

# Expected: Updated cart without the item
```

### **Test 4.5: Clear All Cart Items**
```bash
# Clear entire cart
curl -X DELETE "http://localhost:3000/api/carts/{CART_ID}/items"

# Expected: Empty cart with zero items
```

---

## 🔗 **5. Product Integration Tests**

### **Test 5.1: Product Validation**
```bash
# Test adding non-existent product
curl -X POST "http://localhost:3000/api/carts/{CART_ID}/items" \
  -H "Content-Type: application/json" \
  -d '{"productId": "non-existent-id", "quantity": 1}'

# Expected: 404 Product not found error
```

### **Test 5.2: Product Information Caching**
```bash
# Add product and verify cached information
curl -X POST "http://localhost:3000/api/carts/{CART_ID}/items" \
  -H "Content-Type: application/json" \
  -d '{"productId": "{VALID_PRODUCT_ID}", "quantity": 1}'

# Expected: Cart item with cached product name, image, store info
```

### **Test 5.3: Multiple Products from Different Stores**
```bash
# Add products from different stores
curl -X POST "http://localhost:3000/api/carts/{CART_ID}/items" \
  -H "Content-Type: application/json" \
  -d '{"productId": "{PRODUCT_1}", "quantity": 1}'

curl -X POST "http://localhost:3000/api/carts/{CART_ID}/items" \
  -H "Content-Type: application/json" \
  -d '{"productId": "{PRODUCT_2}", "quantity": 2}'

# Expected: Cart with items from multiple stores
```

---

## ⚠️ **6. Error Handling & Edge Cases**

### **Test 6.1: Invalid Cart ID**
```bash
# Test with non-existent cart ID
curl "http://localhost:3000/api/carts/invalid-cart-id"

# Expected: 404 Cart not found
```

### **Test 6.2: Invalid Item ID**
```bash
# Test removing non-existent item
curl -X DELETE "http://localhost:3000/api/carts/{CART_ID}/items/invalid-item-id"

# Expected: 404 Cart item not found
```

### **Test 6.3: Invalid Quantity**
```bash
# Test with zero or negative quantity
curl -X POST "http://localhost:3000/api/carts/{CART_ID}/items" \
  -H "Content-Type: application/json" \
  -d '{"productId": "{PRODUCT_ID}", "quantity": 0}'

# Expected: 400 Validation error
```

### **Test 6.4: Missing Required Fields**
```bash
# Test without required productId
curl -X POST "http://localhost:3000/api/carts/{CART_ID}/items" \
  -H "Content-Type: application/json" \
  -d '{"quantity": 1}'

# Expected: 400 Validation error
```

### **Test 6.5: Malformed JSON**
```bash
# Test with invalid JSON
curl -X POST "http://localhost:3000/api/carts/{CART_ID}/items" \
  -H "Content-Type: application/json" \
  -d '{"productId": "{PRODUCT_ID", "quantity": 1}'

# Expected: 400 Bad request
```

---

## 🚀 **7. Performance & Load Testing**

### **Test 7.1: Concurrent Cart Operations**
```bash
# Create multiple carts simultaneously
for i in {1..10}; do
  curl -X POST "http://localhost:3000/api/carts" \
    -H "Content-Type: application/json" \
    -d "{\"sessionId\":\"load-test-$i\",\"isGuestCart\":true}" &
done
wait

# Expected: All carts created successfully
```

### **Test 7.2: Large Cart Management**
```bash
# Add many items to single cart
for i in {1..50}; do
  curl -X POST "http://localhost:3000/api/carts/{CART_ID}/items" \
    -H "Content-Type: application/json" \
    -d "{\"productId\":\"{PRODUCT_ID}\",\"quantity\":1}" &
done
wait

# Expected: Cart with 50 items, proper totals calculated
```

### **Test 7.3: Response Time Measurement**
```bash
# Measure response times
time curl "http://localhost:3000/api/carts/guest?sessionId=perf-test"

# Expected: Response time < 100ms for basic operations
```

---

## 📊 **Test Execution Results Template**

### **Test Run Information**
- **Date**: [Test Date]
- **Environment**: Development
- **Services**: Cart Service v1.0
- **Database**: PostgreSQL

### **Results Summary**
- **Total Tests**: 25
- **Passed**: [X]
- **Failed**: [X]
- **Skipped**: [X]

### **Performance Metrics**
- **Average Response Time**: [X]ms
- **Memory Usage**: [X]MB
- **Database Connections**: [X]
- **Error Rate**: [X]%

### **Issues Found**
1. [Issue Description]
2. [Issue Description]

### **Recommendations**
1. [Recommendation]
2. [Recommendation]

---

**Status**: 📋 **TEST PLAN READY FOR EXECUTION**
