# Build Stage
FROM node:18-alpine AS build

WORKDIR /app

# Copy package.json and package-lock.json
COPY services/{{SERVICE_NAME_KEBAB}}/package*.json ./

# Copy shared libraries (from project root context)
COPY libs/common ./libs/common
# COPY libs/messaging ./libs/messaging  # Temporarily disabled

# Install dependencies
RUN npm install --legacy-peer-deps

# Copy source code
COPY services/{{SERVICE_NAME_KEBAB}}/src ./src
COPY services/{{SERVICE_NAME_KEBAB}}/tsconfig.json ./
COPY services/{{SERVICE_NAME_KEBAB}}/nest-cli.json ./

# Build the application
RUN npm run build

# Production Stage
FROM node:18-alpine

WORKDIR /app

# Copy package.json and package-lock.json
COPY services/{{SERVICE_NAME_KEBAB}}/package*.json ./

# Install production dependencies only
RUN npm install --only=production --legacy-peer-deps

# Copy built application from build stage
COPY --from=build /app/dist ./dist
COPY --from=build /app/libs ./libs

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001

# Change ownership of the app directory
RUN chown -R nestjs:nodejs /app
USER nestjs

# Set environment variables
ENV NODE_ENV=production

# Expose port
EXPOSE {{SERVICE_PORT}}

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:{{SERVICE_PORT}}/api/health || exit 1

# Start the application
CMD ["node", "dist/main"]
