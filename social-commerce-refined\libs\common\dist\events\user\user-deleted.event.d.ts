import { BaseEvent } from '../base-event.interface';
export declare class UserDeletedEvent implements BaseEvent<UserDeletedPayload> {
    id: string;
    type: string;
    version: string;
    timestamp: string;
    producer: string;
    payload: UserDeletedPayload;
    constructor(payload: UserDeletedPayload);
}
export interface UserDeletedPayload {
    id: string;
    email: string;
    deletedAt: string;
}
