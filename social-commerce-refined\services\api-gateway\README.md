# API Gateway

The API Gateway is the entry point for all client requests to the Social Commerce Platform. It routes requests to the appropriate microservices, handles authentication, and provides a unified API for clients.

## Features

- **Request Routing**: Routes requests to the appropriate microservices
- **Authentication**: Handles JWT authentication and forwards tokens to microservices
- **Rate Limiting**: Protects against abuse
- **Logging**: Comprehensive request/response logging
- **Error Handling**: Graceful error handling and forwarding
- **Health Monitoring**: Monitors the health of all microservices
- **API Documentation**: Swagger documentation for all endpoints

## Architecture

The API Gateway follows a feature-based organization pattern:

```
src/
├── routing/           # Request routing
│   ├── controllers/   # Controllers for each microservice
│   └── services/      # Routing service
├── middleware/        # Middleware
│   ├── auth/          # Authentication middleware
│   └── logging/       # Logging middleware
└── shared/            # Shared modules and utilities
    ├── controllers/   # Health controller
    ├── filters/       # Exception filters
    ├── interceptors/  # Interceptors
    └── utils/         # Utility functions
```

## API Endpoints

### User Service

- `GET /api/users` - Get all users
- `GET /api/users/:id` - Get a user by ID
- `POST /api/users` - Create a new user
- `PUT /api/users/:id` - Update a user
- `DELETE /api/users/:id` - Delete a user
- `POST /api/users/auth/register` - Register a new user
- `POST /api/users/auth/login` - Login a user
- `GET /api/users/auth/profile` - Get user profile

### Store Service

- `GET /api/stores` - Get all stores
- `GET /api/stores/:id` - Get a store by ID
- `POST /api/stores` - Create a new store
- `PUT /api/stores/:id` - Update a store
- `DELETE /api/stores/:id` - Delete a store
- `GET /api/stores/:storeId/products` - Get all products for a store
- `POST /api/stores/:storeId/products` - Create a new product for a store

### Health

- `GET /api/health` - Check API Gateway health

## Getting Started

### Prerequisites

- Node.js (v18 or later)
- Running microservices (User Service, Store Service, etc.)

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   cd services/api-gateway
   npm install
   ```
3. Configure environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```
4. Start the service:
   ```bash
   npm run start:dev
   ```

### Docker

You can also run the service using Docker:

```bash
docker build -t api-gateway .
docker run -p 3000:3000 api-gateway
```

## Testing

```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# Test coverage
npm run test:cov
```

## Documentation

API documentation is available at `/api/docs` when the service is running.

## Adding a New Service

To add a new service to the API Gateway:

1. Create a new controller in `src/routing/controllers/`
2. Add the controller to the `RoutingModule`
3. Add the service URL to the `.env` file
4. Add the service to the health check in `src/shared/controllers/health.controller.ts`

## License

MIT
