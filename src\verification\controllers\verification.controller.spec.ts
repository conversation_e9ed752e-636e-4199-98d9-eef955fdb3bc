import { Test, TestingModule } from '@nestjs/testing';
import { VerificationController } from './verification.controller';
import { VerificationService } from '../services/verification.service';
import { VerificationToken, TokenType } from '../entities/verification-token.entity';

describe('VerificationController', () => {
  let controller: VerificationController;
  let verificationService: VerificationService;

  beforeEach(async () => {
    // Create mock implementation
    const mockVerificationService = {
      createEmailVerificationToken: jest.fn(),
      verifyEmail: jest.fn(),
      createPhoneVerificationToken: jest.fn(),
      verifyPhone: jest.fn(),
      createPasswordResetToken: jest.fn(),
      resetPassword: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [VerificationController],
      providers: [
        {
          provide: VerificationService,
          useValue: mockVerificationService,
        },
      ],
    }).compile();

    controller = module.get<VerificationController>(VerificationController);
    verificationService = module.get<VerificationService>(VerificationService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('sendEmailVerification', () => {
    it('should send email verification token', async () => {
      // Arrange
      const userId = '123';
      const token = {
        id: '456',
        token: 'verification-token',
        type: TokenType.EMAIL_VERIFICATION,
        userId,
      } as VerificationToken;

      jest.spyOn(verificationService, 'createEmailVerificationToken').mockResolvedValue(token);

      // Act
      const result = await controller.sendEmailVerification(userId);

      // Assert
      expect(result).toEqual({ message: 'Verification email sent successfully' });
      expect(verificationService.createEmailVerificationToken).toHaveBeenCalledWith(userId);
    });
  });

  describe('verifyEmail', () => {
    it('should verify email with token', async () => {
      // Arrange
      const tokenString = 'verification-token';
      jest.spyOn(verificationService, 'verifyEmail').mockResolvedValue(true);

      // Act
      const result = await controller.verifyEmail(tokenString);

      // Assert
      expect(result).toEqual({ verified: true, message: 'Email verified successfully' });
      expect(verificationService.verifyEmail).toHaveBeenCalledWith(tokenString);
    });
  });

  describe('sendPhoneVerification', () => {
    it('should send phone verification token', async () => {
      // Arrange
      const userId = '123';
      const token = {
        id: '456',
        token: 'verification-token',
        type: TokenType.PHONE_VERIFICATION,
        userId,
      } as VerificationToken;

      jest.spyOn(verificationService, 'createPhoneVerificationToken').mockResolvedValue(token);

      // Act
      const result = await controller.sendPhoneVerification(userId);

      // Assert
      expect(result).toEqual({ message: 'Verification SMS sent successfully' });
      expect(verificationService.createPhoneVerificationToken).toHaveBeenCalledWith(userId);
    });
  });

  describe('verifyPhone', () => {
    it('should verify phone with token', async () => {
      // Arrange
      const userId = '123';
      const tokenString = 'verification-token';
      jest.spyOn(verificationService, 'verifyPhone').mockResolvedValue(true);

      // Act
      const result = await controller.verifyPhone(userId, tokenString);

      // Assert
      expect(result).toEqual({ verified: true, message: 'Phone verified successfully' });
      expect(verificationService.verifyPhone).toHaveBeenCalledWith(userId, tokenString);
    });
  });

  describe('forgotPassword', () => {
    it('should request password reset', async () => {
      // Arrange
      const email = '<EMAIL>';
      const token = {
        id: '456',
        token: 'reset-token',
        type: TokenType.PASSWORD_RESET,
      } as VerificationToken;

      jest.spyOn(verificationService, 'createPasswordResetToken').mockResolvedValue(token);

      // Act
      const result = await controller.forgotPassword(email);

      // Assert
      expect(result).toEqual({ message: 'Password reset email sent successfully' });
      expect(verificationService.createPasswordResetToken).toHaveBeenCalledWith(email);
    });
  });

  describe('resetPassword', () => {
    it('should reset password with token', async () => {
      // Arrange
      const tokenString = 'reset-token';
      const password = 'newPassword123';
      jest.spyOn(verificationService, 'resetPassword').mockResolvedValue(true);

      // Act
      const result = await controller.resetPassword(tokenString, password);

      // Assert
      expect(result).toEqual({ reset: true, message: 'Password reset successfully' });
      expect(verificationService.resetPassword).toHaveBeenCalledWith(tokenString, password);
    });
  });

  describe('microservice endpoints', () => {
    it('should verify email token via microservice', async () => {
      // Arrange
      const data = { token: 'verification-token' };
      jest.spyOn(verificationService, 'verifyEmail').mockResolvedValue(true);

      // Act
      const result = await controller.verifyEmailToken(data);

      // Assert
      expect(result).toBe(true);
      expect(verificationService.verifyEmail).toHaveBeenCalledWith(data.token);
    });

    it('should handle forgot password request via microservice', async () => {
      // Arrange
      const data = { email: '<EMAIL>' };
      const token = {
        id: '456',
        token: 'reset-token',
        type: TokenType.PASSWORD_RESET,
      } as VerificationToken;

      jest.spyOn(verificationService, 'createPasswordResetToken').mockResolvedValue(token);

      // Act
      const result = await controller.forgotPasswordRequest(data);

      // Assert
      expect(result).toEqual({ message: 'Password reset email sent successfully' });
      expect(verificationService.createPasswordResetToken).toHaveBeenCalledWith(data.email);
    });

    it('should handle reset password request via microservice', async () => {
      // Arrange
      const data = { token: 'reset-token', password: 'newPassword123' };
      jest.spyOn(verificationService, 'resetPassword').mockResolvedValue(true);

      // Act
      const result = await controller.resetPasswordRequest(data);

      // Assert
      expect(result).toBe(true);
      expect(verificationService.resetPassword).toHaveBeenCalledWith(data.token, data.password);
    });
  });
});
