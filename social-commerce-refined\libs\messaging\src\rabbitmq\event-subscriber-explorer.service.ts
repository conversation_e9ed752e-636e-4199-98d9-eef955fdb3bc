import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { DiscoveryService, MetadataScanner, Reflector } from '@nestjs/core';
import { InstanceWrapper } from '@nestjs/core/injector/instance-wrapper';
import { RabbitMQService } from './rabbitmq.service';
import { EVENT_SUBSCRIBER_METADATA, EventSubscriberOptions } from '../decorators/event-subscriber.decorator';

@Injectable()
export class EventSubscriberExplorerService implements OnModuleInit {
  private readonly logger = new Logger(EventSubscriberExplorerService.name);
  private readonly exchange = 'events';

  constructor(
    private readonly discoveryService: DiscoveryService,
    private readonly metadataScanner: MetadataScanner,
    private readonly reflector: Reflector,
    private readonly rabbitMQService: RabbitMQService,
  ) {}

  async onModuleInit() {
    await this.explore();
  }

  async explore() {
    const providers = this.discoveryService.getProviders();
    const controllers = this.discoveryService.getControllers();
    const wrappers = [...providers, ...controllers];

    for (const wrapper of wrappers) {
      await this.exploreWrapper(wrapper);
    }
  }

  private async exploreWrapper(wrapper: InstanceWrapper) {
    if (!wrapper.instance || !wrapper.metatype) {
      return;
    }

    const instance = wrapper.instance;
    const prototype = Object.getPrototypeOf(instance);

    this.metadataScanner.scanFromPrototype(instance, prototype, async (methodName) => {
      await this.exploreMethod(instance, methodName);
    });
  }

  private async exploreMethod(instance: any, methodName: string) {
    const options = this.reflector.get<EventSubscriberOptions>(
      EVENT_SUBSCRIBER_METADATA,
      instance[methodName],
    );

    if (!options) {
      return;
    }

    await this.registerEventSubscriber(instance, methodName, options);
  }

  private async registerEventSubscriber(instance: any, methodName: string, options: EventSubscriberOptions) {
    const { event, queue } = options;

    try {
      // Create the events exchange if it doesn't exist
      await this.rabbitMQService.createExchange(this.exchange, 'topic', {
        durable: true,
      });

      // Create the queue
      await this.rabbitMQService.createQueue(queue, {
        durable: true,
      });

      // Bind the queue to the exchange with the event type as the routing key
      await this.rabbitMQService.bindQueue(queue, this.exchange, event);

      // Register the consumer
      await this.rabbitMQService.consume(queue, async (msg) => {
        try {
          const content = msg.content.toString();
          const eventData = JSON.parse(content);
          
          this.logger.debug(`Received event ${event} in queue ${queue}`);
          
          // Call the handler method
          await instance[methodName](eventData);
          
          this.logger.debug(`Processed event ${event} in queue ${queue}`);
        } catch (error) {
          this.logger.error(`Error processing event ${event} in queue ${queue}: ${error.message}`);
          throw error;
        }
      });

      this.logger.log(`Registered event subscriber for ${event} in queue ${queue}`);
    } catch (error) {
      this.logger.error(`Error registering event subscriber for ${event} in queue ${queue}: ${error.message}`);
    }
  }
}
