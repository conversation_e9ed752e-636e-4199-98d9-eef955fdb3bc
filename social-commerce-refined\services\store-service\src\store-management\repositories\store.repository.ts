import { Injectable } from '@nestjs/common';
import { Repository, DataSource } from 'typeorm';
import { Store, StoreStatus } from '../entities/store.entity';

@Injectable()
export class StoreRepository extends Repository<Store> {
  constructor(private dataSource: DataSource) {
    super(Store, dataSource.createEntityManager());
  }

  /**
   * Find stores by owner ID
   */
  async findByOwnerId(ownerId: string): Promise<Store[]> {
    return this.find({
      where: { ownerId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Find store by ID and owner ID (for ownership validation)
   */
  async findByIdAndOwnerId(id: string, ownerId: string): Promise<Store | null> {
    return this.findOne({
      where: { id, ownerId },
    });
  }

  /**
   * Find active stores by owner ID
   */
  async findActiveByOwnerId(ownerId: string): Promise<Store[]> {
    return this.find({
      where: { 
        ownerId, 
        status: StoreStatus.ACTIVE 
      },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Count stores by owner ID
   */
  async countByOwnerId(ownerId: string): Promise<number> {
    return this.count({
      where: { ownerId },
    });
  }

  /**
   * Find stores with pagination
   */
  async findWithPagination(
    page: number = 1,
    limit: number = 10,
    ownerId?: string,
  ): Promise<{ stores: Store[]; total: number }> {
    const queryBuilder = this.createQueryBuilder('store');

    if (ownerId) {
      queryBuilder.where('store.ownerId = :ownerId', { ownerId });
    }

    queryBuilder
      .orderBy('store.createdAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit);

    const [stores, total] = await queryBuilder.getManyAndCount();

    return { stores, total };
  }
}
