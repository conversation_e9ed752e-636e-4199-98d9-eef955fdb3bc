(()=>{var e={};e.id=2797,e.ids=[2797],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},85268:(e,r,a)=>{"use strict";a.r(r),a.d(r,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>o,routeModule:()=>h,tree:()=>c});var t=a(67096),s=a(16132),l=a(37284),i=a.n(l),d=a(32564),n={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);a.d(r,n);let c=["",{children:["search",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,9467)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\search\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,9291,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\search\\page.tsx"],m="/search/page",x={require:a,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/search/page",pathname:"/search",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},70248:(e,r,a)=>{Promise.resolve().then(a.bind(a,94287))},94287:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>SearchPage});var t,s,l=a(30784),i=a(9885),d=a(57114),n=a(27870),c=a(14379),o=a(92299),m=a(52451),x=a.n(m);(function(e){e.ALL="all",e.USER="user",e.PRODUCT="product",e.STORE="store",e.POST="post",e.HASHTAG="hashtag"})(t||(t={})),function(e){e.RELEVANCE="relevance",e.DATE_DESC="date_desc",e.DATE_ASC="date_asc",e.POPULARITY="popularity",e.PRICE_ASC="price_asc",e.PRICE_DESC="price_desc"}(s||(s={}));var h=a(56663);let search_SearchAutocomplete=({query:e,onSelect:r,onSearch:a,className:s=""})=>{let{t:c}=(0,n.$G)("search"),m=(0,d.useRouter)(),[g,u]=(0,i.useState)(!1),y=(0,i.useRef)(null),{data:p=[],isLoading:v}=(0,o.ve)(e,{skip:!e||e.length<2});(0,i.useEffect)(()=>{let handleClickOutside=e=>{y.current&&!y.current.contains(e.target)&&u(!1)};return document.addEventListener("mousedown",handleClickOutside),()=>{document.removeEventListener("mousedown",handleClickOutside)}},[]),(0,i.useEffect)(()=>{e&&e.length>=2?u(!0):u(!1)},[e]);let handleSelect=e=>{u(!1),r?.(e),r||m.push(e.url)},renderResultItem=e=>{switch(e.type){case t.PRODUCT:return(0,l.jsxs)("div",{className:"flex items-center",children:[e.imageUrl&&l.jsx("div",{className:"flex-shrink-0 h-10 w-10 rounded-md overflow-hidden mr-3",children:l.jsx(x(),{src:e.imageUrl,alt:e.title,width:40,height:40,className:"object-cover"})}),(0,l.jsxs)("div",{className:"flex-1 min-w-0",children:[l.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 truncate",children:e.title}),l.jsx("p",{className:"text-sm text-primary-600 dark:text-primary-400",children:(0,h.xG)(e.price||0)})]})]});case t.STORE:return(0,l.jsxs)("div",{className:"flex items-center",children:[e.imageUrl&&l.jsx("div",{className:"flex-shrink-0 h-10 w-10 rounded-full overflow-hidden mr-3",children:l.jsx(x(),{src:e.imageUrl,alt:e.title,width:40,height:40,className:"object-cover"})}),(0,l.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,l.jsxs)("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 truncate",children:[e.title,e.isVerified&&l.jsx("span",{className:"ml-1 text-blue-500",children:"✓"})]}),l.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 truncate",children:c("store","Store")})]})]});case t.USER:return(0,l.jsxs)("div",{className:"flex items-center",children:[e.imageUrl&&l.jsx("div",{className:"flex-shrink-0 h-10 w-10 rounded-full overflow-hidden mr-3",children:l.jsx(x(),{src:e.imageUrl,alt:e.title,width:40,height:40,className:"object-cover"})}),(0,l.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,l.jsxs)("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 truncate",children:[e.title,e.isVerified&&l.jsx("span",{className:"ml-1 text-blue-500",children:"✓"})]}),(0,l.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400 truncate",children:["@",e.username]})]})]});default:return l.jsx("div",{className:"flex items-center",children:l.jsx("div",{className:"flex-1 min-w-0",children:l.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 truncate",children:e.title})})})}};return g?l.jsx("div",{ref:y,className:`absolute z-10 mt-2 w-full bg-white dark:bg-gray-800 rounded-md shadow-lg ${s}`,children:l.jsx("div",{className:"py-1",children:v?l.jsx("div",{className:"px-4 py-2 text-sm text-gray-500 dark:text-gray-400",children:c("loading","Loading...")}):0===p.length?l.jsx("div",{className:"px-4 py-2 text-sm text-gray-500 dark:text-gray-400",children:c("noResults","No results found")}):(0,l.jsxs)(l.Fragment,{children:[p.map(e=>l.jsx("button",{className:"w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:()=>handleSelect(e),children:renderResultItem(e)},`${e.type}-${e.id}`)),l.jsx("div",{className:"border-t border-gray-200 dark:border-gray-700 mt-1 pt-1",children:l.jsx("button",{className:"w-full text-left px-4 py-2 text-sm text-primary-600 dark:text-primary-400 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:()=>{u(!1),a?.(e),a||m.push(`/search?q=${encodeURIComponent(e)}`)},children:c("viewAllResults",'View all results for "{{query}}"',{query:e})})})]})})}):null},search_SearchInput=({initialQuery:e="",placeholder:r,onSearch:a,showSuggestions:t=!0,showAutocomplete:s=!0,className:m=""})=>{let{t:x}=(0,n.$G)("search"),{isRtl:h}=(0,c.g)(),g=(0,d.useRouter)(),[u,y]=(0,i.useState)(e),[p,v]=(0,i.useState)(!1),j=(0,i.useRef)(null),b=(0,i.useRef)(null),{data:k}=(0,o.TX)(),{data:f}=(0,o.Rs)(),[w]=(0,o.Ve)();(0,i.useEffect)(()=>{let handleClickOutside=e=>{b.current&&!b.current.contains(e.target)&&j.current&&!j.current.contains(e.target)&&v(!1)};return document.addEventListener("mousedown",handleClickOutside),()=>{document.removeEventListener("mousedown",handleClickOutside)}},[]);let handleSearch=()=>{u.trim()&&(w(u.trim()),a?a(u.trim()):g.push(`/search?q=${encodeURIComponent(u.trim())}`),v(!1))},handleSuggestionClick=e=>{y(e),w(e),a?a(e):g.push(`/search?q=${encodeURIComponent(e)}`),v(!1)};return(0,l.jsxs)("div",{className:`relative ${m}`,children:[(0,l.jsxs)("div",{className:"relative",children:[l.jsx("input",{ref:j,type:"text",value:u,onChange:e=>y(e.target.value),onKeyPress:e=>{"Enter"===e.key&&handleSearch()},onFocus:()=>v(!0),placeholder:r||x("searchPlaceholder","Search..."),className:"w-full px-4 py-2 pr-10 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"}),l.jsx("button",{type:"button",onClick:handleSearch,className:"absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300",children:l.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})})]}),s&&u.length>=2&&l.jsx(search_SearchAutocomplete,{query:u,onSelect:e=>{w(e.title),g.push(e.url),v(!1)},onSearch:e=>{a?a(e):g.push(`/search?q=${encodeURIComponent(e)}`)}}),t&&p&&u.length<2&&(0,l.jsxs)("div",{ref:b,className:"absolute z-10 mt-1 w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden",children:[f&&f.length>0&&(0,l.jsxs)("div",{className:"p-2",children:[l.jsx("h3",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 px-2 py-1",children:x("recentSearches","Recent Searches")}),l.jsx("div",{className:"mt-1",children:f.map((e,r)=>(0,l.jsxs)("button",{className:"w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md flex items-center",onClick:()=>handleSuggestionClick(e),children:[l.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),e]},r))})]}),k&&k.length>0&&(0,l.jsxs)("div",{className:"p-2 border-t border-gray-100 dark:border-gray-700",children:[l.jsx("h3",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 px-2 py-1",children:x("popularSearches","Popular Searches")}),l.jsx("div",{className:"mt-1",children:k.map((e,r)=>(0,l.jsxs)("button",{className:"w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md flex items-center",onClick:()=>handleSuggestionClick(e),children:[l.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})}),e]},r))})]})]})]})};var g=a(59872);let search_SearchFilters=({filters:e,onFilterChange:r,className:a=""})=>{let{t:d}=(0,n.$G)("search"),[c,o]=(0,i.useState)(e),handleFilterChange=(e,r)=>{let a={...c,[e]:r};o(a)};return(0,l.jsxs)("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 ${a}`,children:[l.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4",children:d("filters","Filters")}),(0,l.jsxs)("div",{className:"mb-4",children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:d("contentType","Content Type")}),l.jsx("div",{className:"grid grid-cols-2 gap-2",children:Object.values(t).map(e=>l.jsx("button",{type:"button",className:`px-3 py-2 text-sm rounded-md ${c.type===e?"bg-primary-100 text-primary-700 dark:bg-primary-900/30 dark:text-primary-400":"bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"}`,onClick:()=>handleFilterChange("type",e===c.type?void 0:e),children:d(`type.${e}`,e.charAt(0).toUpperCase()+e.slice(1))},e))})]}),(0,l.jsxs)("div",{className:"mb-4",children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:d("sortBy","Sort By")}),(0,l.jsxs)("select",{className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100",value:c.sortBy||"",onChange:e=>handleFilterChange("sortBy",e.target.value||void 0),children:[l.jsx("option",{value:"",children:d("sortBy.default","Default")}),l.jsx("option",{value:s.RELEVANCE,children:d("sortBy.relevance","Relevance")}),l.jsx("option",{value:s.DATE_DESC,children:d("sortBy.newest","Newest First")}),l.jsx("option",{value:s.DATE_ASC,children:d("sortBy.oldest","Oldest First")}),l.jsx("option",{value:s.POPULARITY,children:d("sortBy.popularity","Popularity")}),c.type===t.PRODUCT&&(0,l.jsxs)(l.Fragment,{children:[l.jsx("option",{value:s.PRICE_ASC,children:d("sortBy.priceAsc","Price: Low to High")}),l.jsx("option",{value:s.PRICE_DESC,children:d("sortBy.priceDesc","Price: High to Low")})]})]})]}),(0,l.jsxs)("div",{className:"mb-4",children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:d("dateRange","Date Range")}),(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,l.jsxs)("div",{children:[l.jsx("label",{className:"block text-xs text-gray-500 dark:text-gray-400 mb-1",children:d("from","From")}),l.jsx("input",{type:"date",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100",value:c.dateRange?.from||"",onChange:e=>{let r={...c.dateRange,from:e.target.value||void 0};handleFilterChange("dateRange",r)}})]}),(0,l.jsxs)("div",{children:[l.jsx("label",{className:"block text-xs text-gray-500 dark:text-gray-400 mb-1",children:d("to","To")}),l.jsx("input",{type:"date",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100",value:c.dateRange?.to||"",onChange:e=>{let r={...c.dateRange,to:e.target.value||void 0};handleFilterChange("dateRange",r)},min:c.dateRange?.from})]})]})]}),c.type===t.PRODUCT&&(0,l.jsxs)("div",{className:"mb-4",children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:d("priceRange","Price Range")}),(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,l.jsxs)("div",{children:[l.jsx("label",{className:"block text-xs text-gray-500 dark:text-gray-400 mb-1",children:d("min","Min")}),l.jsx("input",{type:"number",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100",value:c.priceRange?.min||"",onChange:e=>{let r=e.target.value?Number(e.target.value):void 0,a={...c.priceRange,min:r};handleFilterChange("priceRange",a)},min:0,placeholder:d("minPrice","Min price")})]}),(0,l.jsxs)("div",{children:[l.jsx("label",{className:"block text-xs text-gray-500 dark:text-gray-400 mb-1",children:d("max","Max")}),l.jsx("input",{type:"number",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100",value:c.priceRange?.max||"",onChange:e=>{let r=e.target.value?Number(e.target.value):void 0,a={...c.priceRange,max:r};handleFilterChange("priceRange",a)},min:c.priceRange?.min||0,placeholder:d("maxPrice","Max price")})]})]})]}),(0,l.jsxs)("div",{className:"mb-4",children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:d("tags","Tags")}),l.jsx("input",{type:"text",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100",placeholder:d("tagsPlaceholder","Enter tags separated by commas"),value:c.tags?.join(", ")||"",onChange:e=>{let r=e.target.value,a=r?r.split(",").map(e=>e.trim()).filter(Boolean):void 0;handleFilterChange("tags",a)}})]}),(0,l.jsxs)("div",{className:"flex justify-between mt-6",children:[l.jsx(g.Z,{variant:"outline",onClick:()=>{let e={};o(e),r(e)},children:d("reset","Reset")}),l.jsx(g.Z,{variant:"primary",onClick:()=>{r(c)},children:d("apply","Apply")})]})]})},search_AdvancedSearchFilters=({filters:e,onFilterChange:r,className:a=""})=>{let{t:d}=(0,n.$G)("search"),[c,o]=(0,i.useState)(e),[m,x]=(0,i.useState)(!1),handleFilterChange=(e,r)=>{let a={...c,[e]:r};o(a)};return(0,l.jsxs)("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 ${a}`,children:[(0,l.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[l.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:d("advancedFilters","Advanced Filters")}),l.jsx("button",{type:"button",className:"text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300",onClick:()=>x(!m),children:m?d("collapse","Collapse"):d("expand","Expand")})]}),(0,l.jsxs)("div",{className:"mb-4",children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:d("contentType","Content Type")}),l.jsx("div",{className:"grid grid-cols-2 gap-2",children:Object.values(t).map(e=>l.jsx("button",{type:"button",className:`px-3 py-2 text-sm rounded-md ${c.type===e?"bg-primary-100 text-primary-700 dark:bg-primary-900/30 dark:text-primary-400":"bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"}`,onClick:()=>handleFilterChange("type",e===c.type?void 0:e),children:d(`type.${e}`,e.charAt(0).toUpperCase()+e.slice(1))},e))})]}),m&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:d("sortBy","Sort By")}),(0,l.jsxs)("select",{className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100",value:c.sortBy||"",onChange:e=>handleFilterChange("sortBy",e.target.value||void 0),children:[l.jsx("option",{value:"",children:d("sortBy.default","Default")}),l.jsx("option",{value:s.RELEVANCE,children:d("sortBy.relevance","Relevance")}),l.jsx("option",{value:s.DATE_DESC,children:d("sortBy.newest","Newest First")}),l.jsx("option",{value:s.DATE_ASC,children:d("sortBy.oldest","Oldest First")}),l.jsx("option",{value:s.POPULARITY,children:d("sortBy.popularity","Popularity")}),c.type===t.PRODUCT&&(0,l.jsxs)(l.Fragment,{children:[l.jsx("option",{value:s.PRICE_ASC,children:d("sortBy.priceAsc","Price: Low to High")}),l.jsx("option",{value:s.PRICE_DESC,children:d("sortBy.priceDesc","Price: High to Low")})]})]})]}),(0,l.jsxs)("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:d("dateRange","Date Range")}),(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,l.jsxs)("div",{children:[l.jsx("label",{className:"block text-xs text-gray-500 dark:text-gray-400 mb-1",children:d("from","From")}),l.jsx("input",{type:"date",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100",value:c.dateRange?.from||"",onChange:e=>{let r={...c.dateRange,from:e.target.value||void 0};handleFilterChange("dateRange",r)}})]}),(0,l.jsxs)("div",{children:[l.jsx("label",{className:"block text-xs text-gray-500 dark:text-gray-400 mb-1",children:d("to","To")}),l.jsx("input",{type:"date",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100",value:c.dateRange?.to||"",onChange:e=>{let r={...c.dateRange,to:e.target.value||void 0};handleFilterChange("dateRange",r)},min:c.dateRange?.from})]})]})]}),c.type===t.PRODUCT&&(0,l.jsxs)("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:d("priceRange","Price Range")}),(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,l.jsxs)("div",{children:[l.jsx("label",{className:"block text-xs text-gray-500 dark:text-gray-400 mb-1",children:d("min","Min")}),l.jsx("input",{type:"number",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100",value:c.priceRange?.min||"",onChange:e=>{let r=e.target.value?Number(e.target.value):void 0,a={...c.priceRange,min:r};handleFilterChange("priceRange",a)},min:0,placeholder:d("minPrice","Min price")})]}),(0,l.jsxs)("div",{children:[l.jsx("label",{className:"block text-xs text-gray-500 dark:text-gray-400 mb-1",children:d("max","Max")}),l.jsx("input",{type:"number",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100",value:c.priceRange?.max||"",onChange:e=>{let r=e.target.value?Number(e.target.value):void 0,a={...c.priceRange,max:r};handleFilterChange("priceRange",a)},min:c.priceRange?.min||0,placeholder:d("maxPrice","Max price")})]})]})]}),(0,l.jsxs)("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:d("categories","Categories")}),l.jsx("div",{className:"space-y-2",children:["Electronics","Fashion","Home","Beauty","Sports"].map(e=>(0,l.jsxs)("div",{className:"flex items-center",children:[l.jsx("input",{type:"checkbox",id:`category-${e}`,className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded",checked:c.categories?.includes(e)||!1,onChange:r=>{let a=r.target.checked,t=c.categories||[],s=a?[...t,e]:t.filter(r=>r!==e);handleFilterChange("categories",s.length>0?s:void 0)}}),l.jsx("label",{htmlFor:`category-${e}`,className:"ml-2 block text-sm text-gray-700 dark:text-gray-300",children:e})]},e))})]}),(0,l.jsxs)("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:d("tags","Tags")}),l.jsx("input",{type:"text",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100",placeholder:d("tagsPlaceholder","Enter tags separated by commas"),value:c.tags?.join(", ")||"",onChange:e=>{let r=e.target.value,a=r?r.split(",").map(e=>e.trim()).filter(Boolean):void 0;handleFilterChange("tags",a)}}),l.jsx("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:d("tagsHelp","Example: organic, handmade, sale")})]}),c.type===t.PRODUCT&&(0,l.jsxs)("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:d("minimumRating","Minimum Rating")}),(0,l.jsxs)("div",{className:"flex items-center space-x-1",children:[[1,2,3,4,5].map(e=>l.jsx("button",{type:"button",className:`p-1 ${(c.rating||0)>=e?"text-yellow-400":"text-gray-300 dark:text-gray-600 hover:text-yellow-200"}`,onClick:()=>{let r=c.rating===e?e-1:e;handleFilterChange("rating",r>0?r:void 0)},children:l.jsx("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:l.jsx("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})})},e)),l.jsx("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:c.rating?`${c.rating} ${d("starsAndUp","stars & up")}`:d("anyRating","Any rating")})]})]}),c.type===t.PRODUCT&&(0,l.jsxs)("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:d("availability","Availability")}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[l.jsx("input",{type:"checkbox",id:"in-stock",className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded",checked:c.inStock||!1,onChange:e=>handleFilterChange("inStock",e.target.checked||void 0)}),l.jsx("label",{htmlFor:"in-stock",className:"ml-2 block text-sm text-gray-700 dark:text-gray-300",children:d("inStock","In Stock")})]}),(0,l.jsxs)("div",{className:"flex items-center",children:[l.jsx("input",{type:"checkbox",id:"on-sale",className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded",checked:c.onSale||!1,onChange:e=>handleFilterChange("onSale",e.target.checked||void 0)}),l.jsx("label",{htmlFor:"on-sale",className:"ml-2 block text-sm text-gray-700 dark:text-gray-300",children:d("onSale","On Sale")})]})]})]})]}),(0,l.jsxs)("div",{className:"flex justify-between mt-6",children:[l.jsx(g.Z,{variant:"outline",onClick:()=>{let e={};o(e),r(e)},children:d("reset","Reset")}),l.jsx(g.Z,{variant:"primary",onClick:()=>{r(c)},children:d("apply","Apply")})]})]})};var u=a(11440),y=a.n(u),p=a(73531);let search_SearchResultItem=({result:e,className:r=""})=>{let{t:a}=(0,n.$G)("search"),{isRtl:s}=(0,c.g)();switch(e.type){case t.USER:return l.jsx(y(),{href:`/profile/${e.username}`,className:`block ${r}`,children:(0,l.jsxs)("div",{className:"flex items-center p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-shadow",children:[(0,l.jsxs)("div",{className:"w-12 h-12 relative rounded-full overflow-hidden flex-shrink-0",children:[l.jsx(x(),{src:e.imageUrl||"/images/default-avatar.png",alt:e.displayName||e.username||"",fill:!0,className:"object-cover"}),e.isVerified&&l.jsx("div",{className:"absolute bottom-0 right-0 bg-primary-500 text-white p-0.5 rounded-full",children:l.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-3 w-3",viewBox:"0 0 20 20",fill:"currentColor",children:l.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]}),(0,l.jsxs)("div",{className:"ml-4 flex-1",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[l.jsx("h3",{className:"font-medium text-gray-900 dark:text-gray-100",children:e.displayName||e.username}),e.isVerified&&l.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 ml-1 text-primary-500",viewBox:"0 0 20 20",fill:"currentColor",children:l.jsx("path",{fillRule:"evenodd",d:"M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})]}),(0,l.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:["@",e.username]}),e.description&&l.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 mt-1 line-clamp-2",children:e.description}),l.jsx("div",{className:"mt-2 text-xs text-gray-500 dark:text-gray-400",children:void 0!==e.followersCount&&(0,l.jsxs)("span",{className:"mr-3",children:[e.followersCount," ",a("followers","followers")]})})]})]})});case t.PRODUCT:return l.jsx(y(),{href:`/products/${e.id}`,className:`block ${r}`,children:(0,l.jsxs)("div",{className:"flex p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-shadow",children:[l.jsx("div",{className:"w-20 h-20 relative rounded-md overflow-hidden flex-shrink-0",children:l.jsx(x(),{src:e.imageUrl||"/images/placeholder-product.png",alt:e.title,fill:!0,className:"object-cover"})}),(0,l.jsxs)("div",{className:"ml-4 flex-1",children:[l.jsx("h3",{className:"font-medium text-gray-900 dark:text-gray-100",children:e.title}),l.jsx("div",{className:"mt-1 flex items-center",children:void 0!==e.discountPrice?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("span",{className:"text-primary-600 dark:text-primary-400 font-medium",children:[e.currency||"$",e.discountPrice.toFixed(2)]}),(0,l.jsxs)("span",{className:"ml-2 text-sm text-gray-500 dark:text-gray-400 line-through",children:[e.currency||"$",e.price?.toFixed(2)]})]}):(0,l.jsxs)("span",{className:"text-primary-600 dark:text-primary-400 font-medium",children:[e.currency||"$",e.price?.toFixed(2)]})}),e.description&&l.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 mt-1 line-clamp-2",children:e.description}),void 0!==e.rating&&(0,l.jsxs)("div",{className:"mt-2 flex items-center",children:[l.jsx("div",{className:"flex items-center",children:[void 0,void 0,void 0,void 0,void 0].map((r,a)=>l.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:`h-4 w-4 ${a<Math.floor(e.rating||0)?"text-yellow-400":"text-gray-300 dark:text-gray-600"}`,viewBox:"0 0 20 20",fill:"currentColor",children:l.jsx("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})},a))}),(0,l.jsxs)("span",{className:"ml-2 text-xs text-gray-500 dark:text-gray-400",children:[e.rating.toFixed(1)," (",e.reviewsCount||0," ",a("reviews","reviews"),")"]})]})]})]})});case t.STORE:return l.jsx(y(),{href:`/stores/${e.id}`,className:`block ${r}`,children:(0,l.jsxs)("div",{className:"flex items-center p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-shadow",children:[l.jsx("div",{className:"w-12 h-12 relative rounded-full overflow-hidden flex-shrink-0",children:l.jsx(x(),{src:e.imageUrl||"/images/placeholder-store.png",alt:e.title,fill:!0,className:"object-cover"})}),(0,l.jsxs)("div",{className:"ml-4 flex-1",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[l.jsx("h3",{className:"font-medium text-gray-900 dark:text-gray-100",children:e.title}),e.isVerified&&l.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 ml-1 text-primary-500",viewBox:"0 0 20 20",fill:"currentColor",children:l.jsx("path",{fillRule:"evenodd",d:"M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})]}),e.description&&l.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 mt-1 line-clamp-2",children:e.description}),(0,l.jsxs)("div",{className:"mt-2 text-xs text-gray-500 dark:text-gray-400",children:[void 0!==e.productsCount&&(0,l.jsxs)("span",{className:"mr-3",children:[e.productsCount," ",a("products","products")]}),void 0!==e.followersCount&&(0,l.jsxs)("span",{children:[e.followersCount," ",a("followers","followers")]})]})]})]})});case t.POST:return l.jsx(y(),{href:`/social/posts/${e.id}`,className:`block ${r}`,children:(0,l.jsxs)("div",{className:"p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-shadow",children:[(0,l.jsxs)("div",{className:"flex items-center mb-3",children:[l.jsx("div",{className:"w-8 h-8 relative rounded-full overflow-hidden flex-shrink-0",children:l.jsx(x(),{src:e.authorAvatarUrl||"/images/default-avatar.png",alt:e.authorName||"",fill:!0,className:"object-cover"})}),(0,l.jsxs)("div",{className:"ml-2",children:[l.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e.authorName}),(0,l.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:["@",e.authorUsername," • ",(e=>{try{return(0,p.Z)(new Date(e),{addSuffix:!0})}catch(r){return e}})(e.createdAt)]})]})]}),l.jsx("h3",{className:"font-medium text-gray-900 dark:text-gray-100 mb-2",children:e.title}),e.description&&l.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 mb-3 line-clamp-3",children:e.description}),e.imageUrl&&l.jsx("div",{className:"relative aspect-video rounded-md overflow-hidden mb-3",children:l.jsx(x(),{src:e.imageUrl,alt:e.title,fill:!0,className:"object-cover"})}),(0,l.jsxs)("div",{className:"flex items-center text-xs text-gray-500 dark:text-gray-400",children:[void 0!==e.likesCount&&(0,l.jsxs)("span",{className:"flex items-center mr-4",children:[l.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"})}),e.likesCount]}),void 0!==e.commentsCount&&(0,l.jsxs)("span",{className:"flex items-center",children:[l.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})}),e.commentsCount]})]})]})});case t.HASHTAG:return l.jsx(y(),{href:`/search?tag=${encodeURIComponent(e.title.replace("#",""))}`,className:`block ${r}`,children:(0,l.jsxs)("div",{className:"p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-shadow",children:[l.jsx("h3",{className:"font-medium text-gray-900 dark:text-gray-100 mb-2",children:e.title}),e.description&&l.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 mb-3",children:e.description}),l.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:void 0!==e.postsCount&&(0,l.jsxs)("span",{children:[e.postsCount," ",a("posts","posts")]})})]})});default:return null}},search_SearchResultsList=({results:e,isLoading:r=!1,hasMore:a=!1,onLoadMore:t,emptyMessage:s,className:i=""})=>{let{t:d}=(0,n.$G)("search"),{isRtl:o}=(0,c.g)();return r&&0===e.length?l.jsx("div",{className:`space-y-4 ${i}`,children:Array.from({length:3}).map((e,r)=>(0,l.jsxs)("div",{className:"p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm animate-pulse",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[l.jsx("div",{className:"w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-full"}),(0,l.jsxs)("div",{className:"ml-4 flex-1",children:[l.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-2"}),l.jsx("div",{className:"h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/6"})]})]}),l.jsx("div",{className:"mt-4 h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"}),l.jsx("div",{className:"mt-2 h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"})]},r))}):0===e.length?(0,l.jsxs)("div",{className:`p-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm text-center ${i}`,children:[l.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-12 w-12 text-gray-400 dark:text-gray-600 mx-auto mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})}),l.jsx("p",{className:"text-gray-500 dark:text-gray-400 mb-4",children:s||d("noResultsFound","No results found")})]}):(0,l.jsxs)("div",{className:`space-y-4 ${i}`,children:[e.map(e=>l.jsx(search_SearchResultItem,{result:e},e.id)),a&&l.jsx("div",{className:"flex justify-center mt-6",children:l.jsx(g.Z,{variant:"outline",onClick:t,isLoading:r,disabled:r,children:d("loadMore","Load More")})})]})},search_SearchHistory=({history:e,onClearHistory:r,onClearHistoryItem:a,onSearch:s,className:i=""})=>{let{t:c}=(0,n.$G)("search");if((0,d.useRouter)(),0===e.length)return(0,l.jsxs)("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 ${i}`,children:[l.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2",children:c("searchHistory","Search History")}),l.jsx("p",{className:"text-gray-500 dark:text-gray-400 text-sm",children:c("noSearchHistory","Your search history will appear here")})]});let formatDate=e=>{let r=new Date(e),a=new Date,t=Math.floor((a.getTime()-r.getTime())/864e5);return 0===t?c("today","Today"):1===t?c("yesterday","Yesterday"):t<7?c("daysAgo","{{count}} days ago",{count:t}):r.toLocaleDateString()},o=e.reduce((e,r)=>{let a=new Date(r.timestamp),t=a.toDateString();return e[t]||(e[t]=[]),e[t].push(r),e},{});return(0,l.jsxs)("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 ${i}`,children:[(0,l.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[l.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:c("searchHistory","Search History")}),l.jsx(g.Z,{variant:"text",size:"sm",onClick:r,children:c("clearAll","Clear All")})]}),l.jsx("div",{className:"space-y-4",children:Object.entries(o).map(([e,r])=>(0,l.jsxs)("div",{children:[l.jsx("h4",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 mb-2",children:formatDate(r[0].timestamp)}),l.jsx("ul",{className:"space-y-2",children:r.map(e=>(0,l.jsxs)("li",{className:"flex items-center justify-between",children:[(0,l.jsxs)("button",{className:"flex items-center text-left text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400",onClick:()=>s(e.query,e.type),children:[l.jsx("svg",{className:"w-4 h-4 mr-2 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),l.jsx("span",{className:"truncate max-w-xs",children:e.query}),e.type&&e.type!==t.ALL&&l.jsx("span",{className:"ml-2 text-xs text-gray-500 dark:text-gray-400",children:c(`type.${e.type}`,e.type)})]}),l.jsx("button",{className:"text-gray-400 hover:text-gray-500 dark:hover:text-gray-300",onClick:()=>a(e.id),"aria-label":c("removeFromHistory","Remove from history"),children:l.jsx("svg",{className:"w-4 h-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]},e.id))})]},e))})]})};function SearchPage(){let{t:e}=(0,n.$G)("search"),r=(0,d.useRouter)(),a=(0,d.useSearchParams)(),s=a.get("q")||"",c=a.get("tag"),[m,x]=(0,i.useState)(s),[h,g]=(0,i.useState)({tags:c?[c]:void 0});(0,i.useEffect)(()=>{x(s),!c||h.tags&&h.tags.includes(c)||g(e=>({...e,tags:[...e.tags||[],c]}))},[s,c,h.tags]);let{data:u,isLoading:y}=(0,o.A3)(s,{skip:!s}),{data:p=[]}=(0,o.useGetSearchHistoryQuery)(),[v]=(0,o.useAddSearchHistoryItemMutation)(),[j]=(0,o.useClearSearchHistoryMutation)(),[b]=(0,o.useClearSearchHistoryItemMutation)(),[k,f]=(0,i.useState)(!1),handleSearch=e=>{if(!e.trim())return;x(e);let a=new URLSearchParams;a.set("q",e),h.type&&a.set("type",h.type),h.sortBy&&a.set("sort",h.sortBy),h.categories?.length&&a.set("categories",h.categories.join(",")),h.tags?.length&&a.set("tags",h.tags.join(",")),r.push(`/search?${a.toString()}`),v({query:e,type:h.type})},handleFilterChange=e=>{g(e)};return l.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 py-8",children:(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,l.jsxs)("div",{className:"mb-8",children:[l.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4",children:s?e("searchResults",'Search Results for "{{query}}"',{query:s}):e("search","Search")}),l.jsx(search_SearchInput,{initialQuery:m,onSearch:handleSearch,placeholder:e("searchPlaceholder","Search for anything..."),className:"max-w-3xl"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[l.jsx("div",{className:"lg:col-span-1",children:(0,l.jsxs)("div",{className:"space-y-6 sticky top-8",children:[l.jsx(search_SearchFilters,{filters:h,onFilterChange:handleFilterChange}),l.jsx("button",{type:"button",className:"text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 flex items-center",onClick:()=>f(!k),children:k?(0,l.jsxs)(l.Fragment,{children:[l.jsx("svg",{className:"w-4 h-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 15l7-7 7 7"})}),e("hideAdvancedFilters","Hide Advanced Filters")]}):(0,l.jsxs)(l.Fragment,{children:[l.jsx("svg",{className:"w-4 h-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})}),e("showAdvancedFilters","Show Advanced Filters")]})}),k&&l.jsx(search_AdvancedSearchFilters,{filters:h,onFilterChange:handleFilterChange}),!s&&p.length>0&&l.jsx(search_SearchHistory,{history:p,onClearHistory:()=>j(),onClearHistoryItem:e=>b(e),onSearch:(e,r)=>{g(e=>({...e,type:r})),handleSearch(e)},className:"mt-6"})]})}),l.jsx("div",{className:"lg:col-span-3",children:s?l.jsx("div",{className:"space-y-6",children:u&&l.jsx(l.Fragment,{children:(()=>{let r=[];return u.products&&u.products.forEach(e=>{r.push({id:e.id,type:t.PRODUCT,title:e.title,description:e.description,imageUrl:e.mediaUrls?.[0],url:`/products/${e.id}`,createdAt:new Date().toISOString(),price:e.price||0,discountPrice:.9*(e.price||0),rating:4.5,reviewsCount:10})}),u.stores&&u.stores.forEach(e=>{r.push({id:e.id,type:t.STORE,title:e.displayName||e.username,description:e.description,imageUrl:e.logoUrl,url:`/stores/${e.id}`,createdAt:new Date().toISOString(),isVerified:e.isVerified,productsCount:e.productsCount,followersCount:e.followersCount})}),u.users&&u.users.forEach(e=>{r.push({id:e.id,type:t.USER,title:e.displayName||e.username,description:e.bio,imageUrl:e.profileImageUrl,url:`/profile/${e.username}`,createdAt:new Date().toISOString(),username:e.username,displayName:e.displayName,isVerified:e.isVerified,followersCount:e.followersCount})}),l.jsx(search_SearchResultsList,{results:r,isLoading:y,emptyMessage:e("noResultsFound",'No results found for "{{query}}"',{query:s})})})()})}):(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 text-center",children:[l.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-12 w-12 text-gray-400 dark:text-gray-600 mx-auto mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})}),l.jsx("p",{className:"text-gray-500 dark:text-gray-400 mb-4",children:e("startSearching","Start searching by typing in the search box above")}),p.length>0&&(0,l.jsxs)("div",{className:"mt-8 text-left",children:[l.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4",children:e("recentSearches","Recent Searches")}),l.jsx("div",{className:"flex flex-wrap gap-2",children:p.slice(0,5).map(e=>l.jsx("button",{className:"px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-full text-sm hover:bg-gray-200 dark:hover:bg-gray-600",onClick:()=>{g(r=>({...r,type:e.type})),handleSearch(e.query)},children:e.query},e.id))})]})]})})]})]})})}},9467:(e,r,a)=>{"use strict";a.r(r),a.d(r,{$$typeof:()=>i,__esModule:()=>l,default:()=>n});var t=a(95153);let s=(0,t.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\search\page.tsx`),{__esModule:l,$$typeof:i}=s,d=s.default,n=d}};var r=require("../../webpack-runtime.js");r.C(e);var __webpack_exec__=e=>r(r.s=e),a=r.X(0,[2103,2765,6663],()=>__webpack_exec__(85268));module.exports=a})();