const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');
const { Client } = require('pg');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');

const app = express();
const port = 3010; // Use a different port to avoid conflicts

// Middleware
app.use(cors({
  origin: '*', // Allow requests from any origin
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  credentials: true,
}));

// Add CORS headers to all responses
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  next();
});
app.use(bodyParser.json());

// Serve static files from the public directory
app.use(express.static(path.join(__dirname, 'public')));

// Database connection
const getClient = async () => {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    database: 'social_commerce',
    user: 'postgres',
    password: '1111'
  });
  await client.connect();
  return client;
};

// Routes
app.get('/', (req, res) => {
  res.send('Test server is running!');
});

app.post('/auth/login', async (req, res) => {
  console.log('Login request received:', {
    usernameOrEmail: req.body.usernameOrEmail,
    hasPassword: !!req.body.password,
    passwordLength: req.body.password?.length,
    body: JSON.stringify(req.body),
    headers: req.headers
  });

  try {
    const { usernameOrEmail, password } = req.body;

    if (!usernameOrEmail || !password) {
      console.log('Missing username/email or password');
      return res.status(400).json({ message: 'Username/email and password are required' });
    }

    // Connect to the database
    const client = await getClient();

    // Find the user
    console.log('Finding user with username or email:', usernameOrEmail);
    const findResult = await client.query(
      'SELECT id, username, email, role, "passwordHash" FROM users WHERE username = $1 OR email = $1',
      [usernameOrEmail]
    );

    if (findResult.rows.length === 0) {
      console.log('User not found');
      await client.end();
      return res.status(401).json({ message: 'Invalid credentials (user not found)' });
    }

    const user = findResult.rows[0];
    console.log('User found:', {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      passwordHash: user.passwordHash ? user.passwordHash.substring(0, 10) + '...' : 'Missing'
    });

    // Verify the password
    console.log('Verifying password...');
    console.log('Password from request:', password);
    console.log('Password hash from database:', user.passwordHash);

    // For debugging, let's try to hash the password again and compare
    const newHash = await bcrypt.hash(password, 10);
    console.log('New hash for the same password:', newHash);

    const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
    console.log('Password valid?', isPasswordValid);

    // Let's also try to verify with a known good password
    const knownGoodPassword = 'password123';
    const isKnownGoodPasswordValid = await bcrypt.compare(knownGoodPassword, user.passwordHash);
    console.log('Known good password valid?', isKnownGoodPasswordValid);

    // If the password is not valid, let's update it for testing
    if (!isPasswordValid) {
      console.log('Invalid password, updating it for testing...');
      const updatedHash = await bcrypt.hash(password, 10);

      await client.query(
        'UPDATE users SET "passwordHash" = $1 WHERE id = $2',
        [updatedHash, user.id]
      );

      console.log('Password updated successfully');

      // Verify the updated password
      const updatedFindResult = await client.query(
        'SELECT id, username, email, role, "passwordHash" FROM users WHERE id = $1',
        [user.id]
      );

      const updatedUser = updatedFindResult.rows[0];
      const isUpdatedPasswordValid = await bcrypt.compare(password, updatedUser.passwordHash);
      console.log('Updated password valid?', isUpdatedPasswordValid);

      // Use the updated user for the response
      user.passwordHash = updatedUser.passwordHash;
    }

    await client.end();

    // Generate a JWT token
    console.log('Generating token...');
    const token = jwt.sign(
      {
        sub: user.id,
        username: user.username,
        email: user.email,
        role: user.role
      },
      'your_jwt_secret_key_change_in_production',
      { expiresIn: '1d' }
    );

    console.log('Login successful');

    // Return the user and token
    return res.status(200).json({
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
      },
      token
    });
  } catch (error) {
    console.error('Login error:', error);
    return res.status(500).json({ message: 'Internal server error', error: error.message });
  }
});

// Debug endpoint
app.get('/debug', (req, res) => {
  console.log('Debug endpoint called');
  res.json({ message: 'Debug endpoint is working!' });
});

// Debug endpoint to check user credentials
app.get('/debug/users', async (req, res) => {
  console.log('Debug users endpoint called');

  try {
    // Connect to the database
    const client = await getClient();

    // Get all users
    const usersResult = await client.query('SELECT id, username, email, role, "passwordHash" FROM users');

    await client.end();

    // Return the users (without password hashes)
    return res.status(200).json({
      users: usersResult.rows.map(user => ({
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        hasPasswordHash: !!user.passwordHash
      }))
    });
  } catch (error) {
    console.error('Debug users error:', error);
    return res.status(500).json({ message: 'Internal server error', error: error.message });
  }
});

// Debug endpoint to check a specific user
app.get('/debug/user/:username', async (req, res) => {
  const username = req.params.username;
  console.log('Debug user endpoint called for username:', username);

  try {
    // Connect to the database
    const client = await getClient();

    // Find the user
    const findResult = await client.query(
      'SELECT id, username, email, role, "passwordHash" FROM users WHERE username = $1',
      [username]
    );

    await client.end();

    if (findResult.rows.length === 0) {
      return res.status(404).json({ message: 'User not found' });
    }

    const user = findResult.rows[0];

    // Return the user (without password hash)
    return res.status(200).json({
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        passwordHashFirstChars: user.passwordHash ? user.passwordHash.substring(0, 10) + '...' : 'Missing'
      }
    });
  } catch (error) {
    console.error('Debug user error:', error);
    return res.status(500).json({ message: 'Internal server error', error: error.message });
  }
});

// Debug endpoint to reset a user's password
app.post('/debug/reset-password', async (req, res) => {
  const { username, password } = req.body;
  console.log('Debug reset password endpoint called for username:', username);

  if (!username || !password) {
    return res.status(400).json({ message: 'Username and password are required' });
  }

  try {
    // Connect to the database
    const client = await getClient();

    // Find the user
    const findResult = await client.query(
      'SELECT id, username, email, role, "passwordHash" FROM users WHERE username = $1',
      [username]
    );

    if (findResult.rows.length === 0) {
      await client.end();
      return res.status(404).json({ message: 'User not found' });
    }

    const user = findResult.rows[0];
    console.log('User found:', {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role
    });

    // Hash the new password
    console.log('Hashing new password...');
    const passwordHash = await bcrypt.hash(password, 10);
    console.log('New password hash:', passwordHash);

    // Update the user's password
    await client.query(
      'UPDATE users SET "passwordHash" = $1 WHERE id = $2',
      [passwordHash, user.id]
    );

    console.log('Password updated successfully');

    // Verify the updated password
    const updatedFindResult = await client.query(
      'SELECT id, username, email, role, "passwordHash" FROM users WHERE id = $1',
      [user.id]
    );

    const updatedUser = updatedFindResult.rows[0];
    const isUpdatedPasswordValid = await bcrypt.compare(password, updatedUser.passwordHash);
    console.log('Updated password valid?', isUpdatedPasswordValid);

    await client.end();

    // Return success
    return res.status(200).json({
      message: 'Password reset successfully',
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role
      },
      passwordValid: isUpdatedPasswordValid
    });
  } catch (error) {
    console.error('Debug reset password error:', error);
    return res.status(500).json({ message: 'Internal server error', error: error.message });
  }
});

// Start the server
app.listen(port, () => {
  console.log(`Test server is running on http://localhost:${port}`);
  console.log('Available endpoints:');
  console.log('- GET /: Test server is running');
  console.log('- POST /auth/login: Login endpoint');
  console.log('- GET /debug: Debug endpoint');
  console.log('- GET /debug/users: List all users');
  console.log('- GET /debug/user/:username: Get user details');
  console.log('- POST /debug/reset-password: Reset user password');
});
