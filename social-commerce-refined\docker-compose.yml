version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:latest
    container_name: social-commerce-postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 1111
      POSTGRES_MULTIPLE_DATABASES: user_service,store_service,product_service_db,cart_service_db,order_service_db
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./tools/scripts/create-multiple-postgresql-databases.sh:/docker-entrypoint-initdb.d/create-multiple-postgresql-databases.sh
    ports:
      - "5432:5432"
    networks:
      - social-commerce-network
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  # RabbitMQ
  rabbitmq:
    image: rabbitmq:3-management
    container_name: social-commerce-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: admin
    ports:
      - "5672:5672"
      - "15672:15672"
    networks:
      - social-commerce-network
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    healthcheck:
      test: ["CMD", "rabbitmqctl", "status"]
      interval: 5s
      timeout: 5s
      retries: 5

  # API Gateway
  api-gateway:
    build:
      context: .
      dockerfile: services/api-gateway/Dockerfile
    container_name: social-commerce-api-gateway
    environment:
      NODE_ENV: development
      HTTP_PORT: 3000
      USER_SERVICE_URL: http://user-service:3001/api
      STORE_SERVICE_URL: http://store-service:3002/api
      PRODUCT_SERVICE_URL: http://product-service:3004/api
      CART_SERVICE_URL: http://cart-service:3005/api
      ORDER_SERVICE_URL: http://order-service:3006/api
      # Node.js Memory Optimization
      NODE_OPTIONS: "--max-old-space-size=384"
    ports:
      - "3000:3000"
    depends_on:
      - user-service  # Temporarily removed health check condition
      - store-service  # Temporarily removed health check condition
      - product-service  # Add product service dependency
      - cart-service  # Add cart service dependency
      - order-service  # Add order service dependency
    networks:
      - social-commerce-network
    # volumes:  # ❌ Disabled for production build testing
      # - ./services/api-gateway:/app
      # - /app/node_modules
      # - ./libs:/libs
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # User Service
  user-service:
    build:
      context: .
      dockerfile: services/user-service/Dockerfile
    container_name: social-commerce-user-service
    environment:
      NODE_ENV: development
      HTTP_PORT: 3001
      MICROSERVICE_PORT: 3011
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USERNAME: postgres
      DB_PASSWORD: 1111
      DB_DATABASE: user_service
      DB_SYNCHRONIZE: ${DB_SYNCHRONIZE:-false}
      DB_LOGGING: "true"
      JWT_SECRET: ${JWT_SECRET:-your_jwt_secret_key_here}
      JWT_EXPIRES_IN: 1h
      RABBITMQ_URL: amqp://admin:admin@rabbitmq:5672
      RABBITMQ_QUEUE: user_queue
      NOTIFICATION_QUEUE: notification_queue
      # Node.js Memory Optimization
      NODE_OPTIONS: "--max-old-space-size=640"
    ports:
      - "3001:3001"
    depends_on:
      postgres:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      # notification-service:
      #   condition: service_healthy  # Temporarily disabled for testing
    networks:
      - social-commerce-network
    # volumes:
      # - ./services/user-service:/app  # Disabled for production build testing
      # - /app/node_modules
      # - ./libs:/libs
    deploy:
      resources:
        limits:
          memory: 896M
        reservations:
          memory: 448M
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Store Service
  store-service:
    build:
      context: .
      dockerfile: services/store-service/Dockerfile
    container_name: social-commerce-store-service
    environment:
      NODE_ENV: development
      HTTP_PORT: 3002
      MICROSERVICE_PORT: 3002
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USERNAME: postgres
      DB_PASSWORD: 1111
      DB_DATABASE: store_service
      DB_SYNCHRONIZE: ${DB_SYNCHRONIZE:-false}
      DB_LOGGING: "true"
      JWT_SECRET: ${JWT_SECRET:-your_jwt_secret_key_here}
      JWT_EXPIRES_IN: 1h
      RABBITMQ_URL: amqp://admin:admin@rabbitmq:5672
      RABBITMQ_QUEUE: store_queue
      USER_QUEUE: user_queue
      NOTIFICATION_QUEUE: notification_queue
      USER_SERVICE_URL: http://user-service:3001/api
      # Node.js Memory Optimization
      NODE_OPTIONS: "--max-old-space-size=640"
    ports:
      - "3002:3002"
    depends_on:
      postgres:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      # user-service:  # Temporarily disabled for independent testing
      #   condition: service_healthy
    networks:
      - social-commerce-network
    # volumes:  # ❌ Disabled for production build testing
      # - ./services/store-service:/app
      # - /app/node_modules
      # - ./libs:/libs
    deploy:
      resources:
        limits:
          memory: 896M
        reservations:
          memory: 448M
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3002/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Product Service
  product-service:
    build:
      context: .
      dockerfile: services/product-service/Dockerfile
    container_name: social-commerce-product-service
    ports:
      - "3004:3004"
    environment:
      NODE_ENV: development
      HTTP_PORT: 3004
      MICROSERVICE_PORT: 3014
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USERNAME: postgres
      DB_PASSWORD: 1111
      DB_DATABASE: product_service_db
      DB_SYNCHRONIZE: ${DB_SYNCHRONIZE:-true}
      DB_LOGGING: "true"
      JWT_SECRET: ${JWT_SECRET:-your_jwt_secret_key_here}
      RABBITMQ_URL: amqp://admin:admin@rabbitmq:5672
      RABBITMQ_QUEUE: product_queue
      STORE_QUEUE: store_queue
      # Node.js Memory Optimization
      NODE_OPTIONS: "--max-old-space-size=640"
    depends_on:
      postgres:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - social-commerce-network
    deploy:
      resources:
        limits:
          memory: 896M
        reservations:
          memory: 448M
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3004/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Cart Service
  cart-service:
    build:
      context: .
      dockerfile: services/cart-service/Dockerfile
    container_name: social-commerce-cart-service
    environment:
      NODE_ENV: development
      PORT: 3005
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USERNAME: postgres
      DB_PASSWORD: 1111
      DB_DATABASE: cart_service_db
      DB_SYNCHRONIZE: ${DB_SYNCHRONIZE:-true}
      DB_LOGGING: "true"
      JWT_SECRET: ${JWT_SECRET:-your_jwt_secret_key_here}
      RABBITMQ_URL: amqp://admin:admin@rabbitmq:5672
      PRODUCT_QUEUE: product_queue
      FRONTEND_URL: ${FRONTEND_URL:-http://localhost:3001}
      # Node.js Memory Optimization
      NODE_OPTIONS: "--max-old-space-size=512"
    ports:
      - "3005:3005"
    depends_on:
      - postgres
      - rabbitmq
      - product-service
    networks:
      - social-commerce-network
    # volumes:  # ❌ Disabled for production build testing
      # - ./services/cart-service:/app
      # - /app/node_modules
    deploy:
      resources:
        limits:
          memory: 768M
        reservations:
          memory: 384M
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3005/api/health/simple"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Order Service
  order-service:
    build:
      context: .
      dockerfile: services/order-service/Dockerfile
    container_name: social-commerce-order-service
    environment:
      NODE_ENV: development
      PORT: 3006
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USERNAME: postgres
      DB_PASSWORD: 1111
      DB_DATABASE: order_service_db
      DB_SYNCHRONIZE: ${DB_SYNCHRONIZE:-true}
      DB_LOGGING: "true"
      JWT_SECRET: ${JWT_SECRET:-your_jwt_secret_key_here}
      RABBITMQ_URL: amqp://admin:admin@rabbitmq:5672
      ORDER_QUEUE: order_queue
      CART_QUEUE: cart_queue
      PRODUCT_QUEUE: product_queue
      USER_QUEUE: user_queue
      FRONTEND_URL: ${FRONTEND_URL:-http://localhost:3001}
      # Node.js Memory Optimization
      NODE_OPTIONS: "--max-old-space-size=512"
    ports:
      - "3006:3006"
    depends_on:
      - postgres
      - rabbitmq
      - cart-service
      - product-service
    networks:
      - social-commerce-network
    deploy:
      resources:
        limits:
          memory: 768M
        reservations:
          memory: 384M
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3006/api/health/simple"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Notification Service
  notification-service:
    build:
      context: .
      dockerfile: services/notification-service/Dockerfile
    container_name: social-commerce-notification-service
    environment:
      NODE_ENV: development
      HTTP_PORT: 3007
      RABBITMQ_URL: amqp://admin:admin@rabbitmq:5672
      NOTIFICATION_QUEUE: notification_queue
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USERNAME: postgres
      DB_PASSWORD: 1111
      DB_NAME: notification_service_db
      JWT_SECRET: ${JWT_SECRET:-your-secret-key}
      JWT_EXPIRES_IN: ${JWT_EXPIRES_IN:-1h}
      SMTP_HOST: ${SMTP_HOST:-}
      SMTP_PORT: ${SMTP_PORT:-587}
      SMTP_USER: ${SMTP_USER:-}
      SMTP_PASS: ${SMTP_PASS:-}
      SMTP_FROM: ${SMTP_FROM:-<EMAIL>}
      FRONTEND_URL: ${FRONTEND_URL:-http://localhost:3000}
      # Node.js Memory Optimization
      NODE_OPTIONS: "--max-old-space-size=640"
    ports:
      - "3007:3007"
    depends_on:
      postgres:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - social-commerce-network
    deploy:
      resources:
        limits:
          memory: 896M
        reservations:
          memory: 448M
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3007/api/health/simple"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Integration Tests
  integration-tests:
    build:
      context: .
      dockerfile: libs/testing/integration/Dockerfile
    container_name: social-commerce-integration-tests
    environment:
      API_GATEWAY_URL: http://api-gateway:3000/api
      USER_SERVICE_URL: http://user-service:3001/api
      STORE_SERVICE_URL: http://store-service:3002/api
      TEST_USER_EMAIL: <EMAIL>
      TEST_USER_PASSWORD: password123
      # Node.js Memory Optimization
      NODE_OPTIONS: "--max-old-space-size=256"
    depends_on:
      api-gateway:
        condition: service_healthy
      user-service:
        condition: service_healthy
      store-service:
        condition: service_healthy
    networks:
      - social-commerce-network
    volumes:
      - ./libs/testing/integration:/app
      - /app/node_modules
    deploy:
      resources:
        limits:
          memory: 384M
        reservations:
          memory: 192M

networks:
  social-commerce-network:
    driver: bridge

volumes:
  postgres_data:
