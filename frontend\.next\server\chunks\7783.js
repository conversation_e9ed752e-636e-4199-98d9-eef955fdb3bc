"use strict";exports.id=7783,exports.ids=[7783],exports.modules={77783:(e,t,o)=>{o.d(t,{Ci:()=>a,YU:()=>i,c3:()=>u,j6:()=>d,mt:()=>l,te:()=>S,ti:()=>y});var r=o(86372);let s=r.g.injectEndpoints({endpoints:e=>({getStores:e.query({query:()=>"/stores",providesTags:["Store"]}),getStoreById:e.query({query:e=>`/stores/${e}`,providesTags:["Store"]}),getStoreByUsername:e.query({query:e=>`/stores/username/${e}`,providesTags:["Store"]}),createStore:e.mutation({query:e=>({url:"/stores",method:"POST",body:e}),invalidatesTags:["Store"]}),updateStore:e.mutation({query:({id:e,data:t})=>({url:`/stores/${e}`,method:"PATCH",body:t}),invalidatesTags:["Store"]}),deleteStore:e.mutation({query:e=>({url:`/stores/${e}`,method:"DELETE"}),invalidatesTags:["Store"]}),followStore:e.mutation({query:e=>({url:`/stores/${e}/follow`,method:"POST"}),invalidatesTags:["Store"]}),unfollowStore:e.mutation({query:e=>({url:`/stores/${e}/unfollow`,method:"POST"}),invalidatesTags:["Store"]})})}),{useGetStoresQuery:a,useGetStoreByIdQuery:u,useGetStoreByUsernameQuery:i,useCreateStoreMutation:d,useUpdateStoreMutation:l,useDeleteStoreMutation:n,useFollowStoreMutation:S,useUnfollowStoreMutation:y}=s}};