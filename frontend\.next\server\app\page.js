(()=>{var e={};e.id=1931,e.ids=[1931],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},40165:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>g,originalPathname:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>l});var s=t(67096),a=t(16132),n=t(37284),o=t.n(n),i=t(32564),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(r,d);let l=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,15426)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9291,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\page.tsx"],m="/page",g={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},42842:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,30614,23)),Promise.resolve().then(t.bind(t,34347))},34347:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>home_HomeRecommendations});var s=t(30784);t(9885);var a=t(11440),n=t.n(a),o=t(27870),i=t(78779),d=t(38845),l=t(5387);let product_PersonalizedRecommendations=({userId:e,limit:r=4,title:t,className:a=""})=>{let{t:n}=(0,o.$G)("product"),{data:c,isLoading:m,error:g}=(0,i.mW)({userId:e,limit:r});return g||c&&0===c.items.length?null:(0,s.jsxs)("div",{className:`my-8 ${a}`,children:[s.jsx("h2",{className:"text-2xl font-bold mb-4",children:t||n("recommendedForYou","Recommended For You")}),s.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:m?Array.from({length:r}).map((e,r)=>s.jsx(l.Z,{},r)):c?.items.map(e=>s.jsx(d.Z,{product:e.targetProduct},e.targetProduct.id))})]})},home_HomeRecommendations=({className:e="",isLoggedIn:r=!1,userId:t=""})=>{let{t:a}=(0,o.$G)("product");return r?s.jsx("div",{className:`mb-12 ${e}`,children:s.jsx("div",{className:"border-t border-gray-200 dark:border-gray-700 pt-12",children:s.jsx(product_PersonalizedRecommendations,{userId:t,limit:4})})}):s.jsx("div",{className:`mb-12 ${e}`,children:(0,s.jsxs)("div",{className:"border-t border-gray-200 dark:border-gray-700 pt-12",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6",children:a("recommendedForYou","Recommended For You")}),(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6",children:[s.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:a("signInForRecommendations","Sign in to see personalized recommendations based on your preferences and browsing history.")}),s.jsx(n(),{href:"/login",className:"mt-4 inline-block bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 transition-colors",children:a("signIn","Sign In")})]})]})})}},52300:(e,r,t)=>{"use strict";let{createProxy:s}=t(95153);e.exports=s("C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\node_modules\\next\\dist\\client\\link.js")},24353:(e,r,t)=>{"use strict";e.exports=t(52300)},15426:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>Home});var s=t(4656),a=t(24353),n=t.n(a),o=t(60232),i=t(95153);let d=(0,i.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\components\home\HomeRecommendations.tsx`),{__esModule:l,$$typeof:c}=d,m=d.default;async function Home(){let{t:e,isRtl:r}=await (0,o.$)("navigation");return s.jsx("main",{className:`min-h-screen bg-gray-50 dark:bg-gray-900 ${r?"text-right":"text-left"}`,children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[s.jsx("div",{className:"relative w-full bg-gradient-to-r from-primary-600 to-primary-800 text-white overflow-hidden rounded-lg shadow-xl mb-12",children:(0,s.jsxs)("div",{className:"relative z-10 px-6 py-12 md:py-24 md:px-12 max-w-4xl mx-auto text-center",children:[s.jsx("h1",{className:"text-3xl md:text-5xl font-bold mb-4",children:e("heroTitle","Discover Amazing Products")}),s.jsx("p",{className:"text-lg md:text-xl mb-8 max-w-2xl mx-auto",children:e("heroSubtitle","Shop the latest trends and find great deals on our social commerce platform.")}),s.jsx(n(),{href:"/products",className:"inline-block bg-white text-primary-600 px-6 py-3 rounded-full font-medium text-lg hover:bg-gray-100 transition-colors",children:e("shopNow","Shop Now")})]})}),(0,s.jsxs)("div",{className:"mb-12",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6",children:e("featuredCategories","Featured Categories")}),s.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:["Electronics","Fashion","Home","Beauty"].map((r,t)=>s.jsx(n(),{href:`/products/category/${r.toLowerCase()}`,className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 text-center hover:shadow-md transition-shadow",children:s.jsx("h3",{className:"font-medium text-gray-900 dark:text-gray-100",children:e(`category${r}`,r)})},t))})]}),s.jsx(m,{}),s.jsx("div",{className:"mb-12",children:(0,s.jsxs)("div",{className:"border-t border-gray-200 dark:border-gray-700 pt-12",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6",children:e("trendingProducts","Trending Products")}),s.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",children:[1,2,3,4].map(e=>(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden",children:[s.jsx("div",{className:"aspect-w-1 aspect-h-1 bg-gray-200 dark:bg-gray-700",children:s.jsx("div",{className:"w-full h-48 bg-gray-200 dark:bg-gray-700"})}),(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsxs)("h3",{className:"font-medium text-gray-900 dark:text-gray-100",children:["Trending Product ",e]}),s.jsx("p",{className:"text-primary-600 dark:text-primary-400 font-medium mt-1",children:"$99.99"})]})]},e))}),s.jsx("div",{className:"mt-6 text-center",children:s.jsx(n(),{href:"/products/trending",className:"inline-block text-primary-600 dark:text-primary-400 hover:underline",children:e("viewAllTrending","View All Trending Products")})})]})}),s.jsx("div",{className:"mb-12",children:(0,s.jsxs)("div",{className:"border-t border-gray-200 dark:border-gray-700 pt-12",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6",children:e("quickLinks","Quick Links")}),s.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[{name:"Explore",href:"/explore",description:"Discover products from all stores"},{name:"Group Buy",href:"/fok",description:"Join group buying deals with other users"},{name:"Shopping Cart",href:"/cart",description:"View your shopping cart and checkout"},{name:"Login / Register",href:"/login",description:"Sign in or create a new account"}].map((r,t)=>(0,s.jsxs)(n(),{href:r.href,className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow",children:[s.jsx("h3",{className:"font-medium text-gray-900 dark:text-gray-100 mb-2",children:e(`link${r.name.replace(/\s+/g,"")}`,r.name)}),s.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:e(`link${r.name.replace(/\s+/g,"")}Description`,r.description)})]},t))})]})})]})})}},60232:(e,r,t)=>{"use strict";t.d(r,{$:()=>getServerTranslations});var s=t(47420),a=t(94386);async function getServerTranslations(e="common"){let r=(0,s.G)(),{t}=await (0,s.i)(r,e);return{t,language:r,direction:(0,a.Fp)(r)?"rtl":"ltr",isRtl:(0,a.Fp)(r)}}}};var r=require("../webpack-runtime.js");r.C(e);var __webpack_exec__=e=>r(r.s=e),t=r.X(0,[2103,2765,8845,7622],()=>__webpack_exec__(40165));module.exports=t})();