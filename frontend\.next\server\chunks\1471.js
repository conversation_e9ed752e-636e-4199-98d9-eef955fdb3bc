"use strict";exports.id=1471,exports.ids=[1471],exports.modules={11471:e=>{e.exports=JSON.parse('{"loading":"Loading...","retry":"Retry","cancel":"Cancel","save":"Save","delete":"Delete","edit":"Edit","create":"Create","submit":"Submit","back":"Back","next":"Next","previous":"Previous","search":"Search","filter":"Filter","sort":"Sort","clear":"Clear","close":"Close","yes":"Yes","no":"No","success":"Success","error":"Error","warning":"Warning","info":"Info","translate":"See Translation","language":{"en":"English","fa":"Persian","select":"Select language","disabled":"Language switching will be enabled in a future update"},"direction":"ltr","welcome":{"title":"Welcome to Social Commerce","description":"A social-centric e-commerce platform with internationalization support."},"clientWelcome":{"title":"Client-side Welcome","description":"This component is rendered on the client side with i18n support.","direction":"Current text direction: {direction}"},"i18nTest":{"title":"Internationalization Test Page","serverRendered":"Server-Rendered Content","clientRendered":"Client-Rendered Content","rtlSpecific":"RTL-Specific Features","currentDirection":"Current text direction: {direction}","paragraph1":"This text is rendered on the server side with the correct text direction.","paragraph2":"Numbers should be properly formatted: {number}","textAlignment":"Text alignment should follow the current language direction.","icon":"Icon:","reversedIcon":"Reversed Icon:"}}')}};