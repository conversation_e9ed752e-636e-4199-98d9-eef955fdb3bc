(()=>{var e={};e.id=95,e.ids=[95],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},83074:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>o,routeModule:()=>x,tree:()=>d});var r=a(67096),s=a(16132),l=a(37284),i=a.n(l),c=a(32564),n={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>c[e]);a.d(t,n);let d=["",{children:["dashboard",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,98872)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\dashboard\\products\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,68182)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,9291,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\dashboard\\products\\page.tsx"],u="/dashboard/products/page",m={require:a,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/dashboard/products/page",pathname:"/dashboard/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},32640:(e,t,a)=>{Promise.resolve().then(a.bind(a,84477))},84477:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>ProductsManagementPage});var r,s,l=a(30784),i=a(9885),c=a.n(i),n=a(27870),d=a(14379),o=a(11440),u=a.n(o),m=a(52451),x=a.n(m);let product_ProductListItem=({product:e,isSelected:t,onSelect:a,className:r=""})=>{var s,i;let{t:c}=(0,n.$G)("product"),{isRtl:o}=(0,d.g)();return(0,l.jsxs)("div",{className:`flex items-center p-4 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 ${t?"bg-blue-50 dark:bg-blue-900/20":""} ${r}`,children:[l.jsx("div",{className:"flex-shrink-0 mr-4",children:l.jsx("input",{type:"checkbox",className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded",checked:t,onChange:t=>{a(e.id,t.target.checked)}})}),l.jsx("div",{className:"flex-shrink-0 w-12 h-12 mr-4",children:e.imageUrl?l.jsx(x(),{src:e.imageUrl,alt:e.name,width:48,height:48,className:"w-12 h-12 object-cover rounded"}):l.jsx("div",{className:"w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center",children:l.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})})})}),l.jsx("div",{className:"flex-1 min-w-0",children:(0,l.jsxs)(u(),{href:`/dashboard/products/${e.id}`,className:"block",children:[l.jsx("h3",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 truncate",children:e.name}),(0,l.jsxs)("div",{className:"mt-1 flex flex-col sm:flex-row sm:flex-wrap sm:mt-0 sm:space-x-6",children:[e.sku&&l.jsx("div",{className:"mt-2 flex items-center text-xs text-gray-500 dark:text-gray-400",children:(0,l.jsxs)("span",{className:"truncate",children:[c("details.sku","SKU"),": ",e.sku]})}),l.jsx("div",{className:"mt-2 flex items-center text-xs text-gray-500 dark:text-gray-400",children:(0,l.jsxs)("span",{children:[c("common.price","Price"),": ",null==(s=e.price)?c("common.contactForPrice","Contact for price"):new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(s)]})}),e.createdAt&&l.jsx("div",{className:"mt-2 flex items-center text-xs text-gray-500 dark:text-gray-400",children:(0,l.jsxs)("span",{children:[c("common.created","Created"),": ",(i=e.createdAt)?new Date(i).toLocaleDateString():""]})})]})]})}),l.jsx("div",{className:"ml-4",children:l.jsx("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${(e=>{switch(e){case"active":return"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300";case"inactive":default:return"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300";case"draft":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300"}})(e.status)}`,children:c(`status.${e.status}`,e.status)})}),l.jsx("div",{className:"ml-4 flex-shrink-0 flex",children:l.jsx(u(),{href:`/dashboard/products/${e.id}`,className:"mr-2 text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300",children:l.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:l.jsx("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})})})})]})};var p=a(59872);(function(e){e.UPDATE_STATUS="UPDATE_STATUS",e.UPDATE_CATEGORY="UPDATE_CATEGORY",e.UPDATE_PRICE="UPDATE_PRICE",e.UPDATE_INVENTORY="UPDATE_INVENTORY",e.UPDATE_ATTRIBUTES="UPDATE_ATTRIBUTES",e.DELETE="DELETE",e.DUPLICATE="DUPLICATE",e.EXPORT="EXPORT"})(r||(r={})),function(e){e.PENDING="PENDING",e.PROCESSING="PROCESSING",e.COMPLETED="COMPLETED",e.FAILED="FAILED",e.PARTIALLY_COMPLETED="PARTIALLY_COMPLETED"}(s||(s={}));let bulk_BulkActionButton=({selectedIds:e,onAction:t,disabled:a=!1,className:s=""})=>{let{t:o}=(0,n.$G)("product"),{isRtl:u}=(0,d.g)(),[m,x]=(0,i.useState)(!1),g=(0,i.useRef)(null);c().useEffect(()=>{let handleClickOutside=e=>{g.current&&!g.current.contains(e.target)&&x(!1)};return document.addEventListener("mousedown",handleClickOutside),()=>{document.removeEventListener("mousedown",handleClickOutside)}},[]);let handleActionClick=e=>{x(!1),t(e)};return(0,l.jsxs)("div",{className:`relative ${s}`,ref:g,children:[(0,l.jsxs)(p.Z,{onClick:()=>{x(!m)},disabled:a||0===e.length,variant:"outline",children:[o("bulkActions.title","Bulk Actions"),l.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:`ml-2 h-4 w-4 transition-transform ${m?"rotate-180":""}`,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),m&&l.jsx("div",{className:"absolute z-10 mt-2 w-56 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5",children:(0,l.jsxs)("div",{className:"py-1",role:"menu","aria-orientation":"vertical","aria-labelledby":"options-menu",children:[l.jsx("button",{className:"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700",role:"menuitem",onClick:()=>handleActionClick(r.UPDATE_STATUS),children:o("bulkActions.updateStatus","Update Status")}),l.jsx("button",{className:"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700",role:"menuitem",onClick:()=>handleActionClick(r.UPDATE_CATEGORY),children:o("bulkActions.updateCategories","Update Categories")}),l.jsx("button",{className:"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700",role:"menuitem",onClick:()=>handleActionClick(r.UPDATE_PRICE),children:o("bulkActions.updatePrices","Update Prices")}),l.jsx("button",{className:"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700",role:"menuitem",onClick:()=>handleActionClick(r.UPDATE_INVENTORY),children:o("bulkActions.updateInventory","Update Inventory")}),l.jsx("button",{className:"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700",role:"menuitem",onClick:()=>handleActionClick(r.UPDATE_ATTRIBUTES),children:o("bulkActions.updateAttributes","Update Attributes")}),l.jsx("div",{className:"border-t border-gray-100 dark:border-gray-700 my-1"}),l.jsx("button",{className:"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700",role:"menuitem",onClick:()=>handleActionClick(r.DUPLICATE),children:o("bulkActions.duplicate","Duplicate Products")}),l.jsx("button",{className:"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700",role:"menuitem",onClick:()=>handleActionClick(r.EXPORT),children:o("bulkActions.export","Export Products")}),l.jsx("div",{className:"border-t border-gray-100 dark:border-gray-700 my-1"}),l.jsx("button",{className:"w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20",role:"menuitem",onClick:()=>handleActionClick(r.DELETE),children:o("bulkActions.delete","Delete Products")})]})})]})};var g=a(62763);let bulk_BulkUpdateStatusModal=({isOpen:e,onClose:t,onConfirm:a,selectedCount:r,isLoading:s=!1})=>{let{t:c}=(0,n.$G)("product"),{isRtl:o}=(0,d.g)(),[u,m]=(0,i.useState)("active");return l.jsx(g.Z,{isOpen:e,onClose:t,title:c("bulkActions.updateStatus","Update Status"),children:(0,l.jsxs)("div",{className:`${o?"text-right":"text-left"}`,children:[l.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-4",children:c("bulkActions.updateStatusDescription","Update the status for {{count}} selected products.",{count:r})}),(0,l.jsxs)("div",{className:"mb-4",children:[l.jsx("label",{htmlFor:"status",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("bulkActions.newStatus","New Status")}),(0,l.jsxs)("select",{id:"status",value:u,onChange:e=>{m(e.target.value)},className:"block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-700 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md",children:[l.jsx("option",{value:"active",children:c("status.active","Active")}),l.jsx("option",{value:"inactive",children:c("status.inactive","Inactive")}),l.jsx("option",{value:"draft",children:c("status.draft","Draft")})]})]}),(0,l.jsxs)("div",{className:"mt-6 flex justify-end space-x-3",children:[l.jsx(p.Z,{variant:"outline",onClick:t,disabled:s,children:c("common.cancel","Cancel")}),l.jsx(p.Z,{onClick:()=>{a({status:u})},isLoading:s,disabled:s,children:c("common.update","Update")})]})]})})};!function(){var e=Error("Cannot find module '@/components/product/CategorySelector'");throw e.code="MODULE_NOT_FOUND",e}();let bulk_BulkUpdateCategoriesModal=({isOpen:e,onClose:t,onConfirm:a,selectedCount:r,isLoading:s=!1})=>{let{t:c}=(0,n.$G)("product"),{isRtl:o}=(0,d.g)(),[u,m]=(0,i.useState)("add"),[x,h]=(0,i.useState)([]);return l.jsx(g.Z,{isOpen:e,onClose:t,title:c("bulkActions.updateCategories","Update Categories"),children:(0,l.jsxs)("div",{className:`${o?"text-right":"text-left"}`,children:[l.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-4",children:c("bulkActions.updateCategoriesDescription","Update the categories for {{count}} selected products.",{count:r})}),(0,l.jsxs)("div",{className:"mb-4",children:[l.jsx("label",{htmlFor:"operation",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("bulkActions.operation","Operation")}),(0,l.jsxs)("select",{id:"operation",value:u,onChange:e=>{m(e.target.value)},className:"block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-700 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md",children:[l.jsx("option",{value:"add",children:c("bulkActions.operations.add","Add to existing categories")}),l.jsx("option",{value:"remove",children:c("bulkActions.operations.remove","Remove from existing categories")}),l.jsx("option",{value:"replace",children:c("bulkActions.operations.replace","Replace all categories")})]})]}),(0,l.jsxs)("div",{className:"mb-4",children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("bulkActions.selectCategories","Select Categories")}),l.jsx(Object(function(){var e=Error("Cannot find module '@/components/product/CategorySelector'");throw e.code="MODULE_NOT_FOUND",e}()),{selectedCategories:x,onChange:e=>{h(e)},multiple:!0})]}),(0,l.jsxs)("div",{className:"mt-6 flex justify-end space-x-3",children:[l.jsx(p.Z,{variant:"outline",onClick:t,disabled:s,children:c("common.cancel","Cancel")}),l.jsx(p.Z,{onClick:()=>{a({operation:u,categoryIds:x.map(e=>e.id)})},isLoading:s,disabled:s||0===x.length,children:c("common.update","Update")})]})]})})},bulk_BulkUpdatePricesModal=({isOpen:e,onClose:t,onConfirm:a,selectedCount:r,isLoading:s=!1})=>{let{t:c}=(0,n.$G)("product"),{isRtl:o}=(0,d.g)(),[u,m]=(0,i.useState)("set"),[x,h]=(0,i.useState)(0),[y,b]=(0,i.useState)(!1),[k,f]=(0,i.useState)(!0),[v,j]=(0,i.useState)(!1),[N,A]=(0,i.useState)(!1);return l.jsx(g.Z,{isOpen:e,onClose:t,title:c("bulkActions.updatePrices","Update Prices"),children:(0,l.jsxs)("div",{className:`${o?"text-right":"text-left"}`,children:[l.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-4",children:c("bulkActions.updatePricesDescription","Update the prices for {{count}} selected products.",{count:r})}),(0,l.jsxs)("div",{className:"mb-4",children:[l.jsx("label",{htmlFor:"operation",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("bulkActions.operation","Operation")}),(0,l.jsxs)("select",{id:"operation",value:u,onChange:e=>{m(e.target.value)},className:"block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-700 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md",children:[l.jsx("option",{value:"set",children:c("bulkActions.operations.set","Set to specific value")}),l.jsx("option",{value:"increase",children:c("bulkActions.operations.increase","Increase by amount")}),l.jsx("option",{value:"decrease",children:c("bulkActions.operations.decrease","Decrease by amount")})]})]}),(0,l.jsxs)("div",{className:"mb-4",children:[l.jsx("label",{htmlFor:"value",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("bulkActions.value","Value")}),(0,l.jsxs)("div",{className:"mt-1 relative rounded-md shadow-sm",children:[l.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:!y&&l.jsx("span",{className:"text-gray-500 sm:text-sm",children:"$"})}),l.jsx("input",{type:"number",id:"value",value:x,onChange:e=>{let t=parseFloat(e.target.value);h(isNaN(t)?0:t)},className:`block w-full ${y?"pl-3":"pl-7"} pr-12 py-2 sm:text-sm border-gray-300 dark:border-gray-700 rounded-md focus:ring-primary-500 focus:border-primary-500`,placeholder:"0.00",step:"0.01",min:"set"===u?0:void 0}),l.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:y&&l.jsx("span",{className:"text-gray-500 sm:text-sm",children:"%"})})]})]}),(0,l.jsxs)("div",{className:"mb-4",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[l.jsx("input",{id:"is-percentage",type:"checkbox",checked:y,onChange:e=>{b(e.target.checked)},className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),l.jsx("label",{htmlFor:"is-percentage",className:"ml-2 block text-sm text-gray-700 dark:text-gray-300",children:c("bulkActions.isPercentage","Use percentage")})]}),y&&"set"===u&&l.jsx("p",{className:"mt-1 text-sm text-yellow-600 dark:text-yellow-400",children:c("bulkActions.percentageSetWarning","Setting prices to a percentage will calculate based on the original price.")})]}),(0,l.jsxs)("div",{className:"mb-4",children:[l.jsx("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:c("bulkActions.affectPrices","Affect Price Types")}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[l.jsx("input",{id:"affect-sale-price",type:"checkbox",checked:k,onChange:e=>{f(e.target.checked)},className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),l.jsx("label",{htmlFor:"affect-sale-price",className:"ml-2 block text-sm text-gray-700 dark:text-gray-300",children:c("price.salePrice","Sale Price")})]}),(0,l.jsxs)("div",{className:"flex items-center",children:[l.jsx("input",{id:"affect-compare-price",type:"checkbox",checked:v,onChange:e=>{j(e.target.checked)},className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),l.jsx("label",{htmlFor:"affect-compare-price",className:"ml-2 block text-sm text-gray-700 dark:text-gray-300",children:c("price.comparePrice","Compare Price")})]}),(0,l.jsxs)("div",{className:"flex items-center",children:[l.jsx("input",{id:"affect-cost-price",type:"checkbox",checked:N,onChange:e=>{A(e.target.checked)},className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),l.jsx("label",{htmlFor:"affect-cost-price",className:"ml-2 block text-sm text-gray-700 dark:text-gray-300",children:c("price.costPrice","Cost Price")})]})]}),!k&&!v&&!N&&l.jsx("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:c("bulkActions.selectPriceTypeWarning","Please select at least one price type to update.")})]}),(0,l.jsxs)("div",{className:"mt-6 flex justify-end space-x-3",children:[l.jsx(p.Z,{variant:"outline",onClick:t,disabled:s,children:c("common.cancel","Cancel")}),l.jsx(p.Z,{onClick:()=>{a({operation:u,value:x,isPercentage:y,affectSalePrice:k,affectComparePrice:v,affectCostPrice:N})},isLoading:s,disabled:s||!k&&!v&&!N,children:c("common.update","Update")})]})]})})},bulk_BulkUpdateInventoryModal=({isOpen:e,onClose:t,onConfirm:a,selectedCount:r,isLoading:s=!1})=>{let{t:c}=(0,n.$G)("product"),{isRtl:o}=(0,d.g)(),[u,m]=(0,i.useState)("set"),[x,h]=(0,i.useState)(0),[y,b]=(0,i.useState)(!0);return l.jsx(g.Z,{isOpen:e,onClose:t,title:c("bulkActions.updateInventory","Update Inventory"),children:(0,l.jsxs)("div",{className:`${o?"text-right":"text-left"}`,children:[l.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-4",children:c("bulkActions.updateInventoryDescription","Update the inventory for {{count}} selected products.",{count:r})}),(0,l.jsxs)("div",{className:"mb-4",children:[l.jsx("label",{htmlFor:"operation",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("bulkActions.operation","Operation")}),(0,l.jsxs)("select",{id:"operation",value:u,onChange:e=>{m(e.target.value)},className:"block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-700 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md",children:[l.jsx("option",{value:"set",children:c("bulkActions.operations.set","Set to specific value")}),l.jsx("option",{value:"increase",children:c("bulkActions.operations.increase","Increase by amount")}),l.jsx("option",{value:"decrease",children:c("bulkActions.operations.decrease","Decrease by amount")})]})]}),(0,l.jsxs)("div",{className:"mb-4",children:[l.jsx("label",{htmlFor:"value",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("bulkActions.value","Value")}),l.jsx("input",{type:"number",id:"value",value:x,onChange:e=>{let t=parseInt(e.target.value,10);h(isNaN(t)?0:t)},className:"block w-full pl-3 pr-3 py-2 sm:text-sm border-gray-300 dark:border-gray-700 rounded-md focus:ring-primary-500 focus:border-primary-500",placeholder:"0",step:"1",min:"set"===u?0:void 0}),"decrease"===u&&l.jsx("p",{className:"mt-1 text-sm text-yellow-600 dark:text-yellow-400",children:c("bulkActions.decreaseInventoryWarning","Decreasing inventory below 0 will set it to 0.")})]}),(0,l.jsxs)("div",{className:"mb-4",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[l.jsx("input",{id:"update-all-variants",type:"checkbox",checked:y,onChange:e=>{b(e.target.checked)},className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),l.jsx("label",{htmlFor:"update-all-variants",className:"ml-2 block text-sm text-gray-700 dark:text-gray-300",children:c("bulkActions.updateAllVariants","Update all variants")})]}),l.jsx("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:c("bulkActions.updateAllVariantsDescription","If checked, this will update inventory for all variants of the selected products. Otherwise, only the main product inventory will be updated.")})]}),(0,l.jsxs)("div",{className:"mt-6 flex justify-end space-x-3",children:[l.jsx(p.Z,{variant:"outline",onClick:t,disabled:s,children:c("common.cancel","Cancel")}),l.jsx(p.Z,{onClick:()=>{a({operation:u,value:x,updateAllVariants:y})},isLoading:s,disabled:s,children:c("common.update","Update")})]})]})})},bulk_BulkDeleteModal=({isOpen:e,onClose:t,onConfirm:a,selectedCount:r,isLoading:s=!1})=>{let{t:c}=(0,n.$G)("product"),{isRtl:o}=(0,d.g)(),[u,m]=(0,i.useState)(!1),[x,h]=(0,i.useState)("");return l.jsx(g.Z,{isOpen:e,onClose:t,title:c("bulkActions.deleteProducts","Delete Products"),children:(0,l.jsxs)("div",{className:`${o?"text-right":"text-left"}`,children:[l.jsx("div",{className:"p-4 mb-4 bg-red-50 dark:bg-red-900/20 rounded-md",children:(0,l.jsxs)("div",{className:"flex",children:[l.jsx("div",{className:"flex-shrink-0",children:l.jsx("svg",{className:"h-5 w-5 text-red-400",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",children:l.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,l.jsxs)("div",{className:"ml-3",children:[l.jsx("h3",{className:"text-sm font-medium text-red-800 dark:text-red-200",children:c("bulkActions.deleteWarning","Warning")}),l.jsx("div",{className:"mt-2 text-sm text-red-700 dark:text-red-300",children:l.jsx("p",{children:c("bulkActions.deleteWarningDescription","You are about to delete {{count}} products. This action cannot be undone.",{count:r})})})]})]})}),(0,l.jsxs)("div",{className:"mb-4",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[l.jsx("input",{id:"permanent-delete",type:"checkbox",checked:u,onChange:e=>{m(e.target.checked)},className:"h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"}),l.jsx("label",{htmlFor:"permanent-delete",className:"ml-2 block text-sm text-gray-700 dark:text-gray-300",children:c("bulkActions.permanentDelete","Permanently delete products")})]}),l.jsx("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:c("bulkActions.permanentDeleteDescription","If unchecked, products will be moved to trash and can be restored later. If checked, products will be permanently deleted and cannot be recovered.")})]}),u&&(0,l.jsxs)("div",{className:"mb-4",children:[l.jsx("label",{htmlFor:"confirm-text",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("bulkActions.confirmDelete",'Type "delete" to confirm permanent deletion')}),l.jsx("input",{type:"text",id:"confirm-text",value:x,onChange:e=>{h(e.target.value)},className:"block w-full pl-3 pr-3 py-2 sm:text-sm border-gray-300 dark:border-gray-700 rounded-md focus:ring-red-500 focus:border-red-500",placeholder:"delete"})]}),(0,l.jsxs)("div",{className:"mt-6 flex justify-end space-x-3",children:[l.jsx(p.Z,{variant:"outline",onClick:t,disabled:s,children:c("common.cancel","Cancel")}),l.jsx(p.Z,{variant:"danger",onClick:()=>{a({permanent:u}),h("")},isLoading:s,disabled:s||!(!u||"delete"===x.toLowerCase()),children:c("common.delete","Delete")})]})]})})},bulk_BulkDuplicateModal=({isOpen:e,onClose:t,onConfirm:a,selectedCount:r,isLoading:s=!1})=>{let{t:c}=(0,n.$G)("product"),{isRtl:o}=(0,d.g)(),[u,m]=(0,i.useState)(""),[x,h]=(0,i.useState)(" (Copy)"),[y,b]=(0,i.useState)(""),[k,f]=(0,i.useState)("-COPY"),[v,j]=(0,i.useState)(!0),[N,A]=(0,i.useState)(!0),[P,C]=(0,i.useState)(!1),[T,w]=(0,i.useState)(!0),[S,E]=(0,i.useState)(!0),[U,D]=(0,i.useState)(!0),[I,L]=(0,i.useState)(!0),[_,O]=(0,i.useState)("draft");return l.jsx(g.Z,{isOpen:e,onClose:t,title:c("bulkActions.duplicateProducts","Duplicate Products"),children:(0,l.jsxs)("div",{className:`${o?"text-right":"text-left"}`,children:[l.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-4",children:c("bulkActions.duplicateDescription","Create copies of {{count}} selected products with the following options.",{count:r})}),(0,l.jsxs)("div",{className:"mb-6",children:[l.jsx("h3",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:c("bulkActions.namingOptions","Naming Options")}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[l.jsx("label",{htmlFor:"name-prefix",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("bulkActions.namePrefix","Name Prefix")}),l.jsx("input",{type:"text",id:"name-prefix",value:u,onChange:e=>{m(e.target.value)},className:"block w-full pl-3 pr-3 py-2 sm:text-sm border-gray-300 dark:border-gray-700 rounded-md focus:ring-primary-500 focus:border-primary-500",placeholder:c("bulkActions.namePrefixPlaceholder",'e.g., "New"')})]}),(0,l.jsxs)("div",{children:[l.jsx("label",{htmlFor:"name-suffix",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("bulkActions.nameSuffix","Name Suffix")}),l.jsx("input",{type:"text",id:"name-suffix",value:x,onChange:e=>{h(e.target.value)},className:"block w-full pl-3 pr-3 py-2 sm:text-sm border-gray-300 dark:border-gray-700 rounded-md focus:ring-primary-500 focus:border-primary-500",placeholder:c("bulkActions.nameSuffixPlaceholder",'e.g., "(Copy)"')})]}),(0,l.jsxs)("div",{children:[l.jsx("label",{htmlFor:"sku-prefix",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("bulkActions.skuPrefix","SKU Prefix")}),l.jsx("input",{type:"text",id:"sku-prefix",value:y,onChange:e=>{b(e.target.value)},className:"block w-full pl-3 pr-3 py-2 sm:text-sm border-gray-300 dark:border-gray-700 rounded-md focus:ring-primary-500 focus:border-primary-500",placeholder:c("bulkActions.skuPrefixPlaceholder",'e.g., "COPY-"')})]}),(0,l.jsxs)("div",{children:[l.jsx("label",{htmlFor:"sku-suffix",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("bulkActions.skuSuffix","SKU Suffix")}),l.jsx("input",{type:"text",id:"sku-suffix",value:k,onChange:e=>{f(e.target.value)},className:"block w-full pl-3 pr-3 py-2 sm:text-sm border-gray-300 dark:border-gray-700 rounded-md focus:ring-primary-500 focus:border-primary-500",placeholder:c("bulkActions.skuSuffixPlaceholder",'e.g., "-COPY"')})]})]}),l.jsx("p",{className:"mt-2 text-xs text-gray-500 dark:text-gray-400",children:c("bulkActions.namingOptionsDescription","Prefixes are added before the original name/SKU, and suffixes are added after. At least one of these should be set to ensure unique names and SKUs.")})]}),(0,l.jsxs)("div",{className:"mb-6",children:[l.jsx("h3",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:c("bulkActions.duplicationOptions","Duplication Options")}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[l.jsx("input",{id:"duplicate-media",type:"checkbox",checked:v,onChange:e=>{j(e.target.checked)},className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),l.jsx("label",{htmlFor:"duplicate-media",className:"ml-2 block text-sm text-gray-700 dark:text-gray-300",children:c("bulkActions.duplicateMedia","Duplicate Media")})]}),(0,l.jsxs)("div",{className:"flex items-center",children:[l.jsx("input",{id:"duplicate-variants",type:"checkbox",checked:N,onChange:e=>{A(e.target.checked)},className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),l.jsx("label",{htmlFor:"duplicate-variants",className:"ml-2 block text-sm text-gray-700 dark:text-gray-300",children:c("bulkActions.duplicateVariants","Duplicate Variants")})]}),(0,l.jsxs)("div",{className:"flex items-center",children:[l.jsx("input",{id:"duplicate-inventory",type:"checkbox",checked:P,onChange:e=>{C(e.target.checked)},className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),l.jsx("label",{htmlFor:"duplicate-inventory",className:"ml-2 block text-sm text-gray-700 dark:text-gray-300",children:c("bulkActions.duplicateInventory","Duplicate Inventory")})]}),(0,l.jsxs)("div",{className:"flex items-center",children:[l.jsx("input",{id:"duplicate-prices",type:"checkbox",checked:T,onChange:e=>{w(e.target.checked)},className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),l.jsx("label",{htmlFor:"duplicate-prices",className:"ml-2 block text-sm text-gray-700 dark:text-gray-300",children:c("bulkActions.duplicatePrices","Duplicate Prices")})]}),(0,l.jsxs)("div",{className:"flex items-center",children:[l.jsx("input",{id:"duplicate-attributes",type:"checkbox",checked:S,onChange:e=>{E(e.target.checked)},className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),l.jsx("label",{htmlFor:"duplicate-attributes",className:"ml-2 block text-sm text-gray-700 dark:text-gray-300",children:c("bulkActions.duplicateAttributes","Duplicate Attributes")})]}),(0,l.jsxs)("div",{className:"flex items-center",children:[l.jsx("input",{id:"duplicate-categories",type:"checkbox",checked:U,onChange:e=>{D(e.target.checked)},className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),l.jsx("label",{htmlFor:"duplicate-categories",className:"ml-2 block text-sm text-gray-700 dark:text-gray-300",children:c("bulkActions.duplicateCategories","Duplicate Categories")})]}),(0,l.jsxs)("div",{className:"flex items-center",children:[l.jsx("input",{id:"duplicate-seo",type:"checkbox",checked:I,onChange:e=>{L(e.target.checked)},className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),l.jsx("label",{htmlFor:"duplicate-seo",className:"ml-2 block text-sm text-gray-700 dark:text-gray-300",children:c("bulkActions.duplicateSEO","Duplicate SEO")})]})]})]}),(0,l.jsxs)("div",{className:"mb-6",children:[l.jsx("label",{htmlFor:"status",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("bulkActions.newProductStatus","New Product Status")}),(0,l.jsxs)("select",{id:"status",value:_,onChange:e=>{O(e.target.value)},className:"block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-700 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md",children:[l.jsx("option",{value:"active",children:c("status.active","Active")}),l.jsx("option",{value:"inactive",children:c("status.inactive","Inactive")}),l.jsx("option",{value:"draft",children:c("status.draft","Draft")})]})]}),(0,l.jsxs)("div",{className:"mt-6 flex justify-end space-x-3",children:[l.jsx(p.Z,{variant:"outline",onClick:t,disabled:s,children:c("common.cancel","Cancel")}),l.jsx(p.Z,{onClick:()=>{a({namePrefix:u,nameSuffix:x,skuPrefix:y,skuSuffix:k,duplicateMedia:v,duplicateVariants:N,duplicateInventory:P,duplicatePrices:T,duplicateAttributes:S,duplicateCategories:U,duplicateSEO:I,status:_})},isLoading:s,disabled:s,children:c("bulkActions.duplicate","Duplicate")})]})]})})};var h=a(86372);let y=h.g.injectEndpoints({endpoints:e=>({executeBulkAction:e.mutation({query:e=>({url:"/bulk-actions",method:"POST",body:e}),invalidatesTags:(e,t,{entityType:a})=>[{type:"BulkActions",id:"LIST"},{type:a,id:"LIST"}]}),getBulkActionStatus:e.query({query:e=>`/bulk-actions/${e}`,providesTags:(e,t,a)=>[{type:"BulkActions",id:a}]}),getBulkActionHistory:e.query({query:()=>"/bulk-actions/history",providesTags:["BulkActions"]}),cancelBulkAction:e.mutation({query:e=>({url:`/bulk-actions/${e}/cancel`,method:"POST"}),invalidatesTags:(e,t,a)=>[{type:"BulkActions",id:a},{type:"BulkActions",id:"LIST"}]}),bulkUpdateProductStatus:e.mutation({query:({productIds:e,params:t})=>({url:"/bulk-actions",method:"POST",body:{actionType:r.UPDATE_STATUS,entityType:"Product",entityIds:e,parameters:t}}),invalidatesTags:e=>[{type:"BulkActions",id:"LIST"},{type:"Product",id:"LIST"}]}),bulkUpdateProductCategories:e.mutation({query:({productIds:e,params:t})=>({url:"/bulk-actions",method:"POST",body:{actionType:r.UPDATE_CATEGORY,entityType:"Product",entityIds:e,parameters:t}}),invalidatesTags:e=>[{type:"BulkActions",id:"LIST"},{type:"Product",id:"LIST"},{type:"Category",id:"LIST"}]}),bulkUpdateProductPrices:e.mutation({query:({productIds:e,params:t})=>({url:"/bulk-actions",method:"POST",body:{actionType:r.UPDATE_PRICE,entityType:"Product",entityIds:e,parameters:t}}),invalidatesTags:e=>[{type:"BulkActions",id:"LIST"},{type:"Product",id:"LIST"}]}),bulkUpdateProductInventory:e.mutation({query:({productIds:e,params:t})=>({url:"/bulk-actions",method:"POST",body:{actionType:r.UPDATE_INVENTORY,entityType:"Product",entityIds:e,parameters:t}}),invalidatesTags:e=>[{type:"BulkActions",id:"LIST"},{type:"Product",id:"LIST"},{type:"InventoryLevel",id:"LIST"}]}),bulkUpdateProductAttributes:e.mutation({query:({productIds:e,params:t})=>({url:"/bulk-actions",method:"POST",body:{actionType:r.UPDATE_ATTRIBUTES,entityType:"Product",entityIds:e,parameters:t}}),invalidatesTags:e=>[{type:"BulkActions",id:"LIST"},{type:"Product",id:"LIST"},{type:"AttributeValue",id:"LIST"}]}),bulkDeleteProducts:e.mutation({query:({productIds:e,params:t})=>({url:"/bulk-actions",method:"POST",body:{actionType:r.DELETE,entityType:"Product",entityIds:e,parameters:t}}),invalidatesTags:e=>[{type:"BulkActions",id:"LIST"},{type:"Product",id:"LIST"}]}),bulkDuplicateProducts:e.mutation({query:({productIds:e,params:t})=>({url:"/bulk-actions",method:"POST",body:{actionType:r.DUPLICATE,entityType:"Product",entityIds:e,parameters:t}}),invalidatesTags:e=>[{type:"BulkActions",id:"LIST"},{type:"Product",id:"LIST"}]}),bulkExportProducts:e.mutation({query:({productIds:e,params:t})=>({url:"/bulk-actions",method:"POST",body:{actionType:r.EXPORT,entityType:"Product",entityIds:e,parameters:t}}),invalidatesTags:e=>[{type:"BulkActions",id:"LIST"}]})})}),{useExecuteBulkActionMutation:b,useGetBulkActionStatusQuery:k,useGetBulkActionHistoryQuery:f,useCancelBulkActionMutation:v,useBulkUpdateProductStatusMutation:j,useBulkUpdateProductCategoriesMutation:N,useBulkUpdateProductPricesMutation:A,useBulkUpdateProductInventoryMutation:P,useBulkUpdateProductAttributesMutation:C,useBulkDeleteProductsMutation:T,useBulkDuplicateProductsMutation:w,useBulkExportProductsMutation:S}=y,bulk_BulkActionsManager=({selectedIds:e,onSuccess:t,className:a=""})=>{let{t:s}=(0,n.$G)("product"),{isRtl:c}=(0,d.g)(),[o,u]=(0,i.useState)(null),[m,{isLoading:x}]=j(),[p,{isLoading:g}]=N(),[h,{isLoading:y}]=A(),[b,{isLoading:k}]=P(),[f,{isLoading:v}]=T(),[C,{isLoading:E}]=w(),[U,{isLoading:D}]=S(),handleAction=e=>{u(e)},handleCloseModal=()=>{u(null)},handleUpdateStatus=async a=>{try{await m({productIds:e,params:a}).unwrap(),handleCloseModal(),t&&t(r.UPDATE_STATUS)}catch(e){console.error("Error updating status:",e)}},handleUpdateCategories=async a=>{try{await p({productIds:e,params:a}).unwrap(),handleCloseModal(),t&&t(r.UPDATE_CATEGORY)}catch(e){console.error("Error updating categories:",e)}},handleUpdatePrices=async a=>{try{await h({productIds:e,params:a}).unwrap(),handleCloseModal(),t&&t(r.UPDATE_PRICE)}catch(e){console.error("Error updating prices:",e)}},handleUpdateInventory=async a=>{try{await b({productIds:e,params:a}).unwrap(),handleCloseModal(),t&&t(r.UPDATE_INVENTORY)}catch(e){console.error("Error updating inventory:",e)}},handleDeleteProducts=async a=>{try{await f({productIds:e,params:a}).unwrap(),handleCloseModal(),t&&t(r.DELETE)}catch(e){console.error("Error deleting products:",e)}},handleDuplicateProducts=async a=>{try{await C({productIds:e,params:a}).unwrap(),handleCloseModal(),t&&t(r.DUPLICATE)}catch(e){console.error("Error duplicating products:",e)}},handleExportProducts=async()=>{try{await U({productIds:e,params:{format:"csv",includeVariants:!0,includeInventory:!0,includeMedia:!0,includeAttributes:!0,includeCategories:!0,includeSEO:!0}}).unwrap(),t&&t(r.EXPORT)}catch(e){console.error("Error exporting products:",e)}};return(0,l.jsxs)("div",{className:a,children:[l.jsx(bulk_BulkActionButton,{selectedIds:e,onAction:(e,t)=>{e===r.EXPORT?handleExportProducts():handleAction(e)}}),l.jsx(bulk_BulkUpdateStatusModal,{isOpen:o===r.UPDATE_STATUS,onClose:handleCloseModal,onConfirm:handleUpdateStatus,selectedCount:e.length,isLoading:x}),l.jsx(bulk_BulkUpdateCategoriesModal,{isOpen:o===r.UPDATE_CATEGORY,onClose:handleCloseModal,onConfirm:handleUpdateCategories,selectedCount:e.length,isLoading:g}),l.jsx(bulk_BulkUpdatePricesModal,{isOpen:o===r.UPDATE_PRICE,onClose:handleCloseModal,onConfirm:handleUpdatePrices,selectedCount:e.length,isLoading:y}),l.jsx(bulk_BulkUpdateInventoryModal,{isOpen:o===r.UPDATE_INVENTORY,onClose:handleCloseModal,onConfirm:handleUpdateInventory,selectedCount:e.length,isLoading:k}),l.jsx(bulk_BulkDeleteModal,{isOpen:o===r.DELETE,onClose:handleCloseModal,onConfirm:handleDeleteProducts,selectedCount:e.length,isLoading:v}),l.jsx(bulk_BulkDuplicateModal,{isOpen:o===r.DUPLICATE,onClose:handleCloseModal,onConfirm:handleDuplicateProducts,selectedCount:e.length,isLoading:E})]})};var E=a(42075);let product_ProductListBulkActions=({selectedIds:e,onSelectionChange:t,onRefresh:a,totalCount:s,className:i=""})=>{let{t:c}=(0,n.$G)("product"),{isRtl:o}=(0,d.g)(),{showToast:u}=(0,E.useToast)();return(0,l.jsxs)("div",{className:`flex items-center justify-between p-4 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 ${i}`,children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[l.jsx("input",{id:"select-all",type:"checkbox",className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded",checked:e.length>0&&e.length===s,onChange:()=>{u({title:c("common.info","Information"),message:c("messages.selectAllNotImplemented","Select all functionality is not fully implemented yet."),type:"info"})},indeterminate:e.length>0&&e.length<s}),l.jsx("label",{htmlFor:"select-all",className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:c("common.selectAll","Select All")})]}),e.length>0&&(0,l.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:[c("common.selectedCount","{{count}} selected",{count:e.length}),l.jsx("button",{className:"ml-2 text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300",onClick:()=>{t([])},children:c("common.clear","Clear")})]})]}),l.jsx(bulk_BulkActionsManager,{selectedIds:e,onSuccess:e=>{t([]);let s="";switch(e){case r.UPDATE_STATUS:s=c("messages.bulkStatusUpdated","Product statuses updated successfully");break;case r.UPDATE_CATEGORY:s=c("messages.bulkCategoriesUpdated","Product categories updated successfully");break;case r.UPDATE_PRICE:s=c("messages.bulkPricesUpdated","Product prices updated successfully");break;case r.UPDATE_INVENTORY:s=c("messages.bulkInventoryUpdated","Product inventory updated successfully");break;case r.UPDATE_ATTRIBUTES:s=c("messages.bulkAttributesUpdated","Product attributes updated successfully");break;case r.DELETE:s=c("messages.bulkDeleted","Products deleted successfully");break;case r.DUPLICATE:s=c("messages.bulkDuplicated","Products duplicated successfully");break;case r.EXPORT:s=c("messages.bulkExported","Products exported successfully");break;default:s=c("messages.bulkActionSuccess","Bulk action completed successfully")}u({title:c("common.success","Success"),message:s,type:"success"}),a()}})]})},product_ProductList=({products:e,isLoading:t=!1,onRefresh:a=()=>{},className:r=""})=>{let{t:s}=(0,n.$G)("product"),{isRtl:c}=(0,d.g)(),[o,u]=(0,i.useState)([]);(0,i.useEffect)(()=>{u([])},[e]);let handleProductSelect=(e,t)=>{t?u(t=>[...t,e]):u(t=>t.filter(t=>t!==e))};return t?l.jsx("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden ${r}`,children:l.jsx("div",{className:"animate-pulse",children:[void 0,void 0,void 0,void 0,void 0].map((e,t)=>(0,l.jsxs)("div",{className:"flex items-center p-4 border-b border-gray-200 dark:border-gray-700",children:[l.jsx("div",{className:"h-4 w-4 bg-gray-200 dark:bg-gray-700 rounded mr-4"}),l.jsx("div",{className:"h-12 w-12 bg-gray-200 dark:bg-gray-700 rounded mr-4"}),(0,l.jsxs)("div",{className:"flex-1",children:[l.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-2"}),l.jsx("div",{className:"h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"})]}),l.jsx("div",{className:"h-6 w-16 bg-gray-200 dark:bg-gray-700 rounded-full ml-4"}),l.jsx("div",{className:"h-5 w-5 bg-gray-200 dark:bg-gray-700 rounded-full ml-4"})]},t))})}):0===e.length?(0,l.jsxs)("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center ${r}`,children:[l.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-12 w-12 text-gray-400 mx-auto mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"})}),l.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-1",children:s("list.noProducts","No products found")}),l.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:s("list.noProductsDescription","Get started by creating a new product.")})]}):(0,l.jsxs)("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden ${r}`,children:[l.jsx(product_ProductListBulkActions,{selectedIds:o,onSelectionChange:e=>{u(e)},onRefresh:a,totalCount:e.length}),l.jsx("div",{children:e.map(e=>l.jsx(product_ProductListItem,{product:e,isSelected:o.includes(e.id),onSelect:handleProductSelect},e.id))})]})};var U=a(48042);function ProductsManagementPage(){let{t:e}=(0,n.$G)("product"),{isRtl:t}=(0,d.g)(),[a,r]=(0,i.useState)(1),[s,c]=(0,i.useState)(10),{data:o,isLoading:m,refetch:x}=(0,U.C$)({page:a,limit:s});return l.jsx("div",{className:`min-h-screen p-6 ${t?"text-right":"text-left"}`,children:(0,l.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,l.jsxs)("div",{className:`flex flex-col md:flex-row md:items-center md:justify-between mb-8 gap-4 ${t?"md:flex-row-reverse":""}`,children:[l.jsx("h1",{className:"text-3xl font-bold",children:e("management.title","Product Management")}),l.jsx("div",{className:"flex flex-col sm:flex-row gap-2",children:l.jsx(u(),{href:"/dashboard/products/new",children:l.jsx(p.Z,{children:e("management.createProduct","Create Product")})})})]}),l.jsx(product_ProductList,{products:o?.products||[],isLoading:m,onRefresh:x,className:"mb-6"}),o&&o.products.length>0&&(0,l.jsxs)("div",{className:"flex justify-between items-center mt-6",children:[(0,l.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:[e("management.showing","Showing")," ",(a-1)*s+1," - ",(a-1)*s+o.products.length," ",e("management.of","of")," ",o.total||"?"]}),(0,l.jsxs)("div",{className:"flex space-x-2",children:[l.jsx(p.Z,{variant:"outline",onClick:()=>{a>1&&r(a-1)},disabled:1===a,children:e("common.previous","Previous")}),l.jsx(p.Z,{variant:"outline",onClick:()=>{o&&o.products.length===s&&r(a+1)},disabled:o.products.length<s,children:e("common.next","Next")})]})]})]})})}},98872:(e,t,a)=>{"use strict";a.r(t),a.d(t,{$$typeof:()=>i,__esModule:()=>l,default:()=>n});var r=a(95153);let s=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\dashboard\products\page.tsx`),{__esModule:l,$$typeof:i}=s,c=s.default,n=c}};var t=require("../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),a=t.X(0,[2103,2765,8042,2763,8576],()=>__webpack_exec__(83074));module.exports=a})();