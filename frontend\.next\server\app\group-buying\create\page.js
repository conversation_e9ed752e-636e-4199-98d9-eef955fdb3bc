(()=>{var e={};e.id=2180,e.ids=[2180],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},93850:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>o,routeModule:()=>g,tree:()=>c});var a=r(67096),i=r(16132),s=r(37284),n=r.n(s),d=r(32564),l={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);r.d(t,l);let c=["",{children:["group-buying",{children:["create",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,81760)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\group-buying\\create\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9291,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\group-buying\\create\\page.tsx"],m="/group-buying/create/page",u={require:r,loadChunk:()=>Promise.resolve()},g=new a.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/group-buying/create/page",pathname:"/group-buying/create",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},33:(e,t,r)=>{Promise.resolve().then(r.bind(r,43667))},43667:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>CreateGroupBuyingPage});var a=r(30784),i=r(9885),s=r(27870),n=r(57114),d=r(11440),l=r.n(d),c=r(14379),o=r(69302),m=r(31889),u=r(59872),g=r(706),x=r(92928),p=r(71942),h=r(63048);let ui_DateTimePicker=({id:e,label:t,value:r,onChange:s,error:n,minDate:d,maxDate:l,className:o=""})=>{let{isRtl:m}=(0,c.g)(),[u,g]=(0,i.useState)(""),[x,p]=(0,i.useState)("");(0,i.useEffect)(()=>{r?(g((0,h.ZP)(r,"yyyy-MM-dd")),p((0,h.ZP)(r,"HH:mm"))):(g(""),p(""))},[r]);let y=d?(0,h.ZP)(d,"yyyy-MM-dd"):void 0,b=l?(0,h.ZP)(l,"yyyy-MM-dd"):void 0;return(0,a.jsxs)("div",{className:`mb-4 ${m?"text-right":"text-left"}`,children:[t&&a.jsx("label",{htmlFor:e,className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:t}),(0,a.jsxs)("div",{className:"flex space-x-2 rtl:space-x-reverse",children:[a.jsx("input",{type:"date",id:e,value:u,onChange:e=>{let t=e.target.value;if(g(t),t&&x){let[e,r,a]=t.split("-").map(Number),[i,n]=x.split(":").map(Number),d=new Date(e,r-1,a,i,n);s(d)}else if(t){let[e,r,a]=t.split("-").map(Number),i=new Date(e,r-1,a);s(i)}},min:y,max:b,className:`flex-1 px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 ${n?"border-red-500 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-700"} bg-white dark:bg-gray-800 ${o}`,dir:m?"rtl":"ltr"}),a.jsx("input",{type:"time",value:x,onChange:e=>{let t=e.target.value;if(p(t),u&&t){let[e,r,a]=u.split("-").map(Number),[i,n]=t.split(":").map(Number),d=new Date(e,r-1,a,i,n);s(d)}},className:`w-24 px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 ${n?"border-red-500 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-700"} bg-white dark:bg-gray-800 ${o}`,dir:m?"rtl":"ltr"})]}),n&&a.jsx("p",{className:"mt-1 text-sm text-red-600",children:n})]})};var y=r(99529),b=r(65716);let product_ProductPreview=({productId:e,className:t=""})=>{let{t:r}=(0,s.$G)("product"),{isRtl:i}=(0,c.g)(),{data:n,isLoading:d,error:l}=(0,b.lZ)(e);return d?a.jsx("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 animate-pulse ${t}`,children:(0,a.jsxs)("div",{className:"flex",children:[a.jsx("div",{className:"w-20 h-20 bg-gray-200 dark:bg-gray-700 rounded"}),(0,a.jsxs)("div",{className:"ml-4 flex-1",children:[a.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"}),a.jsx("div",{className:"h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-2"}),a.jsx("div",{className:"h-5 bg-gray-200 dark:bg-gray-700 rounded w-1/4"})]})]})}):l||!n?a.jsx("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 ${t}`,children:a.jsx("p",{className:"text-red-500 dark:text-red-400",children:r("error.productNotFound","Product not found")})}):a.jsx("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 ${t}`,children:(0,a.jsxs)("div",{className:"flex",children:[n.mediaUrls&&n.mediaUrls.length>0?a.jsx("div",{className:"w-20 h-20 flex-shrink-0",children:a.jsx("img",{src:n.mediaUrls[0],alt:n.title,className:"w-full h-full object-cover rounded"})}):a.jsx("div",{className:"w-20 h-20 bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center",children:a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-8 w-8 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})})}),(0,a.jsxs)("div",{className:`${i?"mr-4":"ml-4"} flex-1`,children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:n.title}),n.store&&a.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:n.store.name}),a.jsx("p",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mt-1",children:new Intl.NumberFormat(i?"fa-IR":"en-US",{style:"currency",currency:"USD"}).format(n.price)})]})]})})},groupBuying_GroupBuyingPreview=({title:e,description:t,originalPrice:r,discountedPrice:i,minParticipants:n,maxParticipants:d,startDate:l,endDate:m,type:u,tiers:g=[],className:x=""})=>{let{t:p}=(0,s.$G)("groupBuying"),{isRtl:h}=(0,c.g)(),y=Math.round((r-i)/r*100),b=Math.ceil((new Date(m).getTime()-new Date().getTime())/864e5);return a.jsx("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden ${x}`,children:(0,a.jsxs)("div",{className:"p-4",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2",children:p("preview.title","Preview")}),(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[a.jsx("h4",{className:"text-xl font-bold text-gray-900 dark:text-gray-100 mb-2",children:e||p("preview.defaultTitle","Group Buy Title")}),t&&a.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-4 text-sm",children:t}),(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsxs)("div",{className:"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-sm font-semibold px-2.5 py-0.5 rounded",children:[y,"% ",p("preview.off","OFF")]}),a.jsx("div",{className:"mx-2 text-gray-500 dark:text-gray-400",children:"|"}),a.jsx("div",{className:"text-gray-500 dark:text-gray-400 text-sm",children:p("preview.minParticipants","{{count}} participants needed",{count:n})})]}),(0,a.jsxs)("div",{className:"flex items-end mb-4",children:[a.jsx("div",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:new Intl.NumberFormat(h?"fa-IR":"en-US",{style:"currency",currency:"USD"}).format(i)}),a.jsx("div",{className:"ml-2 text-gray-500 dark:text-gray-400 line-through text-sm",children:new Intl.NumberFormat(h?"fa-IR":"en-US",{style:"currency",currency:"USD"}).format(r)})]}),u===o.wU.TIERED_DISCOUNT&&g.length>0&&(0,a.jsxs)("div",{className:"mb-4",children:[a.jsx("h5",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:p("preview.discountTiers","Discount Tiers")}),a.jsx("div",{className:"space-y-2",children:g.map((e,t)=>(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[a.jsx("span",{children:p("preview.participants","{{count}} participants",{count:e.participantsCount})}),(0,a.jsxs)("span",{className:"font-semibold",children:[e.discountPercentage,"% ",p("preview.off","off")]})]},t))})]}),(0,a.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-3 rounded-md mb-4",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm text-blue-700 dark:text-blue-400",children:[a.jsx("span",{children:p("preview.timeRemaining","Time Remaining")}),(0,a.jsxs)("span",{className:"font-semibold",children:[b," ",p("preview.days","days")]})]}),a.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2.5 mt-2",children:a.jsx("div",{className:"bg-blue-600 h-2.5 rounded-full w-0"})})]}),a.jsx("button",{type:"button",className:"w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-md transition-colors",disabled:!0,children:p("preview.joinNow","Join Now")})]})]})})},groupBuying_GroupBuyingCreateForm=({productId:e,className:t=""})=>{let{t:r}=(0,s.$G)("groupBuying"),{isRtl:d}=(0,c.g)(),l=(0,n.useRouter)(),[h,b]=(0,i.useState)({title:"",description:"",productId:e||"",type:o.wU.FIXED_PRICE,minParticipants:5,maxParticipants:50,originalPrice:0,discountedPrice:0,startDate:new Date().toISOString(),endDate:new Date(Date.now()+6048e5).toISOString(),tiers:[]}),[j,v]=(0,i.useState)([{participantsCount:5,discountPercentage:10},{participantsCount:10,discountPercentage:15},{participantsCount:20,discountPercentage:20}]),[N,P]=(0,i.useState)({}),[f,{isLoading:w}]=(0,m.m$)(),handleInputChange=e=>{let{name:t,value:r}=e.target;b(e=>({...e,[t]:r})),N[t]&&P(e=>{let r={...e};return delete r[t],r})},handleNumberChange=e=>{let{name:t,value:r}=e.target,a=parseFloat(r);!isNaN(a)&&(b(e=>({...e,[t]:a})),N[t]&&P(e=>{let r={...e};return delete r[t],r}))},handleDateChange=(e,t)=>{t&&(b(r=>({...r,[e]:t.toISOString()})),N[e]&&P(t=>{let r={...t};return delete r[e],r}))},validateForm=()=>{let e={};h.title?.trim()||(e.title=r("validation.titleRequired","Title is required")),h.productId?.trim()||(e.productId=r("validation.productRequired","Product is required")),(!h.minParticipants||h.minParticipants<2)&&(e.minParticipants=r("validation.minParticipants","Minimum participants must be at least 2")),(!h.maxParticipants||h.maxParticipants<h.minParticipants)&&(e.maxParticipants=r("validation.maxParticipants","Maximum participants must be greater than minimum participants")),(!h.originalPrice||h.originalPrice<=0)&&(e.originalPrice=r("validation.originalPrice","Original price must be greater than 0")),(!h.discountedPrice||h.discountedPrice<=0)&&(e.discountedPrice=r("validation.discountedPrice","Discounted price must be greater than 0")),h.discountedPrice>=h.originalPrice&&(e.discountedPrice=r("validation.discountedPriceLower","Discounted price must be lower than original price"));let t=new Date(h.startDate),a=new Date(h.endDate),i=new Date;return t<i&&(e.startDate=r("validation.startDateFuture","Start date must be in the future")),a<=t&&(e.endDate=r("validation.endDateAfterStart","End date must be after start date")),P(e),0===Object.keys(e).length},handleSubmit=async e=>{if(e.preventDefault(),validateForm())try{let e=await f(h).unwrap();l.push(`/group-buying/${e.id}`)}catch(e){console.error("Failed to create group buying:",e)}};return a.jsx("form",{onSubmit:handleSubmit,className:`space-y-6 ${t}`,children:(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6",children:[a.jsx("h2",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100 mb-6",children:r("createGroupBuying","Create Group Buy")}),(0,a.jsxs)("div",{className:"space-y-4 mb-8",children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:r("basicInformation","Basic Information")}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"title",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[r("title","Title")," ",a.jsx("span",{className:"text-red-500",children:"*"})]}),a.jsx(g.Z,{id:"title",name:"title",value:h.title||"",onChange:handleInputChange,error:N.title,required:!0})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:r("description","Description")}),a.jsx(x.Z,{id:"description",name:"description",value:h.description||"",onChange:handleInputChange,rows:4})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[r("product","Product")," ",a.jsx("span",{className:"text-red-500",children:"*"})]}),e?(0,a.jsxs)(a.Fragment,{children:[a.jsx(g.Z,{id:"productId",name:"productId",value:h.productId||"",onChange:handleInputChange,error:N.productId,disabled:!0,required:!0}),h.productId&&a.jsx(product_ProductPreview,{productId:h.productId,className:"mt-2"})]}):(0,a.jsxs)("div",{children:[a.jsx(y.Z,{onSelect:e=>{b(t=>({...t,productId:e.id,originalPrice:e.price,discountedPrice:Math.round(90*e.price)/100})),N.productId&&P(e=>{let t={...e};return delete t.productId,t})},placeholder:r("searchProduct","Search for a product...")}),N.productId&&a.jsx("p",{className:"mt-1 text-sm text-red-600",children:N.productId}),h.productId&&a.jsx(product_ProductPreview,{productId:h.productId,className:"mt-2"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"type",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[r("type","Group Buy Type")," ",a.jsx("span",{className:"text-red-500",children:"*"})]}),(0,a.jsxs)(p.Z,{id:"type",name:"type",value:h.type||o.wU.FIXED_PRICE,onChange:handleInputChange,children:[a.jsx("option",{value:o.wU.FIXED_PRICE,children:r("type.fixedPrice","Fixed Price")}),a.jsx("option",{value:o.wU.TIERED_DISCOUNT,children:r("type.tieredDiscount","Tiered Discount")}),a.jsx("option",{value:o.wU.TIME_LIMITED,children:r("type.timeLimited","Time Limited")})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4 mb-8",children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:r("participants","Participants")}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"minParticipants",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[r("minParticipants","Minimum Participants")," ",a.jsx("span",{className:"text-red-500",children:"*"})]}),a.jsx(g.Z,{id:"minParticipants",name:"minParticipants",type:"number",min:"2",value:h.minParticipants||"",onChange:handleNumberChange,error:N.minParticipants,required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"maxParticipants",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[r("maxParticipants","Maximum Participants")," ",a.jsx("span",{className:"text-red-500",children:"*"})]}),a.jsx(g.Z,{id:"maxParticipants",name:"maxParticipants",type:"number",min:h.minParticipants||2,value:h.maxParticipants||"",onChange:handleNumberChange,error:N.maxParticipants,required:!0})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4 mb-8",children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:r("pricing","Pricing")}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"originalPrice",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[r("originalPrice","Original Price")," ",a.jsx("span",{className:"text-red-500",children:"*"})]}),a.jsx(g.Z,{id:"originalPrice",name:"originalPrice",type:"number",min:"0.01",step:"0.01",value:h.originalPrice||"",onChange:handleNumberChange,error:N.originalPrice,required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"discountedPrice",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[r("discountedPrice","Discounted Price")," ",a.jsx("span",{className:"text-red-500",children:"*"})]}),a.jsx(g.Z,{id:"discountedPrice",name:"discountedPrice",type:"number",min:"0.01",step:"0.01",max:h.originalPrice?h.originalPrice-.01:void 0,value:h.discountedPrice||"",onChange:handleNumberChange,error:N.discountedPrice,required:!0})]})]}),h.originalPrice&&h.discountedPrice&&h.originalPrice>h.discountedPrice&&a.jsx("div",{className:"bg-green-50 dark:bg-green-900/20 p-3 rounded-md",children:a.jsx("p",{className:"text-sm text-green-700 dark:text-green-400",children:r("discountPercentage","Discount: {{percentage}}%",{percentage:Math.round((h.originalPrice-h.discountedPrice)/h.originalPrice*100)})})})]}),(0,a.jsxs)("div",{className:"space-y-4 mb-8",children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:r("schedule","Schedule")}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"startDate",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[r("startDate","Start Date")," ",a.jsx("span",{className:"text-red-500",children:"*"})]}),a.jsx(ui_DateTimePicker,{id:"startDate",value:h.startDate?new Date(h.startDate):null,onChange:e=>handleDateChange("startDate",e),error:N.startDate,minDate:new Date})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"endDate",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[r("endDate","End Date")," ",a.jsx("span",{className:"text-red-500",children:"*"})]}),a.jsx(ui_DateTimePicker,{id:"endDate",value:h.endDate?new Date(h.endDate):null,onChange:e=>handleDateChange("endDate",e),error:N.endDate,minDate:h.startDate?new Date(h.startDate):new Date})]})]}),h.startDate&&h.endDate&&a.jsx("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-3 rounded-md",children:a.jsx("p",{className:"text-sm text-blue-700 dark:text-blue-400",children:r("campaignDuration","Campaign Duration: {{days}} days",{days:Math.ceil((new Date(h.endDate).getTime()-new Date(h.startDate).getTime())/864e5)})})})]}),h.type===o.wU.TIERED_DISCOUNT&&(0,a.jsxs)("div",{className:"space-y-4 mb-8",children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:r("tieredDiscount","Tiered Discount")}),a.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:r("tieredDiscountHelp","Define discount tiers based on the number of participants. Higher participant counts can unlock bigger discounts.")}),(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-12 gap-4 mb-2 font-medium text-sm text-gray-700 dark:text-gray-300",children:[a.jsx("div",{className:"col-span-5",children:r("participantsCount","Participants Count")}),a.jsx("div",{className:"col-span-5",children:r("discountPercentage","Discount %")}),a.jsx("div",{className:"col-span-2"})]}),j.map((e,t)=>(0,a.jsxs)("div",{className:"grid grid-cols-12 gap-4 mb-2 items-center",children:[a.jsx("div",{className:"col-span-5",children:a.jsx(g.Z,{type:"number",min:0===t?2:j[t-1].participantsCount+1,value:e.participantsCount,onChange:e=>{let r=parseInt(e.target.value);if(!isNaN(r)&&r>0){let e=[...j];e[t].participantsCount=r,v(e),b(t=>({...t,tiers:e}))}}})}),a.jsx("div",{className:"col-span-5",children:a.jsx(g.Z,{type:"number",min:1,max:99,value:e.discountPercentage,onChange:e=>{let r=parseInt(e.target.value);if(!isNaN(r)&&r>0&&r<100){let e=[...j];e[t].discountPercentage=r,v(e),b(t=>({...t,tiers:e}))}}})}),a.jsx("div",{className:"col-span-2 flex justify-end",children:a.jsx("button",{type:"button",className:"text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300",onClick:()=>{if(j.length>1){let e=j.filter((e,r)=>r!==t);v(e),b(t=>({...t,tiers:e}))}},disabled:j.length<=1,children:a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})})]},t)),a.jsx("div",{className:"mt-4",children:a.jsx(u.Z,{type:"button",variant:"outline",size:"sm",onClick:()=>{let e=j[j.length-1],t={participantsCount:e.participantsCount+5,discountPercentage:Math.min(e.discountPercentage+5,90)},r=[...j,t];v(r),b(e=>({...e,tiers:r}))},children:r("addTier","Add Tier")})})]})]}),(0,a.jsxs)("div",{className:"space-y-4 mb-8",children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:r("image","Image")}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"imageUrl",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:r("imageUrl","Image URL")}),a.jsx(g.Z,{id:"imageUrl",name:"imageUrl",value:h.imageUrl||"",onChange:handleInputChange,placeholder:"https://example.com/image.jpg"}),a.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:r("imageUrlHelp","Enter a URL for the group buy image. If not provided, the product image will be used.")})]})]}),h.title&&h.productId&&h.originalPrice>0&&h.discountedPrice>0&&a.jsx("div",{className:"mt-8",children:a.jsx(groupBuying_GroupBuyingPreview,{title:h.title,description:h.description,originalPrice:h.originalPrice,discountedPrice:h.discountedPrice,minParticipants:h.minParticipants||5,maxParticipants:h.maxParticipants||50,startDate:h.startDate||new Date().toISOString(),endDate:h.endDate||new Date(Date.now()+6048e5).toISOString(),type:h.type||o.wU.FIXED_PRICE,tiers:h.tiers})}),(0,a.jsxs)("div",{className:"mt-8 flex justify-end space-x-3",children:[a.jsx(u.Z,{type:"button",variant:"outline",onClick:()=>l.back(),children:r("cancel","Cancel")}),a.jsx(u.Z,{type:"submit",isLoading:w,disabled:w,children:r("create","Create")})]})]})})};var j=r(70661);function CreateGroupBuyingPage(){let{t:e}=(0,s.$G)("groupBuying"),t=(0,n.useSearchParams)(),r=t.get("productId")||void 0;return a.jsx(j.Z,{children:a.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[a.jsx(l(),{href:"/group-buying",className:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 mr-4",children:a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})})}),a.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-gray-100",children:e("createGroupBuying","Create Group Buy")})]}),a.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:e("createGroupBuyingDescription","Create a new group buying campaign to offer discounts to customers who buy together.")})]}),a.jsx(groupBuying_GroupBuyingCreateForm,{productId:r})]})})})}},81760:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>n,__esModule:()=>s,default:()=>l});var a=r(95153);let i=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\group-buying\create\page.tsx`),{__esModule:s,$$typeof:n}=i,d=i.default,l=d}};var t=require("../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),r=t.X(0,[2103,3048,2765,706,661,2928,7245,9529,1942],()=>__webpack_exec__(93850));module.exports=r})();