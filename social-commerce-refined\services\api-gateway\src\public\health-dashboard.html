<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Social Commerce Platform - Health Dashboard</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
      color: #333;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
    }
    h1 {
      color: #2c3e50;
      text-align: center;
      margin-bottom: 30px;
    }
    .dashboard {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;
    }
    .card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      padding: 20px;
      transition: transform 0.3s ease;
    }
    .card:hover {
      transform: translateY(-5px);
    }
    .card h2 {
      margin-top: 0;
      color: #3498db;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }
    .status {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
    }
    .status-indicator {
      width: 15px;
      height: 15px;
      border-radius: 50%;
      margin-right: 10px;
    }
    .up {
      background-color: #2ecc71;
    }
    .down {
      background-color: #e74c3c;
    }
    .warning {
      background-color: #f39c12;
    }
    .details {
      margin-top: 15px;
      font-size: 14px;
    }
    .details div {
      margin-bottom: 5px;
    }
    .refresh-button {
      display: block;
      margin: 20px auto;
      padding: 10px 20px;
      background-color: #3498db;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      transition: background-color 0.3s ease;
    }
    .refresh-button:hover {
      background-color: #2980b9;
    }
    .last-updated {
      text-align: center;
      margin-top: 20px;
      font-size: 14px;
      color: #7f8c8d;
    }
    .error-message {
      color: #e74c3c;
      text-align: center;
      margin: 20px 0;
      padding: 10px;
      background-color: #fadbd8;
      border-radius: 4px;
      display: none;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Social Commerce Platform - Health Dashboard</h1>
    
    <div id="error-message" class="error-message"></div>
    
    <div id="dashboard" class="dashboard">
      <!-- Cards will be dynamically added here -->
    </div>
    
    <button id="refresh-button" class="refresh-button">Refresh</button>
    
    <div id="last-updated" class="last-updated">
      Last updated: Never
    </div>
  </div>
  
  <script>
    // Service endpoints
    const API_ENDPOINT = '/api/health';
    const SERVICES_ENDPOINT = '/api/health/services';
    
    // DOM elements
    const dashboard = document.getElementById('dashboard');
    const refreshButton = document.getElementById('refresh-button');
    const lastUpdated = document.getElementById('last-updated');
    const errorMessage = document.getElementById('error-message');
    
    // Fetch health data
    async function fetchHealthData() {
      try {
        errorMessage.style.display = 'none';
        
        // Fetch overall health
        const overallResponse = await fetch(API_ENDPOINT);
        const overallHealth = await overallResponse.json();
        
        // Fetch services health
        const servicesResponse = await fetch(SERVICES_ENDPOINT);
        const servicesHealth = await servicesResponse.json();
        
        // Update dashboard
        updateDashboard(overallHealth, servicesHealth);
        
        // Update last updated time
        lastUpdated.textContent = `Last updated: ${new Date().toLocaleString()}`;
      } catch (error) {
        console.error('Error fetching health data:', error);
        errorMessage.textContent = `Error fetching health data: ${error.message}`;
        errorMessage.style.display = 'block';
      }
    }
    
    // Update dashboard with health data
    function updateDashboard(overallHealth, servicesHealth) {
      // Clear dashboard
      dashboard.innerHTML = '';
      
      // Add API Gateway card
      const apiGatewayStatus = overallHealth.info.apiGateway.status === 'up' ? 'up' : 'down';
      addCard('API Gateway', apiGatewayStatus, {
        'Status': overallHealth.info.apiGateway.status,
      });
      
      // Add System Resources card
      const storageStatus = overallHealth.info.storage.status === 'up' ? 'up' : 'down';
      const memoryHeapStatus = overallHealth.info.memory_heap.status === 'up' ? 'up' : 'down';
      const memoryRssStatus = overallHealth.info.memory_rss.status === 'up' ? 'up' : 'down';
      
      addCard('System Resources', 
        storageStatus === 'up' && memoryHeapStatus === 'up' && memoryRssStatus === 'up' ? 'up' : 'warning', 
        {
          'Disk Storage': overallHealth.info.storage.status,
          'Memory Heap': overallHealth.info.memory_heap.status,
          'Memory RSS': overallHealth.info.memory_rss.status,
        }
      );
      
      // Add User Service card
      const userServiceStatus = servicesHealth.info.userService.status === 'up' ? 'up' : 'down';
      addCard('User Service', userServiceStatus, {
        'Status': servicesHealth.info.userService.status,
      });
      
      // Add Store Service card
      const storeServiceStatus = servicesHealth.info.storeService.status === 'up' ? 'up' : 'down';
      addCard('Store Service', storeServiceStatus, {
        'Status': servicesHealth.info.storeService.status,
      });
    }
    
    // Add a card to the dashboard
    function addCard(title, status, details) {
      const card = document.createElement('div');
      card.className = 'card';
      
      const cardTitle = document.createElement('h2');
      cardTitle.textContent = title;
      card.appendChild(cardTitle);
      
      const statusDiv = document.createElement('div');
      statusDiv.className = 'status';
      
      const statusIndicator = document.createElement('div');
      statusIndicator.className = `status-indicator ${status}`;
      statusDiv.appendChild(statusIndicator);
      
      const statusText = document.createElement('span');
      statusText.textContent = status.toUpperCase();
      statusDiv.appendChild(statusText);
      
      card.appendChild(statusDiv);
      
      const detailsDiv = document.createElement('div');
      detailsDiv.className = 'details';
      
      for (const [key, value] of Object.entries(details)) {
        const detailItem = document.createElement('div');
        detailItem.textContent = `${key}: ${value}`;
        detailsDiv.appendChild(detailItem);
      }
      
      card.appendChild(detailsDiv);
      dashboard.appendChild(card);
    }
    
    // Event listeners
    refreshButton.addEventListener('click', fetchHealthData);
    
    // Initial fetch
    fetchHealthData();
    
    // Auto-refresh every 30 seconds
    setInterval(fetchHealthData, 30000);
  </script>
</body>
</html>
