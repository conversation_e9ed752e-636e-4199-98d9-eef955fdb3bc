import { BaseEvent } from '../base-event.interface';
export declare class StoreUpdatedEvent implements BaseEvent<StoreUpdatedPayload> {
    id: string;
    type: string;
    version: string;
    timestamp: string;
    producer: string;
    payload: StoreUpdatedPayload;
    constructor(payload: StoreUpdatedPayload);
}
export interface StoreUpdatedPayload {
    id: string;
    ownerId: string;
    name: string;
    description: string;
    updatedAt: string;
}
