(()=>{var e={};e.id=6931,e.ids=[6931],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},45972:(e,r,o)=>{"use strict";o.r(r),o.d(r,{GlobalError:()=>i.a,__next_app__:()=>h,originalPathname:()=>l,pages:()=>s,routeModule:()=>m,tree:()=>u});var t=o(67096),n=o(16132),a=o(37284),i=o.n(a),c=o(32564),d={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>c[e]);o.d(r,d);let u=["",{children:["verify-email",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(o.bind(o,30692)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\verify-email\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(o.bind(o,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(o.t.bind(o,9291,23)),"next/dist/client/components/not-found-error"]}],s=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\verify-email\\page.tsx"],l="/verify-email/page",h={require:o,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/verify-email/page",pathname:"/verify-email",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},92729:(e,r,o)=>{Promise.resolve().then(o.bind(o,43185))},43185:(e,r,o)=>{"use strict";o.r(r),o.d(r,{default:()=>VerifyEmailPage});var t=o(30784),n=o(9885),a=o(57114);!function(){var e=Error("Cannot find module '@chakra-ui/react'");throw e.code="MODULE_NOT_FOUND",e}();var i=o(19923);function VerifyEmailPage(){let e=(0,a.useSearchParams)(),r=(0,a.useRouter)(),o=e.get("token"),[c,{isLoading:d,isSuccess:u,isError:s,error:l}]=(0,i.qQ)(),[h,m]=(0,n.useState)(!1);(0,n.useEffect)(()=>{o&&!h&&(c({token:o}),m(!0))},[o,c,h]);let handleGoToLogin=()=>{r.push("/login")},handleGoToHome=()=>{r.push("/")};if(!o)return(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@chakra-ui/react'");throw e.code="MODULE_NOT_FOUND",e}()),{maxW:"md",mx:"auto",mt:10,p:6,borderWidth:1,borderRadius:"lg",boxShadow:"lg",children:[(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@chakra-ui/react'");throw e.code="MODULE_NOT_FOUND",e}()),{status:"error",borderRadius:"md",mb:6,children:[t.jsx(Object(function(){var e=Error("Cannot find module '@chakra-ui/react'");throw e.code="MODULE_NOT_FOUND",e}()),{}),t.jsx(Object(function(){var e=Error("Cannot find module '@chakra-ui/react'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Invalid Verification Link"}),t.jsx(Object(function(){var e=Error("Cannot find module '@chakra-ui/react'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"The verification link is missing a token. Please check your email and try again."})]}),t.jsx(Object(function(){var e=Error("Cannot find module '@chakra-ui/react'");throw e.code="MODULE_NOT_FOUND",e}()),{colorScheme:"blue",width:"full",onClick:handleGoToHome,children:"Go to Home"})]});if(d)return(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@chakra-ui/react'");throw e.code="MODULE_NOT_FOUND",e}()),{direction:"column",align:"center",justify:"center",minH:"50vh",children:[t.jsx(Object(function(){var e=Error("Cannot find module '@chakra-ui/react'");throw e.code="MODULE_NOT_FOUND",e}()),{size:"xl",thickness:"4px",speed:"0.65s",color:"blue.500",mb:4}),t.jsx(Object(function(){var e=Error("Cannot find module '@chakra-ui/react'");throw e.code="MODULE_NOT_FOUND",e}()),{size:"md",children:"Verifying your email..."}),t.jsx(Object(function(){var e=Error("Cannot find module '@chakra-ui/react'");throw e.code="MODULE_NOT_FOUND",e}()),{mt:2,color:"gray.600",children:"Please wait while we verify your email address."})]});if(u)return(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@chakra-ui/react'");throw e.code="MODULE_NOT_FOUND",e}()),{maxW:"md",mx:"auto",mt:10,p:6,borderWidth:1,borderRadius:"lg",boxShadow:"lg",children:[(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@chakra-ui/react'");throw e.code="MODULE_NOT_FOUND",e}()),{status:"success",borderRadius:"md",mb:6,children:[t.jsx(Object(function(){var e=Error("Cannot find module '@chakra-ui/react'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@chakra-ui/react'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[t.jsx(Object(function(){var e=Error("Cannot find module '@chakra-ui/react'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Email Verified!"}),t.jsx(Object(function(){var e=Error("Cannot find module '@chakra-ui/react'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Your email has been successfully verified. You can now log in to your account."})]})]}),t.jsx(Object(function(){var e=Error("Cannot find module '@chakra-ui/react'");throw e.code="MODULE_NOT_FOUND",e}()),{colorScheme:"blue",width:"full",onClick:handleGoToLogin,children:"Go to Login"})]});if(s){let e=l?.data?.message||"An error occurred during email verification.";return(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@chakra-ui/react'");throw e.code="MODULE_NOT_FOUND",e}()),{maxW:"md",mx:"auto",mt:10,p:6,borderWidth:1,borderRadius:"lg",boxShadow:"lg",children:[(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@chakra-ui/react'");throw e.code="MODULE_NOT_FOUND",e}()),{status:"error",borderRadius:"md",mb:6,children:[t.jsx(Object(function(){var e=Error("Cannot find module '@chakra-ui/react'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@chakra-ui/react'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[t.jsx(Object(function(){var e=Error("Cannot find module '@chakra-ui/react'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Verification Failed"}),t.jsx(Object(function(){var e=Error("Cannot find module '@chakra-ui/react'");throw e.code="MODULE_NOT_FOUND",e}()),{children:e})]})]}),t.jsx(Object(function(){var e=Error("Cannot find module '@chakra-ui/react'");throw e.code="MODULE_NOT_FOUND",e}()),{mb:4,children:"The verification link may have expired or already been used. Please try logging in or request a new verification email."}),t.jsx(Object(function(){var e=Error("Cannot find module '@chakra-ui/react'");throw e.code="MODULE_NOT_FOUND",e}()),{colorScheme:"blue",width:"full",onClick:handleGoToLogin,mb:3,children:"Go to Login"}),t.jsx(Object(function(){var e=Error("Cannot find module '@chakra-ui/react'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",width:"full",onClick:handleGoToHome,children:"Go to Home"})]})}return(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@chakra-ui/react'");throw e.code="MODULE_NOT_FOUND",e}()),{maxW:"md",mx:"auto",mt:10,p:6,borderWidth:1,borderRadius:"lg",boxShadow:"lg",children:[t.jsx(Object(function(){var e=Error("Cannot find module '@chakra-ui/react'");throw e.code="MODULE_NOT_FOUND",e}()),{size:"lg",mb:6,textAlign:"center",children:"Email Verification"}),t.jsx(Object(function(){var e=Error("Cannot find module '@chakra-ui/react'");throw e.code="MODULE_NOT_FOUND",e}()),{mb:4,children:"Processing your verification request..."}),t.jsx(Object(function(){var e=Error("Cannot find module '@chakra-ui/react'");throw e.code="MODULE_NOT_FOUND",e}()),{colorScheme:"blue",width:"full",onClick:handleGoToHome,children:"Go to Home"})]})}},30692:(e,r,o)=>{"use strict";o.r(r),o.d(r,{$$typeof:()=>i,__esModule:()=>a,default:()=>d});var t=o(95153);let n=(0,t.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\verify-email\page.tsx`),{__esModule:a,$$typeof:i}=n,c=n.default,d=c}};var r=require("../../webpack-runtime.js");r.C(e);var __webpack_exec__=e=>r(r.s=e),o=r.X(0,[2103,2765],()=>__webpack_exec__(45972));module.exports=o})();