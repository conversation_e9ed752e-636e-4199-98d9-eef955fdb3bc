(()=>{var e={};e.id=123,e.ids=[123],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},34173:(e,r,a)=>{"use strict";a.r(r),a.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>g,pages:()=>c,routeModule:()=>m,tree:()=>d});var t=a(67096),s=a(16132),i=a(37284),n=a.n(i),o=a(32564),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);a.d(r,l);let d=["",{children:["group-buying",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,11738)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\group-buying\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,9291,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\group-buying\\page.tsx"],g="/group-buying/page",u={require:a,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/group-buying/page",pathname:"/group-buying",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},46037:(e,r,a)=>{Promise.resolve().then(a.bind(a,31940))},31940:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>GroupBuyingPage});var t=a(30784),s=a(9885),i=a.n(s),n=a(27870),o=a(14379),l=a(11440),d=a.n(l),c=a(59872);let groupBuying_GroupBuyingTabs=({activeTab:e,onTabChange:r,tabs:a=["featured","popular","endingSoon","created","joined"],counts:s,className:i=""})=>{let{t:l}=(0,n.$G)("groupBuying"),{isRtl:d}=(0,o.g)(),getTabLabel=e=>{let r=l(`tabs.${e}`,e),a=s?.[e];return void 0!==a?`${r} (${a})`:r};return t.jsx("div",{className:`border-b border-gray-200 dark:border-gray-700 ${i}`,children:t.jsx("nav",{className:"-mb-px flex space-x-8","aria-label":"Tabs",children:a.map(a=>t.jsx("button",{onClick:()=>r(a),className:`${e===a?"border-primary-500 text-primary-600 dark:text-primary-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600"} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`,children:getTabLabel(a)},a))})})};var g=a(52451),u=a.n(g),m=a(69302),x=a(56663);let groupBuying_GroupBuyingCard=({groupBuying:e,variant:r="default",className:a=""})=>{let{t:s}=(0,n.$G)("groupBuying"),{isRtl:i}=(0,o.g)(),l=Math.min(Math.round(e.currentParticipants/e.minParticipants*100),100),g=new Date(e.endDate),p=new Date,y=g.getTime()-p.getTime(),h=Math.max(0,Math.floor(y/864e5)),j=Math.max(0,Math.floor(y%864e5/36e5)),getTimeRemainingText=()=>e.status===m.ys.COMPLETED||e.status===m.ys.CANCELLED||e.status===m.ys.EXPIRED?s(`status.${e.status.toLowerCase()}`,e.status):h>0?s("timeRemaining.days","{{days}} days left",{days:h}):j>0?s("timeRemaining.hours","{{hours}} hours left",{hours:j}):s("timeRemaining.ending","Ending soon"),getStatusColor=()=>{switch(e.status){case m.ys.ACTIVE:return"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";case m.ys.PENDING:return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";case m.ys.COMPLETED:return"bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";case m.ys.EXPIRED:case m.ys.CANCELLED:return"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400"}},getDiscountText=()=>{let r=Math.round((e.originalPrice-e.discountedPrice)/e.originalPrice*100);return s("discount","{{percentage}}% OFF",{percentage:r})};return"featured"===r?t.jsx("div",{className:`bg-gradient-to-r from-primary-500/10 to-primary-600/10 dark:from-primary-900/20 dark:to-primary-800/20 rounded-lg shadow-md overflow-hidden ${a}`,children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row",children:[(0,t.jsxs)("div",{className:"md:w-2/5 relative aspect-[4/3] md:aspect-auto",children:[t.jsx(u(),{src:e.imageUrl||e.product?.mediaUrls?.[0]||"/images/placeholder-product.png",alt:e.title,fill:!0,className:"object-cover"}),t.jsx("div",{className:"absolute top-0 left-0 bg-primary-500 text-white text-sm font-bold px-3 py-1.5",children:getDiscountText()}),t.jsx("div",{className:"absolute top-0 right-0 m-3",children:t.jsx("div",{className:`px-3 py-1.5 rounded-full text-sm font-medium ${getStatusColor()}`,children:s(`status.${e.status.toLowerCase()}`,e.status)})})]}),(0,t.jsxs)("div",{className:"md:w-3/5 p-6",children:[(0,t.jsxs)("div",{className:"flex items-center mb-2",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[e.store?.logoUrl?t.jsx("div",{className:"w-6 h-6 rounded-full overflow-hidden mr-2",children:t.jsx(u(),{src:e.store.logoUrl,alt:e.store.name,width:24,height:24,className:"object-cover"})}):t.jsx("div",{className:"w-6 h-6 rounded-full bg-gray-200 dark:bg-gray-700 mr-2"}),t.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-300",children:e.store?.name||s("unknownStore","Unknown Store")})]}),t.jsx("div",{className:"ml-auto",children:t.jsx("span",{className:"text-sm text-primary-600 dark:text-primary-400 font-medium",children:getTimeRemainingText()})})]}),t.jsx("h3",{className:"text-xl font-bold text-gray-900 dark:text-gray-100 mb-3",children:e.title}),(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[t.jsx("span",{className:"text-2xl font-bold text-primary-600 dark:text-primary-400",children:(0,x.xG)(e.discountedPrice)}),t.jsx("span",{className:"ml-3 text-lg text-gray-500 dark:text-gray-400 line-through",children:(0,x.xG)(e.originalPrice)})]}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm mb-2",children:[t.jsx("span",{className:"font-medium",children:s("participants","{{current}}/{{min}} joined",{current:e.currentParticipants,min:e.minParticipants})}),(0,t.jsxs)("span",{className:"text-primary-600 dark:text-primary-400 font-medium",children:[Math.round(e.currentParticipants/e.minParticipants*100),"%"]})]}),t.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5",children:t.jsx("div",{className:"bg-primary-500 h-2.5 rounded-full",style:{width:`${l}%`}})})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx(d(),{href:`/group-buying/${e.id}`,className:"flex-1",children:t.jsx(c.Z,{variant:"primary",fullWidth:!0,children:e.status===m.ys.ACTIVE?s("joinNow","Join Now"):s("viewDetails","View Details")})}),t.jsx("button",{className:"ml-4 p-2 rounded-full bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:t.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-gray-600 dark:text-gray-300",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"})})})]})]})]})}):"compact"===r?t.jsx("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden ${a}`,children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsxs)("div",{className:"flex-shrink-0 w-24 h-24 relative",children:[t.jsx(u(),{src:e.imageUrl||e.product?.mediaUrls?.[0]||"/images/placeholder-product.png",alt:e.title,fill:!0,className:"object-cover"}),t.jsx("div",{className:"absolute top-0 left-0 bg-primary-500 text-white text-xs font-bold px-2 py-1",children:getDiscountText()})]}),(0,t.jsxs)("div",{className:"flex-1 p-3",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[t.jsx("h3",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 line-clamp-1",children:e.title}),t.jsx("span",{className:`text-xs px-2 py-1 rounded-full ${getStatusColor()}`,children:s(`status.${e.status.toLowerCase()}`,e.status)})]}),(0,t.jsxs)("div",{className:"mt-1 flex items-center text-sm",children:[t.jsx("span",{className:"font-medium text-primary-600 dark:text-primary-400",children:(0,x.xG)(e.discountedPrice)}),t.jsx("span",{className:"ml-2 text-gray-500 dark:text-gray-400 line-through text-xs",children:(0,x.xG)(e.originalPrice)})]}),(0,t.jsxs)("div",{className:"mt-2",children:[(0,t.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 dark:text-gray-400 mb-1",children:[t.jsx("span",{children:s("participants","{{current}}/{{min}} joined",{current:e.currentParticipants,min:e.minParticipants})}),t.jsx("span",{children:getTimeRemainingText()})]}),t.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5",children:t.jsx("div",{className:"bg-primary-500 h-1.5 rounded-full",style:{width:`${l}%`}})})]})]})]})}):(0,t.jsxs)("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden ${a}`,children:[(0,t.jsxs)("div",{className:"relative aspect-[4/3]",children:[t.jsx(u(),{src:e.imageUrl||e.product?.mediaUrls?.[0]||"/images/placeholder-product.png",alt:e.title,fill:!0,className:"object-cover"}),t.jsx("div",{className:"absolute top-0 left-0 bg-primary-500 text-white text-xs font-bold px-2 py-1",children:getDiscountText()}),t.jsx("div",{className:`absolute top-0 right-0 m-2 text-xs px-2 py-1 rounded-full ${getStatusColor()}`,children:s(`status.${e.status.toLowerCase()}`,e.status)})]}),(0,t.jsxs)("div",{className:"p-4",children:[t.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 line-clamp-2",children:e.title}),(0,t.jsxs)("div",{className:"mt-2 flex items-center",children:[t.jsx("span",{className:"text-xl font-bold text-primary-600 dark:text-primary-400",children:(0,x.xG)(e.discountedPrice)}),t.jsx("span",{className:"ml-2 text-gray-500 dark:text-gray-400 line-through",children:(0,x.xG)(e.originalPrice)})]}),(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm text-gray-500 dark:text-gray-400 mb-1",children:[t.jsx("span",{children:s("participants","{{current}}/{{min}} joined",{current:e.currentParticipants,min:e.minParticipants})}),t.jsx("span",{children:getTimeRemainingText()})]}),t.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:t.jsx("div",{className:"bg-primary-500 h-2 rounded-full",style:{width:`${l}%`}})})]}),(0,t.jsxs)("div",{className:"mt-4 flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[e.store?.logoUrl?t.jsx("div",{className:"w-6 h-6 rounded-full overflow-hidden mr-2",children:t.jsx(u(),{src:e.store.logoUrl,alt:e.store.name,width:24,height:24,className:"object-cover"})}):t.jsx("div",{className:"w-6 h-6 rounded-full bg-gray-200 dark:bg-gray-700 mr-2"}),t.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-300 truncate",children:e.store?.name||s("unknownStore","Unknown Store")})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[e.creator?.avatarUrl?t.jsx("div",{className:"w-6 h-6 rounded-full overflow-hidden mr-2",children:t.jsx(u(),{src:e.creator.avatarUrl,alt:e.creator.displayName||e.creator.username,width:24,height:24,className:"object-cover"})}):t.jsx("div",{className:"w-6 h-6 rounded-full bg-gray-200 dark:bg-gray-700 mr-2"}),t.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-300 truncate",children:e.creator?.displayName||e.creator?.username||s("unknownCreator","Unknown Creator")})]})]}),t.jsx("div",{className:"mt-4",children:t.jsx(d(),{href:`/group-buying/${e.id}`,children:t.jsx(c.Z,{variant:"primary",fullWidth:!0,children:e.status===m.ys.ACTIVE?s("joinNow","Join Now"):s("viewDetails","View Details")})})})]})]})},groupBuying_GroupBuyingGrid=({groupBuying:e,isLoading:r=!1,emptyMessage:a="No group buys found",variant:s="default",columns:i=3,showLoadMore:l=!1,onLoadMore:d,isLoadingMore:g=!1,className:u=""})=>{let{t:m}=(0,n.$G)("groupBuying"),{isRtl:x}=(0,o.g)(),getColumnClass=()=>{switch(i){case 2:return"grid-cols-1 sm:grid-cols-2";case 3:default:return"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3";case 4:return"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"}};return r?t.jsx("div",{className:`grid ${getColumnClass()} gap-6 ${u}`,children:Array.from({length:2*i}).map((e,r)=>(0,t.jsxs)("div",{className:"bg-gray-100 dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden animate-pulse",children:[t.jsx("div",{className:"aspect-[4/3] bg-gray-200 dark:bg-gray-700"}),(0,t.jsxs)("div",{className:"p-4",children:[t.jsx("div",{className:"h-6 bg-gray-200 dark:bg-gray-700 rounded mb-3"}),t.jsx("div",{className:"h-5 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-4"}),t.jsx("div",{className:"h-2 bg-gray-200 dark:bg-gray-700 rounded mb-4"}),t.jsx("div",{className:"h-10 bg-gray-200 dark:bg-gray-700 rounded"})]})]},r))}):0===e.length?(0,t.jsxs)("div",{className:`flex flex-col items-center justify-center py-12 ${u}`,children:[t.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-16 w-16 text-gray-400 dark:text-gray-600 mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"})}),t.jsx("p",{className:"text-gray-500 dark:text-gray-400 text-lg",children:m(a,a)})]}):(0,t.jsxs)("div",{className:u,children:[t.jsx("div",{className:`grid ${getColumnClass()} gap-6`,children:e.map(e=>t.jsx(groupBuying_GroupBuyingCard,{groupBuying:e,variant:s},e.id))}),l&&d&&t.jsx("div",{className:"flex justify-center mt-8",children:t.jsx(c.Z,{variant:"outline",onClick:d,isLoading:g,disabled:g,children:m("loadMore","Load More")})})]})};var p=a(31889);let groupBuying_GroupBuyingTabContent=({activeTab:e,className:r=""})=>{let{t:a}=(0,n.$G)("groupBuying"),{isRtl:l}=(0,o.g)(),[d,c]=(0,s.useState)(1),{data:g,isLoading:u,isFetching:m}=(0,p.M)({page:d,limit:9},{skip:"featured"!==e}),{data:x,isLoading:y,isFetching:h}=(0,p.Vs)({page:d,limit:9},{skip:"popular"!==e}),{data:j,isLoading:b,isFetching:f}=(0,p.cb)({page:d,limit:9},{skip:"endingSoon"!==e}),{data:v,isLoading:N,isFetching:w}=(0,p.Ev)({page:d,limit:9},{skip:"created"!==e}),{data:k,isLoading:B,isFetching:L}=(0,p.Lt)({page:d,limit:9},{skip:"joined"!==e}),handleLoadMore=()=>{c(d+1)};return i().useEffect(()=>{c(1)},[e]),t.jsx("div",{className:`py-6 ${r}`,children:(()=>{switch(e){case"featured":return t.jsx(groupBuying_GroupBuyingGrid,{groupBuying:g?.groupBuying||[],isLoading:u,emptyMessage:"empty.noFeaturedGroupBuying",showLoadMore:g?.groupBuying.length<(g?.total||0),onLoadMore:handleLoadMore,isLoadingMore:!u&&m});case"popular":return t.jsx(groupBuying_GroupBuyingGrid,{groupBuying:x?.groupBuying||[],isLoading:y,emptyMessage:"empty.noPopularGroupBuying",showLoadMore:x?.groupBuying.length<(x?.total||0),onLoadMore:handleLoadMore,isLoadingMore:!y&&h});case"endingSoon":return t.jsx(groupBuying_GroupBuyingGrid,{groupBuying:j?.groupBuying||[],isLoading:b,emptyMessage:"empty.noEndingSoonGroupBuying",showLoadMore:j?.groupBuying.length<(j?.total||0),onLoadMore:handleLoadMore,isLoadingMore:!b&&f});case"created":return t.jsx(groupBuying_GroupBuyingGrid,{groupBuying:v?.groupBuying||[],isLoading:N,emptyMessage:"empty.noCreatedGroupBuying",showLoadMore:v?.groupBuying.length<(v?.total||0),onLoadMore:handleLoadMore,isLoadingMore:!N&&w});case"joined":return t.jsx(groupBuying_GroupBuyingGrid,{groupBuying:k?.groupBuying||[],isLoading:B,emptyMessage:"empty.noJoinedGroupBuying",showLoadMore:k?.groupBuying.length<(k?.total||0),onLoadMore:handleLoadMore,isLoadingMore:!B&&L});default:return null}})()})};function GroupBuyingPage(){let{t:e}=(0,n.$G)("groupBuying"),{isRtl:r}=(0,o.g)(),[a,i]=(0,s.useState)("featured");return t.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between mb-8 gap-4",children:[(0,t.jsxs)("div",{children:[t.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2",children:e("title","Group Buying")}),t.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:e("description","Join with others to get better prices on products you love.")})]}),t.jsx(d(),{href:"/group-buying/create",children:t.jsx(c.Z,{children:e("createGroupBuying","Create Group Buy")})})]}),t.jsx(groupBuying_GroupBuyingTabs,{activeTab:a,onTabChange:e=>{i(e)},className:"mb-4"}),t.jsx(groupBuying_GroupBuyingTabContent,{activeTab:a})]})})}},11738:(e,r,a)=>{"use strict";a.r(r),a.d(r,{$$typeof:()=>n,__esModule:()=>i,default:()=>l});var t=a(95153);let s=(0,t.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\group-buying\page.tsx`),{__esModule:i,$$typeof:n}=s,o=s.default,l=o}};var r=require("../../webpack-runtime.js");r.C(e);var __webpack_exec__=e=>r(r.s=e),a=r.X(0,[2103,2765,6663,7245],()=>__webpack_exec__(34173));module.exports=a})();