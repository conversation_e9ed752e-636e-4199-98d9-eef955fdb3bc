{"name": "integration-tests", "version": "1.0.0", "description": "Integration tests for Social Commerce Platform", "main": "index.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage"}, "keywords": ["integration", "tests", "microservices"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.6.7", "dotenv": "^16.4.5"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^22.15.21", "jest": "^29.7.0", "ts-jest": "^29.3.4", "typescript": "^5.8.3"}}