(()=>{var e={};e.id=671,e.ids=[671],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},74541:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>l,routeModule:()=>p,tree:()=>d});var s=t(67096),a=t(16132),n=t(37284),o=t.n(n),c=t(32564),i={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>c[e]);t.d(r,i);let d=["",{children:["stores",{children:["[username]",{children:["products",{children:["create",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,89244)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\stores\\[username]\\products\\create\\page.tsx"]}]},{}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9291,23)),"next/dist/client/components/not-found-error"]}],l=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\stores\\[username]\\products\\create\\page.tsx"],u="/stores/[username]/products/create/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/stores/[username]/products/create/page",pathname:"/stores/[username]/products/create",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},36776:(e,r,t)=>{Promise.resolve().then(t.bind(t,15601))},15601:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>CreateProductPage});var s=t(30784);t(9885);var a=t(57114),n=t(41245),o=t(59872),c=t(77783);function CreateProductPage({params:e}){let{username:r}=e,t=(0,a.useRouter)(),{data:i,isLoading:d,error:l}=(0,c.YU)(r);return d?s.jsx("div",{className:"min-h-screen p-6 md:p-12",children:(0,s.jsxs)("div",{className:"max-w-3xl mx-auto text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600 mx-auto"}),s.jsx("p",{className:"mt-4 text-lg",children:"Loading store..."})]})}):l||!i?s.jsx("div",{className:"min-h-screen p-6 md:p-12",children:(0,s.jsxs)("div",{className:"max-w-3xl mx-auto text-center",children:[s.jsx("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:"Store Not Found"}),s.jsx("p",{className:"mb-6",children:"The store you're looking for doesn't exist or you don't have permission to add products."}),s.jsx(o.Z,{onClick:()=>t.push("/stores"),children:"Back to Stores"})]})}):s.jsx("div",{className:"min-h-screen p-6 md:p-12",children:(0,s.jsxs)("div",{className:"max-w-3xl mx-auto",children:[(0,s.jsxs)("h1",{className:"text-3xl font-bold mb-8",children:["Add New Product to ",i.displayName||i.username]}),s.jsx(n.Z,{storeId:i.id,storeUsername:i.username})]})})}},89244:(e,r,t)=>{"use strict";t.r(r),t.d(r,{$$typeof:()=>o,__esModule:()=>n,default:()=>i});var s=t(95153);let a=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\stores\[username]\products\create\page.tsx`),{__esModule:n,$$typeof:o}=a,c=a.default,i=c}};var r=require("../../../../../webpack-runtime.js");r.C(e);var __webpack_exec__=e=>r(r.s=e),t=r.X(0,[2103,2765,706,8042,7783,1245],()=>__webpack_exec__(74541));module.exports=t})();