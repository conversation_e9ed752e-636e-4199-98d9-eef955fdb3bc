"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var RabbitMQModule_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RabbitMQModule = void 0;
const common_1 = require("@nestjs/common");
const rabbitmq_service_1 = require("./rabbitmq.service");
let RabbitMQModule = RabbitMQModule_1 = class RabbitMQModule {
    static register(options) {
        const providers = [
            {
                provide: rabbitmq_service_1.RabbitMQService,
                useFactory: () => {
                    return new rabbitmq_service_1.RabbitMQService(options === null || options === void 0 ? void 0 : options.url);
                },
            },
        ];
        return {
            module: RabbitMQModule_1,
            providers,
            exports: [rabbitmq_service_1.RabbitMQService],
        };
    }
    static registerAsync(options) {
        const providers = [
            {
                provide: rabbitmq_service_1.RabbitMQService,
                useFactory: async (...args) => {
                    const config = await options.useFactory(...args);
                    return new rabbitmq_service_1.RabbitMQService(config.url);
                },
                inject: options.inject || [],
            },
        ];
        return {
            module: RabbitMQModule_1,
            providers,
            exports: [rabbitmq_service_1.RabbitMQService],
        };
    }
};
exports.RabbitMQModule = RabbitMQModule;
exports.RabbitMQModule = RabbitMQModule = RabbitMQModule_1 = __decorate([
    (0, common_1.Module)({})
], RabbitMQModule);
//# sourceMappingURL=rabbitmq.module.js.map