# Build stage
FROM node:18-alpine AS build

WORKDIR /app

# Copy package files
COPY services/notification-service/package*.json ./

# Copy shared libraries (from project root context)
COPY libs/common ./libs/common
COPY libs/messaging ./libs/messaging

# Install all dependencies (including dev dependencies for build)
RUN npm ci --legacy-peer-deps && npm cache clean --force

# Copy source code
COPY services/notification-service/src ./src
COPY services/notification-service/tsconfig*.json ./
COPY services/notification-service/nest-cli.json ./

# Build the application
RUN npm run build

# Production stage
FROM node:18-alpine

WORKDIR /app

# Copy package files and install production dependencies
COPY services/notification-service/package*.json ./
RUN npm ci --only=production --legacy-peer-deps && npm cache clean --force

# Copy built application from build stage
COPY --from=build /app/dist ./dist
COPY --from=build /app/libs ./libs

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001

# Change ownership of the app directory
RUN chown -R nestjs:nodejs /app
USER nestjs

# Expose port
EXPOSE 3007

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node dist/main.js --health-check || exit 1

# Start the application
CMD ["node", "dist/main.js"]
