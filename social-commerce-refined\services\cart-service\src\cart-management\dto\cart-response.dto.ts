import { ApiProperty } from '@nestjs/swagger';
import { CartStatus } from '../entities/cart.entity';

export class CartItemResponseDto {
  @ApiProperty({ description: 'Cart item ID' })
  id: string;

  @ApiProperty({ description: 'Cart ID' })
  cartId: string;

  @ApiProperty({ description: 'Product ID' })
  productId: string;

  @ApiProperty({ description: 'Product variant ID', required: false })
  variantId?: string;

  @ApiProperty({ description: 'Quantity of the item' })
  quantity: number;

  @ApiProperty({ description: 'Price per unit' })
  price: number;

  @ApiProperty({ description: 'Discount amount' })
  discount: number;

  @ApiProperty({ description: 'Total price for this item' })
  total: number;

  @ApiProperty({ description: 'Product name' })
  productName: string;

  @ApiProperty({ description: 'Product image URL', required: false })
  productImage?: string;

  @ApiProperty({ description: 'Store ID' })
  storeId: string;

  @ApiProperty({ description: 'Store name' })
  storeName: string;

  @ApiProperty({ description: 'Selected options', required: false })
  selectedOptions?: Record<string, any>;

  @ApiProperty({ description: 'Additional metadata', required: false })
  metadata?: Record<string, any>;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  updatedAt: Date;
}

export class CartResponseDto {
  @ApiProperty({ description: 'Cart ID' })
  id: string;

  @ApiProperty({ description: 'User ID', required: false })
  userId?: string;

  @ApiProperty({ description: 'Session ID', required: false })
  sessionId?: string;

  @ApiProperty({ description: 'Whether this is a guest cart' })
  isGuestCart: boolean;

  @ApiProperty({ description: 'Cart status', enum: CartStatus })
  status: CartStatus;

  @ApiProperty({ description: 'Cart items', type: [CartItemResponseDto] })
  items: CartItemResponseDto[];

  @ApiProperty({ description: 'Number of items in cart' })
  itemCount: number;

  @ApiProperty({ description: 'Subtotal amount' })
  subtotal: number;

  @ApiProperty({ description: 'Tax amount' })
  tax: number;

  @ApiProperty({ description: 'Shipping amount' })
  shipping: number;

  @ApiProperty({ description: 'Discount amount' })
  discount: number;

  @ApiProperty({ description: 'Total amount' })
  total: number;

  @ApiProperty({ description: 'Applied coupon code', required: false })
  couponCode?: string;

  @ApiProperty({ description: 'Shipping method ID', required: false })
  shippingMethodId?: string;

  @ApiProperty({ description: 'Shipping address', required: false })
  shippingAddress?: Record<string, any>;

  @ApiProperty({ description: 'Billing address', required: false })
  billingAddress?: Record<string, any>;

  @ApiProperty({ description: 'Additional metadata', required: false })
  metadata?: Record<string, any>;

  @ApiProperty({ description: 'Last activity timestamp', required: false })
  lastActivity?: Date;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  updatedAt: Date;

  @ApiProperty({ description: 'Whether the cart is empty' })
  isEmpty: boolean;
}
