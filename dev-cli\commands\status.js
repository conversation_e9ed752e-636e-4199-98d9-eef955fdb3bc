const chalk = require('chalk');
const spawn = require('cross-spawn');
const ora = require('ora');
const path = require('path');
const fs = require('fs');
const http = require('http');

/**
 * Status command implementation
 * @param {Object} program - Commander program instance
 */
module.exports = (program) => {
  program
    .command('status')
    .description('Check the status of services')
    .action(() => {
      const spinner = ora('Checking service status...').start();
      
      try {
        const rootDir = path.resolve(__dirname, '../../');
        
        // Check Docker Compose services
        spinner.text = 'Checking Docker Compose services...';
        
        const result = spawn.sync('docker-compose', ['ps'], {
          cwd: rootDir,
          stdio: ['ignore', 'pipe', 'pipe']
        });
        
        if (result.status === 0) {
          spinner.succeed(chalk.green('Docker Compose services status:'));
          console.log(result.stdout.toString());
        } else {
          spinner.warn(chalk.yellow('Failed to get Docker Compose services status'));
          console.error(chalk.yellow('Error: ' + result.stderr.toString()));
        }
        
        // Check service health by making HTTP requests
        checkServiceHealth('Main Gateway', 'http://localhost:3001', '/');
        checkServiceHealth('User Service', 'http://localhost:3002', '/');
        checkServiceHealth('Frontend', 'http://localhost:3000', '/');
        
      } catch (error) {
        spinner.fail(chalk.red('Error checking service status'));
        console.error(chalk.red(error.message));
      }
    });
};

/**
 * Check if a service is healthy by making an HTTP request
 * @param {string} serviceName - Name of the service
 * @param {string} baseUrl - Base URL of the service
 * @param {string} path - Path to check
 */
function checkServiceHealth(serviceName, baseUrl, path) {
  const spinner = ora(`Checking ${serviceName} health...`).start();
  
  const url = new URL(path, baseUrl);
  
  const req = http.get(url, (res) => {
    if (res.statusCode >= 200 && res.statusCode < 400) {
      spinner.succeed(chalk.green(`${serviceName} is running (${res.statusCode})`));
    } else {
      spinner.warn(chalk.yellow(`${serviceName} returned status code ${res.statusCode}`));
    }
  });
  
  req.on('error', (error) => {
    spinner.fail(chalk.red(`${serviceName} is not responding: ${error.message}`));
  });
  
  req.setTimeout(3000, () => {
    req.abort();
    spinner.fail(chalk.red(`${serviceName} request timed out`));
  });
}
