# Contributing to Social Commerce Platform

Thank you for your interest in contributing to the Social Commerce Platform! This document outlines the development workflow, coding standards, and best practices that all contributors (human developers and AI assistants) must follow.

## Table of Contents

- [Implementation-First Approach](#implementation-first-approach)
- [Development Workflow](#development-workflow)
- [Using the Development CLI](#using-the-development-cli)
- [Git Workflow](#git-workflow)
- [Code Style and Standards](#code-style-and-standards)
- [Testing Requirements](#testing-requirements)
- [Documentation Requirements](#documentation-requirements)
- [AI Assistant Guidelines](#ai-assistant-guidelines)
- [Review Process](#review-process)

## Implementation-First Approach

**CRITICAL**: All contributors must follow the implementation-first approach based on lessons learned from our development process.

### Core Principles

1. **Build Working Code First, Then Extract Guidelines**
   - Focus on creating functional, working services before extensive documentation
   - Use working implementations as the source of truth for patterns and architecture
   - Extract guidelines and documentation from proven, working code
   - **DO NOT** spend excessive time on meta-documentation before implementation

2. **Template-Based Development**
   - Use existing working services (like User Service) as templates for new services
   - Copy and modify proven patterns rather than starting from scratch
   - This approach is 10x faster than building from architectural documents alone
   - Maintain consistency by following established patterns

3. **Break Down Tasks into Micro-Steps**
   - Divide complex tasks into extremely small, manageable steps (max 200 lines of code changes)
   - Add verification points after logical groups of steps
   - Execute methodically, one step at a time
   - **AVOID** excessive nesting and complexity in task breakdown

4. **Early Integration Testing**
   - Test integration between services early to validate architecture decisions
   - Validate end-to-end workflows before building all services
   - Use the API Gateway as the foundation for service integration
   - Identify integration issues when they're easier and cheaper to fix

### Strategic Decision Making

When making implementation decisions:

1. **Present Options with Pros and Cons**
   - Always provide multiple approaches when applicable
   - Clearly outline advantages and disadvantages of each option
   - Make data-driven recommendations based on business value and time-to-market

2. **Focus on Business Value**
   - Prioritize features that deliver immediate business value
   - Consider time-to-market in all decisions
   - Balance technical debt with delivery speed

### Environment Consistency Requirements

Based on lessons learned from service startup issues:

1. **Use Docker for Consistent Environments**
   - All services must have Docker configurations
   - Use Docker Compose for development environments
   - Ensure consistent behavior across all developer machines

2. **Implement Comprehensive Health Checks**
   - All services must have health check endpoints
   - Health checks should verify database connections and dependencies
   - Provide clear error messages when services fail

3. **Standardize Scripts and Documentation**
   - Use consistent naming conventions for scripts
   - Document all setup and deployment procedures
   - Automate dependency management where possible

### Avoiding Common Pitfalls

**DO NOT**:
- Create excessive nesting and complexity in code or documentation
- Focus on documentation over working implementation
- Allow scope creep in guidelines - keep them focused and practical
- Build extensive meta-documentation before proving concepts with working code

**DO**:
- Build one complete, working service that demonstrates all patterns
- Use that service as a template for others
- Test integration early and often
- Extract guidelines from what actually works

## Development Workflow

Our development workflow is designed to ensure consistency, quality, and efficiency. All contributors must follow this workflow without exception.

### Key Principles

1. **Use the provided tools**: Always use the development CLI tool for common tasks
2. **Follow the microservices architecture**: Respect service boundaries and communication patterns
3. **Test thoroughly**: Write tests for all new features and bug fixes
4. **Document your work**: Update documentation to reflect your changes
5. **Maintain consistency**: Follow established patterns and conventions

### Required Workflow Steps

1. **Setup**: Use Docker Compose for local development environment
2. **Development**: Use the CLI tool for starting, stopping, and managing services
3. **Testing**: Run tests before submitting changes
4. **Documentation**: Update relevant documentation
5. **Review**: Submit changes for review following the Git workflow

## Using the Development CLI

The Development CLI tool (`dev-cli`) is the primary interface for development tasks. **Always use this tool instead of direct npm/Docker commands.**

### Installation

```bash
cd tools/dev-cli
npm install
npm link
```

### Common Commands

```bash
# Start all services
dev start

# Start a specific service
dev start [service]  # e.g., dev start user

# Stop services
dev stop [service]

# Check service status
dev status

# Install dependencies
dev install [service]

# Build services
dev build [service]
```

### When to Use Each Command

| Task | Command | Notes |
|------|---------|-------|
| Starting development | `dev start` | Always check status after starting |
| Checking if services are running | `dev status` | Run before troubleshooting |
| Installing new dependencies | `dev install` | Always commit package.json changes |
| Building for testing | `dev build` | Required before running tests |

## Git Workflow

1. **Create a feature branch**:
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make small, focused commits**:
   ```bash
   git add .
   git commit -m "feat: add specific feature"
   ```

   Use conventional commit messages:
   - `feat:` for new features
   - `fix:` for bug fixes
   - `docs:` for documentation changes
   - `test:` for test additions or changes
   - `refactor:` for code refactoring
   - `style:` for formatting changes
   - `chore:` for maintenance tasks

3. **Push to the repository**:
   ```bash
   git push origin feature/your-feature-name
   ```

4. **Create a Pull Request** with a clear description of changes

5. **Address review feedback** and update your PR

6. **Merge** once approved

## Code Style and Standards

- Follow the ESLint and Prettier configurations
- Use TypeScript for type safety
- Follow the established architecture patterns
- Use dependency injection where appropriate
- Keep functions small and focused
- Write meaningful comments and documentation

## Testing Requirements

- Write unit tests for all new functionality
- Ensure existing tests pass
- Aim for at least 80% test coverage for new code
- Include integration tests for critical paths
- Test error handling and edge cases

## Documentation Requirements

- Update README.md for significant changes
- Document new API endpoints
- Update SCRIPTS.md when adding new scripts
- Add JSDoc comments to functions and classes
- Update Swagger documentation for API changes

## AI Assistant Guidelines

AI assistants (including Augment AI Code Assistant) must strictly adhere to the guidelines in the [AI Guidelines](./docs/guidelines/AI-README.md) document. These guidelines ensure that AI-generated code adheres to our project standards and best practices.

## Review Process

All contributions will be reviewed for:

1. Functionality: Does it work as expected?
2. Quality: Is the code well-written and maintainable?
3. Tests: Are there sufficient tests?
4. Documentation: Is it well-documented?
5. Workflow Compliance: Does it follow our workflow?

Contributions that don't follow the established workflow will be returned for revision.

## Questions and Support

If you have questions about the development workflow, please:

1. Check the documentation (README.md, CONTRIBUTING.md)
2. Use the `dev help` command for CLI tool guidance
3. Reach out to the team for clarification

Thank you for following these guidelines and contributing to the Social Commerce Platform!
