{"version": 3, "file": "npm.js", "sourceRoot": "", "sources": ["../../src/utils/npm.ts"], "names": [], "mappings": ";;AAWA,wBAyBC;AAOD,kDAEC;AAOD,oCAEC;AAOD,oCAEC;AAQD,kCAGC;AA1ED,8CAA8C;AAC9C,+BAA+B;AAC/B,mCAAwC;AASxC,SAAgB,MAAM,CAAC,OAAe,EAAE,OAAe,EAAE,OAAiB,EAAE;IAC1E,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;;QACrC,MAAM,UAAU,GAAG,IAAA,qBAAa,EAAC,OAAO,CAAC,CAAC;QAC1C,MAAM,UAAU,GAAG,OAAO,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;QAEtD,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,OAAO,aAAa,UAAU,EAAE,CAAC,CAAC,CAAC;QAExE,MAAM,OAAO,GAAG,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC,CAAC;QAEnE,MAAA,OAAO,CAAC,MAAM,0CAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;YAClC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,MAAA,OAAO,CAAC,MAAM,0CAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;YAClC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE;YAC3B,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;gBACf,OAAO,EAAE,CAAC;YACZ,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,IAAI,KAAK,CAAC,gCAAgC,IAAI,EAAE,CAAC,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAOD,SAAgB,mBAAmB,CAAC,OAAe;IACjD,OAAO,MAAM,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;AACpC,CAAC;AAOD,SAAgB,YAAY,CAAC,OAAe;IAC1C,OAAO,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;AAC3C,CAAC;AAOD,SAAgB,YAAY,CAAC,OAAe;IAC1C,OAAO,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;AAC/C,CAAC;AAQD,SAAgB,WAAW,CAAC,OAAe,EAAE,QAAiB,KAAK;IACjE,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,MAAM,CAAC;IAClD,OAAO,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAClC,CAAC"}