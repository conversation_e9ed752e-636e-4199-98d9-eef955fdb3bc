import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';

// Create axios instance
const api = axios.create({
  baseURL: 'http://localhost:3001', // Direct connection to backend
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for adding the auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling errors
api.interceptors.response.use(
  (response) => response,
  (error: AxiosError) => {
    // Handle 401 Unauthorized errors (token expired)
    if (error.response?.status === 401) {
      // Clear token and redirect to login
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Generic request function
const request = async <T>(config: AxiosRequestConfig): Promise<T> => {
  try {
    const response: AxiosResponse<T> = await api(config);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      // Handle specific error cases
      const errorMessage = error.response?.data?.message || error.message;
      console.error(`API Error: ${errorMessage}`);
      throw new Error(errorMessage);
    }
    throw error;
  }
};

// API endpoints
export const authApi = {
  login: (email: string, password: string) =>
    request({
      url: '/api/auth/login',
      method: 'POST',
      data: { email, password },
    }),

  register: (userData: any) =>
    request({
      url: '/api/auth/register',
      method: 'POST',
      data: userData,
    }),

  verifyEmail: (token: string) =>
    request({
      url: `/api/auth/verify-email/${token}`,
      method: 'GET',
    }),

  forgotPassword: (email: string) =>
    request({
      url: '/api/auth/forgot-password',
      method: 'POST',
      data: { email },
    }),

  resetPassword: (token: string, password: string) =>
    request({
      url: '/api/auth/reset-password',
      method: 'POST',
      data: { token, password },
    }),
};

export const userApi = {
  getProfile: () =>
    request({
      url: '/users/profile',
      method: 'GET',
    }),

  updateProfile: (profileData: any) =>
    request({
      url: '/users/profile',
      method: 'PUT',
      data: profileData,
    }),
};

export const storeApi = {
  getAllStores: () =>
    request({
      url: '/stores',
      method: 'GET',
    }),

  getStoreById: (id: string) =>
    request({
      url: `/stores/${id}`,
      method: 'GET',
    }),

  createStore: (storeData: any) =>
    request({
      url: '/stores',
      method: 'POST',
      data: storeData,
    }),

  updateStore: (id: string, storeData: any) =>
    request({
      url: `/stores/${id}`,
      method: 'PUT',
      data: storeData,
    }),

  deleteStore: (id: string) =>
    request({
      url: `/stores/${id}`,
      method: 'DELETE',
    }),
};

export const productApi = {
  getProductsByStore: (storeId: string) =>
    request({
      url: `/stores/${storeId}/products`,
      method: 'GET',
    }),

  getProductById: (storeId: string, productId: string) =>
    request({
      url: `/stores/${storeId}/products/${productId}`,
      method: 'GET',
    }),

  createProduct: (storeId: string, productData: any) =>
    request({
      url: `/stores/${storeId}/products`,
      method: 'POST',
      data: productData,
    }),

  updateProduct: (storeId: string, productId: string, productData: any) =>
    request({
      url: `/stores/${storeId}/products/${productId}`,
      method: 'PUT',
      data: productData,
    }),

  deleteProduct: (storeId: string, productId: string) =>
    request({
      url: `/stores/${storeId}/products/${productId}`,
      method: 'DELETE',
    }),
};

export default api;
