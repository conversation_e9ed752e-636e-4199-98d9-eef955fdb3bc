import { Module, DynamicModule, Provider } from '@nestjs/common';
import { RabbitMQService } from './rabbitmq.service';
import { RabbitMQOptions } from '../interfaces/rabbitmq-options.interface';

@Module({})
export class RabbitMQModule {
  static register(options?: RabbitMQOptions): DynamicModule {
    const providers: Provider[] = [
      {
        provide: RabbitMQService,
        useFactory: () => {
          return new RabbitMQService(options?.url);
        },
      },
    ];

    return {
      module: RabbitMQModule,
      providers,
      exports: [RabbitMQService],
    };
  }

  static registerAsync(options: {
    useFactory: (...args: any[]) => Promise<RabbitMQOptions> | RabbitMQOptions;
    inject?: any[];
  }): DynamicModule {
    const providers: Provider[] = [
      {
        provide: RabbitMQService,
        useFactory: async (...args: any[]) => {
          const config = await options.useFactory(...args);
          return new RabbitMQService(config.url);
        },
        inject: options.inject || [],
      },
    ];

    return {
      module: RabbitMQModule,
      providers,
      exports: [RabbitMQService],
    };
  }
}
