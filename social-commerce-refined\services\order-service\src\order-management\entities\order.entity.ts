import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  Index,
} from 'typeorm';
import { OrderItem } from './order-item.entity';

export enum OrderStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  PROCESSING = 'processing',
  SHIPPED = 'shipped',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
}

export enum PaymentStatus {
  PENDING = 'pending',
  PAID = 'paid',
  FAILED = 'failed',
  REFUNDED = 'refunded',
  PARTIALLY_REFUNDED = 'partially_refunded',
}

@Entity('orders')
@Index(['userId'])
@Index(['storeId'])
@Index(['status'])
@Index(['orderNumber'])
@Index(['createdAt'])
export class Order {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  orderNumber: string;

  @Column('uuid')
  userId: string;

  @Column('uuid')
  storeId: string;

  @Column('uuid', { nullable: true })
  cartId: string;

  @Column({
    type: 'enum',
    enum: OrderStatus,
    default: OrderStatus.PENDING,
  })
  status: OrderStatus;

  @Column({
    type: 'enum',
    enum: PaymentStatus,
    default: PaymentStatus.PENDING,
  })
  paymentStatus: PaymentStatus;

  @OneToMany(() => OrderItem, (orderItem) => orderItem.order, {
    cascade: true,
    eager: true,
  })
  items: OrderItem[];

  // Financial Information
  @Column('decimal', { precision: 10, scale: 2 })
  subtotal: number;

  @Column('decimal', { precision: 10, scale: 2, default: 0 })
  tax: number;

  @Column('decimal', { precision: 10, scale: 2, default: 0 })
  shipping: number;

  @Column('decimal', { precision: 10, scale: 2, default: 0 })
  discount: number;

  @Column('decimal', { precision: 10, scale: 2 })
  total: number;

  // Shipping Information
  @Column('jsonb', { nullable: true })
  shippingAddress: {
    firstName: string;
    lastName: string;
    address1: string;
    address2?: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
    phone?: string;
  };

  @Column('jsonb', { nullable: true })
  billingAddress: {
    firstName: string;
    lastName: string;
    address1: string;
    address2?: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };

  // Payment Information
  @Column({ nullable: true })
  paymentMethodId: string;

  @Column({ nullable: true })
  paymentTransactionId: string;

  @Column('jsonb', { nullable: true })
  paymentDetails: Record<string, any>;

  // Coupon and Discount Information
  @Column({ nullable: true })
  couponCode: string;

  @Column('decimal', { precision: 5, scale: 2, nullable: true })
  couponDiscountPercent: number;

  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  couponDiscountAmount: number;

  // Tracking Information
  @Column({ nullable: true })
  trackingNumber: string;

  @Column({ nullable: true })
  shippingCarrier: string;

  @Column({ type: 'timestamp', nullable: true })
  shippedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  deliveredAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  cancelledAt: Date;

  // Additional Information
  @Column('text', { nullable: true })
  notes: string;

  @Column('jsonb', { nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn({ type: 'timestamp with time zone' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone' })
  updatedAt: Date;

  // Computed properties
  get itemCount(): number {
    return this.items ? this.items.reduce((sum, item) => sum + item.quantity, 0) : 0;
  }

  get isCompleted(): boolean {
    return this.status === OrderStatus.DELIVERED;
  }

  get isCancellable(): boolean {
    return [OrderStatus.PENDING, OrderStatus.CONFIRMED].includes(this.status);
  }

  get isRefundable(): boolean {
    return this.status === OrderStatus.DELIVERED && this.paymentStatus === PaymentStatus.PAID;
  }

  // Helper methods
  updateStatus(newStatus: OrderStatus): void {
    this.status = newStatus;
    
    if (newStatus === OrderStatus.SHIPPED && !this.shippedAt) {
      this.shippedAt = new Date();
    }
    
    if (newStatus === OrderStatus.DELIVERED && !this.deliveredAt) {
      this.deliveredAt = new Date();
    }
    
    if (newStatus === OrderStatus.CANCELLED && !this.cancelledAt) {
      this.cancelledAt = new Date();
    }
  }

  calculateTotals(): void {
    if (!this.items || this.items.length === 0) {
      this.subtotal = 0;
      this.total = 0;
      return;
    }

    this.subtotal = this.items.reduce((sum, item) => sum + item.total, 0);
    
    // Initialize values if they are null/undefined
    this.tax = this.tax || 0;
    this.shipping = this.shipping || 0;
    this.discount = this.discount || 0;
    
    this.total = this.subtotal + this.tax + this.shipping - this.discount;
  }

  generateOrderNumber(): string {
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `ORD-${timestamp}-${random}`;
  }
}
