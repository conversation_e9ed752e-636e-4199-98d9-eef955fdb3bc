"use strict";exports.id=3838,exports.ids=[3838],exports.modules={53838:(e,t,i)=>{i.d(t,{Bh:()=>n,XX:()=>y,k_:()=>r});var s=i(86372);let a=s.g.injectEndpoints({endpoints:e=>({getUserActivityFeed:e.query({query:({userId:e,page:t=1,limit:i=10,types:s})=>{let a=`/users/${e}/activities?page=${t}&limit=${i}`;return s&&s.length>0&&(a+=`&types=${s.join(",")}`),a},providesTags:(e,t,{userId:i})=>[{type:"UserActivity",id:i},"UserActivity"]}),getCurrentUserActivityFeed:e.query({query:({page:e=1,limit:t=10,types:i})=>{let s=`/user/activities?page=${e}&limit=${t}`;return i&&i.length>0&&(s+=`&types=${i.join(",")}`),s},providesTags:["UserActivity"]}),getHomeFeed:e.query({query:({page:e=1,limit:t=10,types:i})=>{let s=`/feed?page=${e}&limit=${t}`;return i&&i.length>0&&(s+=`&types=${i.join(",")}`),s},providesTags:["Feed"]}),createActivity:e.mutation({query:e=>({url:"/user/activities",method:"POST",body:e}),invalidatesTags:["UserActivity","Feed"]}),deleteActivity:e.mutation({query:e=>({url:`/user/activities/${e}`,method:"DELETE"}),invalidatesTags:["UserActivity","Feed"]}),updateActivityVisibility:e.mutation({query:({activityId:e,visibility:t})=>({url:`/user/activities/${e}/visibility`,method:"PATCH",body:{visibility:t}}),invalidatesTags:(e,t,{activityId:i})=>[{type:"UserActivity",id:i},"UserActivity","Feed"]}),getComments:e.query({query:({entityId:e,entityType:t,parentId:i,page:s=1,limit:a=10})=>{let n=`/comments?entityId=${e}&entityType=${t}&page=${s}&limit=${a}`;return i&&(n+=`&parentId=${i}`),n},providesTags:(e,t,{entityId:i,entityType:s,parentId:a})=>[{type:"Comments",id:`${s}-${i}${a?`-${a}`:""}`},"Comments"]}),createComment:e.mutation({query:e=>({url:"/comments",method:"POST",body:e}),invalidatesTags:(e,t,{entityId:i,entityType:s,parentId:a})=>[{type:"Comments",id:`${s}-${i}${a?`-${a}`:""}`},"Comments"]}),updateComment:e.mutation({query:({commentId:e,...t})=>({url:`/comments/${e}`,method:"PATCH",body:t}),invalidatesTags:e=>[{type:"Comments",id:`${e?.entityType}-${e?.entityId}${e?.parentId?`-${e.parentId}`:""}`},"Comments"]}),deleteComment:e.mutation({query:e=>({url:`/comments/${e}`,method:"DELETE"}),invalidatesTags:["Comments"]}),getReactions:e.query({query:({entityId:e,entityType:t,page:i=1,limit:s=10})=>`/reactions?entityId=${e}&entityType=${t}&page=${i}&limit=${s}`,providesTags:(e,t,{entityId:i,entityType:s})=>[{type:"Reactions",id:`${s}-${i}`},"Reactions"]}),createReaction:e.mutation({query:e=>({url:"/reactions",method:"POST",body:e}),invalidatesTags:(e,t,{entityId:i,entityType:s})=>[{type:"Reactions",id:`${s}-${i}`},"Reactions"]}),deleteReaction:e.mutation({query:({entityId:e,entityType:t})=>({url:"/reactions",method:"DELETE",body:{entityId:e,entityType:t}}),invalidatesTags:(e,t,{entityId:i,entityType:s})=>[{type:"Reactions",id:`${s}-${i}`},"Reactions"]}),getNotifications:e.query({query:({page:e=1,limit:t=10,unreadOnly:i})=>{let s=`/notifications?page=${e}&limit=${t}`;return i&&(s+="&unreadOnly=true"),s},providesTags:["Notifications"]}),markNotificationAsRead:e.mutation({query:e=>({url:`/notifications/${e}/read`,method:"POST"}),invalidatesTags:["Notifications"]}),markAllNotificationsAsRead:e.mutation({query:()=>({url:"/notifications/read-all",method:"POST"}),invalidatesTags:["Notifications"]})})}),{useGetUserActivityFeedQuery:n,useGetCurrentUserActivityFeedQuery:o,useGetHomeFeedQuery:r,useCreateActivityMutation:d,useDeleteActivityMutation:y,useUpdateActivityVisibilityMutation:m,useGetCommentsQuery:u,useCreateCommentMutation:c,useUpdateCommentMutation:l,useDeleteCommentMutation:$,useGetReactionsQuery:p,useCreateReactionMutation:v,useDeleteReactionMutation:g,useGetNotificationsQuery:T,useMarkNotificationAsReadMutation:q,useMarkAllNotificationsAsReadMutation:A}=a}};