/**
 * Base interface for all events in the system
 */
export interface BaseEvent<T> {
  /**
   * Unique identifier for the event
   */
  id: string;

  /**
   * Type of the event (e.g., 'user.created', 'order.placed')
   */
  type: string;

  /**
   * Version of the event schema
   */
  version: string;

  /**
   * Timestamp when the event was created
   */
  timestamp: string;

  /**
   * Service that produced the event
   */
  producer: string;

  /**
   * Event data
   */
  payload: T;
}
