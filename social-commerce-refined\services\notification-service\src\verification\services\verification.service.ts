import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { VerificationRepository } from '../repositories/verification.repository';
import { UserRepository } from '../../authentication/repositories/user.repository';
import { VerificationToken, TokenType } from '../entities/verification-token.entity';
// import { ClientProxy } from '@nestjs/microservices'; // Temporarily disabled
import { ConfigService } from '@nestjs/config';

@Injectable()
export class VerificationService {
  private readonly logger = new Logger(VerificationService.name);

  constructor(
    private readonly verificationRepository: VerificationRepository,
    private readonly userRepository: UserRepository,
    private readonly configService: ConfigService,
    // @Inject('NOTIFICATION_SERVICE') private readonly notificationClient: ClientProxy, // Temporarily disabled
  ) {}

  async createEmailVerificationToken(userId: string): Promise<VerificationToken> {
    this.logger.log(`Creating email verification token for user with ID: ${userId}`);

    // Check if user exists
    const user = await this.userRepository.findOne(userId);

    // Invalidate existing tokens
    await this.verificationRepository.invalidateTokensByUserAndType(userId, TokenType.EMAIL_VERIFICATION);

    // Create new token
    const token = await this.verificationRepository.createToken(userId, TokenType.EMAIL_VERIFICATION);

    // Send verification email
    this.sendVerificationEmail(user.email, token.token);

    return token;
  }

  async createPasswordResetToken(email: string): Promise<VerificationToken> {
    this.logger.log(`Creating password reset token for user with email: ${email}`);

    // Check if user exists
    const user = await this.userRepository.findByEmail(email);

    // Invalidate existing tokens
    await this.verificationRepository.invalidateTokensByUserAndType(user.id, TokenType.PASSWORD_RESET);

    // Create new token
    const token = await this.verificationRepository.createToken(user.id, TokenType.PASSWORD_RESET);

    // Send password reset email
    this.sendPasswordResetEmail(user.email, token.token);

    return token;
  }

  async createPhoneVerificationToken(userId: string): Promise<VerificationToken> {
    this.logger.log(`Creating phone verification token for user with ID: ${userId}`);

    // Check if user exists
    const user = await this.userRepository.findOne(userId);

    if (!user.phone) {
      throw new BadRequestException('User does not have a phone number');
    }

    // Invalidate existing tokens
    await this.verificationRepository.invalidateTokensByUserAndType(userId, TokenType.PHONE_VERIFICATION);

    // Create new token (shorter expiration for SMS)
    const token = await this.verificationRepository.createToken(userId, TokenType.PHONE_VERIFICATION, 1);

    // Send verification SMS
    this.sendVerificationSMS(user.phone, token.token);

    return token;
  }

  async verifyEmail(token: string): Promise<boolean> {
    this.logger.log(`Verifying email with token: ${token}`);

    try {
      // Find token
      const verificationToken = await this.verificationRepository.findByToken(token);

      // Check token type
      if (verificationToken.type !== TokenType.EMAIL_VERIFICATION) {
        throw new BadRequestException('Invalid token type');
      }

      // Check if token is valid
      if (!verificationToken.isValid()) {
        throw new BadRequestException('Token is expired or already used');
      }

      // Mark token as used
      await this.verificationRepository.markTokenAsUsed(token);

      // Update user
      await this.userRepository.verifyEmail(verificationToken.userId);

      return true;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new BadRequestException('Invalid verification token');
      }
      throw error;
    }
  }

  async verifyPhone(userId: string, token: string): Promise<boolean> {
    this.logger.log(`Verifying phone for user with ID: ${userId} and token: ${token}`);

    try {
      // Find token
      const verificationToken = await this.verificationRepository.findByToken(token);

      // Check token type
      if (verificationToken.type !== TokenType.PHONE_VERIFICATION) {
        throw new BadRequestException('Invalid token type');
      }

      // Check if token belongs to the user
      if (verificationToken.userId !== userId) {
        throw new BadRequestException('Token does not belong to the user');
      }

      // Check if token is valid
      if (!verificationToken.isValid()) {
        throw new BadRequestException('Token is expired or already used');
      }

      // Mark token as used
      await this.verificationRepository.markTokenAsUsed(token);

      // Update user
      await this.userRepository.verifyPhone(userId);

      return true;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new BadRequestException('Invalid verification token');
      }
      throw error;
    }
  }

  async resetPassword(token: string, newPassword: string): Promise<boolean> {
    this.logger.log(`Resetting password with token: ${token}`);

    try {
      // Find token
      const verificationToken = await this.verificationRepository.findByToken(token);

      // Check token type
      if (verificationToken.type !== TokenType.PASSWORD_RESET) {
        throw new BadRequestException('Invalid token type');
      }

      // Check if token is valid
      if (!verificationToken.isValid()) {
        throw new BadRequestException('Token is expired or already used');
      }

      // Mark token as used
      await this.verificationRepository.markTokenAsUsed(token);

      // Update user password
      await this.userRepository.update(verificationToken.userId, { password: newPassword });

      return true;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new BadRequestException('Invalid reset token');
      }
      throw error;
    }
  }

  private sendVerificationEmail(email: string, token: string): void {
    this.logger.log(`Sending verification email to: ${email}`);

    const verificationUrl = `${this.configService.get<string>('FRONTEND_URL', 'http://localhost:3000')}/verify-email?token=${token}`;

    // TODO: Implement email sending when notification service is available
    this.logger.log(`Verification URL: ${verificationUrl}`);

    // this.notificationClient.emit('send_email', {
    //   to: email,
    //   subject: 'Verify Your Email',
    //   template: 'email-verification',
    //   context: {
    //     verificationUrl,
    //   },
    // });
  }

  private sendPasswordResetEmail(email: string, token: string): void {
    this.logger.log(`Sending password reset email to: ${email}`);

    const resetUrl = `${this.configService.get<string>('FRONTEND_URL', 'http://localhost:3000')}/reset-password?token=${token}`;

    // TODO: Implement email sending when notification service is available
    this.logger.log(`Reset URL: ${resetUrl}`);

    // this.notificationClient.emit('send_email', {
    //   to: email,
    //   subject: 'Reset Your Password',
    //   template: 'password-reset',
    //   context: {
    //     resetUrl,
    //   },
    // });
  }

  private sendVerificationSMS(phone: string, token: string): void {
    this.logger.log(`Sending verification SMS to: ${phone}`);

    // TODO: Implement SMS sending when notification service is available
    this.logger.log(`Verification code: ${token}`);

    // this.notificationClient.emit('send_sms', {
    //   to: phone,
    //   message: `Your verification code is: ${token}`,
    // });
  }
}
