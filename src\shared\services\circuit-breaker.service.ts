import { Injectable, Logger } from '@nestjs/common';
import * as CircuitBreaker from 'opossum';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { AxiosRequestConfig, AxiosResponse } from 'axios';

@Injectable()
export class CircuitBreakerService {
  private readonly logger = new Logger(CircuitBreakerService.name);
  private readonly breakers: Map<string, CircuitBreaker> = new Map();

  constructor(private readonly httpService: HttpService) {}

  /**
   * Get or create a circuit breaker for a service
   * @param serviceName The name of the service
   * @returns A circuit breaker instance
   */
  private getBreaker(serviceName: string): CircuitBreaker {
    if (!this.breakers.has(serviceName)) {
      this.logger.log(`Creating circuit breaker for service: ${serviceName}`);
      
      const options = {
        timeout: 3000, // If our function takes longer than 3 seconds, trigger a failure
        errorThresholdPercentage: 50, // When 50% of requests fail, trip the circuit
        resetTimeout: 10000, // After 10 seconds, try again
        rollingCountTimeout: 60000, // Keep stats for 60 seconds
        rollingCountBuckets: 10, // Divide the rolling window into 10 buckets
      };
      
      const breaker = new CircuitBreaker(async (config: AxiosRequestConfig) => {
        return firstValueFrom(this.httpService.request(config));
      }, options);
      
      // Add listeners
      breaker.on('open', () => {
        this.logger.warn(`Circuit breaker for ${serviceName} is open (failing fast)`);
      });
      
      breaker.on('halfOpen', () => {
        this.logger.log(`Circuit breaker for ${serviceName} is half open (testing)`);
      });
      
      breaker.on('close', () => {
        this.logger.log(`Circuit breaker for ${serviceName} is closed (working normally)`);
      });
      
      breaker.on('fallback', (error) => {
        this.logger.error(`Circuit breaker for ${serviceName} fallback: ${error.message}`);
      });
      
      this.breakers.set(serviceName, breaker);
    }
    
    return this.breakers.get(serviceName);
  }

  /**
   * Make an HTTP request with circuit breaker protection
   * @param serviceName The name of the service
   * @param config The Axios request configuration
   * @returns The Axios response
   */
  async request(serviceName: string, config: AxiosRequestConfig): Promise<AxiosResponse> {
    const breaker = this.getBreaker(serviceName);
    
    try {
      return await breaker.fire(config);
    } catch (error) {
      this.logger.error(`Request to ${serviceName} failed: ${error.message}`);
      
      // Provide fallback response or rethrow
      if (breaker.status === 'open') {
        return this.getFallbackResponse(serviceName, config);
      }
      
      throw error;
    }
  }

  /**
   * Get a fallback response when the circuit is open
   * @param serviceName The name of the service
   * @param config The Axios request configuration
   * @returns A fallback response
   */
  private getFallbackResponse(serviceName: string, config: AxiosRequestConfig): AxiosResponse {
    this.logger.log(`Providing fallback response for ${serviceName}`);
    
    // Create a basic fallback response
    return {
      data: {
        error: 'Service Unavailable',
        message: `The ${serviceName} service is currently unavailable. Please try again later.`,
        status: 503,
      },
      status: 503,
      statusText: 'Service Unavailable',
      headers: {},
      config: config,
    } as AxiosResponse;
  }

  /**
   * Get the status of all circuit breakers
   * @returns The status of all circuit breakers
   */
  getStatus(): Record<string, any> {
    const status = {};
    
    for (const [serviceName, breaker] of this.breakers.entries()) {
      status[serviceName] = {
        state: breaker.status,
        stats: {
          successful: breaker.stats.successes,
          failed: breaker.stats.failures,
          rejected: breaker.stats.rejects,
          timeout: breaker.stats.timeouts,
          fallbacks: breaker.stats.fallbacks,
        },
      };
    }
    
    return status;
  }
}
