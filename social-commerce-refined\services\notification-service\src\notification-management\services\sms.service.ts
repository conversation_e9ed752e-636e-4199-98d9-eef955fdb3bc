import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SendSmsDto } from '../dto/send-sms.dto';

@Injectable()
export class SmsService {
  private readonly logger = new Logger(SmsService.name);

  constructor(private readonly configService: ConfigService) {}

  async sendSms(sendSmsDto: SendSmsDto): Promise<void> {
    this.logger.log(`Sending SMS to: ${sendSmsDto.to}`);

    try {
      // For now, we'll simulate SMS sending
      // In production, integrate with SMS service like Twilio, AWS SNS, etc.
      await this.simulateSmsSending(sendSmsDto);
      
      this.logger.log(`SMS sent successfully to: ${sendSmsDto.to}`);
    } catch (error) {
      this.logger.error(`Failed to send SMS to: ${sendSmsDto.to}`, error.stack);
      throw error;
    }
  }

  async sendVerificationSms(phoneNumber: string, verificationCode: string): Promise<void> {
    this.logger.log(`Sending verification SMS to: ${phoneNumber}`);

    const verificationSmsDto: SendSmsDto = {
      to: phoneNumber,
      message: `Your Social Commerce Platform verification code is: ${verificationCode}. This code expires in 15 minutes.`,
    };

    await this.sendSms(verificationSmsDto);
  }

  async sendWelcomeSms(phoneNumber: string, name: string): Promise<void> {
    this.logger.log(`Sending welcome SMS to: ${phoneNumber}`);

    const welcomeSmsDto: SendSmsDto = {
      to: phoneNumber,
      message: `Welcome ${name}! Thank you for joining Social Commerce Platform. Start exploring stores and products now!`,
    };

    await this.sendSms(welcomeSmsDto);
  }

  async sendOrderUpdateSms(phoneNumber: string, orderId: string, status: string): Promise<void> {
    this.logger.log(`Sending order update SMS to: ${phoneNumber}`);

    const orderUpdateSmsDto: SendSmsDto = {
      to: phoneNumber,
      message: `Order Update: Your order ${orderId} is now ${status}. Check the app for more details.`,
    };

    await this.sendSms(orderUpdateSmsDto);
  }

  async sendPasswordResetSms(phoneNumber: string, resetCode: string): Promise<void> {
    this.logger.log(`Sending password reset SMS to: ${phoneNumber}`);

    const resetSmsDto: SendSmsDto = {
      to: phoneNumber,
      message: `Your password reset code is: ${resetCode}. This code expires in 1 hour. If you didn't request this, ignore this message.`,
    };

    await this.sendSms(resetSmsDto);
  }

  async sendGroupBuyingNotificationSms(phoneNumber: string, productName: string, groupSize: number): Promise<void> {
    this.logger.log(`Sending group buying notification SMS to: ${phoneNumber}`);

    const groupBuyingSmsDto: SendSmsDto = {
      to: phoneNumber,
      message: `Group Buying Alert: ${productName} has reached ${groupSize} participants! Join now for better pricing.`,
    };

    await this.sendSms(groupBuyingSmsDto);
  }

  async sendPromotionalSms(phoneNumber: string, message: string): Promise<void> {
    this.logger.log(`Sending promotional SMS to: ${phoneNumber}`);

    const promotionalSmsDto: SendSmsDto = {
      to: phoneNumber,
      message: message,
    };

    await this.sendSms(promotionalSmsDto);
  }

  private async simulateSmsSending(sendSmsDto: SendSmsDto): Promise<void> {
    // Simulate SMS sending delay
    await new Promise(resolve => setTimeout(resolve, 50));

    // Log SMS details for development
    this.logger.debug('=== SMS SIMULATION ===');
    this.logger.debug(`To: ${sendSmsDto.to}`);
    this.logger.debug(`Message: ${sendSmsDto.message}`);
    this.logger.debug('======================');

    // Validate phone number format (basic validation)
    if (!this.isValidPhoneNumber(sendSmsDto.to)) {
      throw new Error('Invalid phone number format');
    }

    // Simulate occasional failures for testing
    const shouldFail = Math.random() < 0.03; // 3% failure rate
    if (shouldFail) {
      throw new Error('Simulated SMS service failure');
    }
  }

  private isValidPhoneNumber(phoneNumber: string): boolean {
    // Basic phone number validation (should be improved for production)
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    return phoneRegex.test(phoneNumber.replace(/\s+/g, ''));
  }

  // TODO: Implement real SMS service integration
  // private async sendWithTwilio(sendSmsDto: SendSmsDto): Promise<void> {
  //   // Implementation for Twilio
  // }
  
  // private async sendWithAWSSNS(sendSmsDto: SendSmsDto): Promise<void> {
  //   // Implementation for AWS SNS
  // }
}
