import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { OrderStatus, PaymentStatus } from '../entities/order.entity';

export class OrderItemResponseDto {
  @ApiProperty({ description: 'Order item ID' })
  id: string;

  @ApiProperty({ description: 'Product ID' })
  productId: string;

  @ApiPropertyOptional({ description: 'Product variant ID' })
  variantId?: string;

  @ApiProperty({ description: 'Quantity ordered' })
  quantity: number;

  @ApiProperty({ description: 'Unit price at time of order' })
  price: number;

  @ApiProperty({ description: 'Discount amount per unit' })
  discount: number;

  @ApiProperty({ description: 'Total price for this item' })
  total: number;

  @ApiProperty({ description: 'Product name' })
  productName: string;

  @ApiPropertyOptional({ description: 'Product description' })
  productDescription?: string;

  @ApiPropertyOptional({ description: 'Product image URL' })
  productImage?: string;

  @ApiProperty({ description: 'Store ID' })
  storeId: string;

  @ApiPropertyOptional({ description: 'Store name' })
  storeName?: string;

  @ApiPropertyOptional({ description: 'Selected product options' })
  selectedOptions?: Record<string, any>;

  @ApiProperty({ description: 'Item creation date' })
  createdAt: Date;
}

export class AddressResponseDto {
  @ApiProperty({ description: 'First name' })
  firstName: string;

  @ApiProperty({ description: 'Last name' })
  lastName: string;

  @ApiProperty({ description: 'Address line 1' })
  address1: string;

  @ApiPropertyOptional({ description: 'Address line 2' })
  address2?: string;

  @ApiProperty({ description: 'City' })
  city: string;

  @ApiProperty({ description: 'State/Province' })
  state: string;

  @ApiProperty({ description: 'ZIP/Postal code' })
  zipCode: string;

  @ApiProperty({ description: 'Country' })
  country: string;

  @ApiPropertyOptional({ description: 'Phone number' })
  phone?: string;
}

export class OrderResponseDto {
  @ApiProperty({ description: 'Order ID' })
  id: string;

  @ApiProperty({ description: 'Order number' })
  orderNumber: string;

  @ApiProperty({ description: 'User ID' })
  userId: string;

  @ApiProperty({ description: 'Store ID' })
  storeId: string;

  @ApiPropertyOptional({ description: 'Cart ID used to create order' })
  cartId?: string;

  @ApiProperty({ description: 'Order status', enum: OrderStatus })
  status: OrderStatus;

  @ApiProperty({ description: 'Payment status', enum: PaymentStatus })
  paymentStatus: PaymentStatus;

  @ApiProperty({ description: 'Order items', type: [OrderItemResponseDto] })
  items: OrderItemResponseDto[];

  @ApiProperty({ description: 'Subtotal amount' })
  subtotal: number;

  @ApiProperty({ description: 'Tax amount' })
  tax: number;

  @ApiProperty({ description: 'Shipping amount' })
  shipping: number;

  @ApiProperty({ description: 'Discount amount' })
  discount: number;

  @ApiProperty({ description: 'Total amount' })
  total: number;

  @ApiPropertyOptional({ description: 'Shipping address' })
  shippingAddress?: AddressResponseDto;

  @ApiPropertyOptional({ description: 'Billing address' })
  billingAddress?: AddressResponseDto;

  @ApiPropertyOptional({ description: 'Payment method ID' })
  paymentMethodId?: string;

  @ApiPropertyOptional({ description: 'Payment transaction ID' })
  paymentTransactionId?: string;

  @ApiPropertyOptional({ description: 'Coupon code used' })
  couponCode?: string;

  @ApiPropertyOptional({ description: 'Tracking number' })
  trackingNumber?: string;

  @ApiPropertyOptional({ description: 'Shipping carrier' })
  shippingCarrier?: string;

  @ApiPropertyOptional({ description: 'Shipped date' })
  shippedAt?: Date;

  @ApiPropertyOptional({ description: 'Delivered date' })
  deliveredAt?: Date;

  @ApiPropertyOptional({ description: 'Cancelled date' })
  cancelledAt?: Date;

  @ApiPropertyOptional({ description: 'Order notes' })
  notes?: string;

  @ApiProperty({ description: 'Order creation date' })
  createdAt: Date;

  @ApiProperty({ description: 'Order last update date' })
  updatedAt: Date;

  @ApiProperty({ description: 'Total number of items' })
  itemCount: number;

  @ApiProperty({ description: 'Whether order is completed' })
  isCompleted: boolean;

  @ApiProperty({ description: 'Whether order can be cancelled' })
  isCancellable: boolean;

  @ApiProperty({ description: 'Whether order can be refunded' })
  isRefundable: boolean;
}
