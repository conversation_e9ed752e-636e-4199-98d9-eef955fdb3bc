/**
 * Interface for cart data
 */
export interface ICart {
  /**
   * Cart ID
   */
  id: string;

  /**
   * User ID
   */
  userId: string;

  /**
   * Cart items
   */
  items: ICartItem[];

  /**
   * Total amount
   */
  totalAmount: number;

  /**
   * Cart creation timestamp
   */
  createdAt: Date | string;

  /**
   * Cart update timestamp
   */
  updatedAt: Date | string;
}

/**
 * Interface for cart item data
 */
export interface ICartItem {
  /**
   * Product ID
   */
  productId: string;

  /**
   * Product name
   */
  productName: string;

  /**
   * Quantity
   */
  quantity: number;

  /**
   * Price per unit
   */
  price: number;
}
