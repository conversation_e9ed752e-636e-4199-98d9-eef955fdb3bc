import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { AxiosRequestConfig, AxiosResponse } from 'axios';
import { Observable, catchError, map, throwError, from } from 'rxjs';
import { CircuitBreakerService } from '../../shared/services/circuit-breaker.service';

@Injectable()
export class RoutingService {
  private readonly logger = new Logger(RoutingService.name);

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly circuitBreakerService: CircuitBreakerService,
  ) {}

  /**
   * Forward a request to a microservice
   * @param serviceName The name of the microservice
   * @param path The path to forward to
   * @param method The HTTP method
   * @param data The request body
   * @param headers The request headers
   * @returns The response from the microservice
   */
  forwardRequest<T>(
    serviceName: string,
    path: string,
    method: string,
    data?: any,
    headers?: any,
  ): Observable<AxiosResponse<T>> {
    const serviceUrl = this.getServiceUrl(serviceName);
    const url = `${serviceUrl}${path}`;

    this.logger.log(`Forwarding ${method} request to ${url}`);

    // Ensure we forward the correlation ID
    const config: AxiosRequestConfig = {
      headers: {
        ...headers,
        // Ensure the correlation ID is forwarded to the microservice
        'x-correlation-id': headers['x-correlation-id'] || `api-gateway-${Date.now()}`,
      },
    };

    // Create a complete request config for the circuit breaker
    const requestConfig: AxiosRequestConfig = {
      url,
      method: method.toUpperCase(),
      headers: config.headers,
      data,
    };

    // Use circuit breaker for the request
    return from(this.circuitBreakerService.request(serviceName, requestConfig)).pipe(
      map((response) => {
        this.logger.log(`Response from ${url}: ${response.status}`);
        return response;
      }),
      catchError((error) => {
        this.logger.error(`Error forwarding request to ${url}: ${error.message}`, error.stack);

        // Forward the error status and message
        const status = error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR;
        const message = error.response?.data?.message || error.message || 'Internal Server Error';

        return throwError(() => new HttpException(message, status));
      }),
    );
  }

  /**
   * Get the URL for a microservice
   * @param serviceName The name of the microservice
   * @returns The URL for the microservice
   */
  private getServiceUrl(serviceName: string): string {
    const serviceMap = {
      user: this.configService.get<string>('USER_SERVICE_URL', 'http://localhost:3001/api'),
      store: this.configService.get<string>('STORE_SERVICE_URL', 'http://localhost:3002/api'),
      product: this.configService.get<string>('PRODUCT_SERVICE_URL', 'http://localhost:3004/api'),
      cart: this.configService.get<string>('CART_SERVICE_URL', 'http://localhost:3005/api'),
      order: this.configService.get<string>('ORDER_SERVICE_URL', 'http://localhost:3006/api'),
      social: this.configService.get<string>('SOCIAL_SERVICE_URL', 'http://localhost:3007/api'),
      notification: this.configService.get<string>('NOTIFICATION_SERVICE_URL', 'http://localhost:3003/api'),
      search: this.configService.get<string>('SEARCH_SERVICE_URL', 'http://localhost:3008/api'),
      analytics: this.configService.get<string>('ANALYTICS_SERVICE_URL', 'http://localhost:3009/api'),
    };

    const serviceUrl = serviceMap[serviceName];

    if (!serviceUrl) {
      throw new HttpException(`Unknown service: ${serviceName}`, HttpStatus.BAD_REQUEST);
    }

    return serviceUrl;
  }
}
