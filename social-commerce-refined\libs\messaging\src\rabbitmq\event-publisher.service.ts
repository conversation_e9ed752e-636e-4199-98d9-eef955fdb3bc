import { Injectable, Logger } from '@nestjs/common';
import { RabbitMQService } from './rabbitmq.service';

// Local BaseEvent interface for messaging
interface BaseEvent<T> {
  id: string;
  type: string;
  version: string;
  timestamp: string;
  producer: string;
  payload: T;
}

@Injectable()
export class EventPublisherService {
  private readonly logger = new Logger(EventPublisherService.name);
  private readonly exchange = 'events';

  constructor(private readonly rabbitMQService: RabbitMQService) {
    this.initialize();
  }

  private async initialize() {
    try {
      // Create the events exchange
      await this.rabbitMQService.createExchange(this.exchange, 'topic', {
        durable: true,
      });
      this.logger.log(`Exchange ${this.exchange} initialized`);
    } catch (error) {
      this.logger.error(`Error initializing event publisher: ${error.message}`);
    }
  }

  /**
   * Publish an event to the events exchange
   * @param event Event to publish
   * @returns Promise that resolves when the event is published
   */
  async publish<T>(event: BaseEvent<T>): Promise<boolean> {
    try {
      const routingKey = event.type;
      const result = await this.rabbitMQService.publish(this.exchange, routingKey, event);
      this.logger.debug(`Event ${event.type} published with ID ${event.id}`);
      return result;
    } catch (error) {
      this.logger.error(`Error publishing event ${event.type}: ${error.message}`);
      throw error;
    }
  }
}
