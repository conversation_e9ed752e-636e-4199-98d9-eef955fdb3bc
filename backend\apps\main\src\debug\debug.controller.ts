import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  Logger,
  Get,
} from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { Public } from '../decorators/public.decorator';
import { LoginDto } from '@app/common/dto/user.dto';
import { DebugService } from './debug.service';

/**
 * Debug Controller
 * 
 * This controller provides endpoints for debugging and testing purposes.
 * It should only be available in non-production environments.
 */
@ApiTags('debug')
@Controller('debug')
@Public() // All debug endpoints are public
export class DebugController {
  private readonly logger = new Logger(DebugController.name);

  constructor(private readonly debugService: DebugService) {
    this.logger.log('DebugController initialized');
    
    // Log a warning if in production
    if (process.env.NODE_ENV === 'production') {
      this.logger.warn('WARNING: Debug controller is available in production environment!');
    }
  }

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Debug root endpoint' })
  async debugRoot() {
    return {
      message: 'Debug API is active',
      environment: process.env.NODE_ENV || 'development',
      timestamp: new Date().toISOString(),
    };
  }

  @Post('auth/create-test-user')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Create a test user for debugging' })
  async createTestUser() {
    this.logger.debug('Create test user endpoint called');
    return this.debugService.createTestUser();
  }

  @Post('auth/create-test-user-direct')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Create a test user directly through the user service' })
  async createTestUserDirect() {
    this.logger.debug('Create test user direct endpoint called');
    return this.debugService.createTestUserDirect();
  }

  @Post('auth/create-direct-db-user')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Create a test user directly in the database' })
  async createDirectDbUser() {
    this.logger.debug('Create direct DB user endpoint called');
    return this.debugService.createDirectDbUser();
  }

  @Post('auth/create-fixed-hash-user')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Create a test user with a fixed password hash' })
  async createFixedHashUser() {
    this.logger.debug('Create fixed hash user endpoint called');
    return this.debugService.createFixedHashUser();
  }

  @Post('auth/login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Debug login endpoint' })
  async debugLogin(@Body() loginDto: LoginDto) {
    this.logger.debug(`Debug login endpoint called for: ${loginDto.usernameOrEmail}`);
    return this.debugService.debugLogin(loginDto);
  }

  @Post('auth/test-password')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Test password hashing for a user' })
  async testPassword(@Body() payload: { username: string; password: string }) {
    this.logger.debug(`Test password endpoint called for: ${payload.username}`);
    return this.debugService.testPassword(payload);
  }

  @Post('auth/check-user')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Check if a user exists and get their credentials' })
  async checkUser(@Body('username') username: string) {
    this.logger.debug(`Check user endpoint called for: ${username}`);
    return this.debugService.checkUser(username);
  }

  @Post('auth/find-by-username')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Find a user by username or email' })
  async findByUsername(@Body('usernameOrEmail') usernameOrEmail: string) {
    this.logger.debug(`Find by username endpoint called for: ${usernameOrEmail}`);
    return this.debugService.findByUsername(usernameOrEmail);
  }

  @Post('auth/verify-password')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Verify a user password' })
  async verifyPassword(@Body() payload: { userId: string; password: string }) {
    this.logger.debug(`Verify password endpoint called for user ID: ${payload.userId}`);
    return this.debugService.verifyPassword(payload);
  }

  @Post('auth/direct-login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Direct login endpoint bypassing Passport' })
  async directLogin(@Body() loginDto: LoginDto) {
    this.logger.debug(`Direct login endpoint called for: ${loginDto.usernameOrEmail}`);
    return this.debugService.directLogin(loginDto);
  }
}
