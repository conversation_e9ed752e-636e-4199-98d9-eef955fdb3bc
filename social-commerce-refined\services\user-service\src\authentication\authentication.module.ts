import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { PassportModule } from '@nestjs/passport';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { AuthenticationController } from './controllers/authentication.controller';
import { AuthenticationService } from './services/authentication.service';
import { UserRepository } from './repositories/user.repository';
import { User } from './entities/user.entity';
import { JwtStrategy } from './strategies/jwt.strategy';
import { LocalStrategy } from './strategies/local.strategy';

@Module({
  imports: [
    TypeOrmModule.forFeature([User]),
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET', 'your-secret-key'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN', '1h'),
        },
      }),
    }),
    // ClientsModule.registerAsync([
    //   {
    //     name: 'NOTIFICATION_SERVICE',
    //     imports: [ConfigModule],
    //     inject: [ConfigService],
    //     useFactory: (configService: ConfigService) => ({
    //       transport: Transport.RMQ,
    //       options: {
    //         urls: [configService.get<string>('RABBITMQ_URL', 'amqp://admin:admin@localhost:5672')],
    //         queue: configService.get<string>('NOTIFICATION_QUEUE', 'notification_queue'),
    //         queueOptions: {
    //           durable: true,
    //         },
    //       },
    //     }),
    //   },
    // ]), // Temporarily disabled for testing
  ],
  controllers: [AuthenticationController],
  providers: [
    AuthenticationService,
    UserRepository,
    JwtStrategy,
    LocalStrategy,
  ],
  exports: [
    AuthenticationService,
    UserRepository,
    JwtStrategy,
    PassportModule,
  ],
})
export class AuthenticationModule {}
