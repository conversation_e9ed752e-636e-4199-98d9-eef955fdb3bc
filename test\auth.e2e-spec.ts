import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { getRepositoryToken } from '@nestjs/typeorm';
import { User } from '../src/authentication/entities/user.entity';
import { Profile } from '../src/profile-management/entities/profile.entity';
import { Repository } from 'typeorm';

describe('Authentication (e2e)', () => {
  let app: INestApplication;
  let userRepository: Repository<User>;
  let profileRepository: Repository<Profile>;
  
  // Test user data
  const testUser = {
    email: '<EMAIL>',
    password: 'password123',
    profile: {
      firstName: 'John',
      lastName: 'Doe',
    },
  };
  
  // JWT token for authenticated requests
  let jwtToken: string;
  
  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    
    // Set up global pipes
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        transform: true,
        forbidNonWhitelisted: true,
        transformOptions: {
          enableImplicitConversion: true,
        },
      }),
    );
    
    // Get repositories
    userRepository = moduleFixture.get(getRepositoryToken(User));
    profileRepository = moduleFixture.get(getRepositoryToken(Profile));
    
    await app.init();
    
    // Clean up database before tests
    await userRepository.delete({});
    await profileRepository.delete({});
  });

  afterAll(async () => {
    // Clean up database after tests
    await userRepository.delete({});
    await profileRepository.delete({});
    
    await app.close();
  });

  describe('/auth/register (POST)', () => {
    it('should register a new user', () => {
      return request(app.getHttpServer())
        .post('/auth/register')
        .send(testUser)
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('id');
          expect(res.body.email).toBe(testUser.email);
          expect(res.body).not.toHaveProperty('password');
          expect(res.body.profile).toBeDefined();
          expect(res.body.profile.firstName).toBe(testUser.profile.firstName);
          expect(res.body.profile.lastName).toBe(testUser.profile.lastName);
        });
    });

    it('should not register a user with an existing email', () => {
      return request(app.getHttpServer())
        .post('/auth/register')
        .send(testUser)
        .expect(409);
    });

    it('should not register a user with invalid data', () => {
      return request(app.getHttpServer())
        .post('/auth/register')
        .send({
          email: 'invalid-email',
          password: '123', // Too short
        })
        .expect(400);
    });
  });

  describe('/auth/login (POST)', () => {
    it('should login a user and return a JWT token', () => {
      return request(app.getHttpServer())
        .post('/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password,
        })
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('accessToken');
          expect(res.body).toHaveProperty('user');
          expect(res.body.user.email).toBe(testUser.email);
          expect(res.body.user).not.toHaveProperty('password');
          
          // Save JWT token for authenticated requests
          jwtToken = res.body.accessToken;
        });
    });

    it('should not login with invalid credentials', () => {
      return request(app.getHttpServer())
        .post('/auth/login')
        .send({
          email: testUser.email,
          password: 'wrong-password',
        })
        .expect(401);
    });
  });

  describe('/auth/profile (GET)', () => {
    it('should get user profile when authenticated', () => {
      return request(app.getHttpServer())
        .get('/auth/profile')
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('id');
          expect(res.body.email).toBe(testUser.email);
          expect(res.body).not.toHaveProperty('password');
          expect(res.body.profile).toBeDefined();
          expect(res.body.profile.firstName).toBe(testUser.profile.firstName);
          expect(res.body.profile.lastName).toBe(testUser.profile.lastName);
        });
    });

    it('should not get profile when not authenticated', () => {
      return request(app.getHttpServer())
        .get('/auth/profile')
        .expect(401);
    });
  });
});
