'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { useRouter } from 'next/navigation';
import { Box, Heading, Text, Button, Flex, Spinner, Alert, AlertIcon, AlertTitle, AlertDescription } from '@chakra-ui/react';
import { useVerifyEmailMutation } from '@/store/api/userApiSlice';

/**
 * Email Verification Page
 * 
 * This page is used to verify a user's email address.
 * It automatically verifies the email when the page loads using the token from the URL.
 */
export default function VerifyEmailPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const token = searchParams.get('token');
  
  const [verifyEmail, { isLoading, isSuccess, isError, error }] = useVerifyEmailMutation();
  const [verificationAttempted, setVerificationAttempted] = useState(false);

  useEffect(() => {
    // Only attempt verification if we have a token and haven't tried yet
    if (token && !verificationAttempted) {
      verifyEmail({ token });
      setVerificationAttempted(true);
    }
  }, [token, verifyEmail, verificationAttempted]);

  const handleGoToLogin = () => {
    router.push('/login');
  };

  const handleGoToHome = () => {
    router.push('/');
  };

  // If no token is provided
  if (!token) {
    return (
      <Box maxW="md" mx="auto" mt={10} p={6} borderWidth={1} borderRadius="lg" boxShadow="lg">
        <Alert status="error" borderRadius="md" mb={6}>
          <AlertIcon />
          <AlertTitle>Invalid Verification Link</AlertTitle>
          <AlertDescription>
            The verification link is missing a token. Please check your email and try again.
          </AlertDescription>
        </Alert>
        <Button colorScheme="blue" width="full" onClick={handleGoToHome}>
          Go to Home
        </Button>
      </Box>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <Flex direction="column" align="center" justify="center" minH="50vh">
        <Spinner size="xl" thickness="4px" speed="0.65s" color="blue.500" mb={4} />
        <Heading size="md">Verifying your email...</Heading>
        <Text mt={2} color="gray.600">Please wait while we verify your email address.</Text>
      </Flex>
    );
  }

  // Success state
  if (isSuccess) {
    return (
      <Box maxW="md" mx="auto" mt={10} p={6} borderWidth={1} borderRadius="lg" boxShadow="lg">
        <Alert status="success" borderRadius="md" mb={6}>
          <AlertIcon />
          <Box>
            <AlertTitle>Email Verified!</AlertTitle>
            <AlertDescription>
              Your email has been successfully verified. You can now log in to your account.
            </AlertDescription>
          </Box>
        </Alert>
        <Button colorScheme="blue" width="full" onClick={handleGoToLogin}>
          Go to Login
        </Button>
      </Box>
    );
  }

  // Error state
  if (isError) {
    const errorMessage = error?.data?.message || 'An error occurred during email verification.';
    
    return (
      <Box maxW="md" mx="auto" mt={10} p={6} borderWidth={1} borderRadius="lg" boxShadow="lg">
        <Alert status="error" borderRadius="md" mb={6}>
          <AlertIcon />
          <Box>
            <AlertTitle>Verification Failed</AlertTitle>
            <AlertDescription>
              {errorMessage}
            </AlertDescription>
          </Box>
        </Alert>
        <Text mb={4}>
          The verification link may have expired or already been used. Please try logging in or request a new verification email.
        </Text>
        <Button colorScheme="blue" width="full" onClick={handleGoToLogin} mb={3}>
          Go to Login
        </Button>
        <Button variant="outline" width="full" onClick={handleGoToHome}>
          Go to Home
        </Button>
      </Box>
    );
  }

  // Default state (should not reach here)
  return (
    <Box maxW="md" mx="auto" mt={10} p={6} borderWidth={1} borderRadius="lg" boxShadow="lg">
      <Heading size="lg" mb={6} textAlign="center">Email Verification</Heading>
      <Text mb={4}>Processing your verification request...</Text>
      <Button colorScheme="blue" width="full" onClick={handleGoToHome}>
        Go to Home
      </Button>
    </Box>
  );
}
