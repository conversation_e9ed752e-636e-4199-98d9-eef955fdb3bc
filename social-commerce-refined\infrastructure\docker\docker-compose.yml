version: '3.8'

services:
  # Message Broker
  rabbitmq:
    image: rabbitmq:3-management
    container_name: social-commerce-rabbitmq
    ports:
      - "5672:5672"   # AMQP protocol port
      - "15672:15672" # Management UI port
    environment:
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=admin
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - social-commerce-network
    healthcheck:
      test: ["CMD", "rabbitmqctl", "status"]
      interval: 30s
      timeout: 10s
      retries: 5

  # PostgreSQL for User Service
  user-db:
    image: postgres:14
    container_name: social-commerce-user-db
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=1111
      - POSTGRES_DB=user_service
    volumes:
      - user_db_data:/var/lib/postgresql/data
    networks:
      - social-commerce-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # PostgreSQL for Store Service
  store-db:
    image: postgres:14
    container_name: social-commerce-store-db
    ports:
      - "5433:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=1111
      - POSTGRES_DB=store_service
    volumes:
      - store_db_data:/var/lib/postgresql/data
    networks:
      - social-commerce-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # PostgreSQL for Product Service
  product-db:
    image: postgres:14
    container_name: social-commerce-product-db
    ports:
      - "5434:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=1111
      - POSTGRES_DB=product_service
    volumes:
      - product_db_data:/var/lib/postgresql/data
    networks:
      - social-commerce-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # PostgreSQL for Order Service
  order-db:
    image: postgres:14
    container_name: social-commerce-order-db
    ports:
      - "5435:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=1111
      - POSTGRES_DB=order_service
    volumes:
      - order_db_data:/var/lib/postgresql/data
    networks:
      - social-commerce-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for Cart Service
  cart-db:
    image: redis:6
    container_name: social-commerce-cart-db
    ports:
      - "6379:6379"
    volumes:
      - cart_db_data:/data
    networks:
      - social-commerce-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # MongoDB for Social Service
  social-db:
    image: mongo:5
    container_name: social-commerce-social-db
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=admin
    volumes:
      - social_db_data:/data/db
    networks:
      - social-commerce-network
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongo localhost:27017/test --quiet
      interval: 10s
      timeout: 5s
      retries: 5

  # MongoDB for Notification Service
  notification-db:
    image: mongo:5
    container_name: social-commerce-notification-db
    ports:
      - "27018:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=admin
    volumes:
      - notification_db_data:/data/db
    networks:
      - social-commerce-network
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongo localhost:27017/test --quiet
      interval: 10s
      timeout: 5s
      retries: 5

  # Elasticsearch for Search Service
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.14.0
    container_name: social-commerce-elasticsearch
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - social-commerce-network
    healthcheck:
      test: ["CMD-SHELL", "curl --silent --fail localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  rabbitmq_data:
  user_db_data:
  store_db_data:
  product_db_data:
  order_db_data:
  cart_db_data:
  social_db_data:
  notification_db_data:
  elasticsearch_data:

networks:
  social-commerce-network:
    driver: bridge
