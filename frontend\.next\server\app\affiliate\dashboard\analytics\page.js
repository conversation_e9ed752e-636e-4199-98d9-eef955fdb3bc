(()=>{var e={};e.id=2893,e.ids=[2893],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},58525:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>g,pages:()=>o,routeModule:()=>y,tree:()=>c});var r=t(67096),s=t(16132),i=t(37284),n=t.n(i),l=t(32564),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(a,d);let c=["",{children:["affiliate",{children:["dashboard",{children:["analytics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,67117)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\affiliate\\dashboard\\analytics\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9291,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\affiliate\\dashboard\\analytics\\page.tsx"],g="/affiliate/dashboard/analytics/page",x={require:t,loadChunk:()=>Promise.resolve()},y=new r.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/affiliate/dashboard/analytics/page",pathname:"/affiliate/dashboard/analytics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},29092:(e,a,t)=>{Promise.resolve().then(t.bind(t,27234))},27234:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>AffiliateAnalyticsPage});var r=t(30784),s=t(9885),i=t(27870),n=t(14379),l=t(11440),d=t.n(l),c=t(59872),o=t(94820);let affiliate_AffiliateAnalyticsFilter=({dateRange:e,onDateRangeChange:a,onApplyFilters:t,isLoading:s=!1,className:l=""})=>{let{t:d}=(0,i.$G)("affiliate"),{isRtl:o}=(0,n.g)(),g=[{label:d("analytics.today","Today"),days:0},{label:d("analytics.yesterday","Yesterday"),days:1},{label:d("analytics.last7Days","Last 7 Days"),days:7},{label:d("analytics.last30Days","Last 30 Days"),days:30},{label:d("analytics.thisMonth","This Month"),days:"month"},{label:d("analytics.lastMonth","Last Month"),days:"lastMonth"}],handlePredefinedRange=e=>{let t=new Date,r=new Date,s=new Date;"number"==typeof e.days?0===e.days?(r=new Date(t.getFullYear(),t.getMonth(),t.getDate()),s=t):1===e.days?(r=new Date(t.getFullYear(),t.getMonth(),t.getDate()-1),s=new Date(t.getFullYear(),t.getMonth(),t.getDate()-1,23,59,59)):(r=new Date(t.getFullYear(),t.getMonth(),t.getDate()-e.days),s=t):"month"===e.days?(r=new Date(t.getFullYear(),t.getMonth(),1),s=t):"lastMonth"===e.days&&(r=new Date(t.getFullYear(),t.getMonth()-1,1),s=new Date(t.getFullYear(),t.getMonth(),0,23,59,59)),a({startDate:r.toISOString().split("T")[0],endDate:s.toISOString().split("T")[0]})};return(0,r.jsxs)("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 ${l}`,children:[r.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4",children:d("analytics.filters","Filters")}),(0,r.jsxs)("div",{className:"mb-6",children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("analytics.dateRange","Date Range")}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"start-date",className:"block text-xs text-gray-500 dark:text-gray-400 mb-1",children:d("analytics.startDate","Start Date")}),r.jsx("input",{id:"start-date",type:"date",value:e.startDate,onChange:t=>a({...e,startDate:t.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100",disabled:s})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"end-date",className:"block text-xs text-gray-500 dark:text-gray-400 mb-1",children:d("analytics.endDate","End Date")}),r.jsx("input",{id:"end-date",type:"date",value:e.endDate,onChange:t=>a({...e,endDate:t.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100",disabled:s,min:e.startDate})]})]}),r.jsx("div",{className:"flex flex-wrap gap-2",children:g.map((e,a)=>r.jsx("button",{onClick:()=>handlePredefinedRange(e),className:"px-3 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600",disabled:s,children:e.label},a))})]}),r.jsx("div",{className:"flex justify-end",children:r.jsx(c.Z,{onClick:t,isLoading:s,disabled:s,children:d("analytics.applyFilters","Apply Filters")})})]})},affiliate_AffiliateAnalyticsReport=({data:e,isLoading:a=!1,className:t=""})=>{let{t:s}=(0,i.$G)("affiliate"),{isRtl:l}=(0,n.g)(),formatCurrency=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e);return a?r.jsx("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 ${t}`,children:(0,r.jsxs)("div",{className:"animate-pulse",children:[r.jsx("div",{className:"h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"}),r.jsx("div",{className:"h-40 bg-gray-200 dark:bg-gray-700 rounded mb-6"}),r.jsx("div",{className:"h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"}),r.jsx("div",{className:"h-40 bg-gray-200 dark:bg-gray-700 rounded"})]})}):(0,r.jsxs)("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 ${t}`,children:[r.jsx("h2",{className:"text-xl font-bold text-gray-900 dark:text-gray-100 mb-4",children:s("analytics.report","Detailed Report")}),(0,r.jsxs)("div",{className:"mb-8",children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4",children:s("analytics.topProducts","Top Performing Products")}),0===e.topProducts.length?r.jsx("p",{className:"text-gray-500 dark:text-gray-400 py-4 text-center",children:s("analytics.noProductData","No product data available yet")}):r.jsx("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[r.jsx("thead",{className:"bg-gray-50 dark:bg-gray-900",children:(0,r.jsxs)("tr",{children:[r.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:s("analytics.product","Product")}),r.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:s("analytics.clicks","Clicks")}),r.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:s("analytics.conversions","Conversions")}),r.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:s("analytics.earnings","Earnings")})]})}),r.jsx("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:e.topProducts.map(e=>(0,r.jsxs)("tr",{children:[r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100",children:e.title}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:e.clicks}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:e.conversions}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-green-600 dark:text-green-400",children:formatCurrency(e.earnings)})]},e.id))})]})})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4",children:s("analytics.topReferrers","Top Referral Sources")}),0===e.topReferrers.length?r.jsx("p",{className:"text-gray-500 dark:text-gray-400 py-4 text-center",children:s("analytics.noReferrerData","No referrer data available yet")}):r.jsx("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[r.jsx("thead",{className:"bg-gray-50 dark:bg-gray-900",children:(0,r.jsxs)("tr",{children:[r.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:s("analytics.source","Source")}),r.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:s("analytics.clicks","Clicks")}),r.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:s("analytics.conversions","Conversions")}),r.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:s("analytics.earnings","Earnings")})]})}),r.jsx("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:e.topReferrers.map((e,a)=>(0,r.jsxs)("tr",{children:[r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100",children:e.source}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:e.clicks}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:e.conversions}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-green-600 dark:text-green-400",children:formatCurrency(e.earnings)})]},a))})]})})]})]})};var g=t(3619);function AffiliateAnalyticsPage(){let{t:e}=(0,i.$G)("affiliate"),{isRtl:a}=(0,n.g)(),t=new Date().toISOString().split("T")[0],l=new Date(Date.now()-2592e6).toISOString().split("T")[0],[x,y]=(0,s.useState)({startDate:l,endDate:t}),[m,p]=(0,s.useState)({summary:{totalClicks:0,totalConversions:0,totalEarnings:0,conversionRate:0},topProducts:[],topReferrers:[]}),[h,u]=(0,s.useState)(!1),{data:f,isLoading:b}=(0,g.nK)({}),k=f?.accounts?.[0],v=!!k,handleApplyFilters=()=>{v&&(u(!0),setTimeout(()=>{p({summary:{totalClicks:1250,totalConversions:75,totalEarnings:1875.5,conversionRate:6},topProducts:[{id:"product1",title:"Product 1",clicks:450,conversions:30,earnings:750},{id:"product2",title:"Product 2",clicks:350,conversions:25,earnings:625},{id:"product3",title:"Product 3",clicks:250,conversions:15,earnings:375},{id:"product4",title:"Product 4",clicks:200,conversions:5,earnings:125.5}],topReferrers:[{source:"Facebook",clicks:500,conversions:35,earnings:875},{source:"Instagram",clicks:350,conversions:20,earnings:500},{source:"Twitter",clicks:250,conversions:15,earnings:375},{source:"Direct",clicks:150,conversions:5,earnings:125.5}]}),u(!1)},1500))};return(0,s.useEffect)(()=>{v&&handleApplyFilters()},[v]),r.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2",children:e("analytics.title","Analytics & Reports")}),r.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:e("analytics.description","Track and analyze your affiliate performance")})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-1",children:[r.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 mb-6",children:r.jsx(o.Z,{})}),v&&r.jsx(affiliate_AffiliateAnalyticsFilter,{dateRange:x,onDateRangeChange:y,onApplyFilters:handleApplyFilters,isLoading:h})]}),r.jsx("div",{className:"lg:col-span-3",children:v?r.jsx(affiliate_AffiliateAnalyticsReport,{data:m,isLoading:h||b}):r.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6",children:(0,r.jsxs)("div",{className:"text-center py-8",children:[r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-16 w-16 text-gray-400 dark:text-gray-600 mx-auto mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})}),r.jsx("h3",{className:"text-xl font-medium text-gray-900 dark:text-gray-100 mb-2",children:e("noAffiliateAccount","No Affiliate Account")}),r.jsx("p",{className:"text-gray-500 dark:text-gray-400 mb-6 max-w-md mx-auto",children:e("joinProgramFirst","Join an affiliate program to access analytics and reports")}),r.jsx(d(),{href:"/affiliate/programs",children:r.jsx(c.Z,{variant:"primary",children:e("browsePrograms","Browse Programs")})})]})})})]})]})})}},67117:(e,a,t)=>{"use strict";t.r(a),t.d(a,{$$typeof:()=>n,__esModule:()=>i,default:()=>d});var r=t(95153);let s=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\affiliate\dashboard\analytics\page.tsx`),{__esModule:i,$$typeof:n}=s,l=s.default,d=l}};var a=require("../../../../webpack-runtime.js");a.C(e);var __webpack_exec__=e=>a(a.s=e),t=a.X(0,[2103,2765,3619,9522],()=>__webpack_exec__(58525));module.exports=t})();