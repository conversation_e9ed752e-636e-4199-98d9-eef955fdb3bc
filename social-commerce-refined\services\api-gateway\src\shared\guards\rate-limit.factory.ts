import { RateLimitGuard } from './rate-limit.guard';

/**
 * Factory for creating rate limit guards with different configurations
 */
export class RateLimitFactory {
  /**
   * Create a rate limit guard for authentication endpoints
   * @returns A rate limit guard for authentication endpoints
   */
  static createAuthRateLimitGuard(): RateLimitGuard {
    return new RateLimitGuard({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 10, // 10 requests per 15 minutes
      message: 'Too many authentication attempts, please try again later.',
    });
  }

  /**
   * Create a rate limit guard for API endpoints
   * @returns A rate limit guard for API endpoints
   */
  static createApiRateLimitGuard(): RateLimitGuard {
    return new RateLimitGuard({
      windowMs: 60 * 1000, // 1 minute
      max: 100, // 100 requests per minute
      message: 'Too many API requests, please try again later.',
    });
  }

  /**
   * Create a rate limit guard for store endpoints
   * @returns A rate limit guard for store endpoints
   */
  static createStoreRateLimitGuard(): RateLimitGuard {
    return new RateLimitGuard({
      windowMs: 60 * 1000, // 1 minute
      max: 50, // 50 requests per minute
      message: 'Too many store requests, please try again later.',
    });
  }

  /**
   * Create a rate limit guard for product endpoints
   * @returns A rate limit guard for product endpoints
   */
  static createProductRateLimitGuard(): RateLimitGuard {
    return new RateLimitGuard({
      windowMs: 60 * 1000, // 1 minute
      max: 200, // 200 requests per minute
      message: 'Too many product requests, please try again later.',
    });
  }
}
