#!/bin/bash

# Create user service directories
mkdir -p services/user-service/src/authentication
mkdir -p services/user-service/src/profile-management
mkdir -p services/user-service/src/verification
mkdir -p services/user-service/src/shared
mkdir -p services/user-service/src/shared/guards
mkdir -p services/user-service/src/shared/decorators
mkdir -p services/user-service/src/shared/filters
mkdir -p services/user-service/src/shared/interceptors
mkdir -p services/user-service/src/shared/middleware
mkdir -p services/user-service/src/shared/utils
mkdir -p services/user-service/src/shared/dto
mkdir -p services/user-service/src/shared/entities

# Create authentication subdirectories
mkdir -p services/user-service/src/authentication/controllers
mkdir -p services/user-service/src/authentication/services
mkdir -p services/user-service/src/authentication/repositories
mkdir -p services/user-service/src/authentication/entities
mkdir -p services/user-service/src/authentication/dto
mkdir -p services/user-service/src/authentication/guards
mkdir -p services/user-service/src/authentication/strategies

# Create profile-management subdirectories
mkdir -p services/user-service/src/profile-management/controllers
mkdir -p services/user-service/src/profile-management/services
mkdir -p services/user-service/src/profile-management/repositories
mkdir -p services/user-service/src/profile-management/entities
mkdir -p services/user-service/src/profile-management/dto

# Create verification subdirectories
mkdir -p services/user-service/src/verification/controllers
mkdir -p services/user-service/src/verification/services
mkdir -p services/user-service/src/verification/repositories
mkdir -p services/user-service/src/verification/entities
mkdir -p services/user-service/src/verification/dto

echo "Directories created successfully!"
