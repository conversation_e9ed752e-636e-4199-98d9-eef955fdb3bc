import { Controller, Post, Body, Get, Param, Logger, HttpCode, HttpStatus, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { MessagePattern } from '@nestjs/microservices';
import { NotificationService } from '../services/notification.service';
import { CreateNotificationDto } from '../dto/create-notification.dto';
import { SendEmailDto } from '../dto/send-email.dto';
import { SendSmsDto } from '../dto/send-sms.dto';
import { Notification } from '../entities/notification.entity';
import { JwtAuthGuard } from '../../shared/guards/jwt-auth.guard';

@ApiTags('notifications')
@Controller('notifications')
export class NotificationController {
  private readonly logger = new Logger(NotificationController.name);

  constructor(private readonly notificationService: NotificationService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new notification' })
  @ApiResponse({ status: 201, description: 'Notification created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async createNotification(@Body() createNotificationDto: CreateNotificationDto): Promise<Notification> {
    this.logger.log(`Creating notification for user: ${createNotificationDto.userId}`);
    return this.notificationService.createNotification(createNotificationDto);
  }

  @Post('email')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Send an email notification' })
  @ApiResponse({ status: 200, description: 'Email sent successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async sendEmail(@Body() sendEmailDto: SendEmailDto): Promise<Notification> {
    this.logger.log(`Sending email to: ${sendEmailDto.to}`);
    return this.notificationService.sendEmail(sendEmailDto);
  }

  @Post('sms')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Send an SMS notification' })
  @ApiResponse({ status: 200, description: 'SMS sent successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async sendSms(@Body() sendSmsDto: SendSmsDto): Promise<Notification> {
    this.logger.log(`Sending SMS to: ${sendSmsDto.to}`);
    return this.notificationService.sendSms(sendSmsDto);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all notifications' })
  @ApiResponse({ status: 200, description: 'Notifications retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async findAll(): Promise<Notification[]> {
    this.logger.log('Getting all notifications');
    return this.notificationService.findAll();
  }

  @Get('user/:userId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get notifications for a specific user' })
  @ApiResponse({ status: 200, description: 'User notifications retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async findByUserId(@Param('userId') userId: string): Promise<Notification[]> {
    this.logger.log(`Getting notifications for user: ${userId}`);
    return this.notificationService.findByUserId(userId);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get a notification by ID' })
  @ApiResponse({ status: 200, description: 'Notification retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Notification not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async findOne(@Param('id') id: string): Promise<Notification> {
    this.logger.log(`Getting notification with ID: ${id}`);
    return this.notificationService.findOne(id);
  }

  // Microservice endpoints for inter-service communication

  @MessagePattern('notification.send_email')
  async sendEmailMessage(sendEmailDto: SendEmailDto): Promise<Notification> {
    this.logger.log(`Microservice email request received for: ${sendEmailDto.to}`);
    return this.notificationService.sendEmail(sendEmailDto);
  }

  @MessagePattern('notification.send_sms')
  async sendSmsMessage(sendSmsDto: SendSmsDto): Promise<Notification> {
    this.logger.log(`Microservice SMS request received for: ${sendSmsDto.to}`);
    return this.notificationService.sendSms(sendSmsDto);
  }

  @MessagePattern('notification.create')
  async createNotificationMessage(createNotificationDto: CreateNotificationDto): Promise<Notification> {
    this.logger.log(`Microservice notification creation request received for user: ${createNotificationDto.userId}`);
    return this.notificationService.createNotification(createNotificationDto);
  }

  @MessagePattern('notification.find_by_user')
  async findByUserIdMessage(userId: string): Promise<Notification[]> {
    this.logger.log(`Microservice find notifications by user request received for: ${userId}`);
    return this.notificationService.findByUserId(userId);
  }

  @MessagePattern('notification.find_by_id')
  async findOneMessage(id: string): Promise<Notification> {
    this.logger.log(`Microservice find notification by ID request received for: ${id}`);
    return this.notificationService.findOne(id);
  }

  // Event handlers for user events

  @MessagePattern('user.created')
  async handleUserCreated(data: { id: string; email: string; name?: string }): Promise<void> {
    this.logger.log(`User created event received for: ${data.email}`);
    
    try {
      await this.notificationService.sendEmail({
        to: data.email,
        subject: 'Welcome to Social Commerce Platform',
        content: `Welcome ${data.name || 'User'}! Thank you for joining our platform.`,
        userId: data.id,
      });
    } catch (error) {
      this.logger.error(`Failed to send welcome email to: ${data.email}`, error.stack);
    }
  }

  @MessagePattern('user.email_verification_requested')
  async handleEmailVerificationRequested(data: { email: string; verificationCode: string }): Promise<void> {
    this.logger.log(`Email verification requested for: ${data.email}`);
    
    try {
      await this.notificationService.sendEmail({
        to: data.email,
        subject: 'Verify Your Email Address',
        content: `Your verification code is: ${data.verificationCode}`,
      });
    } catch (error) {
      this.logger.error(`Failed to send verification email to: ${data.email}`, error.stack);
    }
  }

  @MessagePattern('order.created')
  async handleOrderCreated(data: { userId: string; orderId: string; userEmail: string; orderDetails: any }): Promise<void> {
    this.logger.log(`Order created event received for order: ${data.orderId}`);
    
    try {
      await this.notificationService.sendEmail({
        to: data.userEmail,
        subject: `Order Confirmation - ${data.orderId}`,
        content: `Your order ${data.orderId} has been confirmed. Thank you for your purchase!`,
        userId: data.userId,
      });
    } catch (error) {
      this.logger.error(`Failed to send order confirmation email for order: ${data.orderId}`, error.stack);
    }
  }
}
