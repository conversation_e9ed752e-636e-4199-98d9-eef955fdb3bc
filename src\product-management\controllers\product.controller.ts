import { Controller, Get, Post, Body, Param, Put, Delete, UseGuards, Req, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ProductService } from '../services/product.service';
import { CreateProductDto } from '../dto/create-product.dto';
import { UpdateProductDto } from '../dto/update-product.dto';
import { Product } from '../entities/product.entity';
import { JwtAuthGuard } from '../../shared/guards/jwt-auth.guard';
import { MessagePattern } from '@nestjs/microservices';

@ApiTags('products')
@Controller('products')
export class ProductController {
  private readonly logger = new Logger(ProductController.name);

  constructor(private readonly productService: ProductService) {}

  @Get()
  @ApiOperation({ summary: 'Get all products' })
  @ApiResponse({ status: 200, description: 'Return all products' })
  async findAll(): Promise<Product[]> {
    this.logger.log('Getting all products');
    return this.productService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a product by ID' })
  @ApiResponse({ status: 200, description: 'Return the product' })
  @ApiResponse({ status: 404, description: 'Product not found' })
  async findOne(@Param('id') id: string): Promise<Product> {
    this.logger.log(`Getting product with ID: ${id}`);
    
    // Increment view count
    await this.productService.incrementViewCount(id);
    
    return this.productService.findOne(id);
  }

  @Get('store/:storeId')
  @ApiOperation({ summary: 'Get products by store ID' })
  @ApiResponse({ status: 200, description: 'Return the products' })
  @ApiResponse({ status: 404, description: 'Store not found' })
  async findByStoreId(@Param('storeId') storeId: string): Promise<Product[]> {
    this.logger.log(`Getting products for store with ID: ${storeId}`);
    return this.productService.findByStoreId(storeId);
  }

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new product' })
  @ApiResponse({ status: 201, description: 'Product created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Store not found' })
  async create(@Body() createProductDto: CreateProductDto): Promise<Product> {
    this.logger.log(`Creating product with name: ${createProductDto.name}`);
    return this.productService.create(createProductDto);
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update a product' })
  @ApiResponse({ status: 200, description: 'Product updated successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Product not found' })
  async update(
    @Param('id') id: string,
    @Body() updateProductDto: UpdateProductDto,
    @Req() req,
  ): Promise<Product> {
    this.logger.log(`Updating product with ID: ${id}`);
    return this.productService.update(id, updateProductDto, req.user.sub);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete a product' })
  @ApiResponse({ status: 200, description: 'Product deleted successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Product not found' })
  async remove(@Param('id') id: string, @Req() req): Promise<void> {
    this.logger.log(`Removing product with ID: ${id}`);
    return this.productService.remove(id, req.user.sub);
  }

  // Microservice endpoints

  @MessagePattern('find_all_products')
  async findAllProducts(): Promise<Product[]> {
    this.logger.log('Microservice find all products request received');
    return this.productService.findAll();
  }

  @MessagePattern('find_product_by_id')
  async findProductById(id: string): Promise<Product> {
    this.logger.log(`Microservice find product by ID request received for: ${id}`);
    return this.productService.findOne(id);
  }

  @MessagePattern('find_products_by_store_id')
  async findProductsByStoreId(storeId: string): Promise<Product[]> {
    this.logger.log(`Microservice find products by store ID request received for: ${storeId}`);
    return this.productService.findByStoreId(storeId);
  }

  @MessagePattern('create_product')
  async createProduct(createProductDto: CreateProductDto): Promise<Product> {
    this.logger.log(`Microservice create product request received for: ${createProductDto.name}`);
    return this.productService.create(createProductDto);
  }

  @MessagePattern('update_product')
  async updateProduct(data: { id: string; updateProductDto: UpdateProductDto; userId: string }): Promise<Product> {
    this.logger.log(`Microservice update product request received for ID: ${data.id}`);
    return this.productService.update(data.id, data.updateProductDto, data.userId);
  }

  @MessagePattern('remove_product')
  async removeProduct(data: { id: string; userId: string }): Promise<void> {
    this.logger.log(`Microservice remove product request received for ID: ${data.id}`);
    return this.productService.remove(data.id, data.userId);
  }

  @MessagePattern('update_product_rating')
  async updateProductRating(data: { id: string; rating: number; reviewCount: number }): Promise<Product> {
    this.logger.log(`Microservice update product rating request received for ID: ${data.id}`);
    return this.productService.updateRating(data.id, data.rating, data.reviewCount);
  }

  @MessagePattern('increment_product_sales')
  async incrementProductSales(data: { id: string; quantity: number }): Promise<Product> {
    this.logger.log(`Microservice increment product sales request received for ID: ${data.id}`);
    return this.productService.incrementSalesCount(data.id, data.quantity);
  }

  @MessagePattern('update_product_quantity')
  async updateProductQuantity(data: { id: string; quantity: number }): Promise<Product> {
    this.logger.log(`Microservice update product quantity request received for ID: ${data.id}`);
    return this.productService.updateQuantity(data.id, data.quantity);
  }
}
