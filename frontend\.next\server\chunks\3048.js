"use strict";exports.id=3048,exports.ids=[3048],exports.modules={63048:(e,t,r)=>{r.d(t,{ZP:()=>p});var n=r(7294),a=r(28791),i=r(52190),o=r(51748),u=r(13838),s=r(58134);function startOfDay(e,t){let r=(0,s.Q)(e,t?.in);return r.setHours(0,0,0,0),r}function differenceInCalendarDays(e,t,r){let[n,a]=(0,o.d)(r?.in,e,t),s=startOfDay(n),d=startOfDay(a),c=+s-(0,i.D)(s),l=+d-(0,i.D)(d);return Math.round((c-l)/u.dP)}function startOfYear(e,t){let r=(0,s.Q)(e,t?.in);return r.setFullYear(r.getFullYear(),0,1),r.setHours(0,0,0,0),r}function getDayOfYear(e,t){let r=(0,s.Q)(e,t?.in),n=differenceInCalendarDays(r,startOfYear(r));return n+1}function startOfWeek(e,t){let r=(0,a.j)(),n=t?.weekStartsOn??t?.locale?.options?.weekStartsOn??r.weekStartsOn??r.locale?.options?.weekStartsOn??0,i=(0,s.Q)(e,t?.in),o=i.getDay();return i.setDate(i.getDate()-((o<n?7:0)+o-n)),i.setHours(0,0,0,0),i}function startOfISOWeek(e,t){return startOfWeek(e,{...t,weekStartsOn:1})}var d=r(43491);function getISOWeekYear(e,t){let r=(0,s.Q)(e,t?.in),n=r.getFullYear(),a=(0,d.L)(r,0);a.setFullYear(n+1,0,4),a.setHours(0,0,0,0);let i=startOfISOWeek(a),o=(0,d.L)(r,0);o.setFullYear(n,0,4),o.setHours(0,0,0,0);let u=startOfISOWeek(o);return r.getTime()>=i.getTime()?n+1:r.getTime()>=u.getTime()?n:n-1}function startOfISOWeekYear(e,t){let r=getISOWeekYear(e,t),n=(0,d.L)(t?.in||e,0);return n.setFullYear(r,0,4),n.setHours(0,0,0,0),startOfISOWeek(n)}function getISOWeek(e,t){let r=(0,s.Q)(e,t?.in),n=+startOfISOWeek(r)-+startOfISOWeekYear(r);return Math.round(n/u.jE)+1}function getWeekYear(e,t){let r=(0,s.Q)(e,t?.in),n=r.getFullYear(),i=(0,a.j)(),o=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??i.firstWeekContainsDate??i.locale?.options?.firstWeekContainsDate??1,u=(0,d.L)(t?.in||e,0);u.setFullYear(n+1,0,o),u.setHours(0,0,0,0);let c=startOfWeek(u,t),l=(0,d.L)(t?.in||e,0);l.setFullYear(n,0,o),l.setHours(0,0,0,0);let f=startOfWeek(l,t);return+r>=+c?n+1:+r>=+f?n:n-1}function startOfWeekYear(e,t){let r=(0,a.j)(),n=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??r.firstWeekContainsDate??r.locale?.options?.firstWeekContainsDate??1,i=getWeekYear(e,t),o=(0,d.L)(t?.in||e,0);o.setFullYear(i,0,n),o.setHours(0,0,0,0);let u=startOfWeek(o,t);return u}function getWeek(e,t){let r=(0,s.Q)(e,t?.in),n=+startOfWeek(r,t)-+startOfWeekYear(r,t);return Math.round(n/u.jE)+1}function addLeadingZeros(e,t){let r=Math.abs(e).toString().padStart(t,"0");return(e<0?"-":"")+r}let c={y(e,t){let r=e.getFullYear(),n=r>0?r:1-r;return addLeadingZeros("yy"===t?n%100:n,t.length)},M(e,t){let r=e.getMonth();return"M"===t?String(r+1):addLeadingZeros(r+1,2)},d:(e,t)=>addLeadingZeros(e.getDate(),t.length),a(e,t){let r=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return r.toUpperCase();case"aaa":return r;case"aaaaa":return r[0];default:return"am"===r?"a.m.":"p.m."}},h:(e,t)=>addLeadingZeros(e.getHours()%12||12,t.length),H:(e,t)=>addLeadingZeros(e.getHours(),t.length),m:(e,t)=>addLeadingZeros(e.getMinutes(),t.length),s:(e,t)=>addLeadingZeros(e.getSeconds(),t.length),S(e,t){let r=t.length,n=e.getMilliseconds();return addLeadingZeros(Math.trunc(n*Math.pow(10,r-3)),t.length)}},l={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},f={G:function(e,t,r){let n=e.getFullYear()>0?1:0;switch(t){case"G":case"GG":case"GGG":return r.era(n,{width:"abbreviated"});case"GGGGG":return r.era(n,{width:"narrow"});default:return r.era(n,{width:"wide"})}},y:function(e,t,r){if("yo"===t){let t=e.getFullYear(),n=t>0?t:1-t;return r.ordinalNumber(n,{unit:"year"})}return c.y(e,t)},Y:function(e,t,r,n){let a=getWeekYear(e,n),i=a>0?a:1-a;if("YY"===t){let e=i%100;return addLeadingZeros(e,2)}return"Yo"===t?r.ordinalNumber(i,{unit:"year"}):addLeadingZeros(i,t.length)},R:function(e,t){let r=getISOWeekYear(e);return addLeadingZeros(r,t.length)},u:function(e,t){let r=e.getFullYear();return addLeadingZeros(r,t.length)},Q:function(e,t,r){let n=Math.ceil((e.getMonth()+1)/3);switch(t){case"Q":return String(n);case"QQ":return addLeadingZeros(n,2);case"Qo":return r.ordinalNumber(n,{unit:"quarter"});case"QQQ":return r.quarter(n,{width:"abbreviated",context:"formatting"});case"QQQQQ":return r.quarter(n,{width:"narrow",context:"formatting"});default:return r.quarter(n,{width:"wide",context:"formatting"})}},q:function(e,t,r){let n=Math.ceil((e.getMonth()+1)/3);switch(t){case"q":return String(n);case"qq":return addLeadingZeros(n,2);case"qo":return r.ordinalNumber(n,{unit:"quarter"});case"qqq":return r.quarter(n,{width:"abbreviated",context:"standalone"});case"qqqqq":return r.quarter(n,{width:"narrow",context:"standalone"});default:return r.quarter(n,{width:"wide",context:"standalone"})}},M:function(e,t,r){let n=e.getMonth();switch(t){case"M":case"MM":return c.M(e,t);case"Mo":return r.ordinalNumber(n+1,{unit:"month"});case"MMM":return r.month(n,{width:"abbreviated",context:"formatting"});case"MMMMM":return r.month(n,{width:"narrow",context:"formatting"});default:return r.month(n,{width:"wide",context:"formatting"})}},L:function(e,t,r){let n=e.getMonth();switch(t){case"L":return String(n+1);case"LL":return addLeadingZeros(n+1,2);case"Lo":return r.ordinalNumber(n+1,{unit:"month"});case"LLL":return r.month(n,{width:"abbreviated",context:"standalone"});case"LLLLL":return r.month(n,{width:"narrow",context:"standalone"});default:return r.month(n,{width:"wide",context:"standalone"})}},w:function(e,t,r,n){let a=getWeek(e,n);return"wo"===t?r.ordinalNumber(a,{unit:"week"}):addLeadingZeros(a,t.length)},I:function(e,t,r){let n=getISOWeek(e);return"Io"===t?r.ordinalNumber(n,{unit:"week"}):addLeadingZeros(n,t.length)},d:function(e,t,r){return"do"===t?r.ordinalNumber(e.getDate(),{unit:"date"}):c.d(e,t)},D:function(e,t,r){let n=getDayOfYear(e);return"Do"===t?r.ordinalNumber(n,{unit:"dayOfYear"}):addLeadingZeros(n,t.length)},E:function(e,t,r){let n=e.getDay();switch(t){case"E":case"EE":case"EEE":return r.day(n,{width:"abbreviated",context:"formatting"});case"EEEEE":return r.day(n,{width:"narrow",context:"formatting"});case"EEEEEE":return r.day(n,{width:"short",context:"formatting"});default:return r.day(n,{width:"wide",context:"formatting"})}},e:function(e,t,r,n){let a=e.getDay(),i=(a-n.weekStartsOn+8)%7||7;switch(t){case"e":return String(i);case"ee":return addLeadingZeros(i,2);case"eo":return r.ordinalNumber(i,{unit:"day"});case"eee":return r.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return r.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return r.day(a,{width:"short",context:"formatting"});default:return r.day(a,{width:"wide",context:"formatting"})}},c:function(e,t,r,n){let a=e.getDay(),i=(a-n.weekStartsOn+8)%7||7;switch(t){case"c":return String(i);case"cc":return addLeadingZeros(i,t.length);case"co":return r.ordinalNumber(i,{unit:"day"});case"ccc":return r.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return r.day(a,{width:"narrow",context:"standalone"});case"cccccc":return r.day(a,{width:"short",context:"standalone"});default:return r.day(a,{width:"wide",context:"standalone"})}},i:function(e,t,r){let n=e.getDay(),a=0===n?7:n;switch(t){case"i":return String(a);case"ii":return addLeadingZeros(a,t.length);case"io":return r.ordinalNumber(a,{unit:"day"});case"iii":return r.day(n,{width:"abbreviated",context:"formatting"});case"iiiii":return r.day(n,{width:"narrow",context:"formatting"});case"iiiiii":return r.day(n,{width:"short",context:"formatting"});default:return r.day(n,{width:"wide",context:"formatting"})}},a:function(e,t,r){let n=e.getHours(),a=n/12>=1?"pm":"am";switch(t){case"a":case"aa":return r.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"aaa":return r.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return r.dayPeriod(a,{width:"narrow",context:"formatting"});default:return r.dayPeriod(a,{width:"wide",context:"formatting"})}},b:function(e,t,r){let n;let a=e.getHours();switch(n=12===a?l.noon:0===a?l.midnight:a/12>=1?"pm":"am",t){case"b":case"bb":return r.dayPeriod(n,{width:"abbreviated",context:"formatting"});case"bbb":return r.dayPeriod(n,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return r.dayPeriod(n,{width:"narrow",context:"formatting"});default:return r.dayPeriod(n,{width:"wide",context:"formatting"})}},B:function(e,t,r){let n;let a=e.getHours();switch(n=a>=17?l.evening:a>=12?l.afternoon:a>=4?l.morning:l.night,t){case"B":case"BB":case"BBB":return r.dayPeriod(n,{width:"abbreviated",context:"formatting"});case"BBBBB":return r.dayPeriod(n,{width:"narrow",context:"formatting"});default:return r.dayPeriod(n,{width:"wide",context:"formatting"})}},h:function(e,t,r){if("ho"===t){let t=e.getHours()%12;return 0===t&&(t=12),r.ordinalNumber(t,{unit:"hour"})}return c.h(e,t)},H:function(e,t,r){return"Ho"===t?r.ordinalNumber(e.getHours(),{unit:"hour"}):c.H(e,t)},K:function(e,t,r){let n=e.getHours()%12;return"Ko"===t?r.ordinalNumber(n,{unit:"hour"}):addLeadingZeros(n,t.length)},k:function(e,t,r){let n=e.getHours();return(0===n&&(n=24),"ko"===t)?r.ordinalNumber(n,{unit:"hour"}):addLeadingZeros(n,t.length)},m:function(e,t,r){return"mo"===t?r.ordinalNumber(e.getMinutes(),{unit:"minute"}):c.m(e,t)},s:function(e,t,r){return"so"===t?r.ordinalNumber(e.getSeconds(),{unit:"second"}):c.s(e,t)},S:function(e,t){return c.S(e,t)},X:function(e,t,r){let n=e.getTimezoneOffset();if(0===n)return"Z";switch(t){case"X":return formatTimezoneWithOptionalMinutes(n);case"XXXX":case"XX":return formatTimezone(n);default:return formatTimezone(n,":")}},x:function(e,t,r){let n=e.getTimezoneOffset();switch(t){case"x":return formatTimezoneWithOptionalMinutes(n);case"xxxx":case"xx":return formatTimezone(n);default:return formatTimezone(n,":")}},O:function(e,t,r){let n=e.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+formatTimezoneShort(n,":");default:return"GMT"+formatTimezone(n,":")}},z:function(e,t,r){let n=e.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+formatTimezoneShort(n,":");default:return"GMT"+formatTimezone(n,":")}},t:function(e,t,r){return addLeadingZeros(Math.trunc(+e/1e3),t.length)},T:function(e,t,r){return addLeadingZeros(+e,t.length)}};function formatTimezoneShort(e,t=""){let r=e>0?"-":"+",n=Math.abs(e),a=Math.trunc(n/60),i=n%60;return 0===i?r+String(a):r+String(a)+t+addLeadingZeros(i,2)}function formatTimezoneWithOptionalMinutes(e,t){if(e%60==0){let t=e>0?"-":"+";return t+addLeadingZeros(Math.abs(e)/60,2)}return formatTimezone(e,t)}function formatTimezone(e,t=""){let r=Math.abs(e),n=addLeadingZeros(Math.trunc(r/60),2),a=addLeadingZeros(r%60,2);return(e>0?"-":"+")+n+t+a}let dateLongFormatter=(e,t)=>{switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});default:return t.date({width:"full"})}},timeLongFormatter=(e,t)=>{switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});default:return t.time({width:"full"})}},g={p:timeLongFormatter,P:(e,t)=>{let r;let n=e.match(/(P+)(p+)?/)||[],a=n[1],i=n[2];if(!i)return dateLongFormatter(e,t);switch(a){case"P":r=t.dateTime({width:"short"});break;case"PP":r=t.dateTime({width:"medium"});break;case"PPP":r=t.dateTime({width:"long"});break;default:r=t.dateTime({width:"full"})}return r.replace("{{date}}",dateLongFormatter(a,t)).replace("{{time}}",timeLongFormatter(i,t))}},h=/^D+$/,m=/^Y+$/,w=["D","DD","YY","YYYY"];function isProtectedDayOfYearToken(e){return h.test(e)}function isProtectedWeekYearToken(e){return m.test(e)}function warnOrThrowProtectedError(e,t,r){let n=message(e,t,r);if(console.warn(n),w.includes(e))throw RangeError(n)}function message(e,t,r){let n="Y"===e[0]?"years":"days of the month";return`Use \`${e.toLowerCase()}\` instead of \`${e}\` (in \`${t}\`) for formatting ${n} to the input \`${r}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}function isDate(e){return e instanceof Date||"object"==typeof e&&"[object Date]"===Object.prototype.toString.call(e)}function isValid(e){return!(!isDate(e)&&"number"!=typeof e||isNaN(+(0,s.Q)(e)))}let b=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,k=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,O=/^'([^]*?)'?$/,y=/''/g,L=/[a-zA-Z]/;function format(e,t,r){let i=(0,a.j)(),o=r?.locale??i.locale??n._,u=r?.firstWeekContainsDate??r?.locale?.options?.firstWeekContainsDate??i.firstWeekContainsDate??i.locale?.options?.firstWeekContainsDate??1,d=r?.weekStartsOn??r?.locale?.options?.weekStartsOn??i.weekStartsOn??i.locale?.options?.weekStartsOn??0,c=(0,s.Q)(e,r?.in);if(!isValid(c))throw RangeError("Invalid time value");let l=t.match(k).map(e=>{let t=e[0];if("p"===t||"P"===t){let r=g[t];return r(e,o.formatLong)}return e}).join("").match(b).map(e=>{if("''"===e)return{isToken:!1,value:"'"};let t=e[0];if("'"===t)return{isToken:!1,value:cleanEscapedString(e)};if(f[t])return{isToken:!0,value:e};if(t.match(L))throw RangeError("Format string contains an unescaped latin alphabet character `"+t+"`");return{isToken:!1,value:e}});o.localize.preprocessor&&(l=o.localize.preprocessor(c,l));let h={firstWeekContainsDate:u,weekStartsOn:d,locale:o};return l.map(n=>{if(!n.isToken)return n.value;let a=n.value;(!r?.useAdditionalWeekYearTokens&&isProtectedWeekYearToken(a)||!r?.useAdditionalDayOfYearTokens&&isProtectedDayOfYearToken(a))&&warnOrThrowProtectedError(a,t,String(e));let i=f[a[0]];return i(c,a,o.localize,h)}).join("")}function cleanEscapedString(e){let t=e.match(O);return t?t[1].replace(y,"'"):e}let p=format}};