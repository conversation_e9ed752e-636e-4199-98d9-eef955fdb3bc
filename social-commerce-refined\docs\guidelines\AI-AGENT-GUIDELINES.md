# 🚨 AI AGENT GUIDELINES - SOCIAL COMMERCE PROJECT

## **⚠️ CRITICAL: READ BEFORE ANY OPERATIONS**

**ALL AI AGENTS MUST READ AND FOLLOW THESE GUIDELINES BEFORE PERFORMING ANY OPERATIONS**

---

## **📍 STEP 1: VERIFY WORKING DIRECTORY**

### **🔍 ROOT CAUSE ANALYSIS - DIRECTORY SWITCHING ISSUE**

**CRITICAL PROBLEM IDENTIFIED:**
Each `launch-process` command starts a **NEW terminal session** that defaults to the parent directory `/c/Users/<USER>/Documents/augment/social-commerce` instead of maintaining working directory context.

**ROOT CAUSE:** Terminal sessions don't persist working directory between `launch-process` calls.

### **✅ MANDATORY SOLUTION - COMMAND PATTERN**

**EVERY `launch-process` command MUST use this pattern:**
```bash
cd /c/Users/<USER>/Documents/augment/social-commerce/social-commerce-refined && [actual command]
```

**VERIFICATION COMMAND (run this FIRST):**
```bash
cd /c/Users/<USER>/Documents/augment/social-commerce/social-commerce-refined && pwd && echo "✅ Correct directory confirmed"
```

**Expected output:**
```
/c/Users/<USER>/Documents/augment/social-commerce/social-commerce-refined
✅ Correct directory confirmed
```

**❌ WRONG (causes directory switching):**
```bash
docker-compose ps
```

**✅ CORRECT (prevents directory switching):**
```bash
cd /c/Users/<USER>/Documents/augment/social-commerce/social-commerce-refined && docker-compose ps
```

---

## **📋 STEP 2: UNDERSTAND PROJECT STRUCTURE**

**MANDATORY: Our established structure:**

```
social-commerce/
├── social-commerce-refined/          ← MAIN WORKING DIRECTORY
│   ├── services/                     ← All microservices go here
│   │   ├── user-service/            ← ✅ Working service
│   │   ├── api-gateway/             ← ✅ Correct location
│   │   ├── product-service/
│   │   └── [other-services]/
│   ├── libs/                        ← Shared libraries
│   │   ├── common/                  ← Common utilities
│   │   ├── messaging/               ← Message queue utilities
│   │   └── testing/                 ← Testing utilities
│   │       └── integration/         ← Integration tests go here
│   ├── tools/                       ← Development tools
│   │   ├── dev-cli/                 ← ✅ CLI tools
│   │   └── scripts/                 ← Build/deployment scripts
│   ├── frontend/                    ← Frontend application
│   ├── docs/                        ← Documentation
│   │   └── guidelines/              ← This file location
│   └── infrastructure/              ← Docker, K8s, etc.
├── backend/                         ← Legacy backend (reference only)
└── frontend/                        ← Legacy frontend (reference only)
```

---

## **🚫 ANTI-DUPLICATION RULES**

### **❌ NEVER CREATE THESE LOCATIONS:**
- `./api-gateway` (use `./social-commerce-refined/services/api-gateway`)
- `./integration-tests` (use `./social-commerce-refined/libs/testing/integration`)
- `./dev-cli` (use `./social-commerce-refined/tools/dev-cli`)
- `./services/` (use `./social-commerce-refined/services/`)
- `./scripts/` (use `./social-commerce-refined/tools/scripts/`)

### **✅ ALWAYS USE CORRECT LOCATIONS:**
- **Services**: `./social-commerce-refined/services/[service-name]/`
- **Integration Tests**: `./social-commerce-refined/libs/testing/integration/`
- **Scripts**: `./social-commerce-refined/tools/scripts/`
- **CLI Tools**: `./social-commerce-refined/tools/dev-cli/`
- **Documentation**: `./social-commerce-refined/docs/`

---

## **🎯 ABSOLUTE PATHS PROTOCOL**

**✅ ALWAYS USE:**
```bash
# In save-file operations
path: "social-commerce-refined/services/user-service/src/main.ts"

# In launch-process operations
cwd: "/c/Users/<USER>/Documents/augment/social-commerce/social-commerce-refined/services/user-service"
```

**❌ NEVER USE:**
```bash
# Relative paths that create duplicates!
path: "services/user-service/src/main.ts"  # Creates wrong location!
```

---

## **🔧 VERIFICATION CHECKLIST**

**Before ANY operation, verify:**
- [ ] Current working directory is `/c/Users/<USER>/Documents/augment/social-commerce/social-commerce-refined`
- [ ] the paren directory is `/c/Users/<USER>/Documents/augment/social-commerce` and archived. do not create any file and     directory or implementation on it.
- [ ] No duplicate directories will be created
- [ ] Using correct paths according to established structure
- [ ] Following existing guidelines and patterns

---

## **🚨 EMERGENCY CLEANUP PROTOCOL**

**If you create wrong directories or files:**
1. **STOP** immediately
2. **Check for important code** in wrong location
3. **Move code** to correct location if needed
4. **Remove** wrong directories/files
5. **Verify** correct structure
6. **Continue** with proper paths

---

## **📝 MANDATORY DOCUMENTATION PROTOCOL**

### **🔍 ISSUE & SOLUTION DOCUMENTATION RULE**

**ALL AI AGENTS MUST DOCUMENT EVERY ISSUE AND SOLUTION:**

**✅ REQUIRED ACTIONS:**
1. **Document ALL issues encountered** during development
2. **Document ALL solutions implemented** with code examples
3. **Create comprehensive documentation files** in `docs/development/`
4. **Update documentation index** after adding new files
5. **Include technical details** (code before/after, root causes, impact)

**📁 DOCUMENTATION STRUCTURE:**
```
social-commerce-refined/docs/development/
├── README.md                              ← Index of all documentation
├── authentication-issues-solutions.md    ← Auth system issues & fixes
├── circular-reference-issues-solutions.md ← Dependency issues & fixes
├── docker-images-usage.md               ← Docker setup & troubleshooting
├── [feature-name]-issues-solutions.md   ← Feature-specific documentation
└── [component-name]-debugging.md        ← Component-specific debugging
```

**📋 DOCUMENTATION TEMPLATE:**
```markdown
# [Feature/Component] - Issues & Solutions Documentation

## Overview
Brief description of the feature/component and issues encountered.

## Critical Issues & Solutions

### 1. **[Issue Name]** ⭐ **CRITICAL FIX** (if critical)

**Problem:** Clear description of the issue
**Symptoms:** What the user/developer experiences
**Root Cause:** Technical explanation of why it happens
**Code Before (Problematic):** [code example]
**Code After (Fixed):** [code example]
**Solution Details:** Step-by-step explanation
**Impact:** What this fix enables

## Files Modified
List all files changed with brief description

## Testing Results
What was verified to work after the fix

## Future Improvements
Recommendations for enhancement
```

**🚨 MANDATORY TRIGGERS:**
Document when you encounter:
- ❌ **Any error or failure** during implementation
- 🔧 **Any bug fix or workaround** applied
- 🔄 **Any circular dependency resolution**
- 🐳 **Any Docker/infrastructure issue**
- 🔐 **Any authentication/security fix**
- 📊 **Any database schema change**
- 🎯 **Any architectural decision or change**

**✅ DOCUMENTATION WORKFLOW:**
1. **Encounter issue** → Document immediately
2. **Implement solution** → Update documentation with fix
3. **Verify solution** → Add testing results
4. **Complete feature** → Update `docs/development/README.md` index
5. **Continue development** → Repeat for next issues

**📍 LOCATION REQUIREMENTS:**
- **All documentation** → `social-commerce-refined/docs/development/`
- **Index file** → `social-commerce-refined/docs/development/README.md`
- **Follow naming** → `[component]-issues-solutions.md`

---

## **📏 MANDATORY SERVICE NAMING CONVENTIONS**

### **🔍 NAMING CONVENTION COMPLIANCE RULE**

**ALL AI AGENTS MUST FOLLOW ESTABLISHED NAMING CONVENTIONS:**

**✅ REQUIRED ACTIONS:**
1. **Check existing naming patterns** before creating new services
2. **Follow established conventions** for all new components
3. **Validate naming compliance** before implementation
4. **Reference naming guidelines** for all service creation
5. **Maintain consistency** across all environments

**📋 NAMING CONVENTION REFERENCE:**
- **Full Guidelines:** `docs/guidelines/SERVICE-NAMING-CONVENTIONS.md`
- **Must be consulted** before creating any new service, database, container, or queue

**🎯 MANDATORY PATTERNS:**

**Database Names:**
- Pattern: `{service_name}_service`
- Examples: `user_service`, `store_service`, `product_service`

**Docker Images:**
- Services: `{service-name}-service`
- Infrastructure: `social-commerce-{component}`
- Examples: `user-service`, `store-service`, `social-commerce-frontend`

**Container Names:**
- Pattern: `social-commerce-{component}`
- Examples: `social-commerce-user-service`, `social-commerce-postgres`

**Queue Names:**
- Pattern: `{service_name}_queue`
- Examples: `user_queue`, `store_queue`, `product_queue`

**Environment Variables:**
- Service URLs: `{SERVICE_NAME}_SERVICE_URL`
- Database: `DB_DATABASE_{SERVICE_NAME}`
- Examples: `USER_SERVICE_URL`, `DB_DATABASE_USER`

**🚨 MANDATORY TRIGGERS:**
Apply naming conventions when creating:
- ✅ **New microservices** → Follow service naming patterns
- ✅ **New databases** → Follow database naming patterns
- ✅ **New Docker containers** → Follow container naming patterns
- ✅ **New message queues** → Follow queue naming patterns
- ✅ **New environment variables** → Follow variable naming patterns
- ✅ **New directories** → Follow directory structure patterns

**✅ NAMING VALIDATION WORKFLOW:**
1. **Before creating** → Check `SERVICE-NAMING-CONVENTIONS.md`
2. **During creation** → Apply correct naming patterns
3. **After creation** → Verify compliance with guidelines
4. **Document** → Update relevant documentation with new names
5. **Test** → Ensure naming works across all environments

**📍 COMPLIANCE REQUIREMENTS:**
- **All new services** → Must follow naming conventions
- **All new components** → Must use established patterns
- **All environment configs** → Must use standard variable names
- **All documentation** → Must reference correct names

**🚫 FORBIDDEN ACTIONS:**
- ❌ Creating services without checking naming guidelines
- ❌ Using inconsistent naming patterns
- ❌ Ignoring established conventions
- ❌ Creating ad-hoc naming schemes

---

## **🏗️ MANDATORY TEMPLATE CONSISTENCY RULES**

### **🔍 TEMPLATE CONSISTENCY COMPLIANCE RULE**

**ALL AI AGENTS MUST FOLLOW STANDARDIZED SERVICE TEMPLATING:**

**✅ REQUIRED ACTIONS:**
1. **ALWAYS** verify template consistency before declaring service-specific issues
2. **MANDATORY** use User Service as exact template for all new services
3. **PROHIBITED** creating services with different base configurations
4. **REQUIRED** question configuration differences before accepting them as legitimate

**📋 TEMPLATE CONSISTENCY REFERENCE:**
- **Full Guidelines:** `docs/guidelines/STANDARDIZED-SERVICE-TEMPLATING-RULES.md`
- **Must be consulted** before creating any new service or modifying existing services

**🎯 MANDATORY PATTERNS:**

**Service Creation Process:**
1. **Copy User Service exactly** - Use file copying, not manual recreation
2. **Verify template consistency** - Before first build
3. **Document legitimate differences** - Only after template verification
4. **Apply standardization first** - Before service-specific analysis

**Configuration Files (MUST BE IDENTICAL):**
- `tsconfig.json` - Exact copy from User Service
- `nest-cli.json` - Must include `"webpack": true`
- `Dockerfile` - Must use `CMD ["node", "dist/main.js"]`
- Base `package.json` structure

**🚨 CRITICAL ENFORCEMENT:**
- **Template violations** are treated as critical bugs
- **Configuration drift** must be fixed immediately
- **Service-specific analysis** only after template verification
- **Strategic questioning** of configuration differences is mandatory

**🚫 FORBIDDEN ACTIONS:**
- ❌ Creating services with different TypeScript configurations
- ❌ Accepting configuration differences without investigation
- ❌ Implementing "service-specific" solutions without template verification
- ❌ Manual recreation of configuration files

---

**🎯 SUCCESS = Following established structure + No duplicates + Correct paths + Complete documentation + Naming conventions compliance + Template consistency**
