"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var EventPublisherModule_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventPublisherModule = void 0;
const common_1 = require("@nestjs/common");
const rabbitmq_module_1 = require("./rabbitmq.module");
const event_publisher_service_1 = require("./event-publisher.service");
let EventPublisherModule = EventPublisherModule_1 = class EventPublisherModule {
    static register(options) {
        return {
            module: EventPublisherModule_1,
            imports: [rabbitmq_module_1.RabbitMQModule.register(options)],
            providers: [event_publisher_service_1.EventPublisherService],
            exports: [event_publisher_service_1.EventPublisherService],
        };
    }
    static registerAsync(options) {
        return {
            module: EventPublisherModule_1,
            imports: [rabbitmq_module_1.RabbitMQModule.registerAsync(options)],
            providers: [event_publisher_service_1.EventPublisherService],
            exports: [event_publisher_service_1.EventPublisherService],
        };
    }
};
exports.EventPublisherModule = EventPublisherModule;
exports.EventPublisherModule = EventPublisherModule = EventPublisherModule_1 = __decorate([
    (0, common_1.Module)({})
], EventPublisherModule);
//# sourceMappingURL=event-publisher.module.js.map