# Documentation Example

This document provides examples of correctly implemented documentation following the Social Commerce Platform's patterns and best practices.

## Table of Contents

1. [Code Documentation](#code-documentation)
2. [API Documentation](#api-documentation)
3. [README Documentation](#readme-documentation)
4. [Best Practices](#best-practices)

## Code Documentation

### Class Documentation

```typescript
/**
 * Service responsible for user management operations
 * 
 * This service handles all user-related operations including:
 * - User creation and registration
 * - User profile management
 * - User authentication
 * - Email and phone verification
 * 
 * @class UserService
 */
@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly verificationService: VerificationService,
    private readonly emailService: EmailService,
  ) {}

  // Method implementations...
}
```

### Method Documentation

```typescript
/**
 * Creates a new user account
 * 
 * This method:
 * 1. Validates the user input
 * 2. Checks if a user with the same email already exists
 * 3. Hashes the password
 * 4. Creates the user record
 * 5. Sends a verification email
 * 
 * @param createUserDto - The data transfer object containing user information
 * @returns The newly created user entity (without password)
 * @throws AppError if a user with the same email already exists
 * @throws AppError if there's an error during user creation
 */
async create(createUserDto: CreateUserDto): Promise<User> {
  this.logger.log(`Creating user with email: ${createUserDto.email}`);

  try {
    // Implementation...
  } catch (error) {
    // Error handling...
  }
}
```

### Interface Documentation

```typescript
/**
 * Represents the structure of a user in the system
 * 
 * @interface User
 */
export interface User {
  /**
   * Unique identifier for the user
   */
  id: string;

  /**
   * User's email address (must be unique)
   */
  email: string;

  /**
   * User's full name
   */
  name: string;

  /**
   * User's hashed password
   */
  password: string;

  /**
   * Whether the user's email has been verified
   */
  isEmailVerified: boolean;

  /**
   * Whether the user's phone has been verified
   */
  isPhoneVerified: boolean;

  /**
   * User's phone number (optional)
   */
  phone?: string;

  /**
   * URL to the user's profile picture (optional)
   */
  profilePicture?: string;

  /**
   * When the user was created
   */
  createdAt: Date;

  /**
   * When the user was last updated
   */
  updatedAt: Date;
}
```

## API Documentation

### Swagger Decorators

```typescript
@ApiTags('users')
@Controller('users')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post()
  @Public()
  @ApiOperation({ summary: 'Create a new user' })
  @ApiDescription('Creates a new user account and sends a verification email')
  @ApiBody({ type: CreateUserDto })
  @ApiResponse({ 
    status: 201, 
    description: 'User created successfully', 
    type: UserResponseDto 
  })
  @ApiResponse({ 
    status: 400, 
    description: 'Bad request - Invalid input or email already exists',
    type: ErrorResponseDto
  })
  @ApiResponse({ 
    status: 500, 
    description: 'Internal server error',
    type: ErrorResponseDto
  })
  async create(@Body() createUserDto: CreateUserDto): Promise<UserResponseDto> {
    // Implementation...
  }
}
```

### DTO Documentation

```typescript
/**
 * Data transfer object for creating a new user
 */
export class CreateUserDto {
  /**
   * User's email address (must be unique)
   * @example "<EMAIL>"
   */
  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  /**
   * User's password (min 8 characters, max 100 characters)
   * @example "password123"
   */
  @ApiProperty({
    description: 'User password (min 8 characters)',
    example: 'password123',
    minLength: 8,
    maxLength: 100,
  })
  @IsString()
  @MinLength(8)
  @MaxLength(100)
  password: string;

  /**
   * User's full name
   * @example "John Doe"
   */
  @ApiProperty({
    description: 'User full name',
    example: 'John Doe',
  })
  @IsString()
  @IsNotEmpty()
  name: string;
}
```

## README Documentation

### Module README Example

```markdown
# User Module

This module handles all user-related functionality in the Social Commerce Platform.

## Features

- User registration and authentication
- Profile management
- Email and phone verification
- Password reset
- User search and filtering
- Following/follower relationships

## Architecture

The User Module follows a layered architecture:

- **Controllers**: Handle HTTP requests and responses
- **Services**: Implement business logic
- **Repositories**: Handle data access
- **Entities**: Define the data model
- **DTOs**: Define data transfer objects for validation and serialization

## API Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|--------------|
| POST | /users | Create a new user | No |
| GET | /users | Get all users | Yes |
| GET | /users/:id | Get a user by ID | Yes |
| PUT | /users/:id | Update a user | Yes |
| DELETE | /users/:id | Delete a user | Yes |
| POST | /users/verify-email | Verify email | No |
| POST | /users/reset-password | Reset password | No |

## Usage Examples

### Creating a User

```typescript
// Example code for creating a user
const userService = app.get(UserService);
const newUser = await userService.create({
  email: '<EMAIL>',
  password: 'password123',
  name: 'John Doe',
});
```

## Dependencies

- NestJS
- TypeORM
- PostgreSQL
- JWT
- Nodemailer

## Testing

Run the tests with:

```bash
npm run test:user-service
```
```

## Best Practices

When documenting code for the Social Commerce Platform, follow these best practices:

1. **Use JSDoc comments** for all classes, methods, and properties
2. **Include examples** where appropriate
3. **Document parameters and return values** with types and descriptions
4. **Document exceptions** that may be thrown
5. **Keep documentation up-to-date** when code changes
6. **Use Swagger decorators** for API documentation
7. **Create README files** for modules and important components
8. **Use consistent formatting** across all documentation
9. **Include usage examples** for complex functionality
10. **Document both public and private APIs** (with appropriate visibility)

By following these patterns and best practices, AI assistants can ensure they create documentation that adheres to the project's standards and effectively communicates the functionality and usage of the code.
