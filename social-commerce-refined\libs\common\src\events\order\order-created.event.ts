import { BaseEvent } from '../base-event.interface';

/**
 * Event emitted when a new order is created
 */
export class OrderCreatedEvent implements BaseEvent<OrderCreatedPayload> {
  id: string;
  type: string = 'order.created';
  version: string = '1.0';
  timestamp: string;
  producer: string = 'order-service';
  payload: OrderCreatedPayload;

  constructor(payload: OrderCreatedPayload) {
    this.id = payload.id;
    this.timestamp = new Date().toISOString();
    this.payload = payload;
  }
}

/**
 * Payload for OrderCreatedEvent
 */
export interface OrderCreatedPayload {
  /**
   * Order ID
   */
  id: string;

  /**
   * User ID
   */
  userId: string;

  /**
   * Total amount
   */
  totalAmount: number;

  /**
   * Order status
   */
  status: string;

  /**
   * Order items
   */
  items: OrderItemPayload[];

  /**
   * Order creation timestamp
   */
  createdAt: string;
}

/**
 * Order item payload
 */
export interface OrderItemPayload {
  /**
   * Product ID
   */
  productId: string;

  /**
   * Product name
   */
  productName: string;

  /**
   * Quantity
   */
  quantity: number;

  /**
   * Price per unit
   */
  price: number;
}
