"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CartItemRemovedEvent = void 0;
class CartItemRemovedEvent {
    constructor(payload) {
        this.type = 'cart.item.removed';
        this.version = '1.0';
        this.producer = 'cart-service';
        this.id = `${payload.cartId}-${payload.productId}-${Date.now()}`;
        this.timestamp = new Date().toISOString();
        this.payload = payload;
    }
}
exports.CartItemRemovedEvent = CartItemRemovedEvent;
//# sourceMappingURL=cart-item-removed.event.js.map