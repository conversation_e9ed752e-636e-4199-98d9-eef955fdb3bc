exports.id=8576,exports.ids=[8576],exports.modules={47642:(r,s,a)=>{Promise.resolve().then(a.bind(a,44967))},44967:(r,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>DashboardLayout});var e=a(30784);a(9885);var d=a(27870),o=a(14379),t=a(11440),i=a.n(t);function DashboardLayout({children:r}){let{t:s}=(0,d.$G)("common"),{isRtl:a}=(0,o.g)();return(0,e.jsxs)("div",{className:`min-h-screen flex ${a?"text-right":"text-left"}`,children:[(0,e.jsxs)("div",{className:"w-64 bg-gray-800 text-white",children:[e.jsx("div",{className:"p-4",children:e.jsx("h1",{className:"text-xl font-bold",children:s("dashboard.title","Dashboard")})}),e.jsx("nav",{className:"mt-6",children:(0,e.jsxs)("ul",{children:[e.jsx("li",{children:e.jsx(i(),{href:"/dashboard",className:"block py-2.5 px-4 rounded transition duration-200 hover:bg-gray-700",children:s("dashboard.overview","Overview")})}),e.jsx("li",{children:e.jsx(i(),{href:"/dashboard/products",className:"block py-2.5 px-4 rounded transition duration-200 hover:bg-gray-700",children:s("dashboard.products","Products")})}),e.jsx("li",{children:e.jsx(i(),{href:"/dashboard/orders",className:"block py-2.5 px-4 rounded transition duration-200 hover:bg-gray-700",children:s("dashboard.orders","Orders")})}),e.jsx("li",{children:e.jsx(i(),{href:"/dashboard/customers",className:"block py-2.5 px-4 rounded transition duration-200 hover:bg-gray-700",children:s("dashboard.customers","Customers")})}),e.jsx("li",{children:e.jsx(i(),{href:"/dashboard/analytics",className:"block py-2.5 px-4 rounded transition duration-200 hover:bg-gray-700",children:s("dashboard.analytics","Analytics")})}),e.jsx("li",{children:e.jsx(i(),{href:"/dashboard/settings",className:"block py-2.5 px-4 rounded transition duration-200 hover:bg-gray-700",children:s("dashboard.settings","Settings")})})]})})]}),e.jsx("div",{className:"flex-1 bg-gray-100 dark:bg-gray-900",children:r})]})}},68182:(r,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>t,__esModule:()=>o,default:()=>n});var e=a(95153);let d=(0,e.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\dashboard\layout.tsx`),{__esModule:o,$$typeof:t}=d,i=d.default,n=i}};