(()=>{var e={};e.id=8994,e.ids=[8994],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},96713:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>u,tree:()=>n});var r=a(67096),s=a(16132),i=a(37284),l=a.n(i),c=a(32564),d={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>c[e]);a.d(t,d);let n=["",{children:["activity",{children:["feed",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,50150)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\activity\\feed\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,9291,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\activity\\feed\\page.tsx"],x="/activity/feed/page",m={require:a,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/activity/feed/page",pathname:"/activity/feed",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},12697:(e,t,a)=>{Promise.resolve().then(a.bind(a,90673))},90673:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>ActivityFeedPage});var r=a(30784),s=a(9885),i=a(27870),l=a(53838),c=a(11440),d=a.n(c),n=a(52451),o=a.n(n),x=a(73531),m=a(3902);let activity_ActivityItem=({activity:e,className:t=""})=>{let{t:a}=(0,i.$G)("social");return r.jsx("div",{className:`p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm ${t}`,children:(0,r.jsxs)("div",{className:"flex items-start",children:[r.jsx(d(),{href:`/profile/${e.user?.username||e.userId}`,className:"flex-shrink-0",children:r.jsx("div",{className:"w-10 h-10 relative rounded-full overflow-hidden",children:r.jsx(o(),{src:e.user?.avatarUrl||"/images/default-avatar.png",alt:e.user?.displayName||"User",fill:!0,className:"object-cover"})})}),(0,r.jsxs)("div",{className:"ml-3 flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx(d(),{href:`/profile/${e.user?.username||e.userId}`,className:"font-medium text-gray-900 dark:text-gray-100 hover:underline",children:e.user?.displayName||"User"}),r.jsx("span",{className:"mx-1 text-gray-500 dark:text-gray-400",children:(()=>{switch(e.type){case m.T8.PRODUCT_VIEW:return a("viewedProduct","viewed a product");case m.T8.PRODUCT_PURCHASE:return a("purchasedProduct","purchased a product");case m.T8.PRODUCT_REVIEW:return a("reviewedProduct","reviewed a product");case m.T8.PRODUCT_SHARE:return a("sharedProduct","shared a product");case m.T8.PRODUCT_LIKE:return a("likedProduct","liked a product");case m.T8.PRODUCT_COMMENT:return a("commentedOnProduct","commented on a product");case m.T8.STORE_FOLLOW:return a("followedStore","followed a store");case m.T8.USER_FOLLOW:return a("followedUser","followed a user");case m.T8.GROUP_JOIN:return a("joinedGroup","joined a group");case m.T8.GROUP_CREATE:return a("createdGroup","created a group");case m.T8.POST_CREATE:return a("createdPost","created a post");case m.T8.POST_LIKE:return a("likedPost","liked a post");case m.T8.POST_COMMENT:return a("commentedOnPost","commented on a post");case m.T8.POST_SHARE:return a("sharedPost","shared a post");default:return a("performedAction","performed an action")}})()}),r.jsx(d(),{href:(()=>{switch(e.type){case m.T8.PRODUCT_VIEW:case m.T8.PRODUCT_PURCHASE:case m.T8.PRODUCT_REVIEW:case m.T8.PRODUCT_SHARE:case m.T8.PRODUCT_LIKE:case m.T8.PRODUCT_COMMENT:return`/products/${e.entityId}`;case m.T8.STORE_FOLLOW:return`/stores/${e.entityId}`;case m.T8.USER_FOLLOW:return`/profile/${e.entityId}`;case m.T8.GROUP_JOIN:case m.T8.GROUP_CREATE:return`/groups/${e.entityId}`;case m.T8.POST_CREATE:case m.T8.POST_LIKE:case m.T8.POST_COMMENT:case m.T8.POST_SHARE:return`/social/posts/${e.entityId}`;default:return"#"}})(),className:"font-medium text-primary-600 dark:text-primary-400 hover:underline",children:e.entityType})]}),r.jsx("div",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:(e=>{try{return(0,x.Z)(new Date(e),{addSuffix:!0})}catch(t){return e}})(e.createdAt)})]}),r.jsx("div",{className:"ml-3 flex-shrink-0",children:(()=>{switch(e.type){case m.T8.PRODUCT_VIEW:return(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-blue-500",viewBox:"0 0 20 20",fill:"currentColor",children:[r.jsx("path",{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"}),r.jsx("path",{fillRule:"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z",clipRule:"evenodd"})]});case m.T8.PRODUCT_PURCHASE:return r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-green-500",viewBox:"0 0 20 20",fill:"currentColor",children:r.jsx("path",{d:"M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"})});case m.T8.PRODUCT_REVIEW:return r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-yellow-500",viewBox:"0 0 20 20",fill:"currentColor",children:r.jsx("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})});case m.T8.USER_FOLLOW:case m.T8.STORE_FOLLOW:return r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-purple-500",viewBox:"0 0 20 20",fill:"currentColor",children:r.jsx("path",{d:"M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z"})});case m.T8.POST_CREATE:return r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-indigo-500",viewBox:"0 0 20 20",fill:"currentColor",children:r.jsx("path",{fillRule:"evenodd",d:"M12 1.586l-4 4v12.828l4-4V1.586zM3.707 3.293A1 1 0 002 4v10a1 1 0 00.293.707L6 18.414V5.586L3.707 3.293zM17.707 5.293L14 1.586v12.828l2.293 2.293A1 1 0 0018 16V6a1 1 0 00-.293-.707z",clipRule:"evenodd"})});default:return r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-gray-500",viewBox:"0 0 20 20",fill:"currentColor",children:r.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})})}})()})]})})};var u=a(59872);let activity_ActivityList=({activities:e,isLoading:t=!1,hasMore:a=!1,onLoadMore:s,className:l=""})=>{let{t:c}=(0,i.$G)("social");return t&&0===e.length?r.jsx("div",{className:`space-y-4 ${l}`,children:Array.from({length:3}).map((e,t)=>r.jsx("div",{className:"p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm animate-pulse",children:(0,r.jsxs)("div",{className:"flex items-start",children:[r.jsx("div",{className:"w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full"}),(0,r.jsxs)("div",{className:"ml-3 flex-1",children:[r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"}),r.jsx("div",{className:"h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/4"})]})]})},t))}):0===e.length?(0,r.jsxs)("div",{className:`p-6 bg-white dark:bg-gray-800 rounded-lg text-center ${l}`,children:[r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-12 w-12 text-gray-400 dark:text-gray-600 mx-auto mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),r.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:c("noActivities","No activities to display")})]}):(0,r.jsxs)("div",{className:`space-y-4 ${l}`,children:[e.map(e=>r.jsx(activity_ActivityItem,{activity:e},e.id)),a&&r.jsx("div",{className:"flex justify-center mt-4",children:r.jsx(u.Z,{variant:"outline",onClick:s,isLoading:t,disabled:t,children:c("loadMore","Load More")})})]})},activity_ActivityFilter=({filters:e,onFilterChange:t,className:a=""})=>{let{t:l}=(0,i.$G)("social"),[c,d]=(0,s.useState)(e),handleActivityTypeChange=(e,t)=>{let a;let r=c.types||[];a=t?[...r,e]:r.filter(t=>t!==e);let s={...c,types:a};d(s)},handleDateChange=(e,t)=>{let a={...c,[e]:t};d(a)};return(0,r.jsxs)("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 ${a}`,children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4",children:l("filterActivities","Filter Activities")}),(0,r.jsxs)("div",{className:"mb-4",children:[r.jsx("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:l("activityTypes","Activity Types")}),r.jsx("div",{className:"space-y-2",children:Object.values(m.T8).map(e=>(0,r.jsxs)("label",{className:"flex items-center",children:[r.jsx("input",{type:"checkbox",className:"rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50",checked:c.types?.includes(e)||!1,onChange:t=>handleActivityTypeChange(e,t.target.checked)}),r.jsx("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:l(`activityType.${e}`,e)})]},e))})]}),(0,r.jsxs)("div",{className:"mb-4",children:[r.jsx("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:l("visibility","Visibility")}),(0,r.jsxs)("select",{className:"w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-700 dark:text-gray-300 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50",value:c.visibility||"",onChange:e=>{let t=e.target.value,a={...c,visibility:t};d(a)},children:[r.jsx("option",{value:"",children:l("allVisibilities","All Visibilities")}),r.jsx("option",{value:m.uk.PUBLIC,children:l("public","Public")}),r.jsx("option",{value:m.uk.CONNECTIONS,children:l("connections","Connections Only")}),r.jsx("option",{value:m.uk.PRIVATE,children:l("private","Private")})]})]}),(0,r.jsxs)("div",{className:"mb-4",children:[r.jsx("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:l("dateRange","Date Range")}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-xs text-gray-500 dark:text-gray-400 mb-1",children:l("from","From")}),r.jsx("input",{type:"date",className:"w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-700 dark:text-gray-300 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50",value:c.startDate||"",onChange:e=>handleDateChange("startDate",e.target.value)})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-xs text-gray-500 dark:text-gray-400 mb-1",children:l("to","To")}),r.jsx("input",{type:"date",className:"w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-700 dark:text-gray-300 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50",value:c.endDate||"",onChange:e=>handleDateChange("endDate",e.target.value)})]})]})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx(u.Z,{variant:"primary",onClick:()=>{t(c)},className:"flex-1",children:l("apply","Apply")}),r.jsx(u.Z,{variant:"outline",onClick:()=>{let e={};d(e),t(e)},className:"flex-1",children:l("reset","Reset")})]})]})},activity_ActivityFeed=({activities:e,isLoading:t=!1,hasMore:a=!1,onLoadMore:l,onFilterChange:c,className:d=""})=>{let{t:n}=(0,i.$G)("social"),[o,x]=(0,s.useState)({});return(0,r.jsxs)("div",{className:`grid grid-cols-1 lg:grid-cols-4 gap-6 ${d}`,children:[r.jsx("div",{className:"lg:col-span-1",children:r.jsx(activity_ActivityFilter,{filters:o,onFilterChange:e=>{x(e),c?.(e)},className:"sticky top-8"})}),(0,r.jsxs)("div",{className:"lg:col-span-3",children:[r.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6",children:n("activityFeed","Activity Feed")}),r.jsx(activity_ActivityList,{activities:e,isLoading:t,hasMore:a,onLoadMore:l})]})]})};function ActivityFeedPage(){let{t:e}=(0,i.$G)("social"),[t,a]=(0,s.useState)(1),[c,d]=(0,s.useState)([]),{data:n,isLoading:o,error:x}=(0,l.k_)({page:t,limit:10,types:c.length>0?c:void 0}),[m]=(0,l.XX)(),handleDeleteActivity=async e=>{try{await m(e).unwrap()}catch(e){console.error("Failed to delete activity:",e)}};return o&&!n?r.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 py-8",children:r.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:r.jsx("div",{className:"flex items-center justify-center py-12",children:r.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"})})})}):x?r.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 py-8",children:r.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center py-12",children:[r.jsx("p",{className:"text-red-500 dark:text-red-400 mb-4",children:e("activity.errorLoading","Error loading activity feed")}),r.jsx(u.Z,{variant:"outline",onClick:()=>a(1),children:e("common.retry","Retry")})]})})}):r.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 py-8",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"mb-6",children:[r.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:e("activity.feed","Activity Feed")}),r.jsx("p",{className:"text-gray-500 dark:text-gray-400 mt-1",children:e("activity.feedDescription","See what your connections have been up to")})]}),r.jsx(activity_ActivityFeed,{activities:n?.activities||[],isLoading:o,hasMore:!!n&&n.total>10*t,onLoadMore:()=>{a(t+1)},onFilterChange:e=>{d(e),a(1)},onDeleteActivity:handleDeleteActivity})]})})}},50150:(e,t,a)=>{"use strict";a.r(t),a.d(t,{$$typeof:()=>l,__esModule:()=>i,default:()=>d});var r=a(95153);let s=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\activity\feed\page.tsx`),{__esModule:i,$$typeof:l}=s,c=s.default,d=c}};var t=require("../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),a=t.X(0,[2103,2765,3902,3838],()=>__webpack_exec__(96713));module.exports=a})();