import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, FindManyOptions } from 'typeorm';
import { Order, OrderStatus } from '../entities/order.entity';

@Injectable()
export class OrderRepository {
  constructor(
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
  ) {}

  async create(orderData: Partial<Order>): Promise<Order> {
    const order = this.orderRepository.create(orderData);
    return this.orderRepository.save(order);
  }

  async findById(id: string): Promise<Order | null> {
    return this.orderRepository.findOne({
      where: { id },
      relations: ['items'],
    });
  }

  async findByOrderNumber(orderNumber: string): Promise<Order | null> {
    return this.orderRepository.findOne({
      where: { orderNumber },
      relations: ['items'],
    });
  }

  async findByUserId(
    userId: string,
    options: {
      limit?: number;
      offset?: number;
      status?: OrderStatus;
    } = {},
  ): Promise<{ orders: Order[]; total: number }> {
    const { limit = 10, offset = 0, status } = options;

    const where: FindOptionsWhere<Order> = { userId };
    if (status) {
      where.status = status;
    }

    const findOptions: FindManyOptions<Order> = {
      where,
      relations: ['items'],
      order: { createdAt: 'DESC' },
      take: limit,
      skip: offset,
    };

    const [orders, total] = await this.orderRepository.findAndCount(findOptions);

    return { orders, total };
  }

  async findByStoreId(
    storeId: string,
    options: {
      limit?: number;
      offset?: number;
      status?: OrderStatus;
    } = {},
  ): Promise<{ orders: Order[]; total: number }> {
    const { limit = 10, offset = 0, status } = options;

    const where: FindOptionsWhere<Order> = { storeId };
    if (status) {
      where.status = status;
    }

    const findOptions: FindManyOptions<Order> = {
      where,
      relations: ['items'],
      order: { createdAt: 'DESC' },
      take: limit,
      skip: offset,
    };

    const [orders, total] = await this.orderRepository.findAndCount(findOptions);

    return { orders, total };
  }

  async update(id: string, updateData: Partial<Order>): Promise<Order> {
    await this.orderRepository.update(id, updateData);
    return this.findById(id);
  }

  async delete(id: string): Promise<void> {
    await this.orderRepository.delete(id);
  }

  async findAll(options: {
    limit?: number;
    offset?: number;
    status?: OrderStatus;
    userId?: string;
    storeId?: string;
  } = {}): Promise<{ orders: Order[]; total: number }> {
    const { limit = 10, offset = 0, status, userId, storeId } = options;

    const where: FindOptionsWhere<Order> = {};
    if (status) where.status = status;
    if (userId) where.userId = userId;
    if (storeId) where.storeId = storeId;

    const findOptions: FindManyOptions<Order> = {
      where,
      relations: ['items'],
      order: { createdAt: 'DESC' },
      take: limit,
      skip: offset,
    };

    const [orders, total] = await this.orderRepository.findAndCount(findOptions);

    return { orders, total };
  }

  async countByStatus(status: OrderStatus): Promise<number> {
    return this.orderRepository.count({ where: { status } });
  }

  async findRecentOrders(limit: number = 10): Promise<Order[]> {
    return this.orderRepository.find({
      relations: ['items'],
      order: { createdAt: 'DESC' },
      take: limit,
    });
  }

  async findOrdersByDateRange(
    startDate: Date,
    endDate: Date,
    options: {
      userId?: string;
      storeId?: string;
      status?: OrderStatus;
    } = {},
  ): Promise<Order[]> {
    const { userId, storeId, status } = options;

    const queryBuilder = this.orderRepository
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.items', 'items')
      .where('order.createdAt >= :startDate', { startDate })
      .andWhere('order.createdAt <= :endDate', { endDate });

    if (userId) {
      queryBuilder.andWhere('order.userId = :userId', { userId });
    }

    if (storeId) {
      queryBuilder.andWhere('order.storeId = :storeId', { storeId });
    }

    if (status) {
      queryBuilder.andWhere('order.status = :status', { status });
    }

    return queryBuilder.orderBy('order.createdAt', 'DESC').getMany();
  }

  async save(order: Order): Promise<Order> {
    return this.orderRepository.save(order);
  }
}
