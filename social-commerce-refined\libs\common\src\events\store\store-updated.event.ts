import { BaseEvent } from '../base-event.interface';

/**
 * Event emitted when a store is updated
 */
export class StoreUpdatedEvent implements BaseEvent<StoreUpdatedPayload> {
  id: string;
  type: string = 'store.updated';
  version: string = '1.0';
  timestamp: string;
  producer: string = 'store-service';
  payload: StoreUpdatedPayload;

  constructor(payload: StoreUpdatedPayload) {
    this.id = payload.id;
    this.timestamp = new Date().toISOString();
    this.payload = payload;
  }
}

/**
 * Payload for StoreUpdatedEvent
 */
export interface StoreUpdatedPayload {
  /**
   * Store ID
   */
  id: string;

  /**
   * Owner user ID
   */
  ownerId: string;

  /**
   * Store name
   */
  name: string;

  /**
   * Store description
   */
  description: string;

  /**
   * Store update timestamp
   */
  updatedAt: string;
}
