const { Client } = require('pg');

async function testDatabaseConnection() {
  try {
    console.log('Connecting to PostgreSQL database...');
    const client = new Client({
      host: 'localhost',
      port: 5432,
      database: 'social_commerce',
      user: 'postgres',
      password: '1111'
    });

    await client.connect();
    console.log('Connected to PostgreSQL database');

    // Check if the users table exists
    console.log('Checking if the users table exists...');
    const tableResult = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'users'
      );
    `);

    const usersTableExists = tableResult.rows[0].exists;
    console.log('Users table exists:', usersTableExists);

    if (usersTableExists) {
      // Count the number of users
      console.log('Counting the number of users...');
      const countResult = await client.query('SELECT COUNT(*) FROM users');
      console.log('Number of users:', countResult.rows[0].count);

      // Get all users
      console.log('Getting all users...');
      const usersResult = await client.query('SELECT id, username, email, role, "passwordHash" FROM users');

      console.log('Users:');
      usersResult.rows.forEach(user => {
        console.log('-----------------------------------');
        console.log('ID:', user.id);
        console.log('Username:', user.username);
        console.log('Email:', user.email);
        console.log('Role:', user.role);
        console.log('Password Hash:', user.passwordHash ? 'Present (not shown)' : 'Missing');
      });
    }

    await client.end();
    console.log('Disconnected from PostgreSQL database');
  } catch (error) {
    console.error('Error:', error);
  }
}

testDatabaseConnection();
