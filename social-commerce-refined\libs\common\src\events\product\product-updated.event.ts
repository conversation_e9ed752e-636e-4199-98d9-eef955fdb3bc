import { BaseEvent } from '../base-event.interface';

/**
 * Event emitted when a product is updated
 */
export class ProductUpdatedEvent implements BaseEvent<ProductUpdatedPayload> {
  id: string;
  type: string = 'product.updated';
  version: string = '1.0';
  timestamp: string;
  producer: string = 'product-service';
  payload: ProductUpdatedPayload;

  constructor(payload: ProductUpdatedPayload) {
    this.id = payload.id;
    this.timestamp = new Date().toISOString();
    this.payload = payload;
  }
}

/**
 * Payload for ProductUpdatedEvent
 */
export interface ProductUpdatedPayload {
  /**
   * Product ID
   */
  id: string;

  /**
   * Store ID
   */
  storeId: string;

  /**
   * Product name
   */
  name: string;

  /**
   * Product description
   */
  description: string;

  /**
   * Product price
   */
  price: number;

  /**
   * Product inventory count
   */
  inventory: number;

  /**
   * Product categories
   */
  categories: string[];

  /**
   * Product update timestamp
   */
  updatedAt: string;
}
