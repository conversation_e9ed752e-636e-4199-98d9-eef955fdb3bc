/**
 * Interface for product data
 */
export interface IProduct {
  /**
   * Product ID
   */
  id: string;

  /**
   * Store ID
   */
  storeId: string;

  /**
   * Product name
   */
  name: string;

  /**
   * Product description
   */
  description: string;

  /**
   * Product price
   */
  price: number;

  /**
   * Product inventory count
   */
  inventory: number;

  /**
   * Product categories
   */
  categories: string[];

  /**
   * Product creation timestamp
   */
  createdAt: Date | string;

  /**
   * Product update timestamp
   */
  updatedAt: Date | string;
}
