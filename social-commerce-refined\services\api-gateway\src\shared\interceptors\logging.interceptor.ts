import { Injectable, NestInterceptor, Execution<PERSON>ontext, <PERSON><PERSON><PERSON><PERSON>, Logger } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { Request, Response } from 'express';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();

    const { method, url, body, headers } = request;
    const userAgent = headers['user-agent'] || 'unknown';
    const contentLength = headers['content-length'] || 0;

    // Get or generate request ID
    const requestId = headers['x-request-id'] || this.generateRequestId();
    response.setHeader('x-request-id', requestId);

    // Get correlation ID (should be set by the CorrelationIdMiddleware)
    const correlationId = headers['x-correlation-id'] || 'unknown';

    const startTime = Date.now();

    this.logger.log(
      `[${requestId}] [${correlationId}] ${method} ${url} - User-Agent: ${userAgent} - Content-Length: ${contentLength}`,
    );

    if (Object.keys(body || {}).length > 0) {
      this.logger.debug(`[${requestId}] [${correlationId}] Request Body: ${this.sanitizeBody(body)}`);
    }

    return next.handle().pipe(
      tap({
        next: (data: any) => {
          const endTime = Date.now();
          const duration = endTime - startTime;

          this.logger.log(
            `[${requestId}] [${correlationId}] ${method} ${url} ${response.statusCode} - ${duration}ms`,
          );

          if (data && process.env.NODE_ENV === 'development') {
            this.logger.debug(`[${requestId}] [${correlationId}] Response Body: ${this.sanitizeResponse(data)}`);
          }
        },
        error: (error) => {
          const endTime = Date.now();
          const duration = endTime - startTime;

          this.logger.error(
            `[${requestId}] [${correlationId}] ${method} ${url} ${error.status || 500} - ${duration}ms - ${error.message}`,
          );
        },
      }),
    );
  }

  private generateRequestId(): string {
    return `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private sanitizeBody(body: any): string {
    if (!body) return 'empty';

    // Create a copy of the body
    const sanitized = { ...body };

    // Remove sensitive fields
    if (sanitized.password) sanitized.password = '***';
    if (sanitized.passwordConfirmation) sanitized.passwordConfirmation = '***';
    if (sanitized.token) sanitized.token = '***';
    if (sanitized.accessToken) sanitized.accessToken = '***';
    if (sanitized.refreshToken) sanitized.refreshToken = '***';

    return JSON.stringify(sanitized);
  }

  private sanitizeResponse(data: any): string {
    if (!data) return 'empty';

    // Create a copy of the data
    const sanitized = { ...data };

    // Remove sensitive fields
    if (sanitized.password) sanitized.password = '***';
    if (sanitized.token) sanitized.token = '***';
    if (sanitized.accessToken) sanitized.accessToken = '***';
    if (sanitized.refreshToken) sanitized.refreshToken = '***';

    return JSON.stringify(sanitized);
  }
}
