import { Controller, Get, Post, Put, Delete, Patch, Param, Body, Query, Headers, Req, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { RoutingService } from '../services/routing.service';
import { Request } from 'express';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

@ApiTags('users')
@Controller('users')
export class UserController {
  private readonly logger = new Logger(UserController.name);
  private readonly SERVICE_NAME = 'user';

  constructor(
    private readonly routingService: RoutingService,
  ) {}

  @Get()
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all users' })
  @ApiResponse({ status: 200, description: 'Return all users' })
  getAllUsers(@Req() req: Request, @Headers() headers: any, @Query() query: any): Observable<any> {
    this.logger.log('Forwarding GET /users request');
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, '/users', 'GET', null, headers)
      .pipe(map((response) => response.data));
  }

  @Get(':id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get a user by ID' })
  @ApiResponse({ status: 200, description: 'Return the user' })
  @ApiResponse({ status: 404, description: 'User not found' })
  getUserById(@Param('id') id: string, @Headers() headers: any): Observable<any> {
    this.logger.log(`Forwarding GET /users/${id} request`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, `/users/${id}`, 'GET', null, headers)
      .pipe(map((response) => response.data));
  }

  @Post()
  @ApiOperation({ summary: 'Create a new user' })
  @ApiResponse({ status: 201, description: 'User created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  createUser(@Body() body: any, @Headers() headers: any): Observable<any> {
    this.logger.log('Forwarding POST /users request');
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, '/users', 'POST', body, headers)
      .pipe(map((response) => response.data));
  }

  @Put(':id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update a user' })
  @ApiResponse({ status: 200, description: 'User updated successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'User not found' })
  updateUser(@Param('id') id: string, @Body() body: any, @Headers() headers: any): Observable<any> {
    this.logger.log(`Forwarding PUT /users/${id} request`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, `/users/${id}`, 'PUT', body, headers)
      .pipe(map((response) => response.data));
  }

  @Delete(':id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete a user' })
  @ApiResponse({ status: 200, description: 'User deleted successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  deleteUser(@Param('id') id: string, @Headers() headers: any): Observable<any> {
    this.logger.log(`Forwarding DELETE /users/${id} request`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, `/users/${id}`, 'DELETE', null, headers)
      .pipe(map((response) => response.data));
  }

  // Authentication routes
  @Post('auth/register')
  @ApiOperation({ summary: 'Register a new user' })
  @ApiResponse({ status: 201, description: 'User registered successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 429, description: 'Too many requests' })
  register(@Body() body: any, @Headers() headers: any): Observable<any> {
    this.logger.log('Forwarding POST /auth/register request');
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, '/auth/register', 'POST', body, headers)
      .pipe(map((response) => response.data));
  }

  @Post('auth/login')
  @ApiOperation({ summary: 'Login a user' })
  @ApiResponse({ status: 200, description: 'Login successful' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 429, description: 'Too many requests' })
  login(@Body() body: any, @Headers() headers: any): Observable<any> {
    this.logger.log('Forwarding POST /auth/login request');
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, '/auth/login', 'POST', body, headers)
      .pipe(map((response) => response.data));
  }

  @Get('auth/profile')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user profile' })
  @ApiResponse({ status: 200, description: 'Profile retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  getProfile(@Headers() headers: any): Observable<any> {
    this.logger.log('Forwarding GET /auth/profile request');
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, '/auth/profile', 'GET', null, headers)
      .pipe(map((response) => response.data));
  }

  // Catch-all route for other user service endpoints
  @Get('*')
  @ApiOperation({ summary: 'Forward any GET request to the user service' })
  forwardGetRequest(@Req() req: Request, @Headers() headers: any): Observable<any> {
    const path = req.url;
    this.logger.log(`Forwarding GET ${path} request to user service`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, path, 'GET', null, headers)
      .pipe(map((response) => response.data));
  }

  @Post('*')
  @ApiOperation({ summary: 'Forward any POST request to the user service' })
  forwardPostRequest(@Req() req: Request, @Body() body: any, @Headers() headers: any): Observable<any> {
    const path = req.url;
    this.logger.log(`Forwarding POST ${path} request to user service`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, path, 'POST', body, headers)
      .pipe(map((response) => response.data));
  }

  @Put('*')
  @ApiOperation({ summary: 'Forward any PUT request to the user service' })
  forwardPutRequest(@Req() req: Request, @Body() body: any, @Headers() headers: any): Observable<any> {
    const path = req.url;
    this.logger.log(`Forwarding PUT ${path} request to user service`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, path, 'PUT', body, headers)
      .pipe(map((response) => response.data));
  }

  @Delete('*')
  @ApiOperation({ summary: 'Forward any DELETE request to the user service' })
  forwardDeleteRequest(@Req() req: Request, @Headers() headers: any): Observable<any> {
    const path = req.url;
    this.logger.log(`Forwarding DELETE ${path} request to user service`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, path, 'DELETE', null, headers)
      .pipe(map((response) => response.data));
  }

  @Patch('*')
  @ApiOperation({ summary: 'Forward any PATCH request to the user service' })
  forwardPatchRequest(@Req() req: Request, @Body() body: any, @Headers() headers: any): Observable<any> {
    const path = req.url;
    this.logger.log(`Forwarding PATCH ${path} request to user service`);
    return this.routingService
      .forwardRequest(this.SERVICE_NAME, path, 'PATCH', body, headers)
      .pipe(map((response) => response.data));
  }
}
