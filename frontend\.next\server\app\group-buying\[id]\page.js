(()=>{var e={};e.id=603,e.ids=[603],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},50453:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>c,routeModule:()=>g,tree:()=>o});var a=t(67096),s=t(16132),i=t(37284),n=t.n(i),l=t(32564),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(r,d);let o=["",{children:["group-buying",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,69089)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\group-buying\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9291,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\group-buying\\[id]\\page.tsx"],x="/group-buying/[id]/page",m={require:t,loadChunk:()=>Promise.resolve()},g=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/group-buying/[id]/page",pathname:"/group-buying/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},39526:(e,r,t)=>{Promise.resolve().then(t.bind(t,40569))},40569:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>GroupBuyingDetailsPage});var a=t(30784),s=t(9885),i=t(27870),n=t(57114),l=t(11440),d=t.n(l),o=t(52451),c=t.n(o),x=t(59872),m=t(31889),g=t(62763),u=t(706),h=t(56663),p=t(17560);let groupBuying_GroupBuyingJoinModal=({groupBuying:e,isOpen:r,onClose:t,onSuccess:n})=>{let{t:l}=(0,i.$G)("groupBuying"),{showToast:d}=(0,p.pm)(),[o,c]=(0,s.useState)(1),[y,{isLoading:j}]=(0,m.SS)(),v=e.discountedPrice*o,handleJoin=async()=>{try{await y({groupBuyingId:e.id,quantity:o}).unwrap(),d(l("joinSuccess","Successfully joined the group buy!"),"success"),n?.(),t()}catch(e){console.error("Failed to join group buying:",e),d(l("joinError","Failed to join the group buy. Please try again."),"error")}};return a.jsx(g.Z,{isOpen:r,onClose:t,title:l("joinGroupBuying","Join Group Buy"),children:(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:e.title}),a.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:l("joinGroupBuyingDescription","You are about to join this group buying campaign. Please specify the quantity you want to purchase.")})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[a.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:l("price","Price")}),a.jsx("span",{className:"text-sm text-gray-900 dark:text-gray-100",children:(0,h.xG)(e.discountedPrice)})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[a.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:l("originalPrice","Original Price")}),a.jsx("span",{className:"text-sm text-gray-500 dark:text-gray-400 line-through",children:(0,h.xG)(e.originalPrice)})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[a.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:l("discount","Discount")}),(0,a.jsxs)("span",{className:"text-sm text-green-600 dark:text-green-400",children:[Math.round((e.originalPrice-e.discountedPrice)/e.originalPrice*100),"%"]})]}),(0,a.jsxs)("div",{className:"border-t border-gray-200 dark:border-gray-700 pt-4",children:[a.jsx("label",{htmlFor:"quantity",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:l("quantity","Quantity")}),a.jsx(u.Z,{id:"quantity",type:"number",min:"1",value:o,onChange:e=>{let r=parseInt(e.target.value);!isNaN(r)&&r>0&&c(r)}})]})]}),a.jsx("div",{className:"border-t border-gray-200 dark:border-gray-700 pt-4 mb-6",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx("span",{className:"text-base font-medium text-gray-900 dark:text-gray-100",children:l("total","Total")}),a.jsx("span",{className:"text-lg font-bold text-primary-600 dark:text-primary-400",children:(0,h.xG)(v)})]})}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:[a.jsx(x.Z,{variant:"outline",onClick:t,children:l("cancel","Cancel")}),a.jsx(x.Z,{onClick:handleJoin,isLoading:j,disabled:j,children:l("joinNow","Join Now")})]})]})})};var y=t(92928);let groupBuying_GroupBuyingInviteModal=({groupBuying:e,isOpen:r,onClose:t,onSuccess:n})=>{let{t:l}=(0,i.$G)("groupBuying"),[d,o]=(0,s.useState)(""),[c,u]=(0,s.useState)(""),[h,{isLoading:p}]=(0,m.Zl)(),validateEmails=e=>{let r=/^[^\s@]+@[^\s@]+\.[^\s@]+$/,t=e.filter(e=>!r.test(e));return!(t.length>0)||(u(l("invalidEmails","The following emails are invalid: {{emails}}",{emails:t.join(", ")})),!1)},handleInvite=async()=>{let r=d.split(",").map(e=>e.trim()).filter(e=>""!==e);if(0===r.length){u(l("noEmails","Please enter at least one email address"));return}if(validateEmails(r))try{await h({groupBuyingId:e.id,emails:r}).unwrap(),n?.(),t()}catch(e){console.error("Failed to invite to group buying:",e),u(l("inviteError","Failed to send invitations. Please try again."))}};return a.jsx(g.Z,{isOpen:r,onClose:t,title:l("inviteFriends","Invite Friends"),children:(0,a.jsxs)("div",{className:"p-4",children:[a.jsx("div",{className:"mb-6",children:a.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:l("inviteDescription","Invite your friends to join this group buy and get better prices together.")})}),c&&a.jsx("div",{className:"mb-4 p-3 bg-red-100 text-red-700 rounded-md dark:bg-red-900 dark:text-red-100",children:c}),(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("label",{htmlFor:"emails",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:l("emailAddresses","Email Addresses")}),a.jsx(y.Z,{id:"emails",value:d,onChange:e=>{o(e.target.value),u("")},placeholder:l("emailsPlaceholder","Enter email addresses separated by commas"),rows:4}),a.jsx("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:l("emailsHelp","Enter email addresses separated by commas (e.g., <EMAIL>, <EMAIL>)")})]}),a.jsx("div",{className:"mb-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"flex-grow border-t border-gray-200 dark:border-gray-700"}),a.jsx("span",{className:"mx-4 text-sm text-gray-500 dark:text-gray-400",children:l("or","OR")}),a.jsx("div",{className:"flex-grow border-t border-gray-200 dark:border-gray-700"})]})}),a.jsx("div",{className:"mb-6",children:(0,a.jsxs)(x.Z,{variant:"outline",fullWidth:!0,onClick:()=>{let r=`${window.location.origin}/group-buying/${e.id}`;navigator.clipboard.writeText(r)},children:[a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})}),l("copyInviteLink","Copy Invite Link")]})}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:[a.jsx(x.Z,{variant:"outline",onClick:t,children:l("cancel","Cancel")}),a.jsx(x.Z,{onClick:handleInvite,isLoading:p,disabled:p,children:l("sendInvites","Send Invites")})]})]})})};var j=t(69302);let groupBuying_GroupBuyingAnalytics=({groupBuying:e,className:r=""})=>{let{t}=(0,i.$G)("groupBuying"),s=Math.min(Math.round(e.currentParticipants/e.minParticipants*100),100),n=Math.round((e.originalPrice-e.discountedPrice)/e.originalPrice*100),l=(e.originalPrice-e.discountedPrice)*e.currentParticipants,d=new Date(e.endDate),o=new Date,c=d.getTime()-o.getTime(),x=Math.max(0,Math.floor(c/864e5)),m=Math.max(0,Math.floor(c%864e5/36e5));return(0,a.jsxs)("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 ${r}`,children:[a.jsx("h2",{className:"text-xl font-bold text-gray-900 dark:text-gray-100 mb-4",children:t("analytics","Analytics")}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[a.jsx("div",{className:"text-sm text-gray-500 dark:text-gray-400 mb-1",children:t("participants","Participants")}),(0,a.jsxs)("div",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:[e.currentParticipants,(0,a.jsxs)("span",{className:"text-sm font-normal text-gray-500 dark:text-gray-400 ml-1",children:["/ ",e.minParticipants]})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[a.jsx("div",{className:"text-sm text-gray-500 dark:text-gray-400 mb-1",children:t("discount","Discount")}),(0,a.jsxs)("div",{className:"text-2xl font-bold text-green-600 dark:text-green-400",children:[n,"%"]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[a.jsx("div",{className:"text-sm text-gray-500 dark:text-gray-400 mb-1",children:t("totalSavings","Total Savings")}),a.jsx("div",{className:"text-2xl font-bold text-primary-600 dark:text-primary-400",children:(0,h.xG)(l)})]}),(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[a.jsx("div",{className:"text-sm text-gray-500 dark:text-gray-400 mb-1",children:t("timeRemaining","Time Remaining")}),a.jsx("div",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:e.status===j.ys.COMPLETED||e.status===j.ys.CANCELLED||e.status===j.ys.EXPIRED?t(`status.${e.status.toLowerCase()}`,e.status):x>0?t("timeRemaining.days","{{days}} days left",{days:x}):m>0?t("timeRemaining.hours","{{hours}} hours left",{hours:m}):t("timeRemaining.ending","Ending soon")})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm mb-2",children:[a.jsx("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:t("progress","Progress")}),(0,a.jsxs)("span",{className:"text-primary-600 dark:text-primary-400 font-medium",children:[s,"%"]})]}),a.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5",children:a.jsx("div",{className:"bg-primary-500 h-2.5 rounded-full",style:{width:`${s}%`}})})]}),(0,a.jsxs)("div",{className:"border-t border-gray-200 dark:border-gray-700 pt-4",children:[(0,a.jsxs)("div",{className:"flex justify-between mb-2",children:[a.jsx("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:t("originalPrice","Original Price")}),a.jsx("span",{className:"text-sm text-gray-500 dark:text-gray-400 line-through",children:(0,h.xG)(e.originalPrice)})]}),(0,a.jsxs)("div",{className:"flex justify-between mb-2",children:[a.jsx("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:t("groupPrice","Group Price")}),a.jsx("span",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:(0,h.xG)(e.discountedPrice)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:t("youSave","You Save")}),(0,a.jsxs)("span",{className:"text-sm font-medium text-green-600 dark:text-green-400",children:[(0,h.xG)(e.originalPrice-e.discountedPrice),(0,a.jsxs)("span",{className:"ml-1",children:["(",n,"%)"]})]})]})]})]})};function GroupBuyingDetailsPage(){let{t:e}=(0,i.$G)("groupBuying"),r=(0,n.useParams)(),t=r.id,[l,o]=(0,s.useState)(!1),[g,u]=(0,s.useState)(!1),{data:p,isLoading:y,error:v}=(0,m.Y)(t),[,{isLoading:b}]=(0,m.SS)(),[f,{isLoading:N}]=(0,m.A7)();if(y)return a.jsx("div",{className:"min-h-screen p-4 flex items-center justify-center",children:a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"})});if(v||!p)return(0,a.jsxs)("div",{className:"min-h-screen p-4 flex flex-col items-center justify-center",children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4",children:e("error.notFound","Group Buy Not Found")}),a.jsx("p",{className:"text-gray-500 dark:text-gray-400 mb-6",children:e("error.notFoundDescription","The group buy you are looking for does not exist or has been removed.")}),a.jsx(d(),{href:"/group-buying",children:a.jsx(x.Z,{children:e("backToGroupBuying","Back to Group Buying")})})]});let k=Math.min(Math.round(p.currentParticipants/p.minParticipants*100),100),w=new Date(p.endDate),P=new Date,C=w.getTime()-P.getTime(),B=Math.max(0,Math.floor(C/864e5)),M=Math.max(0,Math.floor(C%864e5/36e5));return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 py-8",children:[(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[a.jsx("nav",{className:"flex mb-6","aria-label":"Breadcrumb",children:(0,a.jsxs)("ol",{className:"flex items-center space-x-2",children:[a.jsx("li",{children:a.jsx(d(),{href:"/",className:"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300",children:e("home","Home")})}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx("svg",{className:"h-5 w-5 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20",children:a.jsx("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})}),a.jsx(d(),{href:"/group-buying",className:"ml-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300",children:e("title","Group Buying")})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx("svg",{className:"h-5 w-5 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20",children:a.jsx("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})}),a.jsx("span",{className:"ml-2 text-gray-700 dark:text-gray-300 truncate max-w-xs",children:p.title})]})]})}),a.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{className:"relative aspect-square md:aspect-auto md:h-full",children:[a.jsx(c(),{src:p.imageUrl||p.product?.mediaUrls?.[0]||"/images/placeholder-product.png",alt:p.title,fill:!0,className:"object-cover",priority:!0}),a.jsx("div",{className:"absolute top-4 left-4 bg-primary-500 text-white text-sm font-bold px-3 py-1.5 rounded-md",children:(()=>{let r=Math.round((p.originalPrice-p.discountedPrice)/p.originalPrice*100);return e("discount","{{percentage}}% OFF",{percentage:r})})()})]}),(0,a.jsxs)("div",{className:"p-6 md:p-8",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[a.jsx("h1",{className:"text-2xl md:text-3xl font-bold text-gray-900 dark:text-gray-100",children:p.title}),a.jsx("span",{className:`px-3 py-1 rounded-full text-sm font-medium ${(()=>{switch(p.status){case j.ys.ACTIVE:return"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";case j.ys.PENDING:return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";case j.ys.COMPLETED:return"bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";case j.ys.EXPIRED:case j.ys.CANCELLED:return"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400"}})()}`,children:e(`status.${p.status.toLowerCase()}`,p.status)})]}),p.description&&a.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-6",children:p.description}),(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[a.jsx("span",{className:"text-3xl font-bold text-primary-600 dark:text-primary-400",children:(0,h.xG)(p.discountedPrice)}),a.jsx("span",{className:"ml-3 text-xl text-gray-500 dark:text-gray-400 line-through",children:(0,h.xG)(p.originalPrice)})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm mb-2",children:[a.jsx("span",{className:"font-medium",children:e("participants","{{current}}/{{min}} joined",{current:p.currentParticipants,min:p.minParticipants})}),(0,a.jsxs)("span",{className:"text-primary-600 dark:text-primary-400 font-medium",children:[k,"%"]})]}),a.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5",children:a.jsx("div",{className:"bg-primary-500 h-2.5 rounded-full",style:{width:`${k}%`}})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-gray-500 dark:text-gray-400 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),a.jsx("span",{className:"text-gray-700 dark:text-gray-300",children:p.status===j.ys.COMPLETED||p.status===j.ys.CANCELLED||p.status===j.ys.EXPIRED?e(`status.${p.status.toLowerCase()}`,p.status):B>0?e("timeRemaining.days","{{days}} days left",{days:B}):M>0?e("timeRemaining.hours","{{hours}} hours left",{hours:M}):e("timeRemaining.ending","Ending soon")})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-gray-500 dark:text-gray-400 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})}),a.jsx("span",{className:"text-gray-700 dark:text-gray-300",children:e("minParticipants","Min. {{count}} participants",{count:p.minParticipants})})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[p.status===j.ys.ACTIVE&&(0,a.jsxs)(a.Fragment,{children:[a.jsx(x.Z,{variant:"primary",fullWidth:!0,onClick:()=>{u(!0)},isLoading:b,disabled:b,children:e("joinGroupBuying","Join Group Buy")}),a.jsx(x.Z,{variant:"outline",fullWidth:!0,onClick:()=>o(!0),children:e("inviteFriends","Invite Friends")})]}),(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsxs)(x.Z,{variant:"outline",className:"flex-1",onClick:()=>{navigator.clipboard.writeText(window.location.href)},children:[a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"})}),e("shareGroupBuying","Share")]}),p.product&&a.jsx(d(),{href:`/products/${p.product.id}`,className:"flex-1",children:(0,a.jsxs)(x.Z,{variant:"outline",fullWidth:!0,children:[a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"})}),e("viewProduct","View Product")]})})]})]})]})]})}),(0,a.jsxs)("div",{className:"mt-8 grid grid-cols-1 md:grid-cols-3 gap-8",children:[a.jsx(groupBuying_GroupBuyingAnalytics,{groupBuying:p}),(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6",children:[a.jsx("h2",{className:"text-xl font-bold text-gray-900 dark:text-gray-100 mb-4",children:e("product","Product")}),p.product?(0,a.jsxs)("div",{children:[(0,a.jsxs)(d(),{href:`/products/${p.product.id}`,className:"flex items-center mb-4",children:[a.jsx("div",{className:"w-16 h-16 relative flex-shrink-0",children:a.jsx(c(),{src:p.product.mediaUrls?.[0]||"/images/placeholder-product.png",alt:p.product.title,fill:!0,className:"object-cover rounded-md"})}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("h3",{className:"font-medium text-gray-900 dark:text-gray-100",children:p.product.title}),a.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:(0,h.xG)(p.product.price)})]})]}),a.jsx(d(),{href:`/products/${p.product.id}`,children:a.jsx(x.Z,{variant:"outline",size:"sm",fullWidth:!0,children:e("viewProductDetails","View Product Details")})})]}):a.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:e("productNotAvailable","Product information not available")})]}),(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6",children:[a.jsx("h2",{className:"text-xl font-bold text-gray-900 dark:text-gray-100 mb-4",children:e("store","Store")}),p.store?(0,a.jsxs)("div",{children:[(0,a.jsxs)(d(),{href:`/stores/${p.store.id}`,className:"flex items-center mb-4",children:[a.jsx("div",{className:"w-12 h-12 relative flex-shrink-0",children:a.jsx(c(),{src:p.store.logoUrl||"/images/placeholder-store.png",alt:p.store.name,fill:!0,className:"object-cover rounded-full"})}),a.jsx("div",{className:"ml-4",children:a.jsx("h3",{className:"font-medium text-gray-900 dark:text-gray-100",children:p.store.name})})]}),a.jsx(d(),{href:`/stores/${p.store.id}`,children:a.jsx(x.Z,{variant:"outline",size:"sm",fullWidth:!0,children:e("viewStore","View Store")})})]}):a.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:e("storeNotAvailable","Store information not available")})]}),(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6",children:[a.jsx("h2",{className:"text-xl font-bold text-gray-900 dark:text-gray-100 mb-4",children:e("creator","Creator")}),p.creator?(0,a.jsxs)("div",{children:[(0,a.jsxs)(d(),{href:`/profile/${p.creator.username}`,className:"flex items-center mb-4",children:[a.jsx("div",{className:"w-12 h-12 relative flex-shrink-0",children:a.jsx(c(),{src:p.creator.avatarUrl||"/images/default-avatar.png",alt:p.creator.displayName||p.creator.username,fill:!0,className:"object-cover rounded-full"})}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("h3",{className:"font-medium text-gray-900 dark:text-gray-100",children:p.creator.displayName||p.creator.username}),a.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:e("createdAt","Created on {{date}}",{date:new Date(p.createdAt).toLocaleDateString()})})]})]}),a.jsx(d(),{href:`/profile/${p.creator.username}`,children:a.jsx(x.Z,{variant:"outline",size:"sm",fullWidth:!0,children:e("viewProfile","View Profile")})})]}):a.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:e("creatorNotAvailable","Creator information not available")})]})]}),p&&l&&a.jsx(groupBuying_GroupBuyingInviteModal,{groupBuying:p,isOpen:l,onClose:()=>o(!1),onSuccess:()=>{console.log("Invites sent successfully")}})]}),p&&a.jsx(groupBuying_GroupBuyingJoinModal,{groupBuying:p,isOpen:g,onClose:()=>u(!1),onSuccess:()=>{}})]})}},69089:(e,r,t)=>{"use strict";t.r(r),t.d(r,{$$typeof:()=>n,__esModule:()=>i,default:()=>d});var a=t(95153);let s=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\group-buying\[id]\page.tsx`),{__esModule:i,$$typeof:n}=s,l=s.default,d=l}};var r=require("../../../webpack-runtime.js");r.C(e);var __webpack_exec__=e=>r(r.s=e),t=r.X(0,[2103,2765,706,2763,6663,2928,7245],()=>__webpack_exec__(50453));module.exports=t})();