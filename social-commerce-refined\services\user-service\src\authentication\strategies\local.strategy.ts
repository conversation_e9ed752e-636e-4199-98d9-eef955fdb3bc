import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-local';
import { AuthenticationService } from '../services/authentication.service';

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
  private readonly logger = new Logger(LocalStrategy.name);

  constructor(private readonly authenticationService: AuthenticationService) {
    super({
      usernameField: 'email',
    });
  }

  async validate(email: string, password: string) {
    this.logger.log(`Validating user with email: ${email}`);
    
    try {
      const user = await this.authenticationService.validateUser(email, password);
      return user;
    } catch (error) {
      this.logger.error(`Local strategy validation failed: ${error.message}`);
      throw new UnauthorizedException('Invalid credentials');
    }
  }
}
