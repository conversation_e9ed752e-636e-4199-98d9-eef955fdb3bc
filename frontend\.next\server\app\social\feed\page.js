(()=>{var e={};e.id=3498,e.ids=[3498],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},15020:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>d,routeModule:()=>p,tree:()=>c});var a=r(67096),t=r(16132),i=r(37284),n=r.n(i),o=r(32564),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(s,l);let c=["",{children:["social",{children:["feed",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,66780)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\social\\feed\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9291,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\social\\feed\\page.tsx"],x="/social/feed/page",m={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/social/feed/page",pathname:"/social/feed",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},20281:(e,s,r)=>{Promise.resolve().then(r.bind(r,75945))},75945:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>SocialFeedPage});var a=r(30784),t=r(9885),i=r(27870),n=r(51020),o=r(95499),l=r(93603),c=r(19911),d=r(59872),x=r(70661);function SocialFeedPage(){let{t:e}=(0,i.$G)("social"),[s,r]=(0,t.useState)(1),[m,p]=(0,t.useState)({following:!0}),[u,g]=(0,t.useState)(!1),{data:h,isLoading:f,error:y}=(0,n.useGetSocialFeedQuery)({page:s,limit:10,...m}),[j,{isLoading:b}]=(0,n.u3)(),handleCreatePost=async e=>{try{await j(e).unwrap(),g(!1)}catch(e){console.error("Failed to create post:",e)}};return f&&!h?a.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 py-8",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:a.jsx("div",{className:"flex items-center justify-center py-12",children:a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"})})})}):y?a.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 py-8",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center py-12",children:[a.jsx("p",{className:"text-red-500 dark:text-red-400 mb-4",children:e("social.errorLoading","Error loading social feed")}),a.jsx(d.Z,{variant:"outline",onClick:()=>r(1),children:e("common.retry","Retry")})]})})}):a.jsx(x.Z,{children:a.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 py-8",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:e("social.feed","Social Feed")}),a.jsx("p",{className:"text-gray-500 dark:text-gray-400 mt-1",children:e("social.feedDescription","See what your connections are sharing")})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[a.jsx("div",{className:"lg:col-span-1",children:a.jsx(c.Z,{onFilterChange:e=>{p(e),r(1)},filters:m,onCreatePost:()=>g(!0)})}),(0,a.jsxs)("div",{className:"lg:col-span-3",children:[a.jsx("div",{className:"lg:hidden mb-4",children:a.jsx(d.Z,{onClick:()=>g(!0),fullWidth:!0,children:e("social.createPost","Create Post")})}),u&&a.jsx("div",{className:"mb-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4",children:a.jsx(l.Z,{onSubmit:handleCreatePost,onCancel:()=>g(!1),isLoading:b})}),a.jsx(o.Z,{posts:h?.posts||[],isLoading:f,hasMore:!!h&&h.total>10*s,onLoadMore:()=>{r(s+1)}})]})]})]})})})}},66780:(e,s,r)=>{"use strict";r.r(s),r.d(s,{$$typeof:()=>n,__esModule:()=>i,default:()=>l});var a=r(95153);let t=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\social\feed\page.tsx`),{__esModule:i,$$typeof:n}=t,o=t.default,l=o}};var s=require("../../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),r=s.X(0,[2103,2765,2763,3902,1020,2522,4694,661,4154,5499,2928,3456],()=>__webpack_exec__(15020));module.exports=r})();