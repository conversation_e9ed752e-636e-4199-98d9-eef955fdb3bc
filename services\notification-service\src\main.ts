import { NestFactory } from '@nestjs/core';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { ValidationPipe, Logger } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';

async function bootstrap() {
  const logger = new Logger('NotificationService');

  // Create HTTP application
  const app = await NestFactory.create(AppModule);

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  // Enable CORS
  app.enableCors({
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true,
  });

  // Swagger documentation
  const config = new DocumentBuilder()
    .setTitle('Notification Service API')
    .setDescription('Social Commerce Platform - Notification Service')
    .setVersion('1.0')
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  // Connect microservice
  try {
    const microservice = app.connectMicroservice<MicroserviceOptions>({
      transport: Transport.RMQ,
      options: {
        urls: [process.env.RABBITMQ_URL || 'amqp://admin:admin@localhost:5672'],
        queue: process.env.NOTIFICATION_QUEUE || 'notification_queue',
        queueOptions: {
          durable: true,
        },
      },
    });

    await app.startAllMicroservices();
    logger.log('🔗 Microservice connected to RabbitMQ');
  } catch (error) {
    logger.error('Failed to connect microservice to RabbitMQ:', error.message);
    logger.warn('Continuing without microservice connection...');
  }

  // Start HTTP server
  const port = process.env.HTTP_PORT || process.env.PORT || 3007;
  await app.listen(port);

  logger.log(`🚀 Notification Service running on port ${port}`);
  logger.log(`📚 API Documentation: http://localhost:${port}/api/docs`);
  logger.log(`🏥 Health Check: http://localhost:${port}/api/health`);
}

bootstrap().catch((error) => {
  console.error('Failed to start Notification Service:', error);
  process.exit(1);
});
