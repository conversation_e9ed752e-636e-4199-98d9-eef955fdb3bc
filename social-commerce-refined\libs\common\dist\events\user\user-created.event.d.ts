import { BaseEvent } from '../base-event.interface';
export declare class UserCreatedEvent implements BaseEvent<UserCreatedPayload> {
    id: string;
    type: string;
    version: string;
    timestamp: string;
    producer: string;
    payload: UserCreatedPayload;
    constructor(payload: UserCreatedPayload);
}
export interface UserCreatedPayload {
    id: string;
    email: string;
    name: string;
    isEmailVerified: boolean;
    createdAt: string;
}
