import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { MessagePattern } from '@nestjs/microservices';
import { EmailService } from './email.service';
import { SmsService } from './sms.service';
import { Notification, NotificationType, NotificationStatus } from '../entities/notification.entity';
import { CreateNotificationDto } from '../dto/create-notification.dto';
import { SendEmailDto } from '../dto/send-email.dto';
import { SendSmsDto } from '../dto/send-sms.dto';

@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);

  constructor(
    @InjectRepository(Notification)
    private readonly notificationRepository: Repository<Notification>,
    private readonly emailService: EmailService,
    private readonly smsService: SmsService,
  ) {}

  // HTTP API Methods
  async createNotification(createNotificationDto: CreateNotificationDto): Promise<Notification> {
    const notification = this.notificationRepository.create(createNotificationDto);
    return this.notificationRepository.save(notification);
  }

  async sendEmailDirect(sendEmailDto: SendEmailDto): Promise<{ success: boolean; messageId?: string }> {
    try {
      const result = await this.emailService.sendEmail(sendEmailDto);
      this.logger.log(`Email sent successfully to: ${sendEmailDto.to}`);
      return { success: true, messageId: result.messageId };
    } catch (error) {
      this.logger.error(`Failed to send email to ${sendEmailDto.to}: ${error.message}`);
      return { success: false };
    }
  }

  async sendSmsDirect(sendSmsDto: SendSmsDto): Promise<{ success: boolean; messageId?: string }> {
    try {
      const result = await this.smsService.sendSms(sendSmsDto);
      this.logger.log(`SMS sent successfully to: ${sendSmsDto.to}`);
      return { success: true, messageId: result.messageId };
    } catch (error) {
      this.logger.error(`Failed to send SMS to ${sendSmsDto.to}: ${error.message}`);
      return { success: false };
    }
  }

  // Microservice Message Patterns
  @MessagePattern('notification.sendEmail')
  async sendEmail(data: SendEmailDto): Promise<{ success: boolean; messageId?: string }> {
    return this.sendEmailDirect(data);
  }

  @MessagePattern('notification.sendSms')
  async sendSms(data: SendSmsDto): Promise<{ success: boolean; messageId?: string }> {
    return this.sendSmsDirect(data);
  }

  @MessagePattern('notification.sendWelcomeEmail')
  async sendWelcomeEmail(data: { email: string; firstName?: string }): Promise<{ success: boolean }> {
    const emailData: SendEmailDto = {
      to: data.email,
      subject: 'Welcome to Social Commerce Platform!',
      message: `
        <h1>Welcome${data.firstName ? `, ${data.firstName}` : ''}!</h1>
        <p>Thank you for joining our Social Commerce Platform.</p>
        <p>You can now start exploring and creating your own store.</p>
        <p>Best regards,<br>The Social Commerce Team</p>
      `,
      templateData: { firstName: data.firstName },
    };

    return this.sendEmail(emailData);
  }

  @MessagePattern('notification.sendVerificationEmail')
  async sendVerificationEmail(data: { 
    email: string; 
    verificationToken: string; 
    firstName?: string 
  }): Promise<{ success: boolean }> {
    const verificationLink = `${process.env.FRONTEND_URL}/verify-email?token=${data.verificationToken}`;
    
    const emailData: SendEmailDto = {
      to: data.email,
      subject: 'Verify Your Email Address',
      message: `
        <h1>Email Verification</h1>
        <p>Hello${data.firstName ? ` ${data.firstName}` : ''},</p>
        <p>Please click the link below to verify your email address:</p>
        <p><a href="${verificationLink}">Verify Email Address</a></p>
        <p>If you didn't create an account, please ignore this email.</p>
        <p>Best regards,<br>The Social Commerce Team</p>
      `,
      templateData: { 
        firstName: data.firstName, 
        verificationLink,
        verificationToken: data.verificationToken 
      },
    };

    return this.sendEmail(emailData);
  }
}
