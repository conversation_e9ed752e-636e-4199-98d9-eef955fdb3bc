import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../entities/user.entity';
// import { Profile } from '../../profile-management/entities/profile.entity'; // Temporarily disabled
import { CreateUserDto } from '../dto/create-user.dto';
import { UpdateUserDto } from '../dto/update-user.dto';

@Injectable()
export class UserRepository {
  private readonly logger = new Logger(UserRepository.name);

  constructor(
    @InjectRepository(User)
    private readonly repository: Repository<User>,
  ) {}

  async findAll(): Promise<User[]> {
    this.logger.log('Finding all users');
    return this.repository.find();
    // relations: ['profile'] temporarily disabled to avoid circular reference
  }

  async findOne(id: string): Promise<User> {
    this.logger.log(`Finding user with ID: ${id}`);
    const user = await this.repository.findOne({
      where: { id },
      // relations: ['profile'], // Temporarily disabled to avoid circular reference
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    return user;
  }

  async findByEmail(email: string): Promise<User> {
    this.logger.log(`Finding user with email: ${email}`);
    return this.repository.findOne({
      where: { email },
      // relations: ['profile'], // Temporarily disabled to avoid circular reference
    });
  }

  async create(createUserDto: CreateUserDto): Promise<User> {
    this.logger.log(`Creating user with email: ${createUserDto.email}`);

    const user = this.repository.create(createUserDto);

    // TODO: Create profile separately when profile relation is re-enabled
    // For now, we'll just create the user without the profile to avoid circular reference

    return this.repository.save(user);
  }

  async update(id: string, updateUserDto: UpdateUserDto): Promise<User> {
    this.logger.log(`Updating user with ID: ${id}`);

    const user = await this.findOne(id);

    // Update user properties
    Object.assign(user, updateUserDto);

    // TODO: Update profile separately when profile relation is re-enabled
    // For now, we'll ignore profile updates to avoid circular reference

    return this.repository.save(user);
  }

  async remove(id: string): Promise<void> {
    this.logger.log(`Removing user with ID: ${id}`);

    const user = await this.findOne(id);

    await this.repository.remove(user);
  }

  async updateLastLogin(id: string): Promise<User> {
    this.logger.log(`Updating last login for user with ID: ${id}`);

    const user = await this.findOne(id);

    user.lastLogin = new Date();

    return this.repository.save(user);
  }

  async verifyEmail(id: string): Promise<User> {
    this.logger.log(`Verifying email for user with ID: ${id}`);

    const user = await this.findOne(id);

    user.isEmailVerified = true;

    return this.repository.save(user);
  }

  async verifyPhone(id: string): Promise<User> {
    this.logger.log(`Verifying phone for user with ID: ${id}`);

    const user = await this.findOne(id);

    user.isPhoneVerified = true;

    return this.repository.save(user);
  }
}
