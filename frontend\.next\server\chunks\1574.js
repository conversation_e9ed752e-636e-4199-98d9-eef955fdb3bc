"use strict";exports.id=1574,exports.ids=[1574],exports.modules={16027:(e,a,r)=>{r.d(a,{Z:()=>__WEBPACK_DEFAULT_EXPORT__});var l=r(30784),s=r(9885),d=r(57114),i=r(706),o=r(59872),m=r(77783);let __WEBPACK_DEFAULT_EXPORT__=({store:e,isEditing:a=!1})=>{let[r,n]=(0,s.useState)({username:e?.username||"",displayName:e?.displayName||"",bio:e?.bio||"",profileImageUrl:e?.profileImageUrl||"",headerImageUrl:e?.headerImageUrl||""}),[t,g]=(0,s.useState)({}),h=(0,d.useRouter)(),[u,{isLoading:b}]=(0,m.j6)(),[c,{isLoading:p}]=(0,m.mt)(),handleChange=e=>{let{name:a,value:r}=e.target;n(e=>({...e,[a]:r})),t[a]&&g(e=>({...e,[a]:""}))},validateForm=()=>{let e={};return r.username.trim()?/^[a-zA-Z0-9_]+$/.test(r.username)||(e.username="Username can only contain letters, numbers, and underscores"):e.username="Username is required",r.bio&&r.bio.length>500&&(e.bio="Bio must be less than 500 characters"),r.profileImageUrl&&!isValidUrl(r.profileImageUrl)&&(e.profileImageUrl="Please enter a valid URL"),r.headerImageUrl&&!isValidUrl(r.headerImageUrl)&&(e.headerImageUrl="Please enter a valid URL"),g(e),0===Object.keys(e).length},isValidUrl=e=>{try{return new URL(e),!0}catch(e){return!1}},handleSubmit=async l=>{if(l.preventDefault(),validateForm())try{a&&e?await c({id:e.id,data:{displayName:r.displayName||void 0,bio:r.bio||void 0,profileImageUrl:r.profileImageUrl||void 0,headerImageUrl:r.headerImageUrl||void 0}}).unwrap():await u({username:r.username,displayName:r.displayName||void 0,bio:r.bio||void 0,profileImageUrl:r.profileImageUrl||void 0,headerImageUrl:r.headerImageUrl||void 0}).unwrap(),h.push("/stores")}catch(a){let e=a.data?.message||"An error occurred";g({form:e})}};return(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6",children:[l.jsx("h2",{className:"text-xl font-semibold mb-4",children:a?"Edit Store":"Create New Store"}),t.form&&l.jsx("div",{className:"p-4 mb-4 text-red-700 bg-red-100 rounded-md dark:bg-red-900 dark:text-red-100",children:t.form}),(0,l.jsxs)("form",{onSubmit:handleSubmit,children:[l.jsx(i.Z,{label:"Username",name:"username",value:r.username,onChange:handleChange,error:t.username,disabled:a,required:!0}),l.jsx(i.Z,{label:"Display Name",name:"displayName",value:r.displayName,onChange:handleChange,error:t.displayName}),(0,l.jsxs)("div",{className:"mb-4",children:[l.jsx("label",{htmlFor:"bio",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Bio"}),l.jsx("textarea",{id:"bio",name:"bio",rows:4,className:`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 ${t.bio?"border-red-500 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-700"}`,value:r.bio,onChange:handleChange}),t.bio&&l.jsx("p",{className:"mt-1 text-sm text-red-600",children:t.bio})]}),l.jsx(i.Z,{label:"Profile Image URL",name:"profileImageUrl",value:r.profileImageUrl,onChange:handleChange,error:t.profileImageUrl}),l.jsx(i.Z,{label:"Header Image URL",name:"headerImageUrl",value:r.headerImageUrl,onChange:handleChange,error:t.headerImageUrl}),(0,l.jsxs)("div",{className:"flex justify-end gap-3 mt-6",children:[l.jsx(o.Z,{type:"button",variant:"outline",onClick:()=>h.push("/stores"),children:"Cancel"}),l.jsx(o.Z,{type:"submit",isLoading:b||p,children:a?"Save Changes":"Create Store"})]})]})]})}}};