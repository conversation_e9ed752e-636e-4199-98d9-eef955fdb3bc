import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { Store } from './entities/store.entity';
import { StoreRepository } from './repositories/store.repository';
import { StoreService } from './services/store.service';
import { StoreController } from './controllers/store.controller';
import { JwtStrategy } from '../shared/strategies/jwt.strategy';

@Module({
  imports: [
    TypeOrmModule.forFeature([Store]),
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET', 'your-secret-key'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN', '1h'),
        },
      }),
    }),
    ClientsModule.registerAsync([
      {
        name: 'USER_SERVICE',
        imports: [ConfigModule],
        inject: [ConfigService],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.RMQ,
          options: {
            urls: [configService.get<string>('RABBITMQ_URL', 'amqp://admin:admin@rabbitmq:5672')],
            queue: configService.get<string>('USER_QUEUE', 'user_queue'),
            queueOptions: {
              durable: true,
            },
          },
        }),
      },
    ]), // Re-enabled for production
  ],
  controllers: [StoreController],
  providers: [StoreService, StoreRepository, JwtStrategy],
  exports: [StoreService, StoreRepository, JwtStrategy, PassportModule],
})
export class StoreManagementModule {}
