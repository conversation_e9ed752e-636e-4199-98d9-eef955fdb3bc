import { Injectable, <PERSON><PERSON>, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import * as amqp from 'amqplib';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class RabbitMQService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(RabbitMQService.name);
  private connection: amqp.Connection | null = null;
  private channel: amqp.Channel | null = null;
  private readonly url: string;

  constructor(url?: string) {
    this.url = url || process.env.RABBITMQ_URL || 'amqp://admin:admin@localhost:5672';
  }

  async onModuleInit() {
    await this.connect();
  }

  async onModuleDestroy() {
    await this.close();
  }

  async connect() {
    try {
      this.logger.log(`Connecting to RabbitMQ at ${this.url}`);
      this.connection = await amqp.connect(this.url);
      this.channel = await this.connection.createChannel();
      this.logger.log('Connected to RabbitMQ');

      this.connection.on('error', (err) => {
        this.logger.error(`RabbitMQ connection error: ${err.message}`);
        this.reconnect();
      });

      this.connection.on('close', () => {
        this.logger.warn('RabbitMQ connection closed');
        this.reconnect();
      });
    } catch (error) {
      this.logger.error(`Failed to connect to RabbitMQ: ${error.message}`);
      this.reconnect();
    }
  }

  private async reconnect() {
    this.logger.log('Attempting to reconnect to RabbitMQ...');
    setTimeout(async () => {
      try {
        await this.connect();
      } catch (error) {
        this.logger.error(`Failed to reconnect to RabbitMQ: ${error.message}`);
        this.reconnect();
      }
    }, 5000);
  }

  async close() {
    try {
      if (this.channel) {
        await this.channel.close();
      }
      if (this.connection) {
        await this.connection.close();
      }
      this.logger.log('Disconnected from RabbitMQ');
    } catch (error) {
      this.logger.error(`Error closing RabbitMQ connection: ${error.message}`);
    }
  }

  async createQueue(queue: string, options?: amqp.Options.AssertQueue) {
    try {
      if (!this.channel) {
        throw new Error('Channel not available');
      }
      await this.channel.assertQueue(queue, options);
      this.logger.log(`Queue ${queue} created`);
    } catch (error) {
      this.logger.error(`Error creating queue ${queue}: ${error.message}`);
      throw error;
    }
  }

  async createExchange(exchange: string, type: string, options?: amqp.Options.AssertExchange) {
    try {
      await this.channel.assertExchange(exchange, type, options);
      this.logger.log(`Exchange ${exchange} created`);
    } catch (error) {
      this.logger.error(`Error creating exchange ${exchange}: ${error.message}`);
      throw error;
    }
  }

  async bindQueue(queue: string, exchange: string, routingKey: string) {
    try {
      await this.channel.bindQueue(queue, exchange, routingKey);
      this.logger.log(`Queue ${queue} bound to exchange ${exchange} with routing key ${routingKey}`);
    } catch (error) {
      this.logger.error(`Error binding queue ${queue} to exchange ${exchange}: ${error.message}`);
      throw error;
    }
  }

  async publish(exchange: string, routingKey: string, message: any, options?: amqp.Options.Publish) {
    try {
      const messageBuffer = Buffer.from(JSON.stringify(message));
      const defaultOptions: amqp.Options.Publish = {
        persistent: true,
        messageId: uuidv4(),
        timestamp: Date.now(),
        contentType: 'application/json',
        ...options,
      };

      const result = this.channel.publish(exchange, routingKey, messageBuffer, defaultOptions);

      if (result) {
        this.logger.debug(`Message published to exchange ${exchange} with routing key ${routingKey}`);
      } else {
        this.logger.warn(`Channel write buffer is full - exchange ${exchange}, routing key ${routingKey}`);
        // Wait for drain event
        await new Promise((resolve) => this.channel.once('drain', resolve));
        this.logger.debug(`Channel drained - exchange ${exchange}, routing key ${routingKey}`);
      }

      return result;
    } catch (error) {
      this.logger.error(`Error publishing message to exchange ${exchange}: ${error.message}`);
      throw error;
    }
  }

  async sendToQueue(queue: string, message: any, options?: amqp.Options.Publish) {
    try {
      const messageBuffer = Buffer.from(JSON.stringify(message));
      const defaultOptions: amqp.Options.Publish = {
        persistent: true,
        messageId: uuidv4(),
        timestamp: Date.now(),
        contentType: 'application/json',
        ...options,
      };

      const result = this.channel.sendToQueue(queue, messageBuffer, defaultOptions);

      if (result) {
        this.logger.debug(`Message sent to queue ${queue}`);
      } else {
        this.logger.warn(`Channel write buffer is full - queue ${queue}`);
        // Wait for drain event
        await new Promise((resolve) => this.channel.once('drain', resolve));
        this.logger.debug(`Channel drained - queue ${queue}`);
      }

      return result;
    } catch (error) {
      this.logger.error(`Error sending message to queue ${queue}: ${error.message}`);
      throw error;
    }
  }

  async consume(queue: string, onMessage: (msg: amqp.ConsumeMessage) => Promise<void>, options?: amqp.Options.Consume) {
    try {
      const defaultOptions: amqp.Options.Consume = {
        noAck: false,
        ...options,
      };

      await this.channel.consume(queue, async (msg) => {
        if (msg) {
          try {
            await onMessage(msg);
            this.channel.ack(msg);
          } catch (error) {
            this.logger.error(`Error processing message from queue ${queue}: ${error.message}`);
            // Negative acknowledgment - requeue the message
            this.channel.nack(msg, false, true);
          }
        }
      }, defaultOptions);

      this.logger.log(`Consumer registered for queue ${queue}`);
    } catch (error) {
      this.logger.error(`Error consuming from queue ${queue}: ${error.message}`);
      throw error;
    }
  }

  async purgeQueue(queue: string) {
    try {
      await this.channel.purgeQueue(queue);
      this.logger.log(`Queue ${queue} purged`);
    } catch (error) {
      this.logger.error(`Error purging queue ${queue}: ${error.message}`);
      throw error;
    }
  }

  async deleteQueue(queue: string, options?: amqp.Options.DeleteQueue) {
    try {
      await this.channel.deleteQueue(queue, options);
      this.logger.log(`Queue ${queue} deleted`);
    } catch (error) {
      this.logger.error(`Error deleting queue ${queue}: ${error.message}`);
      throw error;
    }
  }

  async deleteExchange(exchange: string, options?: amqp.Options.DeleteExchange) {
    try {
      await this.channel.deleteExchange(exchange, options);
      this.logger.log(`Exchange ${exchange} deleted`);
    } catch (error) {
      this.logger.error(`Error deleting exchange ${exchange}: ${error.message}`);
      throw error;
    }
  }
}
