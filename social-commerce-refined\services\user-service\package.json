{"name": "user-service", "version": "1.0.0", "description": "User Service for Social Commerce Platform", "main": "dist/main.js", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:auth": "jest --config ./test/jest-e2e.json auth.e2e-spec.ts", "test:profile": "jest --config ./test/jest-e2e.json profile.e2e-spec.ts", "test:health": "jest --config ./test/jest-e2e.json health.e2e-spec.ts"}, "keywords": ["microservice", "user", "authentication", "<PERSON><PERSON><PERSON>"], "author": "", "license": "MIT", "dependencies": {"@nestjs/axios": "^3.0.2", "@nestjs/common": "^11.1.1", "@nestjs/config": "^3.2.0", "@nestjs/core": "^11.1.1", "@nestjs/jwt": "^10.2.0", "@nestjs/microservices": "^11.1.1", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^11.1.1", "@nestjs/swagger": "^7.3.0", "@nestjs/terminus": "^10.2.0", "@nestjs/typeorm": "^10.0.2", "amqp-connection-manager": "^4.1.14", "amqplib": "^0.10.3", "axios": "^1.6.7", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "nodemailer": "^6.9.13", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.11.3", "reflect-metadata": "^0.2.1", "rxjs": "^7.8.1", "typeorm": "^0.3.20"}, "devDependencies": {"@nestjs/cli": "^11.0.7", "@nestjs/testing": "^11.1.1", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.2", "@types/jest": "^29.5.14", "@types/node": "^22.15.21", "@types/nodemailer": "^6.4.17", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "jest": "^29.7.0", "supertest": "^7.1.1", "ts-jest": "^29.3.4", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}