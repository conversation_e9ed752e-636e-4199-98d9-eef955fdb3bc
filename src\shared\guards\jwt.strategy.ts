import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  private readonly logger = new Logger(JwtStrategy.name);
  private readonly userServiceUrl: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('JWT_SECRET', 'your-secret-key'),
    });
    
    this.userServiceUrl = this.configService.get<string>('USER_SERVICE_URL', 'http://localhost:3001/api');
  }

  async validate(payload: any) {
    this.logger.log(`Validating JWT for user ID: ${payload.sub}`);
    
    try {
      // Verify user exists and is active by calling the User Service
      const response = await firstValueFrom(
        this.httpService.get(`${this.userServiceUrl}/users/${payload.sub}`, {
          headers: {
            Authorization: `Bearer ${this.configService.get<string>('JWT_SECRET')}`,
          },
        })
      );
      
      const user = response.data;
      
      if (!user || !user.isActive) {
        throw new UnauthorizedException('User is inactive or not found');
      }
      
      return {
        sub: payload.sub,
        email: payload.email,
        role: payload.role,
      };
    } catch (error) {
      this.logger.error(`JWT validation failed: ${error.message}`);
      throw new UnauthorizedException('Invalid token');
    }
  }
}
