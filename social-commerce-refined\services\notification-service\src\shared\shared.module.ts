import { Module, Global } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { TerminusModule } from '@nestjs/terminus';
import { HttpModule } from '@nestjs/axios';
import { PassportModule } from '@nestjs/passport';
import { HealthController } from './controllers/health.controller';
import { LoggingInterceptor } from './interceptors/logging.interceptor';
import { JwtStrategy } from './strategies/jwt.strategy';
import { JwtAuthGuard } from './guards/jwt-auth.guard';

@Global()
@Module({
  imports: [
    PassportModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET', 'your-secret-key'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN', '1h'),
        },
      }),
    }),
    TerminusModule,
    HttpModule,
  ],
  controllers: [HealthController],
  providers: [
    LoggingInterceptor,
    JwtStrategy,
    JwtAuthGuard,
  ],
  exports: [
    JwtModule,
    LoggingInterceptor,
    JwtStrategy,
    JwtAuthGuard,
  ],
})
export class SharedModule {}
