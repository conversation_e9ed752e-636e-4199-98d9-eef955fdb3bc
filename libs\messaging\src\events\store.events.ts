import { BaseEvent } from './base.event';

/**
 * Event emitted when a store is created
 */
export class StoreCreatedEvent extends BaseEvent {
  readonly type = 'store.created';

  constructor(
    public readonly storeId: string,
    public readonly name: string,
    public readonly ownerId: string,
  ) {
    super();
  }
}

/**
 * Event emitted when a store is updated
 */
export class StoreUpdatedEvent extends BaseEvent {
  readonly type = 'store.updated';

  constructor(
    public readonly storeId: string,
    public readonly name: string,
    public readonly ownerId: string,
    public readonly changes: Record<string, any>,
  ) {
    super();
  }
}

/**
 * Event emitted when a store is deleted
 */
export class StoreDeletedEvent extends BaseEvent {
  readonly type = 'store.deleted';

  constructor(
    public readonly storeId: string,
    public readonly name: string,
    public readonly ownerId: string,
  ) {
    super();
  }
}

/**
 * Event emitted when a product is created
 */
export class ProductCreatedEvent extends BaseEvent {
  readonly type = 'product.created';

  constructor(
    public readonly productId: string,
    public readonly name: string,
    public readonly price: number,
    public readonly storeId: string,
  ) {
    super();
  }
}

/**
 * Event emitted when a product is updated
 */
export class ProductUpdatedEvent extends BaseEvent {
  readonly type = 'product.updated';

  constructor(
    public readonly productId: string,
    public readonly name: string,
    public readonly storeId: string,
    public readonly changes: Record<string, any>,
  ) {
    super();
  }
}

/**
 * Event emitted when a product is deleted
 */
export class ProductDeletedEvent extends BaseEvent {
  readonly type = 'product.deleted';

  constructor(
    public readonly productId: string,
    public readonly name: string,
    public readonly storeId: string,
  ) {
    super();
  }
}
