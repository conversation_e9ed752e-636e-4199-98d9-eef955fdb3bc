(()=>{var e={};e.id=6263,e.ids=[6263],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},39040:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>d,originalPathname:()=>p,pages:()=>l,routeModule:()=>m,tree:()=>u});var s=r(67096),n=r(16132),a=r(37284),i=r.n(a),o=r(32564),c={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);r.d(t,c);let u=["",{children:["activity",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,31147)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\activity\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9291,23)),"next/dist/client/components/not-found-error"]}],l=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\activity\\page.tsx"],p="/activity/page",d={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/activity/page",pathname:"/activity",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},15103:(e,t,r)=>{Promise.resolve().then(r.bind(r,5690))},5690:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>ActivityRedirectPage});var s=r(9885),n=r(57114);function ActivityRedirectPage(){let e=(0,n.useRouter)();return(0,s.useEffect)(()=>{e.replace("/activity/feed")},[e]),null}},31147:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>i,__esModule:()=>a,default:()=>c});var s=r(95153);let n=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\activity\page.tsx`),{__esModule:a,$$typeof:i}=n,o=n.default,c=o}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),r=t.X(0,[2103,2765],()=>__webpack_exec__(39040));module.exports=r})();