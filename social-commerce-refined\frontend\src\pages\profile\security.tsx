import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  Heading,
  Text,
  Container,
  useToast,
  Tabs,
  TabList,
  Tab,
  TabPanels,
  TabPanel,
} from '@chakra-ui/react';
import MainLayout from '@/components/layout/MainLayout';
import { useAuth } from '@/context/AuthContext';
import SecuritySettings from '@/components/profile/SecuritySettings';

interface PasswordFormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

const Security = () => {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const toast = useToast();
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, isLoading, router]);

  // Handle password change
  const handlePasswordChange = async (data: PasswordFormData) => {
    // In a real app, we would call the API to change the password
    // For now, just simulate a delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Simulate success
    return Promise.resolve();
  };

  // Handle two-factor authentication toggle
  const handleTwoFactorToggle = async (enabled: boolean) => {
    // In a real app, we would call the API to enable/disable 2FA
    // For now, just simulate a delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Update local state
    setTwoFactorEnabled(enabled);
    
    // Simulate success
    return Promise.resolve();
  };

  // Show loading if not authenticated
  if (isLoading || !isAuthenticated) {
    return (
      <Box textAlign="center" py={10}>
        <Text>Loading...</Text>
      </Box>
    );
  }

  return (
    <MainLayout>
      <Container maxW="container.lg" py={8}>
        <Heading as="h1" size="xl" mb={6}>
          Account Settings
        </Heading>
        
        <Tabs colorScheme="brand" mb={6}>
          <TabList>
            <Tab>Security</Tab>
            <Tab>Notifications</Tab>
            <Tab>Privacy</Tab>
          </TabList>
          
          <TabPanels>
            <TabPanel px={0}>
              <SecuritySettings
                onPasswordChange={handlePasswordChange}
                onTwoFactorToggle={handleTwoFactorToggle}
                twoFactorEnabled={twoFactorEnabled}
              />
            </TabPanel>
            
            <TabPanel>
              <Box p={6} bg="white" borderRadius="lg" borderWidth="1px">
                <Heading size="md" mb={4}>
                  Notification Settings
                </Heading>
                <Text>Notification settings will be implemented in a future update.</Text>
              </Box>
            </TabPanel>
            
            <TabPanel>
              <Box p={6} bg="white" borderRadius="lg" borderWidth="1px">
                <Heading size="md" mb={4}>
                  Privacy Settings
                </Heading>
                <Text>Privacy settings will be implemented in a future update.</Text>
              </Box>
            </TabPanel>
          </TabPanels>
        </Tabs>
      </Container>
    </MainLayout>
  );
};

export default Security;
