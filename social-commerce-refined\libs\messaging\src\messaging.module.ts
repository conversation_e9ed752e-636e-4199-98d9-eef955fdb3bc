import { Module, DynamicModule } from '@nestjs/common';
import { RabbitMQModule } from './rabbitmq/rabbitmq.module';
import { EventPublisherModule } from './rabbitmq/event-publisher.module';
import { EventSubscriberModule } from './rabbitmq/event-subscriber.module';

export interface MessagingModuleOptions {
  rabbitmqUrl?: string;
  serviceName?: string;
}

@Module({})
export class MessagingModule {
  static register(options?: MessagingModuleOptions): DynamicModule {
    return {
      module: MessagingModule,
      imports: [
        RabbitMQModule.register({
          url: options?.rabbitmqUrl || 'amqp://admin:admin@localhost:5672',
        }),
        EventPublisherModule,
        EventSubscriberModule,
      ],
      exports: [
        RabbitMQModule,
        EventPublisherModule,
        EventSubscriberModule,
      ],
    };
  }

  static registerAsync(options: {
    useFactory: (...args: any[]) => Promise<MessagingModuleOptions> | MessagingModuleOptions;
    inject?: any[];
  }): DynamicModule {
    return {
      module: MessagingModule,
      imports: [
        RabbitMQModule.registerAsync({
          useFactory: async (...args: any[]) => {
            const config = await options.useFactory(...args);
            return {
              url: config.rabbitmqUrl || 'amqp://admin:admin@localhost:5672',
            };
          },
          inject: options.inject || [],
        }),
        EventPublisherModule,
        EventSubscriberModule,
      ],
      exports: [
        RabbitMQModule,
        EventPublisherModule,
        EventSubscriberModule,
      ],
    };
  }
}
