(()=>{var e={};e.id=1063,e.ids=[1063],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},69526:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>l});var r=s(67096),a=s(16132),n=s(37284),o=s.n(n),d=s(32564),i={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>d[e]);s.d(t,i);let l=["",{children:["set-new-password",{children:["[token]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,62702)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\set-new-password\\[token]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9291,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\set-new-password\\[token]\\page.tsx"],u="/set-new-password/[token]/page",p={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/set-new-password/[token]/page",pathname:"/set-new-password/[token]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},19940:(e,t,s)=>{Promise.resolve().then(s.bind(s,28342))},28342:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>__WEBPACK_DEFAULT_EXPORT__});var r=s(30784),a=s(9885),n=s(57114),o=s(11440),d=s.n(o),i=s(27870),l=s(14379),c=s(706),u=s(59872),p=s(19923);let __WEBPACK_DEFAULT_EXPORT__=({token:e})=>{let[t,s]=(0,a.useState)(""),[o,m]=(0,a.useState)(""),[w,x]=(0,a.useState)(""),[g,h]=(0,a.useState)(""),f=(0,n.useRouter)(),{t:v}=(0,i.$G)("auth"),{isRtl:_}=(0,l.g)(),{data:P,isLoading:y,error:b}=(0,p.RR)(e),[k,{isLoading:N}]=(0,p.Xv)();(0,a.useEffect)(()=>{b&&x(v("passwordReset.invalidToken","Invalid or expired token"))},[b,v]);let handleSubmit=async s=>{if(s.preventDefault(),!t){x(v("validation.required",{field:v("login.password")}));return}if(t.length<8){x(v("validation.minLength",{field:v("login.password"),length:8}));return}if(t!==o){x(v("validation.passwordMatch"));return}try{await k({token:e,password:t}).unwrap(),h(v("passwordReset.passwordChanged","Password changed successfully")),x(""),setTimeout(()=>{f.push("/login")},3e3)}catch(t){let e=t.data?.message||v("passwordReset.error");x(e),h("")}};return y?r.jsx("div",{className:"w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-md dark:bg-gray-800 text-center",children:r.jsx("p",{children:v("common.loading")})}):b||P&&!P.valid?(0,r.jsxs)("div",{className:"w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-md dark:bg-gray-800 text-center",children:[r.jsx("h1",{className:"text-3xl font-bold text-red-600",children:v("passwordReset.invalidToken","Invalid or expired token")}),r.jsx("p",{className:"mt-4",children:v("passwordReset.requestNewLink","Please request a new password reset link")}),r.jsx("div",{className:"mt-6",children:r.jsx(d(),{href:"/password-reset",className:"text-primary-600 hover:text-primary-500",children:v("passwordReset.backToReset","Back to Password Reset")})})]}):(0,r.jsxs)("div",{className:`w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-md dark:bg-gray-800 ${_?"text-right":"text-left"}`,children:[(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("h1",{className:"text-3xl font-bold",children:v("passwordReset.setNewPassword","Set New Password")}),r.jsx("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:v("passwordReset.setNewPasswordSubtitle","Create a new password for your account")})]}),w&&r.jsx("div",{className:"p-4 text-red-700 bg-red-100 rounded-md dark:bg-red-900 dark:text-red-100",children:w}),g&&r.jsx("div",{className:"p-4 text-green-700 bg-green-100 rounded-md dark:bg-green-900 dark:text-green-100",children:g}),(0,r.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:handleSubmit,children:[r.jsx(c.Z,{label:v("login.password"),name:"password",type:"password",value:t,onChange:e=>s(e.target.value),required:!0}),r.jsx(c.Z,{label:v("register.confirmPassword"),name:"confirmPassword",type:"password",value:o,onChange:e=>m(e.target.value),required:!0}),r.jsx(u.Z,{type:"submit",fullWidth:!0,isLoading:N,children:v("passwordReset.setNewPasswordButton","Set New Password")})]}),r.jsx("div",{className:"text-center mt-4",children:r.jsx(d(),{href:"/login",className:"text-primary-600 hover:text-primary-500",children:v("passwordReset.backToLogin")})})]})}},62702:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>SetNewPassword,generateMetadata:()=>generateMetadata});var r=s(4656),a=s(60232),n=s(95153);let o=(0,n.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\components\auth\SetNewPasswordForm.tsx`),{__esModule:d,$$typeof:i}=o,l=o.default;async function generateMetadata(){let{t:e}=await (0,a.$)("auth");return{title:e("passwordReset.setNewPassword","Set New Password"),description:e("passwordReset.setNewPasswordSubtitle","Create a new password for your account")}}async function SetNewPassword({params:e}){let{isRtl:t}=await (0,a.$)("auth"),{token:s}=e;return r.jsx("div",{className:`flex min-h-screen flex-col items-center justify-center p-8 md:p-24 ${t?"text-right":"text-left"}`,children:r.jsx(l,{token:s})})}},60232:(e,t,s)=>{"use strict";s.d(t,{$:()=>getServerTranslations});var r=s(47420),a=s(94386);async function getServerTranslations(e="common"){let t=(0,r.G)(),{t:s}=await (0,r.i)(t,e);return{t:s,language:t,direction:(0,a.Fp)(t)?"rtl":"ltr",isRtl:(0,a.Fp)(t)}}}};var t=require("../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),s=t.X(0,[2103,2765,706],()=>__webpack_exec__(69526));module.exports=s})();