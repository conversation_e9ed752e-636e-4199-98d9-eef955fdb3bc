(()=>{var e={};e.id=564,e.ids=[564],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},7400:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>d,pages:()=>u,routeModule:()=>m,tree:()=>l});var s=r(67096),a=r(16132),n=r(37284),o=r.n(n),i=r(32564),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);r.d(t,c);let l=["",{children:["stores",{children:["create",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,54290)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\stores\\create\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9291,23)),"next/dist/client/components/not-found-error"]}],u=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\stores\\create\\page.tsx"],d="/stores/create/page",p={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/stores/create/page",pathname:"/stores/create",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},3353:(e,t,r)=>{Promise.resolve().then(r.bind(r,82926))},82926:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>CreateStorePage});var s=r(30784);r(9885);var a=r(16027);function CreateStorePage(){return s.jsx("div",{className:"min-h-screen p-6 md:p-12",children:(0,s.jsxs)("div",{className:"max-w-3xl mx-auto",children:[s.jsx("h1",{className:"text-3xl font-bold mb-8",children:"Create New Store"}),s.jsx(a.Z,{})]})})}},54290:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>o,__esModule:()=>n,default:()=>c});var s=r(95153);let a=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\stores\create\page.tsx`),{__esModule:n,$$typeof:o}=a,i=a.default,c=i}};var t=require("../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),r=t.X(0,[2103,2765,706,7783,1574],()=>__webpack_exec__(7400));module.exports=r})();