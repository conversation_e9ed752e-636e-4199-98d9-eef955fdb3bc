{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-05-24T07:42:08.556Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-05-24T07:42:08.596Z"}
{"context":"InstanceLoader","level":"info","message":"HttpModule dependencies initialized","timestamp":"2025-05-24T07:42:08.600Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-05-24T07:42:08.603Z"}
{"context":"InstanceLoader","level":"info","message":"CacheModule dependencies initialized","timestamp":"2025-05-24T07:42:08.604Z"}
{"context":"InstanceLoader","level":"info","message":"TerminusModule dependencies initialized","timestamp":"2025-05-24T07:42:08.606Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-05-24T07:42:08.607Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-05-24T07:42:08.607Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-05-24T07:42:08.616Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-05-24T07:42:08.617Z"}
{"context":"InstanceLoader","level":"info","message":"SharedModule dependencies initialized","timestamp":"2025-05-24T07:42:08.618Z"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized","timestamp":"2025-05-24T07:42:08.618Z"}
{"context":"InstanceLoader","level":"info","message":"RoutingModule dependencies initialized","timestamp":"2025-05-24T07:42:08.630Z"}
{"context":"LegacyRouteConverter","level":"warn","message":"Unsupported route path: \"/api/users/*\". In previous versions, the symbols ?, *, and + were used to denote optional or repeating path parameters. The latest version of \"path-to-regexp\" now requires the use of named parameters. For example, instead of using a route like /users/* to capture all routes starting with \"/users\", you should use /users/*path. For more details, refer to the migration guide. Attempting to auto-convert...","timestamp":"2025-05-24T07:42:16.792Z"}
{"context":"LegacyRouteConverter","level":"warn","message":"Unsupported route path: \"/api/users/*\". In previous versions, the symbols ?, *, and + were used to denote optional or repeating path parameters. The latest version of \"path-to-regexp\" now requires the use of named parameters. For example, instead of using a route like /users/* to capture all routes starting with \"/users\", you should use /users/*path. For more details, refer to the migration guide. Attempting to auto-convert...","timestamp":"2025-05-24T07:42:16.793Z"}
{"context":"LegacyRouteConverter","level":"warn","message":"Unsupported route path: \"/api/users/*\". In previous versions, the symbols ?, *, and + were used to denote optional or repeating path parameters. The latest version of \"path-to-regexp\" now requires the use of named parameters. For example, instead of using a route like /users/* to capture all routes starting with \"/users\", you should use /users/*path. For more details, refer to the migration guide. Attempting to auto-convert...","timestamp":"2025-05-24T07:42:16.795Z"}
{"context":"LegacyRouteConverter","level":"warn","message":"Unsupported route path: \"/api/users/*\". In previous versions, the symbols ?, *, and + were used to denote optional or repeating path parameters. The latest version of \"path-to-regexp\" now requires the use of named parameters. For example, instead of using a route like /users/* to capture all routes starting with \"/users\", you should use /users/*path. For more details, refer to the migration guide. Attempting to auto-convert...","timestamp":"2025-05-24T07:42:16.800Z"}
{"context":"LegacyRouteConverter","level":"warn","message":"Unsupported route path: \"/api/users/*\". In previous versions, the symbols ?, *, and + were used to denote optional or repeating path parameters. The latest version of \"path-to-regexp\" now requires the use of named parameters. For example, instead of using a route like /users/* to capture all routes starting with \"/users\", you should use /users/*path. For more details, refer to the migration guide. Attempting to auto-convert...","timestamp":"2025-05-24T07:42:16.801Z"}
{"context":"LegacyRouteConverter","level":"warn","message":"Unsupported route path: \"/api/stores/*\". In previous versions, the symbols ?, *, and + were used to denote optional or repeating path parameters. The latest version of \"path-to-regexp\" now requires the use of named parameters. For example, instead of using a route like /users/* to capture all routes starting with \"/users\", you should use /users/*path. For more details, refer to the migration guide. Attempting to auto-convert...","timestamp":"2025-05-24T07:42:16.807Z"}
{"context":"LegacyRouteConverter","level":"warn","message":"Unsupported route path: \"/api/stores/*\". In previous versions, the symbols ?, *, and + were used to denote optional or repeating path parameters. The latest version of \"path-to-regexp\" now requires the use of named parameters. For example, instead of using a route like /users/* to capture all routes starting with \"/users\", you should use /users/*path. For more details, refer to the migration guide. Attempting to auto-convert...","timestamp":"2025-05-24T07:42:16.807Z"}
{"context":"LegacyRouteConverter","level":"warn","message":"Unsupported route path: \"/api/stores/*\". In previous versions, the symbols ?, *, and + were used to denote optional or repeating path parameters. The latest version of \"path-to-regexp\" now requires the use of named parameters. For example, instead of using a route like /users/* to capture all routes starting with \"/users\", you should use /users/*path. For more details, refer to the migration guide. Attempting to auto-convert...","timestamp":"2025-05-24T07:42:16.808Z"}
{"context":"LegacyRouteConverter","level":"warn","message":"Unsupported route path: \"/api/stores/*\". In previous versions, the symbols ?, *, and + were used to denote optional or repeating path parameters. The latest version of \"path-to-regexp\" now requires the use of named parameters. For example, instead of using a route like /users/* to capture all routes starting with \"/users\", you should use /users/*path. For more details, refer to the migration guide. Attempting to auto-convert...","timestamp":"2025-05-24T07:42:16.809Z"}
{"context":"LegacyRouteConverter","level":"warn","message":"Unsupported route path: \"/api/stores/*\". In previous versions, the symbols ?, *, and + were used to denote optional or repeating path parameters. The latest version of \"path-to-regexp\" now requires the use of named parameters. For example, instead of using a route like /users/* to capture all routes starting with \"/users\", you should use /users/*path. For more details, refer to the migration guide. Attempting to auto-convert...","timestamp":"2025-05-24T07:42:16.810Z"}
{"context":"LegacyRouteConverter","level":"warn","message":"Unsupported route path: \"/api/products/*\". In previous versions, the symbols ?, *, and + were used to denote optional or repeating path parameters. The latest version of \"path-to-regexp\" now requires the use of named parameters. For example, instead of using a route like /users/* to capture all routes starting with \"/users\", you should use /users/*path. For more details, refer to the migration guide. Attempting to auto-convert...","timestamp":"2025-05-24T07:42:16.815Z"}
{"context":"LegacyRouteConverter","level":"warn","message":"Unsupported route path: \"/api/products/*\". In previous versions, the symbols ?, *, and + were used to denote optional or repeating path parameters. The latest version of \"path-to-regexp\" now requires the use of named parameters. For example, instead of using a route like /users/* to capture all routes starting with \"/users\", you should use /users/*path. For more details, refer to the migration guide. Attempting to auto-convert...","timestamp":"2025-05-24T07:42:16.817Z"}
{"context":"LegacyRouteConverter","level":"warn","message":"Unsupported route path: \"/api/products/*\". In previous versions, the symbols ?, *, and + were used to denote optional or repeating path parameters. The latest version of \"path-to-regexp\" now requires the use of named parameters. For example, instead of using a route like /users/* to capture all routes starting with \"/users\", you should use /users/*path. For more details, refer to the migration guide. Attempting to auto-convert...","timestamp":"2025-05-24T07:42:16.818Z"}
{"context":"LegacyRouteConverter","level":"warn","message":"Unsupported route path: \"/api/products/*\". In previous versions, the symbols ?, *, and + were used to denote optional or repeating path parameters. The latest version of \"path-to-regexp\" now requires the use of named parameters. For example, instead of using a route like /users/* to capture all routes starting with \"/users\", you should use /users/*path. For more details, refer to the migration guide. Attempting to auto-convert...","timestamp":"2025-05-24T07:42:16.820Z"}
{"context":"LegacyRouteConverter","level":"warn","message":"Unsupported route path: \"/api/products/*\". In previous versions, the symbols ?, *, and + were used to denote optional or repeating path parameters. The latest version of \"path-to-regexp\" now requires the use of named parameters. For example, instead of using a route like /users/* to capture all routes starting with \"/users\", you should use /users/*path. For more details, refer to the migration guide. Attempting to auto-convert...","timestamp":"2025-05-24T07:42:16.822Z"}
{"context":"LegacyRouteConverter","level":"warn","message":"Unsupported route path: \"/api/*\". In previous versions, the symbols ?, *, and + were used to denote optional or repeating path parameters. The latest version of \"path-to-regexp\" now requires the use of named parameters. For example, instead of using a route like /users/* to capture all routes starting with \"/users\", you should use /users/*path. For more details, refer to the migration guide. Attempting to auto-convert...","timestamp":"2025-05-24T07:42:16.955Z"}
{"context":"RoutesResolver","level":"info","message":"UserController {/api/users}:","timestamp":"2025-05-24T07:42:16.993Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/users, GET} route","timestamp":"2025-05-24T07:42:17.028Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/users/:id, GET} route","timestamp":"2025-05-24T07:42:17.031Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/users, POST} route","timestamp":"2025-05-24T07:42:17.033Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/users/:id, PUT} route","timestamp":"2025-05-24T07:42:17.035Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/users/:id, DELETE} route","timestamp":"2025-05-24T07:42:17.038Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/users/auth/register, POST} route","timestamp":"2025-05-24T07:42:17.039Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/users/auth/login, POST} route","timestamp":"2025-05-24T07:42:17.039Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/users/auth/profile, GET} route","timestamp":"2025-05-24T07:42:17.071Z"}
{"context":"LegacyRouteConverter","level":"warn","message":"Unsupported route path: \"/api/users/*\". In previous versions, the symbols ?, *, and + were used to denote optional or repeating path parameters. The latest version of \"path-to-regexp\" now requires the use of named parameters. For example, instead of using a route like /users/* to capture all routes starting with \"/users\", you should use /users/*path. For more details, refer to the migration guide. Attempting to auto-convert...","timestamp":"2025-05-24T07:42:17.073Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/users/*, GET} route","timestamp":"2025-05-24T07:42:17.080Z"}
{"context":"LegacyRouteConverter","level":"warn","message":"Unsupported route path: \"/api/users/*\". In previous versions, the symbols ?, *, and + were used to denote optional or repeating path parameters. The latest version of \"path-to-regexp\" now requires the use of named parameters. For example, instead of using a route like /users/* to capture all routes starting with \"/users\", you should use /users/*path. For more details, refer to the migration guide. Attempting to auto-convert...","timestamp":"2025-05-24T07:42:17.097Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/users/*, POST} route","timestamp":"2025-05-24T07:42:17.098Z"}
{"context":"LegacyRouteConverter","level":"warn","message":"Unsupported route path: \"/api/users/*\". In previous versions, the symbols ?, *, and + were used to denote optional or repeating path parameters. The latest version of \"path-to-regexp\" now requires the use of named parameters. For example, instead of using a route like /users/* to capture all routes starting with \"/users\", you should use /users/*path. For more details, refer to the migration guide. Attempting to auto-convert...","timestamp":"2025-05-24T07:42:17.098Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/users/*, PUT} route","timestamp":"2025-05-24T07:42:17.099Z"}
{"context":"LegacyRouteConverter","level":"warn","message":"Unsupported route path: \"/api/users/*\". In previous versions, the symbols ?, *, and + were used to denote optional or repeating path parameters. The latest version of \"path-to-regexp\" now requires the use of named parameters. For example, instead of using a route like /users/* to capture all routes starting with \"/users\", you should use /users/*path. For more details, refer to the migration guide. Attempting to auto-convert...","timestamp":"2025-05-24T07:42:17.099Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/users/*, DELETE} route","timestamp":"2025-05-24T07:42:17.100Z"}
{"context":"LegacyRouteConverter","level":"warn","message":"Unsupported route path: \"/api/users/*\". In previous versions, the symbols ?, *, and + were used to denote optional or repeating path parameters. The latest version of \"path-to-regexp\" now requires the use of named parameters. For example, instead of using a route like /users/* to capture all routes starting with \"/users\", you should use /users/*path. For more details, refer to the migration guide. Attempting to auto-convert...","timestamp":"2025-05-24T07:42:17.101Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/users/*, PATCH} route","timestamp":"2025-05-24T07:42:17.104Z"}
{"context":"RoutesResolver","level":"info","message":"StoreController {/api/stores}:","timestamp":"2025-05-24T07:42:17.105Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/stores, GET} route","timestamp":"2025-05-24T07:42:17.107Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/stores/:id, GET} route","timestamp":"2025-05-24T07:42:17.108Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/stores, POST} route","timestamp":"2025-05-24T07:42:17.109Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/stores/:id, PUT} route","timestamp":"2025-05-24T07:42:17.110Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/stores/:id, DELETE} route","timestamp":"2025-05-24T07:42:17.111Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/stores/:storeId/products, GET} route","timestamp":"2025-05-24T07:42:17.112Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/stores/:storeId/products, POST} route","timestamp":"2025-05-24T07:42:17.114Z"}
{"context":"LegacyRouteConverter","level":"warn","message":"Unsupported route path: \"/api/stores/*\". In previous versions, the symbols ?, *, and + were used to denote optional or repeating path parameters. The latest version of \"path-to-regexp\" now requires the use of named parameters. For example, instead of using a route like /users/* to capture all routes starting with \"/users\", you should use /users/*path. For more details, refer to the migration guide. Attempting to auto-convert...","timestamp":"2025-05-24T07:42:17.115Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/stores/*, GET} route","timestamp":"2025-05-24T07:42:17.116Z"}
{"context":"LegacyRouteConverter","level":"warn","message":"Unsupported route path: \"/api/stores/*\". In previous versions, the symbols ?, *, and + were used to denote optional or repeating path parameters. The latest version of \"path-to-regexp\" now requires the use of named parameters. For example, instead of using a route like /users/* to capture all routes starting with \"/users\", you should use /users/*path. For more details, refer to the migration guide. Attempting to auto-convert...","timestamp":"2025-05-24T07:42:17.117Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/stores/*, POST} route","timestamp":"2025-05-24T07:42:17.117Z"}
{"context":"LegacyRouteConverter","level":"warn","message":"Unsupported route path: \"/api/stores/*\". In previous versions, the symbols ?, *, and + were used to denote optional or repeating path parameters. The latest version of \"path-to-regexp\" now requires the use of named parameters. For example, instead of using a route like /users/* to capture all routes starting with \"/users\", you should use /users/*path. For more details, refer to the migration guide. Attempting to auto-convert...","timestamp":"2025-05-24T07:42:17.118Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/stores/*, PUT} route","timestamp":"2025-05-24T07:42:17.119Z"}
{"context":"LegacyRouteConverter","level":"warn","message":"Unsupported route path: \"/api/stores/*\". In previous versions, the symbols ?, *, and + were used to denote optional or repeating path parameters. The latest version of \"path-to-regexp\" now requires the use of named parameters. For example, instead of using a route like /users/* to capture all routes starting with \"/users\", you should use /users/*path. For more details, refer to the migration guide. Attempting to auto-convert...","timestamp":"2025-05-24T07:42:17.122Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/stores/*, DELETE} route","timestamp":"2025-05-24T07:42:17.123Z"}
{"context":"LegacyRouteConverter","level":"warn","message":"Unsupported route path: \"/api/stores/*\". In previous versions, the symbols ?, *, and + were used to denote optional or repeating path parameters. The latest version of \"path-to-regexp\" now requires the use of named parameters. For example, instead of using a route like /users/* to capture all routes starting with \"/users\", you should use /users/*path. For more details, refer to the migration guide. Attempting to auto-convert...","timestamp":"2025-05-24T07:42:17.124Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/stores/*, PATCH} route","timestamp":"2025-05-24T07:42:17.124Z"}
{"context":"RoutesResolver","level":"info","message":"ProductController {/api/products}:","timestamp":"2025-05-24T07:42:17.125Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/products, GET} route","timestamp":"2025-05-24T07:42:17.126Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/products/:id, GET} route","timestamp":"2025-05-24T07:42:17.133Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/products, POST} route","timestamp":"2025-05-24T07:42:17.136Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/products/:id, PUT} route","timestamp":"2025-05-24T07:42:17.137Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/products/:id, DELETE} route","timestamp":"2025-05-24T07:42:17.139Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/products/store/:storeId, GET} route","timestamp":"2025-05-24T07:42:17.140Z"}
{"context":"LegacyRouteConverter","level":"warn","message":"Unsupported route path: \"/api/products/*\". In previous versions, the symbols ?, *, and + were used to denote optional or repeating path parameters. The latest version of \"path-to-regexp\" now requires the use of named parameters. For example, instead of using a route like /users/* to capture all routes starting with \"/users\", you should use /users/*path. For more details, refer to the migration guide. Attempting to auto-convert...","timestamp":"2025-05-24T07:42:17.142Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/products/*, GET} route","timestamp":"2025-05-24T07:42:17.143Z"}
{"context":"LegacyRouteConverter","level":"warn","message":"Unsupported route path: \"/api/products/*\". In previous versions, the symbols ?, *, and + were used to denote optional or repeating path parameters. The latest version of \"path-to-regexp\" now requires the use of named parameters. For example, instead of using a route like /users/* to capture all routes starting with \"/users\", you should use /users/*path. For more details, refer to the migration guide. Attempting to auto-convert...","timestamp":"2025-05-24T07:42:17.144Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/products/*, POST} route","timestamp":"2025-05-24T07:42:17.145Z"}
{"context":"LegacyRouteConverter","level":"warn","message":"Unsupported route path: \"/api/products/*\". In previous versions, the symbols ?, *, and + were used to denote optional or repeating path parameters. The latest version of \"path-to-regexp\" now requires the use of named parameters. For example, instead of using a route like /users/* to capture all routes starting with \"/users\", you should use /users/*path. For more details, refer to the migration guide. Attempting to auto-convert...","timestamp":"2025-05-24T07:42:17.361Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/products/*, PUT} route","timestamp":"2025-05-24T07:42:17.365Z"}
{"context":"LegacyRouteConverter","level":"warn","message":"Unsupported route path: \"/api/products/*\". In previous versions, the symbols ?, *, and + were used to denote optional or repeating path parameters. The latest version of \"path-to-regexp\" now requires the use of named parameters. For example, instead of using a route like /users/* to capture all routes starting with \"/users\", you should use /users/*path. For more details, refer to the migration guide. Attempting to auto-convert...","timestamp":"2025-05-24T07:42:17.367Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/products/*, DELETE} route","timestamp":"2025-05-24T07:42:17.368Z"}
{"context":"LegacyRouteConverter","level":"warn","message":"Unsupported route path: \"/api/products/*\". In previous versions, the symbols ?, *, and + were used to denote optional or repeating path parameters. The latest version of \"path-to-regexp\" now requires the use of named parameters. For example, instead of using a route like /users/* to capture all routes starting with \"/users\", you should use /users/*path. For more details, refer to the migration guide. Attempting to auto-convert...","timestamp":"2025-05-24T07:42:17.409Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/products/*, PATCH} route","timestamp":"2025-05-24T07:42:17.411Z"}
{"context":"RoutesResolver","level":"info","message":"HealthController {/api/health}:","timestamp":"2025-05-24T07:42:17.412Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/health, GET} route","timestamp":"2025-05-24T07:42:17.413Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/health/services, GET} route","timestamp":"2025-05-24T07:42:17.414Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/health/service/:name, GET} route","timestamp":"2025-05-24T07:42:17.415Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/health/circuit-breaker, GET} route","timestamp":"2025-05-24T07:42:17.416Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-05-24T07:42:17.451Z"}
{"level":"info","message":"API Gateway is running on: http://localhost:3000","timestamp":"2025-05-24T07:42:17.522Z"}
{"level":"info","message":"Swagger documentation is available at: http://localhost:3000/api/docs","timestamp":"2025-05-24T07:42:17.532Z"}
{"level":"info","message":"Health dashboard is available at: http://localhost:3000/dashboard/health-dashboard.html","timestamp":"2025-05-24T07:42:17.533Z"}
{"context":"CorrelationIdMiddleware","level":"info","message":"Request GET /api/health assigned correlation ID: dc1c2c60-ef6b-4ab9-bac5-dfb3bd332250","timestamp":"2025-05-24T07:43:52.986Z"}
{"context":"LoggingInterceptor","level":"info","message":"[req-1748072632992-tv0p7hcok] [dc1c2c60-ef6b-4ab9-bac5-dfb3bd332250] GET /api/health - User-Agent: curl/8.12.1 - Content-Length: 0","timestamp":"2025-05-24T07:43:52.992Z"}
{"context":"HealthController","level":"info","message":"Overall health check requested","timestamp":"2025-05-24T07:43:52.995Z"}
{"context":"LoggingInterceptor","level":"error","message":"[req-1748072632992-tv0p7hcok] [dc1c2c60-ef6b-4ab9-bac5-dfb3bd332250] GET /api/health 500 - 47ms - The following path is invalid (should be X:\\...): /","stack":[null],"timestamp":"2025-05-24T07:43:53.040Z"}
{"context":"AllExceptionsFilter","level":"error","message":"GET /api/health 500 - The following path is invalid (should be X:\\...): /","stack":["InvalidPathError: The following path is invalid (should be X:\\...): /\n    at checkWin32 (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\check-disk-space\\dist\\check-disk-space.cjs:155:35)\n    at DiskHealthIndicator.checkDiskSpace (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\check-disk-space\\dist\\check-disk-space.cjs:202:16)\n    at DiskHealthIndicator.<anonymous> (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\@nestjs\\terminus\\dist\\health-indicator\\disk\\disk.health.js:75:47)\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\@nestjs\\terminus\\dist\\health-indicator\\disk\\disk.health.js:20:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\@nestjs\\terminus\\dist\\health-indicator\\disk\\disk.health.js:16:12)\n    at DiskHealthIndicator.checkStorage (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\@nestjs\\terminus\\dist\\health-indicator\\disk\\disk.health.js:73:16)\n    at C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\dist\\main.js:1481:35\n    at HealthCheckExecutor.<anonymous> (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\@nestjs\\terminus\\dist\\health-check\\health-check-executor.service.js:64:135)"],"timestamp":"2025-05-24T07:43:53.042Z"}
{"context":"CorrelationIdMiddleware","level":"info","message":"Request GET /api/users assigned correlation ID: 0a6acde0-8d30-4cda-a35e-22928b96d00a","timestamp":"2025-05-24T08:44:14.874Z"}
{"context":"LoggingInterceptor","level":"info","message":"[req-*************-n7jpfutjl] [0a6acde0-8d30-4cda-a35e-22928b96d00a] GET /api/users - User-Agent: curl/8.12.1 - Content-Length: 0","timestamp":"2025-05-24T08:44:14.877Z"}
{"context":"UserController","level":"info","message":"Forwarding GET /users request","timestamp":"2025-05-24T08:44:14.884Z"}
{"context":"RoutingService","level":"info","message":"Forwarding GET request to http://localhost:3001/api/users","timestamp":"2025-05-24T08:44:14.887Z"}
{"context":"RoutingService","level":"error","message":"Error forwarding request to http://localhost:3001/api/users: ","stack":["AggregateError\n    at AxiosError.from (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\axios\\dist\\node\\axios.cjs:863:14)\n    at RedirectableRequest.handleRequestError (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\axios\\dist\\node\\axios.cjs:3187:25)\n    at RedirectableRequest.emit (node:events:518:28)\n    at eventHandlers.<computed> (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\follow-redirects\\index.js:49:24)\n    at ClientRequest.emit (node:events:518:28)\n    at emitErrorEvent (node:_http_client:104:11)\n    at Socket.socketErrorListener (node:_http_client:518:5)\n    at Socket.emit (node:events:518:28)\n    at emitErrorNT (node:internal/streams/destroy:170:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:129:3)\n    at Axios.request (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CircuitBreakerService.execute (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\dist\\main.js:585:28)"],"timestamp":"2025-05-24T08:44:14.903Z"}
{"context":"LoggingInterceptor","level":"error","message":"[req-*************-n7jpfutjl] [0a6acde0-8d30-4cda-a35e-22928b96d00a] GET /api/users 500 - 27ms - Internal Server Error","stack":[null],"timestamp":"2025-05-24T08:44:14.905Z"}
{"context":"AllExceptionsFilter","level":"error","message":"GET /api/users 500 - Internal Server Error","stack":["HttpException: Internal Server Error\n    at C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\dist\\main.js:506:49\n    at Observable.init [as _subscribe] (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\rxjs\\dist\\cjs\\internal\\observable\\throwError.js:8:64)\n    at Observable._trySubscribe (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\rxjs\\dist\\cjs\\internal\\Observable.js:41:25)\n    at C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\rxjs\\dist\\cjs\\internal\\Observable.js:35:31\n    at Object.errorContext (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\rxjs\\dist\\cjs\\internal\\util\\errorContext.js:22:9)\n    at Observable.subscribe (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\rxjs\\dist\\cjs\\internal\\Observable.js:26:24)\n    at C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\rxjs\\dist\\cjs\\internal\\operators\\catchError.js:17:31\n    at OperatorSubscriber._this._error (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\rxjs\\dist\\cjs\\internal\\operators\\OperatorSubscriber.js:43:21)\n    at Subscriber.error (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\rxjs\\dist\\cjs\\internal\\Subscriber.js:60:18)\n    at Subscriber._error (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\rxjs\\dist\\cjs\\internal\\Subscriber.js:84:30)"],"timestamp":"2025-05-24T08:44:14.906Z"}
