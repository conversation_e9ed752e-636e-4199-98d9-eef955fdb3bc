const bcrypt = require('bcrypt');
const { Client } = require('pg');

async function testLogin() {
  try {
    console.log('Connecting to PostgreSQL database...');
    const client = new Client({
      host: 'localhost',
      port: 5432,
      database: 'social_commerce',
      user: 'postgres',
      password: '1111'
    });

    await client.connect();
    console.log('Connected to PostgreSQL database');

    // Create a test user
    const username = 'testuser456';
    const email = '<EMAIL>';
    const password = 'password123';
    const passwordHash = await bcrypt.hash(password, 10);
    
    console.log('Creating test user...');
    console.log('Password hash:', passwordHash);
    
    try {
      // Check if user already exists
      const checkResult = await client.query(
        'SELECT * FROM users WHERE username = $1 OR email = $2',
        [username, email]
      );
      
      if (checkResult.rows.length > 0) {
        console.log('User already exists, skipping creation');
      } else {
        // Create the user
        await client.query(
          'INSERT INTO users (id, username, email, password_hash, role, is_email_verified, is_phone_verified, created_at, updated_at) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)',
          [
            '00000000-0000-0000-0000-000000000456', // UUID
            username,
            email,
            passwordHash,
            'user', // role
            false, // isEmailVerified
            false, // isPhoneVerified
            new Date(), // createdAt
            new Date() // updatedAt
          ]
        );
        console.log('Test user created successfully');
      }
    } catch (error) {
      console.error('Error creating test user:', error);
    }
    
    // Find the user
    console.log('Finding user...');
    const findResult = await client.query(
      'SELECT * FROM users WHERE username = $1 OR email = $2',
      [username, email]
    );
    
    if (findResult.rows.length === 0) {
      console.log('User not found');
      await client.end();
      return;
    }
    
    const user = findResult.rows[0];
    console.log('User found:', {
      id: user.id,
      username: user.username,
      email: user.email,
      passwordHash: user.password_hash ? 'Present (not shown)' : 'Missing'
    });
    
    // Test password verification
    console.log('Testing password verification...');
    const isPasswordValid = await bcrypt.compare(password, user.password_hash);
    console.log('Password valid?', isPasswordValid);
    
    // Create a new hash for comparison
    const newHash = await bcrypt.hash(password, 10);
    console.log('New hash created:', newHash);
    
    // Compare the new hash with the stored hash
    const hashesMatch = await bcrypt.compare(password, newHash);
    console.log('New hash valid?', hashesMatch);
    
    await client.end();
    console.log('Disconnected from PostgreSQL database');
    
    console.log('\nLogin credentials for testing:');
    console.log('Username:', username);
    console.log('Email:', email);
    console.log('Password:', password);
  } catch (error) {
    console.error('Error:', error);
  }
}

testLogin();
