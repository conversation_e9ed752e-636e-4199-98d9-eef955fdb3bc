(()=>{var e={};e.id=3090,e.ids=[3090],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},82681:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>n});var s=t(67096),a=t(16132),l=t(37284),i=t.n(l),d=t(32564),o={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);t.d(r,o);let n=["",{children:["dashboard",{children:["products",{children:["import",{children:["template",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,57538)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\dashboard\\products\\import\\template\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,68182)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9291,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\dashboard\\products\\import\\template\\page.tsx"],m="/dashboard/products/import/template/page",p={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/dashboard/products/import/template/page",pathname:"/dashboard/products/import/template",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},24988:(e,r,t)=>{Promise.resolve().then(t.bind(t,60151))},60151:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>ProductImportTemplatePage});var s=t(30784),a=t(9885),l=t(27870),i=t(14379),d=t(57114),o=t(11440),n=t.n(o),c=t(59872),m=t(93003),p=t(75444);function ProductImportTemplatePage(){let{t:e}=(0,l.$G)("import"),{isRtl:r}=(0,i.g)();(0,d.useRouter)();let[t,o]=(0,a.useState)(m.X2.CSV),[x,{isLoading:u,error:h}]=(0,p._G)(),handleDownload=async()=>{try{let e=await x({entityType:m.TS.PRODUCT,fileType:t}).unwrap();window.open(e.downloadUrl,"_blank")}catch(e){console.error("Error downloading template:",e)}};return s.jsx("div",{className:`min-h-screen p-6 ${r?"text-right":"text-left"}`,children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsxs)("div",{className:`flex flex-col md:flex-row md:items-center md:justify-between mb-8 gap-4 ${r?"md:flex-row-reverse":""}`,children:[(0,s.jsxs)("div",{children:[s.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2",children:e("downloadTemplate","Download Template")}),s.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:e("downloadTemplateDescription","Download a template file with the correct format for importing {{entityType}} data.",{entityType:e("entityTypes.product","product").toLowerCase()})})]}),s.jsx("div",{children:s.jsx(n(),{href:"/dashboard/products/import",children:s.jsx(c.Z,{variant:"outline",children:e("back","Back")})})})]}),(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6",children:[(0,s.jsxs)("div",{className:"mb-6",children:[s.jsx("label",{htmlFor:"file-type",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("fileType","File Type")}),(0,s.jsxs)("select",{id:"file-type",value:t,onChange:e=>o(e.target.value),className:"block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-700 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md",children:[s.jsx("option",{value:m.X2.CSV,children:"CSV"}),s.jsx("option",{value:m.X2.EXCEL,children:"Excel"})]})]}),(0,s.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-900 p-4 rounded-lg mb-6",children:[s.jsx("h3",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("templateIncludes","Template Includes")}),(0,s.jsxs)("ul",{className:"list-disc list-inside text-sm text-gray-500 dark:text-gray-400 space-y-1",children:[s.jsx("li",{children:e("fields.name","Name")}),s.jsx("li",{children:e("fields.sku","SKU")}),s.jsx("li",{children:e("fields.description","Description")}),s.jsx("li",{children:e("fields.price","Price")}),s.jsx("li",{children:e("fields.comparePrice","Compare Price")}),s.jsx("li",{children:e("fields.costPrice","Cost Price")}),s.jsx("li",{children:e("fields.status","Status")}),s.jsx("li",{children:e("fields.categories","Categories")}),s.jsx("li",{children:e("fields.tags","Tags")}),s.jsx("li",{children:e("fields.images","Images")}),s.jsx("li",{children:e("fields.weight","Weight")}),s.jsx("li",{children:e("fields.inventory","Inventory")}),s.jsx("li",{children:e("fields.attributes","Attributes")})]})]}),s.jsx("div",{className:"flex justify-end",children:s.jsx(c.Z,{onClick:handleDownload,isLoading:u,disabled:u,children:e("download","Download")})}),h&&s.jsx("div",{className:"mt-4 p-4 bg-red-50 dark:bg-red-900/20 rounded-md",children:(0,s.jsxs)("div",{className:"flex",children:[s.jsx("div",{className:"flex-shrink-0",children:s.jsx("svg",{className:"h-5 w-5 text-red-400",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:s.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,s.jsxs)("div",{className:"ml-3",children:[s.jsx("h3",{className:"text-sm font-medium text-red-800 dark:text-red-200",children:e("downloadError","Download Error")}),s.jsx("div",{className:"mt-2 text-sm text-red-700 dark:text-red-300",children:s.jsx("p",{children:e("downloadErrorDescription","There was an error downloading the template. Please try again.")})})]})]})})]})]})})}},57538:(e,r,t)=>{"use strict";t.r(r),t.d(r,{$$typeof:()=>i,__esModule:()=>l,default:()=>o});var s=t(95153);let a=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\dashboard\products\import\template\page.tsx`),{__esModule:l,$$typeof:i}=a,d=a.default,o=d}};var r=require("../../../../../webpack-runtime.js");r.C(e);var __webpack_exec__=e=>r(r.s=e),t=r.X(0,[2103,2765,8576,1070],()=>__webpack_exec__(82681));module.exports=t})();