"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateCommand = generateCommand;
const chalk_1 = require("chalk");
const inquirer_1 = require("inquirer");
const fs = require("fs-extra");
const path = require("path");
const paths_1 = require("../utils/paths");
function toKebabCase(str) {
    return str
        .replace(/([a-z])([A-Z])/g, '$1-$2')
        .replace(/\s+/g, '-')
        .toLowerCase();
}
function toCamelCase(str) {
    return str
        .replace(/-([a-z])/g, (_, letter) => letter.toUpperCase())
        .replace(/^\w/, c => c.toLowerCase());
}
function toPascalCase(str) {
    return str
        .replace(/-([a-z])/g, (_, letter) => letter.toUpperCase())
        .replace(/^\w/, c => c.toUpperCase());
}
async function generateService(name) {
    const kebabName = toKebabCase(name);
    const pascalName = toPascalCase(name);
    const camelName = toCamelCase(name);
    const answers = await inquirer_1.default.prompt([
        {
            type: 'input',
            name: 'serviceName',
            message: 'Enter the service name (e.g., user, product):',
            default: 'user',
        },
    ]);
    const serviceName = answers.serviceName;
    const serviceDir = path.join((0, paths_1.getServicesDir)(), `${serviceName}-service`);
    if (!fs.existsSync(serviceDir)) {
        console.log(chalk_1.default.yellow(`Service directory ${serviceName}-service does not exist. Creating it...`));
        fs.mkdirSync(serviceDir, { recursive: true });
    }
    const srcDir = path.join(serviceDir, 'src');
    if (!fs.existsSync(srcDir)) {
        fs.mkdirSync(srcDir, { recursive: true });
    }
    const serviceContent = `import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class ${pascalName}Service {
  private readonly logger = new Logger(${pascalName}Service.name);

  constructor() {}

  async findAll() {
    this.logger.log('Finding all ${camelName}s');
    return [];
  }

  async findOne(id: string) {
    this.logger.log(\`Finding ${camelName} with ID: \${id}\`);
    return { id };
  }

  async create(data: any) {
    this.logger.log('Creating new ${camelName}');
    return { id: 'new-id', ...data };
  }

  async update(id: string, data: any) {
    this.logger.log(\`Updating ${camelName} with ID: \${id}\`);
    return { id, ...data };
  }

  async remove(id: string) {
    this.logger.log(\`Removing ${camelName} with ID: \${id}\`);
    return { id };
  }
}`;
    const serviceFilePath = path.join(srcDir, `${kebabName}.service.ts`);
    fs.writeFileSync(serviceFilePath, serviceContent);
    console.log(chalk_1.default.green(`Service file created: ${serviceFilePath}`));
}
async function generateController(name) {
    const kebabName = toKebabCase(name);
    const pascalName = toPascalCase(name);
    const camelName = toCamelCase(name);
    const answers = await inquirer_1.default.prompt([
        {
            type: 'input',
            name: 'serviceName',
            message: 'Enter the service name (e.g., user, product):',
            default: 'user',
        },
    ]);
    const serviceName = answers.serviceName;
    const serviceDir = path.join((0, paths_1.getServicesDir)(), `${serviceName}-service`);
    if (!fs.existsSync(serviceDir)) {
        console.log(chalk_1.default.yellow(`Service directory ${serviceName}-service does not exist. Creating it...`));
        fs.mkdirSync(serviceDir, { recursive: true });
    }
    const srcDir = path.join(serviceDir, 'src');
    if (!fs.existsSync(srcDir)) {
        fs.mkdirSync(srcDir, { recursive: true });
    }
    const controllerContent = `import { Controller, Get, Post, Body, Param, Delete, Put, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { ${pascalName}Service } from './${kebabName}.service';

@ApiTags('${camelName}s')
@Controller('${camelName}s')
export class ${pascalName}Controller {
  private readonly logger = new Logger(${pascalName}Controller.name);

  constructor(private readonly ${camelName}Service: ${pascalName}Service) {}

  @Get()
  @ApiOperation({ summary: 'Get all ${camelName}s' })
  @ApiResponse({ status: 200, description: 'Return all ${camelName}s' })
  async findAll() {
    this.logger.log('Getting all ${camelName}s');
    return this.${camelName}Service.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a ${camelName} by ID' })
  @ApiResponse({ status: 200, description: 'Return the ${camelName}' })
  @ApiResponse({ status: 404, description: '${pascalName} not found' })
  async findOne(@Param('id') id: string) {
    this.logger.log(\`Getting ${camelName} with ID: \${id}\`);
    return this.${camelName}Service.findOne(id);
  }

  @Post()
  @ApiOperation({ summary: 'Create a new ${camelName}' })
  @ApiResponse({ status: 201, description: '${pascalName} created successfully' })
  async create(@Body() createDto: any) {
    this.logger.log('Creating new ${camelName}');
    return this.${camelName}Service.create(createDto);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a ${camelName}' })
  @ApiResponse({ status: 200, description: '${pascalName} updated successfully' })
  @ApiResponse({ status: 404, description: '${pascalName} not found' })
  async update(@Param('id') id: string, @Body() updateDto: any) {
    this.logger.log(\`Updating ${camelName} with ID: \${id}\`);
    return this.${camelName}Service.update(id, updateDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a ${camelName}' })
  @ApiResponse({ status: 200, description: '${pascalName} deleted successfully' })
  @ApiResponse({ status: 404, description: '${pascalName} not found' })
  async remove(@Param('id') id: string) {
    this.logger.log(\`Removing ${camelName} with ID: \${id}\`);
    return this.${camelName}Service.remove(id);
  }
}`;
    const controllerFilePath = path.join(srcDir, `${kebabName}.controller.ts`);
    fs.writeFileSync(controllerFilePath, controllerContent);
    console.log(chalk_1.default.green(`Controller file created: ${controllerFilePath}`));
}
async function generateModule(name) {
    const kebabName = toKebabCase(name);
    const pascalName = toPascalCase(name);
    const camelName = toCamelCase(name);
    const answers = await inquirer_1.default.prompt([
        {
            type: 'input',
            name: 'serviceName',
            message: 'Enter the service name (e.g., user, product):',
            default: 'user',
        },
    ]);
    const serviceName = answers.serviceName;
    const serviceDir = path.join((0, paths_1.getServicesDir)(), `${serviceName}-service`);
    if (!fs.existsSync(serviceDir)) {
        console.log(chalk_1.default.yellow(`Service directory ${serviceName}-service does not exist. Creating it...`));
        fs.mkdirSync(serviceDir, { recursive: true });
    }
    const srcDir = path.join(serviceDir, 'src');
    if (!fs.existsSync(srcDir)) {
        fs.mkdirSync(srcDir, { recursive: true });
    }
    const moduleContent = `import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ${pascalName}Controller } from './${kebabName}.controller';
import { ${pascalName}Service } from './${kebabName}.service';
import { ${pascalName} } from './entities/${kebabName}.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([${pascalName}]),
  ],
  controllers: [${pascalName}Controller],
  providers: [${pascalName}Service],
  exports: [${pascalName}Service],
})
export class ${pascalName}Module {}`;
    const moduleFilePath = path.join(srcDir, `${kebabName}.module.ts`);
    fs.writeFileSync(moduleFilePath, moduleContent);
    console.log(chalk_1.default.green(`Module file created: ${moduleFilePath}`));
}
async function generateDto(name) {
    const kebabName = toKebabCase(name);
    const pascalName = toPascalCase(name);
    const answers = await inquirer_1.default.prompt([
        {
            type: 'input',
            name: 'serviceName',
            message: 'Enter the service name (e.g., user, product):',
            default: 'user',
        },
        {
            type: 'list',
            name: 'dtoType',
            message: 'What type of DTO do you want to generate?',
            choices: ['create', 'update', 'response'],
            default: 'create',
        },
    ]);
    const serviceName = answers.serviceName;
    const dtoType = answers.dtoType;
    const serviceDir = path.join((0, paths_1.getServicesDir)(), `${serviceName}-service`);
    if (!fs.existsSync(serviceDir)) {
        console.log(chalk_1.default.yellow(`Service directory ${serviceName}-service does not exist. Creating it...`));
        fs.mkdirSync(serviceDir, { recursive: true });
    }
    const srcDir = path.join(serviceDir, 'src');
    if (!fs.existsSync(srcDir)) {
        fs.mkdirSync(srcDir, { recursive: true });
    }
    const dtoDir = path.join(srcDir, 'dto');
    if (!fs.existsSync(dtoDir)) {
        fs.mkdirSync(dtoDir, { recursive: true });
    }
    let dtoContent = '';
    if (dtoType === 'create') {
        dtoContent = `import { IsString, IsNotEmpty, IsOptional, IsNumber } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class Create${pascalName}Dto {
  @ApiProperty({ description: 'The name of the ${kebabName}' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: 'The description of the ${kebabName}' })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: 'The price of the ${kebabName}' })
  @IsNumber()
  @IsOptional()
  price?: number;
}`;
    }
    else if (dtoType === 'update') {
        dtoContent = `import { IsString, IsOptional, IsNumber } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class Update${pascalName}Dto {
  @ApiProperty({ description: 'The name of the ${kebabName}' })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ description: 'The description of the ${kebabName}' })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: 'The price of the ${kebabName}' })
  @IsNumber()
  @IsOptional()
  price?: number;
}`;
    }
    else if (dtoType === 'response') {
        dtoContent = `import { ApiProperty } from '@nestjs/swagger';

export class ${pascalName}ResponseDto {
  @ApiProperty({ description: 'The ID of the ${kebabName}' })
  id: string;

  @ApiProperty({ description: 'The name of the ${kebabName}' })
  name: string;

  @ApiProperty({ description: 'The description of the ${kebabName}' })
  description?: string;

  @ApiProperty({ description: 'The price of the ${kebabName}' })
  price?: number;

  @ApiProperty({ description: 'The creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'The update timestamp' })
  updatedAt: Date;
}`;
    }
    const dtoFilePath = path.join(dtoDir, `${dtoType}-${kebabName}.dto.ts`);
    fs.writeFileSync(dtoFilePath, dtoContent);
    console.log(chalk_1.default.green(`DTO file created: ${dtoFilePath}`));
}
async function generateEntity(name) {
    const kebabName = toKebabCase(name);
    const pascalName = toPascalCase(name);
    const answers = await inquirer_1.default.prompt([
        {
            type: 'input',
            name: 'serviceName',
            message: 'Enter the service name (e.g., user, product):',
            default: 'user',
        },
    ]);
    const serviceName = answers.serviceName;
    const serviceDir = path.join((0, paths_1.getServicesDir)(), `${serviceName}-service`);
    if (!fs.existsSync(serviceDir)) {
        console.log(chalk_1.default.yellow(`Service directory ${serviceName}-service does not exist. Creating it...`));
        fs.mkdirSync(serviceDir, { recursive: true });
    }
    const srcDir = path.join(serviceDir, 'src');
    if (!fs.existsSync(srcDir)) {
        fs.mkdirSync(srcDir, { recursive: true });
    }
    const entitiesDir = path.join(srcDir, 'entities');
    if (!fs.existsSync(entitiesDir)) {
        fs.mkdirSync(entitiesDir, { recursive: true });
    }
    const entityContent = `import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('${kebabName}s')
export class ${pascalName} {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  price: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}`;
    const entityFilePath = path.join(entitiesDir, `${kebabName}.entity.ts`);
    fs.writeFileSync(entityFilePath, entityContent);
    console.log(chalk_1.default.green(`Entity file created: ${entityFilePath}`));
}
async function generateRepository(name) {
    const kebabName = toKebabCase(name);
    const pascalName = toPascalCase(name);
    const camelName = toCamelCase(name);
    const answers = await inquirer_1.default.prompt([
        {
            type: 'input',
            name: 'serviceName',
            message: 'Enter the service name (e.g., user, product):',
            default: 'user',
        },
    ]);
    const serviceName = answers.serviceName;
    const serviceDir = path.join((0, paths_1.getServicesDir)(), `${serviceName}-service`);
    if (!fs.existsSync(serviceDir)) {
        console.log(chalk_1.default.yellow(`Service directory ${serviceName}-service does not exist. Creating it...`));
        fs.mkdirSync(serviceDir, { recursive: true });
    }
    const srcDir = path.join(serviceDir, 'src');
    if (!fs.existsSync(srcDir)) {
        fs.mkdirSync(srcDir, { recursive: true });
    }
    const repositoriesDir = path.join(srcDir, 'repositories');
    if (!fs.existsSync(repositoriesDir)) {
        fs.mkdirSync(repositoriesDir, { recursive: true });
    }
    const repositoryContent = `import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ${pascalName} } from '../entities/${kebabName}.entity';

@Injectable()
export class ${pascalName}Repository {
  private readonly logger = new Logger(${pascalName}Repository.name);

  constructor(
    @InjectRepository(${pascalName})
    private readonly repository: Repository<${pascalName}>,
  ) {}

  async findAll(): Promise<${pascalName}[]> {
    this.logger.log('Finding all ${camelName}s');
    return this.repository.find();
  }

  async findOne(id: string): Promise<${pascalName}> {
    this.logger.log(\`Finding ${camelName} with ID: \${id}\`);
    return this.repository.findOne({ where: { id } });
  }

  async create(data: Partial<${pascalName}>): Promise<${pascalName}> {
    this.logger.log('Creating new ${camelName}');
    const entity = this.repository.create(data);
    return this.repository.save(entity);
  }

  async update(id: string, data: Partial<${pascalName}>): Promise<${pascalName}> {
    this.logger.log(\`Updating ${camelName} with ID: \${id}\`);
    await this.repository.update(id, data);
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    this.logger.log(\`Removing ${camelName} with ID: \${id}\`);
    await this.repository.delete(id);
  }
}`;
    const repositoryFilePath = path.join(repositoriesDir, `${kebabName}.repository.ts`);
    fs.writeFileSync(repositoryFilePath, repositoryContent);
    console.log(chalk_1.default.green(`Repository file created: ${repositoryFilePath}`));
}
function generateCommand(program) {
    program
        .command('generate [type] [name]')
        .description('Generate code (service, controller, etc.)')
        .action(async (type, name) => {
        try {
            if (!type) {
                const answers = await inquirer_1.default.prompt([
                    {
                        type: 'list',
                        name: 'type',
                        message: 'What do you want to generate?',
                        choices: ['service', 'controller', 'module', 'dto', 'entity', 'repository'],
                    },
                ]);
                type = answers.type;
            }
            if (!name) {
                const answers = await inquirer_1.default.prompt([
                    {
                        type: 'input',
                        name: 'name',
                        message: `Enter a name for the ${type}:`,
                        validate: (input) => {
                            if (!input) {
                                return 'Name is required';
                            }
                            return true;
                        },
                    },
                ]);
                name = answers.name;
            }
            if (!name) {
                console.error(chalk_1.default.red('Name is required'));
                return;
            }
            switch (type) {
                case 'service':
                    await generateService(name);
                    break;
                case 'controller':
                    await generateController(name);
                    break;
                case 'module':
                    await generateModule(name);
                    break;
                case 'dto':
                    await generateDto(name);
                    break;
                case 'entity':
                    await generateEntity(name);
                    break;
                case 'repository':
                    await generateRepository(name);
                    break;
                default:
                    console.error(chalk_1.default.red(`Unknown type: ${type}`));
                    return;
            }
            console.log(chalk_1.default.green(`${type} ${name} generated successfully`));
        }
        catch (error) {
            console.error(chalk_1.default.red(`Error: ${error.message}`));
            process.exit(1);
        }
    });
}
//# sourceMappingURL=generate.js.map