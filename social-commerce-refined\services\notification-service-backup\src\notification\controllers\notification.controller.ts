import {
  <PERSON>,
  Post,
  Body,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
} from '@nestjs/swagger';
import { NotificationService } from '../services/notification.service';
import { SendEmailDto, SendSmsDto } from '../dto';

@ApiTags('notifications')
@Controller('notifications')
export class NotificationController {
  private readonly logger = new Logger(NotificationController.name);

  constructor(private readonly notificationService: NotificationService) {}

  @Post('email')
  @ApiOperation({ summary: 'Send email notification' })
  @ApiResponse({
    status: 201,
    description: 'Email sent successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        messageId: { type: 'string' },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async sendEmail(@Body() sendEmailDto: SendEmailDto) {
    this.logger.log(`HTTP request to send email to: ${sendEmailDto.to}`);
    return this.notificationService.sendEmail(sendEmailDto);
  }

  @Post('sms')
  @ApiOperation({ summary: 'Send SMS notification' })
  @ApiResponse({
    status: 201,
    description: 'SMS sent successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        messageId: { type: 'string' },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async sendSms(@Body() sendSmsDto: SendSmsDto) {
    this.logger.log(`HTTP request to send SMS to: ${sendSmsDto.to}`);
    return this.notificationService.sendSms(sendSmsDto);
  }

  @Post('welcome-email')
  @ApiOperation({ summary: 'Send welcome email to new user' })
  @ApiResponse({
    status: 201,
    description: 'Welcome email sent successfully',
  })
  async sendWelcomeEmail(
    @Body() data: { email: string; firstName?: string },
  ) {
    this.logger.log(`HTTP request to send welcome email to: ${data.email}`);
    return this.notificationService.sendWelcomeEmail(data);
  }

  @Post('verification-email')
  @ApiOperation({ summary: 'Send email verification' })
  @ApiResponse({
    status: 201,
    description: 'Verification email sent successfully',
  })
  async sendVerificationEmail(
    @Body() data: { email: string; verificationToken: string; firstName?: string },
  ) {
    this.logger.log(`HTTP request to send verification email to: ${data.email}`);
    return this.notificationService.sendVerificationEmail(data);
  }
}
