(()=>{var e={};e.id=2312,e.ids=[2312],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},9188:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>l,routeModule:()=>x,tree:()=>c});var r=s(67096),n=s(16132),a=s(37284),o=s.n(a),i=s(32564),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);s.d(t,d);let c=["",{children:["stores",{children:["[username]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,48073)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\stores\\[username]\\edit\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9291,23)),"next/dist/client/components/not-found-error"]}],l=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\stores\\[username]\\edit\\page.tsx"],m="/stores/[username]/edit/page",u={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/stores/[username]/edit/page",pathname:"/stores/[username]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},69569:(e,t,s)=>{Promise.resolve().then(s.bind(s,12780))},12780:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>EditStorePage});var r=s(30784);s(9885);var n=s(57114),a=s(16027),o=s(59872),i=s(77783);function EditStorePage({params:e}){let{username:t}=e,s=(0,n.useRouter)(),{data:d,isLoading:c,error:l}=(0,i.YU)(t);return c?r.jsx("div",{className:"min-h-screen p-6 md:p-12",children:(0,r.jsxs)("div",{className:"max-w-3xl mx-auto text-center",children:[r.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600 mx-auto"}),r.jsx("p",{className:"mt-4 text-lg",children:"Loading store..."})]})}):l||!d?r.jsx("div",{className:"min-h-screen p-6 md:p-12",children:(0,r.jsxs)("div",{className:"max-w-3xl mx-auto text-center",children:[r.jsx("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:"Store Not Found"}),r.jsx("p",{className:"mb-6",children:"The store you're looking for doesn't exist or you don't have permission to edit it."}),r.jsx(o.Z,{onClick:()=>s.push("/stores"),children:"Back to Stores"})]})}):r.jsx("div",{className:"min-h-screen p-6 md:p-12",children:(0,r.jsxs)("div",{className:"max-w-3xl mx-auto",children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold mb-8",children:["Edit Store: ",d.displayName||d.username]}),r.jsx(a.Z,{store:d,isEditing:!0})]})})}},48073:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>o,__esModule:()=>a,default:()=>d});var r=s(95153);let n=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\stores\[username]\edit\page.tsx`),{__esModule:a,$$typeof:o}=n,i=n.default,d=i}};var t=require("../../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),s=t.X(0,[2103,2765,706,7783,1574],()=>__webpack_exec__(9188));module.exports=s})();