(()=>{var e={};e.id=7702,e.ids=[7702],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},73557:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>l});var a=r(67096),s=r(16132),d=r(37284),o=r.n(d),n=r(32564),i={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);r.d(t,i);let l=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,42918)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\dashboard\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,68182)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9291,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\dashboard\\page.tsx"],m="/dashboard/page",x={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},96034:(e,t,r)=>{Promise.resolve().then(r.bind(r,97131))},97131:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>DashboardPage});var a=r(30784);r(9885);var s=r(27870),d=r(14379);function DashboardPage(){let{t:e}=(0,s.$G)("common"),{isRtl:t}=(0,d.g)();return(0,a.jsxs)("div",{className:`p-6 ${t?"text-right":"text-left"}`,children:[a.jsx("h1",{className:"text-3xl font-bold mb-6",children:e("dashboard.overview","Overview")}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md",children:[a.jsx("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:e("dashboard.totalProducts","Total Products")}),a.jsx("p",{className:"text-3xl font-bold text-primary-600 dark:text-primary-400",children:"0"})]}),(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md",children:[a.jsx("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:e("dashboard.totalOrders","Total Orders")}),a.jsx("p",{className:"text-3xl font-bold text-primary-600 dark:text-primary-400",children:"0"})]}),(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md",children:[a.jsx("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:e("dashboard.totalCustomers","Total Customers")}),a.jsx("p",{className:"text-3xl font-bold text-primary-600 dark:text-primary-400",children:"0"})]}),(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md",children:[a.jsx("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:e("dashboard.totalRevenue","Total Revenue")}),a.jsx("p",{className:"text-3xl font-bold text-primary-600 dark:text-primary-400",children:"$0.00"})]})]}),(0,a.jsxs)("div",{className:"mt-8",children:[a.jsx("h2",{className:"text-2xl font-bold mb-4",children:e("dashboard.recentActivity","Recent Activity")}),a.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6",children:a.jsx("p",{className:"text-gray-500 dark:text-gray-400 text-center py-8",children:e("dashboard.noRecentActivity","No recent activity to display")})})]})]})}},42918:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>o,__esModule:()=>d,default:()=>i});var a=r(95153);let s=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\dashboard\page.tsx`),{__esModule:d,$$typeof:o}=s,n=s.default,i=n}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),r=t.X(0,[2103,2765,8576],()=>__webpack_exec__(73557));module.exports=r})();