# Technical Debugging Discoveries

## Overview
This document captures specific technical discoveries, debugging techniques, and implementation details discovered during the microservices dependency resolution process.

## Docker Container Debugging Techniques

### 1. **Container Startup Diagnosis**

#### Symptoms and Diagnosis Methods
```bash
# Symptom: Service appears to start but logs are empty
docker-compose logs service-name
# Result: No output

# Diagnosis Steps:
# 1. Check if container is actually running
docker ps | grep service-name

# 2. Check container status in compose
docker-compose ps service-name

# 3. Try manual container run to isolate Docker Compose issues
docker run --rm service-image

# 4. Check for hanging processes
docker run --rm service-image timeout 30s node dist/main.js
```

#### Common Root Causes Discovered
1. **Microservice connection hanging** - Service waits indefinitely for RabbitMQ
2. **Missing main.js file** - Docker<PERSON>le points to wrong entry point
3. **Network connectivity issues** - Can't reach dependent services
4. **Environment variable issues** - Missing required configuration

### 2. **Build vs Runtime Issue Separation**

#### Build Issues
```bash
# Symptoms: Build fails with compilation errors
ERROR in ./src/module.ts:X:Y
TS2307: Cannot find module '@nestjs/terminus'

# Debugging approach:
# 1. Check package.json dependencies
# 2. Verify import statements
# 3. Check for typos in module names
# 4. Verify version compatibility
```

#### Runtime Issues
```bash
# Symptoms: Build succeeds but container won't start
# Container starts but immediately exits
# Container hangs without logs

# Debugging approach:
# 1. Check Dockerfile CMD/ENTRYPOINT
# 2. Verify file paths in container
# 3. Test application startup manually
# 4. Check environment variables
```

## Microservices Communication Debugging

### 1. **ClientsModule Configuration Issues**

#### Problem Pattern
```typescript
// Service tries to inject client
constructor(@Inject('USER_SERVICE') private client: ClientProxy) {}

// Error: Nest can't resolve dependencies
// Root cause: ClientsModule not imported in module
```

#### Systematic Debugging
```bash
# 1. Check if ClientsModule is imported
grep -r "ClientsModule" src/

# 2. Check if import statement is uncommented
grep -r "import.*ClientsModule" src/

# 3. Verify service name matches injection token
grep -r "USER_SERVICE" src/

# 4. Check if service is registered in ClientsModule
```

### 2. **RabbitMQ Connection Debugging**

#### Connection Hanging Issues
```typescript
// Problem: Service hangs on microservice connection
await app.startAllMicroservices(); // Hangs here

// Solution: Add timeout and error handling
try {
  const microservice = app.connectMicroservice(config);
  await Promise.race([
    app.startAllMicroservices(),
    new Promise((_, reject) => 
      setTimeout(() => reject(new Error('Connection timeout')), 10000)
    )
  ]);
} catch (error) {
  logger.warn('Microservice connection failed, continuing with HTTP only');
}
```

#### Network Connectivity Testing
```bash
# Test RabbitMQ connectivity from service container
docker-compose exec service-name ping rabbitmq

# Check RabbitMQ management interface
curl http://localhost:15672/api/overview

# Verify queue creation
docker-compose exec rabbitmq rabbitmqctl list_queues
```

## Build Error Resolution Patterns

### 1. **Missing Dependencies Pattern**

#### Error Pattern
```bash
ERROR in ./src/health/health.controller.ts:7:8
error TS2307: Cannot find module '@nestjs/terminus'
```

#### Systematic Resolution
```bash
# 1. Identify missing package
# Error shows: @nestjs/terminus

# 2. Add to package.json dependencies
{
  "dependencies": {
    "@nestjs/terminus": "^10.0.0"
  }
}

# 3. Check for related dependencies
# @nestjs/terminus often needs @nestjs/axios

# 4. Rebuild with clean cache
docker-compose build --no-cache service-name
```

### 2. **API Method Name Errors**

#### Error Pattern
```bash
error TS2551: Property 'createTransporter' does not exist on type 'typeof import("nodemailer")'
Did you mean 'createTransport'?
```

#### Resolution Process
```bash
# 1. Check official API documentation
# 2. Look for similar method names in error message
# 3. Update code to use correct method name
# 4. Verify with TypeScript definitions
```

### 3. **Import Statement Issues**

#### Error Pattern
```bash
ERROR in ./src/app.module.ts:65:5
TS2304: Cannot find name 'ClientsModule'
```

#### Common Causes and Solutions
```typescript
// Cause 1: Import statement commented out
// import { ClientsModule, Transport } from '@nestjs/microservices'; // Commented

// Solution: Uncomment the import
import { ClientsModule, Transport } from '@nestjs/microservices';

// Cause 2: Missing dependency
// Solution: Add @nestjs/microservices to package.json

// Cause 3: Typo in import
// Solution: Verify exact module name and export
```

## Service Architecture Debugging

### 1. **Dependency Chain Analysis**

#### Systematic Mapping Process
```bash
# 1. Identify failing service
Store Service fails to start

# 2. Check error message for dependency
Error: Can't resolve "USER_SERVICE"

# 3. Check if dependency service exists
ls services/ | grep user-service

# 4. Check if dependency service is configured
grep -r "USER_SERVICE" services/user-service/

# 5. Map complete dependency chain
Store Service → USER_SERVICE → NOTIFICATION_SERVICE
```

#### Resolution Order
```bash
# Always resolve from deepest dependency first
1. Implement NOTIFICATION_SERVICE (no dependencies)
2. Configure USER_SERVICE to use NOTIFICATION_SERVICE
3. Configure STORE_SERVICE to use USER_SERVICE
```

### 2. **Service Communication Testing**

#### HTTP Endpoint Testing
```bash
# Test service health
curl http://localhost:3001/health

# Test API endpoints
curl -X POST http://localhost:3001/api/endpoint \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}'
```

#### Microservice Communication Testing
```bash
# Check RabbitMQ queues
docker-compose exec rabbitmq rabbitmqctl list_queues

# Test message sending (from another service)
# Use RabbitMQ management interface to send test messages
```

## Environment Configuration Debugging

### 1. **Docker Compose Environment Issues**

#### Variable Resolution Testing
```bash
# Check environment variables in container
docker-compose exec service-name env | grep SERVICE

# Test configuration loading
docker-compose exec service-name node -e "console.log(process.env.RABBITMQ_URL)"

# Verify service can reach dependencies
docker-compose exec service-name ping postgres
docker-compose exec service-name ping rabbitmq
```

### 2. **Port Conflict Resolution**

#### Systematic Port Management
```bash
# Check port usage
netstat -tulpn | grep :3001

# Verify Docker port mapping
docker-compose ps

# Test port accessibility
curl http://localhost:3001/health
```

## Performance and Optimization Discoveries

### 1. **Docker Build Optimization**

#### Build Time Issues
```bash
# Problem: npm install takes very long time
# Solution: Use multi-stage builds and layer caching

# Optimize Dockerfile layer order
COPY package.json ./          # Changes rarely
RUN npm install              # Cached if package.json unchanged
COPY src ./                  # Changes frequently
RUN npm run build           # Only runs if src changes
```

### 2. **Container Startup Optimization**

#### Startup Time Reduction
```typescript
// Problem: Service takes long time to start
// Solution: Implement health checks and readiness probes

// Add startup logging
logger.log('🚀 Service starting...');
logger.log('📦 Dependencies loaded');
logger.log('🔗 Database connected');
logger.log('🐰 RabbitMQ connected');
logger.log('✅ Service ready');
```

## Testing and Validation Techniques

### 1. **Isolation Testing**

#### Service-by-Service Testing
```bash
# Test individual service without dependencies
docker run --rm -p 3001:3001 service-image

# Test with minimal dependencies
docker-compose up -d postgres
docker-compose up service-name

# Test full integration
docker-compose up -d
```

### 2. **Integration Validation**

#### End-to-End Testing Process
```bash
# 1. Start infrastructure
docker-compose up -d postgres rabbitmq

# 2. Start services in dependency order
docker-compose up -d notification-service
docker-compose up -d user-service
docker-compose up -d store-service

# 3. Verify each service health
curl http://localhost:3003/health  # notification
curl http://localhost:3001/health  # user
curl http://localhost:3002/health  # store

# 4. Test service communication
# (Use appropriate API calls)
```

## Lessons for Future Development

### 1. **Debugging Workflow**
1. **Isolate the problem** - Test components individually
2. **Check logs systematically** - Start with most basic service
3. **Verify configuration** - Environment variables, imports, dependencies
4. **Test connectivity** - Network, ports, service discovery
5. **Document findings** - For future reference

### 2. **Prevention Strategies**
1. **Implement health checks** from the start
2. **Add comprehensive logging** for debugging
3. **Use systematic naming conventions**
4. **Test services individually** before integration
5. **Document configuration requirements**

## Related Documentation
- [Microservices Dependency Resolution](./microservices-dependency-resolution.md)
- [Microservices Troubleshooting](./microservices-troubleshooting.md)
- [Chat Thread Lessons Learned](./chat-thread-lessons-learned.md)
