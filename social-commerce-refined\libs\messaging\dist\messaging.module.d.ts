import { DynamicModule } from '@nestjs/common';
export interface MessagingModuleOptions {
    rabbitmqUrl?: string;
    serviceName?: string;
}
export declare class MessagingModule {
    static register(options?: MessagingModuleOptions): DynamicModule;
    static registerAsync(options: {
        useFactory: (...args: any[]) => Promise<MessagingModuleOptions> | MessagingModuleOptions;
        inject?: any[];
    }): DynamicModule;
}
