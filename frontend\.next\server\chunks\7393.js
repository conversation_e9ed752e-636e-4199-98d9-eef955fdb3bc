"use strict";exports.id=7393,exports.ids=[7393],exports.modules={7393:e=>{e.exports=JSON.parse('{"import":"Import","importProducts":"Import Products","importCategories":"Import Categories","importCustomers":"Import Customers","importOrders":"Import Orders","uploadFile":"Upload {{entityType}} File","uploadInstructions":"Upload a file containing {{entityType}} data. Accepted file types: {{types}}","dragAndDrop":"Drag and drop your file here","or":"or","browseFiles":"Browse Files","acceptedFileTypes":"Accepted file types","maxFileSize":"Maximum file size","changeFile":"Change File","uploading":"Uploading...","previewFile":"Preview {{entityType}} File","totalRows":"Total rows","skipFirstRow":"First row contains headers","showingPreview":"Showing preview of first 5 rows out of {{total}}","noData":"No data available","mapColumns":"Map Columns","mapColumnsDescription":"Match the columns in your file to the appropriate fields in our system.","fileColumn":"File Column","systemField":"System Field","preview":"Preview","doNotImport":"-- Do not import --","requiredField":"This field is required","requiredFieldsWarning":"Required fields not mapped","requiredFieldsWarningDescription":"Please map all required fields (marked with *) before continuing.","importOptions":"Import Options","importOptionsDescription":"Configure how the {{entityType}} data should be imported.","identifierOptions":"Identifier Options","updateExisting":"Update Existing Records","updateExistingDescription":"If a record with the same identifier exists, update it instead of creating a new one.","identifierField":"Identifier Field","createMissing":"Create Missing Records","createMissingDescription":"Create new records for items that don\'t exist in the system.","processingOptions":"Processing Options","validateOnly":"Validate Only","validateOnlyDescription":"Check the file for errors without actually importing the data.","batchSize":"Batch Size","batchSizeDescription":"Number of records to process in each batch. Higher values may be faster but use more memory.","contentOptions":"Content Options","importImages":"Import Images","importImagesDescription":"Download and import images from URLs in the file.","defaultStatus":"Default Status","defaultStatusDescription":"Default status for new records if not specified in the import file.","importStatus":"Import Status","rowsProcessed":"rows processed","successfulRows":"Successful","errorRows":"Errors","warningRows":"Warnings","importErrors":"Import Errors","errorsDescription":"The following errors occurred during the import process:","row":"Row","column":"Column","value":"Value","error":"Error","showing":"Showing","of":"of","importComplete":"Import Complete","importCompleteDescription":"Your import has been successfully completed.","importError":"Import Error","importErrorDescription":"There was an error processing your import. Please try again.","downloadTemplate":"Download Template","downloadTemplateDescription":"Download a template file with the correct format for importing {{entityType}} data.","saveAsTemplate":"Save as Template","templateName":"Template Name","templateDescription":"Template Description","saveTemplate":"Save Template","importHistory":"Import History","noImportHistory":"No import history available","importDate":"Import Date","importType":"Import Type","importFile":"Import File","importUser":"Import User","importResult":"Import Result","viewDetails":"View Details","entityTypes":{"product":"Product","category":"Category","customer":"Customer","order":"Order"},"fields":{"id":"ID","sku":"SKU","slug":"Slug","name":"Name","description":"Description","price":"Price","comparePrice":"Compare Price","costPrice":"Cost Price","status":"Status","categories":"Categories","tags":"Tags","images":"Images","weight":"Weight","dimensions":"Dimensions","attributes":"Attributes","variants":"Variants","inventory":"Inventory","seo":"SEO"},"status":{"pending":"Pending","validating":"Validating","mapping":"Mapping","processing":"Processing","completed":"Completed","failed":"Failed","partiallyCompleted":"Partially Completed","cancelled":"Cancelled"},"errors":{"fileTooLarge":"File is too large. Maximum size is {{size}}MB.","invalidFileType":"Invalid file type. Accepted types: {{types}}","uploadFailed":"File upload failed. Please try again.","previewFailed":"Failed to load preview. Please try again.","statusFailed":"Failed to load import status. Please try again.","errorsFailed":"Failed to load import errors. Please try again."},"back":"Back","continue":"Continue","cancel":"Cancel","upload":"Upload","close":"Close","done":"Done","previous":"Previous","next":"Next","viewErrors":"View Errors","noErrors":"No errors found","noImportStatus":"No import status available"}')}};