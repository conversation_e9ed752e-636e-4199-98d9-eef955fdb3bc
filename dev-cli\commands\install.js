const chalk = require('chalk');
const spawn = require('cross-spawn');
const ora = require('ora');
const path = require('path');
const fs = require('fs');

/**
 * Install command implementation
 * @param {Object} program - Commander program instance
 */
module.exports = (program) => {
  program
    .command('install')
    .description('Install dependencies for services')
    .argument('[service]', 'Service to install dependencies for (all, backend, frontend)', 'all')
    .option('--clean', 'Clean node_modules before installing', false)
    .action((service, options) => {
      const spinner = ora('Installing dependencies...').start();
      
      try {
        const rootDir = path.resolve(__dirname, '../../');
        
        if (service === 'all' || service === 'backend') {
          // Install backend dependencies
          spinner.text = 'Installing backend dependencies...';
          
          const backendDir = path.join(rootDir, 'backend');
          if (!fs.existsSync(backendDir)) {
            spinner.warn(chalk.yellow('Backend directory not found'));
          } else {
            if (options.clean) {
              spinner.text = 'Cleaning backend node_modules...';
              
              const cleanResult = spawn.sync('rm', ['-rf', 'node_modules'], {
                cwd: backendDir,
                stdio: 'inherit'
              });
              
              if (cleanResult.status !== 0) {
                spinner.warn(chalk.yellow('Failed to clean backend node_modules'));
              }
            }
            
            spinner.text = 'Installing backend dependencies...';
            
            const installResult = spawn.sync('npm', ['install'], {
              cwd: backendDir,
              stdio: 'inherit'
            });
            
            if (installResult.status === 0) {
              spinner.succeed(chalk.green('Backend dependencies installed successfully'));
            } else {
              spinner.fail(chalk.red('Failed to install backend dependencies'));
            }
          }
        }
        
        if (service === 'all' || service === 'frontend') {
          // Install frontend dependencies
          spinner.text = 'Installing frontend dependencies...';
          
          const frontendDir = path.join(rootDir, 'frontend');
          if (!fs.existsSync(frontendDir)) {
            spinner.warn(chalk.yellow('Frontend directory not found'));
          } else {
            if (options.clean) {
              spinner.text = 'Cleaning frontend node_modules...';
              
              const cleanResult = spawn.sync('rm', ['-rf', 'node_modules'], {
                cwd: frontendDir,
                stdio: 'inherit'
              });
              
              if (cleanResult.status !== 0) {
                spinner.warn(chalk.yellow('Failed to clean frontend node_modules'));
              }
            }
            
            spinner.text = 'Installing frontend dependencies...';
            
            const installResult = spawn.sync('npm', ['install'], {
              cwd: frontendDir,
              stdio: 'inherit'
            });
            
            if (installResult.status === 0) {
              spinner.succeed(chalk.green('Frontend dependencies installed successfully'));
            } else {
              spinner.fail(chalk.red('Failed to install frontend dependencies'));
            }
          }
        }
        
        if (service !== 'all' && service !== 'backend' && service !== 'frontend') {
          spinner.fail(chalk.red(`Unknown service: ${service}`));
          console.error(chalk.yellow('Available services: all, backend, frontend'));
        }
      } catch (error) {
        spinner.fail(chalk.red('Error installing dependencies'));
        console.error(chalk.red(error.message));
      }
    });
};
