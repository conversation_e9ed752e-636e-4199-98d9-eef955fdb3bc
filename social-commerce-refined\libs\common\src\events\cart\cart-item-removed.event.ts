import { BaseEvent } from '../base-event.interface';

/**
 * Event emitted when an item is removed from a cart
 */
export class CartItemRemovedEvent implements BaseEvent<CartItemRemovedPayload> {
  id: string;
  type: string = 'cart.item.removed';
  version: string = '1.0';
  timestamp: string;
  producer: string = 'cart-service';
  payload: CartItemRemovedPayload;

  constructor(payload: CartItemRemovedPayload) {
    this.id = `${payload.cartId}-${payload.productId}-${Date.now()}`;
    this.timestamp = new Date().toISOString();
    this.payload = payload;
  }
}

/**
 * Payload for CartItemRemovedEvent
 */
export interface CartItemRemovedPayload {
  /**
   * Cart ID
   */
  cartId: string;

  /**
   * User ID
   */
  userId: string;

  /**
   * Product ID
   */
  productId: string;

  /**
   * Timestamp
   */
  removedAt: string;
}
