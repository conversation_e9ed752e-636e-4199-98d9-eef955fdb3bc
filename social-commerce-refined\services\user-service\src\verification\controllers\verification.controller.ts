import { Controller, Post, Body, Param, UseGuards, Logger, HttpCode, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { VerificationService } from '../services/verification.service';
import { JwtAuthGuard } from '../../authentication/guards/jwt-auth.guard';
import { MessagePattern } from '@nestjs/microservices';

@ApiTags('verification')
@Controller('verification')
export class VerificationController {
  private readonly logger = new Logger(VerificationController.name);

  constructor(private readonly verificationService: VerificationService) {}

  @Post('email/:userId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Send email verification token' })
  @ApiResponse({ status: 201, description: 'Verification email sent successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async sendEmailVerification(@Param('userId') userId: string) {
    this.logger.log(`Email verification request received for user ID: ${userId}`);
    const token = await this.verificationService.createEmailVerificationToken(userId);
    return { message: 'Verification email sent successfully' };
  }

  @Post('verify-email')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Verify email with token' })
  @ApiResponse({ status: 200, description: 'Email verified successfully' })
  @ApiResponse({ status: 400, description: 'Invalid token' })
  async verifyEmail(@Body('token') token: string) {
    this.logger.log(`Email verification token received: ${token}`);
    const verified = await this.verificationService.verifyEmail(token);
    return { verified, message: 'Email verified successfully' };
  }

  @Post('phone/:userId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Send phone verification token' })
  @ApiResponse({ status: 201, description: 'Verification SMS sent successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async sendPhoneVerification(@Param('userId') userId: string) {
    this.logger.log(`Phone verification request received for user ID: ${userId}`);
    const token = await this.verificationService.createPhoneVerificationToken(userId);
    return { message: 'Verification SMS sent successfully' };
  }

  @Post('verify-phone/:userId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Verify phone with token' })
  @ApiResponse({ status: 200, description: 'Phone verified successfully' })
  @ApiResponse({ status: 400, description: 'Invalid token' })
  async verifyPhone(
    @Param('userId') userId: string,
    @Body('token') token: string,
  ) {
    this.logger.log(`Phone verification token received for user ID: ${userId}`);
    const verified = await this.verificationService.verifyPhone(userId, token);
    return { verified, message: 'Phone verified successfully' };
  }

  @Post('forgot-password')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Request password reset' })
  @ApiResponse({ status: 200, description: 'Password reset email sent successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async forgotPassword(@Body('email') email: string) {
    this.logger.log(`Password reset request received for email: ${email}`);
    const token = await this.verificationService.createPasswordResetToken(email);
    return { message: 'Password reset email sent successfully' };
  }

  @Post('reset-password')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Reset password with token' })
  @ApiResponse({ status: 200, description: 'Password reset successfully' })
  @ApiResponse({ status: 400, description: 'Invalid token' })
  async resetPassword(
    @Body('token') token: string,
    @Body('password') password: string,
  ) {
    this.logger.log(`Password reset token received`);
    const reset = await this.verificationService.resetPassword(token, password);
    return { reset, message: 'Password reset successfully' };
  }

  // Microservice endpoints

  @MessagePattern('verify_email')
  async verifyEmailToken(data: { token: string }): Promise<boolean> {
    this.logger.log(`Microservice verify email request received for token: ${data.token}`);
    return this.verificationService.verifyEmail(data.token);
  }

  @MessagePattern('forgot_password')
  async forgotPasswordRequest(data: { email: string }): Promise<any> {
    this.logger.log(`Microservice forgot password request received for email: ${data.email}`);
    const token = await this.verificationService.createPasswordResetToken(data.email);
    return { message: 'Password reset email sent successfully' };
  }

  @MessagePattern('reset_password')
  async resetPasswordRequest(data: { token: string; password: string }): Promise<boolean> {
    this.logger.log(`Microservice reset password request received`);
    return this.verificationService.resetPassword(data.token, data.password);
  }
}
