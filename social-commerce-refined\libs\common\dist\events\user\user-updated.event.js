"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserUpdatedEvent = void 0;
class UserUpdatedEvent {
    constructor(payload) {
        this.type = 'user.updated';
        this.version = '1.0';
        this.producer = 'user-service';
        this.id = payload.id;
        this.timestamp = new Date().toISOString();
        this.payload = payload;
    }
}
exports.UserUpdatedEvent = UserUpdatedEvent;
//# sourceMappingURL=user-updated.event.js.map