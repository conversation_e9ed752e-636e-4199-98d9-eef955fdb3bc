import { IsNotEmpty, <PERSON><PERSON><PERSON>ber, <PERSON><PERSON>ptional, IsString, <PERSON>U<PERSON><PERSON>, Min, IsObject } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class AddToCartDto {
  @ApiProperty({
    description: 'Product ID to add to cart',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsNotEmpty()
  @IsUUID()
  productId: string;

  @ApiProperty({
    description: 'Product variant ID (if applicable)',
    example: 'variant_123',
    required: false,
  })
  @IsOptional()
  @IsString()
  variantId?: string;

  @ApiProperty({
    description: 'Quantity to add to cart',
    example: 2,
    minimum: 1,
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  quantity: number;

  @ApiProperty({
    description: 'Selected product options (size, color, etc.)',
    example: { size: 'L', color: 'blue' },
    required: false,
  })
  @IsOptional()
  @IsObject()
  selectedOptions?: Record<string, any>;

  @ApiProperty({
    description: 'Additional metadata for the cart item',
    example: { gift_wrap: true, message: 'Happy Birthday!' },
    required: false,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
