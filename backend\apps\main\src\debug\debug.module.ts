import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { JwtModule } from '@nestjs/jwt';
import { SERVICES } from '@app/common';
import { DebugController } from './debug.controller';
import { DebugService } from './debug.service';
import { AuthModule } from '../auth/auth.module';

/**
 * Debug Module
 *
 * This module contains debugging tools and endpoints that are only available in non-production environments.
 * It should not be loaded in production.
 */
@Module({
  imports: [
    ConfigModule.forRoot(),
    ClientsModule.register([
      {
        name: SERVICES.USER_SERVICE,
        transport: Transport.TCP,
        options: {
          host: process.env.USER_SERVICE_HOST || 'localhost',
          port: parseInt(process.env.USER_SERVICE_PORT || '3002'),
        },
      },
    ]),
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'dev-secret-key',
      signOptions: { expiresIn: '30d' },
    }),
    AuthModule,
  ],
  controllers: [DebugController],
  providers: [DebugService],
})
export class DebugModule {
  constructor() {
    // Log a warning that debug module is loaded
    if (process.env.NODE_ENV === 'production') {
      console.warn('WARNING: Debug module is loaded in production environment!');
    } else {
      console.log('Debug module loaded (non-production environment)');
    }
  }
}
