#!/bin/bash

# Create API Gateway directories
mkdir -p services/api-gateway/src/routing/controllers
mkdir -p services/api-gateway/src/routing/services
mkdir -p services/api-gateway/src/middleware/auth
mkdir -p services/api-gateway/src/middleware/logging
mkdir -p services/api-gateway/src/shared/guards
mkdir -p services/api-gateway/src/shared/decorators
mkdir -p services/api-gateway/src/shared/filters
mkdir -p services/api-gateway/src/shared/interceptors
mkdir -p services/api-gateway/src/shared/utils

echo "API Gateway directories created successfully!"
