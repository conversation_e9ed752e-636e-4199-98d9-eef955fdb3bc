import { BaseEvent } from '../base-event.interface';
export declare class ProductUpdatedEvent implements BaseEvent<ProductUpdatedPayload> {
    id: string;
    type: string;
    version: string;
    timestamp: string;
    producer: string;
    payload: ProductUpdatedPayload;
    constructor(payload: ProductUpdatedPayload);
}
export interface ProductUpdatedPayload {
    id: string;
    storeId: string;
    name: string;
    description: string;
    price: number;
    inventory: number;
    categories: string[];
    updatedAt: string;
}
