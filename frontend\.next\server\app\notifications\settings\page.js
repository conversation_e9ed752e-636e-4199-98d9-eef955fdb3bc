(()=>{var e={};e.id=8860,e.ids=[8860],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},20625:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>r.a,__next_app__:()=>g,originalPathname:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>c});var a=t(67096),n=t(16132),i=t(37284),r=t.n(i),l=t(32564),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let c=["",{children:["notifications",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,32338)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\notifications\\settings\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9291,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\notifications\\settings\\page.tsx"],m="/notifications/settings/page",g={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/notifications/settings/page",pathname:"/notifications/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},67325:(e,s,t)=>{Promise.resolve().then(t.bind(t,41849))},41849:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>NotificationSettingsPage});var a=t(30784),n=t(9885),i=t(57114),r=t(27870),l=t(59872);let notifications_NotificationSettings=({settings:e,onSave:s,isLoading:t=!1,className:i=""})=>{let{t:o}=(0,r.$G)("notifications"),[c,d]=(0,n.useState)(e),[m,g]=(0,n.useState)(!1),handleToggle=(e,s)=>{d(t=>({...t,[e]:{...t[e],[s]:!t[e][s]}}))},handleSave=async()=>{g(!0);try{await s(c)}catch(e){console.error("Failed to save notification settings:",e)}finally{g(!1)}},x=JSON.stringify(e)!==JSON.stringify(c);return(0,a.jsxs)("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 ${i}`,children:[a.jsx("h2",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100 mb-6",children:o("settings.title","Notification Settings")}),(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx("p",{className:"text-gray-500 dark:text-gray-400 mb-6",children:o("settings.description","Configure how you want to receive notifications")}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:o("settings.emailNotifications","Email Notifications")}),(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:o("settings.follow","Follow Notifications")}),a.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:o("settings.followDescription","When someone follows you or your store")})]}),(0,a.jsxs)("div",{className:"relative inline-block w-10 mr-2 align-middle select-none",children:[a.jsx("input",{type:"checkbox",id:"email-follow",name:"email-follow",className:"sr-only",checked:c.email.follow,onChange:()=>handleToggle("email","follow"),disabled:t||m}),a.jsx("label",{htmlFor:"email-follow",className:`block overflow-hidden h-6 rounded-full bg-gray-300 dark:bg-gray-600 cursor-pointer ${c.email.follow?"bg-primary-500":""}`,children:a.jsx("span",{className:`block h-6 w-6 rounded-full bg-white dark:bg-gray-800 shadow transform transition-transform ${c.email.follow?"translate-x-4":"translate-x-0"}`})})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:o("settings.like","Like Notifications")}),a.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:o("settings.likeDescription","When someone likes your post or comment")})]}),(0,a.jsxs)("div",{className:"relative inline-block w-10 mr-2 align-middle select-none",children:[a.jsx("input",{type:"checkbox",id:"email-like",name:"email-like",className:"sr-only",checked:c.email.like,onChange:()=>handleToggle("email","like"),disabled:t||m}),a.jsx("label",{htmlFor:"email-like",className:`block overflow-hidden h-6 rounded-full bg-gray-300 dark:bg-gray-600 cursor-pointer ${c.email.like?"bg-primary-500":""}`,children:a.jsx("span",{className:`block h-6 w-6 rounded-full bg-white dark:bg-gray-800 shadow transform transition-transform ${c.email.like?"translate-x-4":"translate-x-0"}`})})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:o("settings.comment","Comment Notifications")}),a.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:o("settings.commentDescription","When someone comments on your post")})]}),(0,a.jsxs)("div",{className:"relative inline-block w-10 mr-2 align-middle select-none",children:[a.jsx("input",{type:"checkbox",id:"email-comment",name:"email-comment",className:"sr-only",checked:c.email.comment,onChange:()=>handleToggle("email","comment"),disabled:t||m}),a.jsx("label",{htmlFor:"email-comment",className:`block overflow-hidden h-6 rounded-full bg-gray-300 dark:bg-gray-600 cursor-pointer ${c.email.comment?"bg-primary-500":""}`,children:a.jsx("span",{className:`block h-6 w-6 rounded-full bg-white dark:bg-gray-800 shadow transform transition-transform ${c.email.comment?"translate-x-4":"translate-x-0"}`})})]})]})]})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:o("settings.pushNotifications","Push Notifications")}),(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:o("settings.follow","Follow Notifications")}),a.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:o("settings.followDescription","When someone follows you or your store")})]}),(0,a.jsxs)("div",{className:"relative inline-block w-10 mr-2 align-middle select-none",children:[a.jsx("input",{type:"checkbox",id:"push-follow",name:"push-follow",className:"sr-only",checked:c.push.follow,onChange:()=>handleToggle("push","follow"),disabled:t||m}),a.jsx("label",{htmlFor:"push-follow",className:`block overflow-hidden h-6 rounded-full bg-gray-300 dark:bg-gray-600 cursor-pointer ${c.push.follow?"bg-primary-500":""}`,children:a.jsx("span",{className:`block h-6 w-6 rounded-full bg-white dark:bg-gray-800 shadow transform transition-transform ${c.push.follow?"translate-x-4":"translate-x-0"}`})})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:o("settings.like","Like Notifications")}),a.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:o("settings.likeDescription","When someone likes your post or comment")})]}),(0,a.jsxs)("div",{className:"relative inline-block w-10 mr-2 align-middle select-none",children:[a.jsx("input",{type:"checkbox",id:"push-like",name:"push-like",className:"sr-only",checked:c.push.like,onChange:()=>handleToggle("push","like"),disabled:t||m}),a.jsx("label",{htmlFor:"push-like",className:`block overflow-hidden h-6 rounded-full bg-gray-300 dark:bg-gray-600 cursor-pointer ${c.push.like?"bg-primary-500":""}`,children:a.jsx("span",{className:`block h-6 w-6 rounded-full bg-white dark:bg-gray-800 shadow transform transition-transform ${c.push.like?"translate-x-4":"translate-x-0"}`})})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:o("settings.comment","Comment Notifications")}),a.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:o("settings.commentDescription","When someone comments on your post")})]}),(0,a.jsxs)("div",{className:"relative inline-block w-10 mr-2 align-middle select-none",children:[a.jsx("input",{type:"checkbox",id:"push-comment",name:"push-comment",className:"sr-only",checked:c.push.comment,onChange:()=>handleToggle("push","comment"),disabled:t||m}),a.jsx("label",{htmlFor:"push-comment",className:`block overflow-hidden h-6 rounded-full bg-gray-300 dark:bg-gray-600 cursor-pointer ${c.push.comment?"bg-primary-500":""}`,children:a.jsx("span",{className:`block h-6 w-6 rounded-full bg-white dark:bg-gray-800 shadow transform transition-transform ${c.push.comment?"translate-x-4":"translate-x-0"}`})})]})]})]})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:o("settings.inAppNotifications","In-App Notifications")}),(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:o("settings.follow","Follow Notifications")}),a.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:o("settings.followDescription","When someone follows you or your store")})]}),(0,a.jsxs)("div",{className:"relative inline-block w-10 mr-2 align-middle select-none",children:[a.jsx("input",{type:"checkbox",id:"inApp-follow",name:"inApp-follow",className:"sr-only",checked:c.inApp.follow,onChange:()=>handleToggle("inApp","follow"),disabled:t||m}),a.jsx("label",{htmlFor:"inApp-follow",className:`block overflow-hidden h-6 rounded-full bg-gray-300 dark:bg-gray-600 cursor-pointer ${c.inApp.follow?"bg-primary-500":""}`,children:a.jsx("span",{className:`block h-6 w-6 rounded-full bg-white dark:bg-gray-800 shadow transform transition-transform ${c.inApp.follow?"translate-x-4":"translate-x-0"}`})})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:o("settings.like","Like Notifications")}),a.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:o("settings.likeDescription","When someone likes your post or comment")})]}),(0,a.jsxs)("div",{className:"relative inline-block w-10 mr-2 align-middle select-none",children:[a.jsx("input",{type:"checkbox",id:"inApp-like",name:"inApp-like",className:"sr-only",checked:c.inApp.like,onChange:()=>handleToggle("inApp","like"),disabled:t||m}),a.jsx("label",{htmlFor:"inApp-like",className:`block overflow-hidden h-6 rounded-full bg-gray-300 dark:bg-gray-600 cursor-pointer ${c.inApp.like?"bg-primary-500":""}`,children:a.jsx("span",{className:`block h-6 w-6 rounded-full bg-white dark:bg-gray-800 shadow transform transition-transform ${c.inApp.like?"translate-x-4":"translate-x-0"}`})})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:o("settings.comment","Comment Notifications")}),a.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:o("settings.commentDescription","When someone comments on your post")})]}),(0,a.jsxs)("div",{className:"relative inline-block w-10 mr-2 align-middle select-none",children:[a.jsx("input",{type:"checkbox",id:"inApp-comment",name:"inApp-comment",className:"sr-only",checked:c.inApp.comment,onChange:()=>handleToggle("inApp","comment"),disabled:t||m}),a.jsx("label",{htmlFor:"inApp-comment",className:`block overflow-hidden h-6 rounded-full bg-gray-300 dark:bg-gray-600 cursor-pointer ${c.inApp.comment?"bg-primary-500":""}`,children:a.jsx("span",{className:`block h-6 w-6 rounded-full bg-white dark:bg-gray-800 shadow transform transition-transform ${c.inApp.comment?"translate-x-4":"translate-x-0"}`})})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:o("settings.message","Message Notifications")}),a.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:o("settings.messageDescription","When you receive a new message")})]}),(0,a.jsxs)("div",{className:"relative inline-block w-10 mr-2 align-middle select-none",children:[a.jsx("input",{type:"checkbox",id:"inApp-message",name:"inApp-message",className:"sr-only",checked:c.inApp.message,onChange:()=>handleToggle("inApp","message"),disabled:t||m}),a.jsx("label",{htmlFor:"inApp-message",className:`block overflow-hidden h-6 rounded-full bg-gray-300 dark:bg-gray-600 cursor-pointer ${c.inApp.message?"bg-primary-500":""}`,children:a.jsx("span",{className:`block h-6 w-6 rounded-full bg-white dark:bg-gray-800 shadow transform transition-transform ${c.inApp.message?"translate-x-4":"translate-x-0"}`})})]})]})]})]})]}),(0,a.jsxs)("div",{className:"mt-8 flex justify-end space-x-4",children:[a.jsx(l.Z,{variant:"outline",onClick:()=>d(e),disabled:!x||t||m,children:o("settings.cancel","Cancel")}),a.jsx(l.Z,{onClick:handleSave,isLoading:m,disabled:!x||t||m,children:o("settings.save","Save Changes")})]})]})};var o=t(70661),c=t(53717);function NotificationSettingsPage(){let e=(0,i.useRouter)(),{t:s}=(0,r.$G)("notifications"),{data:t,isLoading:n}=(0,c.H9)(),[d,{isLoading:m}]=(0,c.mT)(),handleSaveSettings=async e=>{try{await d(e).unwrap()}catch(e){console.error("Failed to update notification settings:",e)}};return a.jsx(o.Z,{children:a.jsx("div",{className:"min-h-screen p-6",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsxs)("div",{className:"mb-6 flex items-center",children:[(0,a.jsxs)(l.Z,{variant:"outline",onClick:()=>e.push("/notifications"),className:"mr-4",children:[a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),s("backToNotifications","Back to Notifications")]}),a.jsx("h1",{className:"text-3xl font-bold",children:s("notificationSettings","Notification Settings")})]}),n?(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 animate-pulse",children:[a.jsx("div",{className:"h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-6"}),(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"}),a.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"}),a.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-2/3"})]})]}):a.jsx(notifications_NotificationSettings,{settings:t||{email:{follow:!0,like:!1,comment:!0,mention:!0,order:!0,payment:!0,system:!0},push:{follow:!0,like:!0,comment:!0,mention:!0,order:!0,payment:!0,system:!0},inApp:{follow:!0,like:!0,comment:!0,mention:!0,order:!0,payment:!0,system:!0}},onSave:handleSaveSettings,isLoading:m})]})})})}},32338:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>r,__esModule:()=>i,default:()=>o});var a=t(95153);let n=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\notifications\settings\page.tsx`),{__esModule:i,$$typeof:r}=n,l=n.default,o=l}};var s=require("../../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),t=s.X(0,[2103,2765,661],()=>__webpack_exec__(20625));module.exports=t})();