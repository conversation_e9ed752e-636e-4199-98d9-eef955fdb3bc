"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderStatusUpdatedEvent = void 0;
class OrderStatusUpdatedEvent {
    constructor(payload) {
        this.type = 'order.status.updated';
        this.version = '1.0';
        this.producer = 'order-service';
        this.id = payload.id;
        this.timestamp = new Date().toISOString();
        this.payload = payload;
    }
}
exports.OrderStatusUpdatedEvent = OrderStatusUpdatedEvent;
//# sourceMappingURL=order-status-updated.event.js.map