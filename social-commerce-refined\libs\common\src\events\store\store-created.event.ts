import { BaseEvent } from '../base-event.interface';

/**
 * Event emitted when a new store is created
 */
export class StoreCreatedEvent implements BaseEvent<StoreCreatedPayload> {
  id: string;
  type: string = 'store.created';
  version: string = '1.0';
  timestamp: string;
  producer: string = 'store-service';
  payload: StoreCreatedPayload;

  constructor(payload: StoreCreatedPayload) {
    this.id = payload.id;
    this.timestamp = new Date().toISOString();
    this.payload = payload;
  }
}

/**
 * Payload for StoreCreatedEvent
 */
export interface StoreCreatedPayload {
  /**
   * Store ID
   */
  id: string;

  /**
   * Owner user ID
   */
  ownerId: string;

  /**
   * Store name
   */
  name: string;

  /**
   * Store description
   */
  description: string;

  /**
   * Store creation timestamp
   */
  createdAt: string;
}
