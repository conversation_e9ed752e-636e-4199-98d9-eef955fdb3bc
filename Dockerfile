# Build Stage
FROM node:18-alpine AS build

WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Copy shared libraries
COPY ../../libs/common ./libs/common
COPY ../../libs/messaging ./libs/messaging

# Install dependencies
RUN npm install

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production Stage
FROM node:18-alpine

WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Install production dependencies only
RUN npm install --only=production

# Copy built application from build stage
COPY --from=build /app/dist ./dist
COPY --from=build /app/libs ./libs

# Set environment variables
ENV NODE_ENV=production

# Expose port
EXPOSE 3002

# Start the application
CMD ["node", "dist/main"]
