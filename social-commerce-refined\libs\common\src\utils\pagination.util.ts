import { PaginationDto } from '../dto/pagination.dto';
import { PaginationResponseDto } from '../dto/pagination-response.dto';

/**
 * Create a pagination response
 * @param items Array of items
 * @param total Total number of items
 * @param paginationDto Pagination parameters
 * @returns Pagination response
 */
export function createPaginationResponse<T>(
  items: T[],
  total: number,
  paginationDto: PaginationDto,
): PaginationResponseDto<T> {
  const { page = 1, limit = 10 } = paginationDto;
  return new PaginationResponseDto<T>(items, total, page, limit);
}

/**
 * Calculate skip value for pagination
 * @param paginationDto Pagination parameters
 * @returns Skip value
 */
export function calculateSkip(paginationDto: PaginationDto): number {
  const { page = 1, limit = 10 } = paginationDto;
  return (page - 1) * limit;
}
