import axios from 'axios';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

describe('Health Check Integration Tests', () => {
  // API endpoints
  const API_GATEWAY_URL = process.env.API_GATEWAY_URL || 'http://localhost:3000/api';
  const USER_SERVICE_URL = process.env.USER_SERVICE_URL || 'http://localhost:3001/api';
  const STORE_SERVICE_URL = process.env.STORE_SERVICE_URL || 'http://localhost:3002/api';
  
  // Increase timeout for integration tests
  jest.setTimeout(30000);
  
  describe('API Gateway Health', () => {
    it('should return healthy status for API Gateway', async () => {
      const response = await axios.get(`${API_GATEWAY_URL}/health`);
      
      expect(response.status).toBe(200);
      expect(response.data).toBeDefined();
      expect(response.data.status).toBe('ok');
      expect(response.data.info).toBeDefined();
      expect(response.data.info.apiGateway).toBeDefined();
      expect(response.data.info.apiGateway.status).toBe('up');
    });
    
    it('should return services health status', async () => {
      const response = await axios.get(`${API_GATEWAY_URL}/health/services`);
      
      expect(response.status).toBe(200);
      expect(response.data).toBeDefined();
      expect(response.data.status).toBe('ok');
      expect(response.data.info).toBeDefined();
      expect(response.data.info.userService).toBeDefined();
      expect(response.data.info.storeService).toBeDefined();
    });
    
    it('should return User Service health status', async () => {
      const response = await axios.get(`${API_GATEWAY_URL}/health/service/user`);
      
      expect(response.status).toBe(200);
      expect(response.data).toBeDefined();
      expect(response.data.status).toBe('ok');
      expect(response.data.info).toBeDefined();
      expect(response.data.info.userService).toBeDefined();
      expect(response.data.info.userService.status).toBe('up');
    });
    
    it('should return Store Service health status', async () => {
      const response = await axios.get(`${API_GATEWAY_URL}/health/service/store`);
      
      expect(response.status).toBe(200);
      expect(response.data).toBeDefined();
      expect(response.data.status).toBe('ok');
      expect(response.data.info).toBeDefined();
      expect(response.data.info.storeService).toBeDefined();
      expect(response.data.info.storeService.status).toBe('up');
    });
  });
  
  describe('User Service Health', () => {
    it('should return healthy status for User Service', async () => {
      const response = await axios.get(`${USER_SERVICE_URL}/health`);
      
      expect(response.status).toBe(200);
      expect(response.data).toBeDefined();
      expect(response.data.status).toBe('ok');
      expect(response.data.info).toBeDefined();
      expect(response.data.info.database).toBeDefined();
      expect(response.data.info.database.status).toBe('up');
    });
  });
  
  describe('Store Service Health', () => {
    it('should return healthy status for Store Service', async () => {
      const response = await axios.get(`${STORE_SERVICE_URL}/health`);
      
      expect(response.status).toBe(200);
      expect(response.data).toBeDefined();
      expect(response.data.status).toBe('ok');
      expect(response.data.info).toBeDefined();
      expect(response.data.info.database).toBeDefined();
      expect(response.data.info.database.status).toBe('up');
    });
  });
});
