(()=>{var e={};e.id=2953,e.ids=[2953],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},9148:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o});var t=r(67096),a=r(16132),l=r(37284),n=r.n(l),d=r(32564),i={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>d[e]);r.d(s,i);let o=["",{children:["stores",{children:["[username]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,10110)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\stores\\[username]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9291,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\stores\\[username]\\page.tsx"],m="/stores/[username]/page",x={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/stores/[username]/page",pathname:"/stores/[username]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},2423:(e,s,r)=>{Promise.resolve().then(r.bind(r,24226))},24226:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>StorePage});var t=r(30784);r(9885);var a=r(52451),l=r.n(a),n=r(57114),d=r(59872),i=r(77783),o=r(19923),c=r(48042);function StorePage({params:e}){let{username:s}=e,r=(0,n.useRouter)(),{data:a,isLoading:m}=(0,i.YU)(s),{data:x}=(0,o.Mx)(),{data:u,isLoading:p}=(0,c.VJ)(a?.id||"",{skip:!a?.id}),[g,{isLoading:h}]=(0,i.te)(),[j,{isLoading:f}]=(0,i.ti)(),y=x?.id===a?.ownerId,handleFollowToggle=async()=>{if(a)try{await g(a.id).unwrap()}catch(e){console.error("Failed to follow/unfollow store:",e)}};return m?t.jsx("div",{className:"min-h-screen p-6 md:p-12",children:(0,t.jsxs)("div",{className:"max-w-6xl mx-auto text-center",children:[t.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600 mx-auto"}),t.jsx("p",{className:"mt-4 text-lg",children:"Loading store..."})]})}):a?(0,t.jsxs)("div",{className:"min-h-screen",children:[t.jsx("div",{className:"relative h-48 md:h-64 lg:h-80 bg-gradient-to-r from-primary-500 to-secondary-500",children:a.headerImageUrl&&t.jsx(l(),{src:a.headerImageUrl,alt:a.displayName||a.username,fill:!0,className:"object-cover"})}),(0,t.jsxs)("div",{className:"max-w-6xl mx-auto px-6 md:px-12",children:[t.jsx("div",{className:"relative -mt-16 mb-8",children:t.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row items-center md:items-start",children:[t.jsx("div",{className:"relative w-24 h-24 md:w-32 md:h-32 mb-4 md:mb-0 md:mr-6 border-4 border-white dark:border-gray-800 rounded-full overflow-hidden bg-gray-200 dark:bg-gray-700",children:a.profileImageUrl?t.jsx(l(),{src:a.profileImageUrl,alt:a.displayName||a.username,fill:!0,className:"object-cover"}):t.jsx("div",{className:"flex items-center justify-center h-full text-3xl font-bold text-gray-400",children:(a.displayName||a.username).charAt(0).toUpperCase()})}),(0,t.jsxs)("div",{className:"flex-1 text-center md:text-left",children:[t.jsx("h1",{className:"text-2xl md:text-3xl font-bold",children:a.displayName||a.username}),(0,t.jsxs)("p",{className:"text-gray-500 dark:text-gray-400",children:["@",a.username]}),a.bio&&t.jsx("p",{className:"mt-2 text-gray-600 dark:text-gray-300",children:a.bio}),(0,t.jsxs)("div",{className:"mt-4 flex flex-wrap justify-center md:justify-start gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:a.followerCount})," ",t.jsx("span",{className:"text-gray-500 dark:text-gray-400",children:"followers"})]}),(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:a.postCount})," ",t.jsx("span",{className:"text-gray-500 dark:text-gray-400",children:"posts"})]}),(0,t.jsxs)("div",{children:[t.jsx("span",{className:"font-medium",children:a.starRating.toFixed(1)})," ",t.jsx("span",{className:"text-gray-500 dark:text-gray-400",children:"★"})]})]})]}),t.jsx("div",{className:"mt-4 md:mt-0 flex flex-col gap-2",children:y?(0,t.jsxs)(t.Fragment,{children:[t.jsx(d.Z,{onClick:()=>r.push(`/stores/${a.username}/edit`),variant:"outline",children:"Edit Store"}),t.jsx(d.Z,{onClick:()=>r.push(`/stores/${a.username}/products/create`),children:"Add Product"})]}):t.jsx(d.Z,{onClick:handleFollowToggle,isLoading:h||f,variant:"primary",children:"Follow"})})]})})}),(0,t.jsxs)("div",{className:"mb-12",children:[t.jsx("h2",{className:"text-2xl font-semibold mb-6",children:"Products"}),p?(0,t.jsxs)("div",{className:"text-center py-12",children:[t.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600 mx-auto"}),t.jsx("p",{className:"mt-4 text-lg",children:"Loading products..."})]}):u&&u.length>0?t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:t.jsx("div",{className:"bg-gray-100 dark:bg-gray-700 p-12 rounded-lg text-center",children:t.jsx("p",{children:"Product cards will be implemented in the next step"})})}):(0,t.jsxs)("div",{className:"text-center py-12 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[t.jsx("p",{className:"text-lg text-gray-600 dark:text-gray-300",children:"No products available yet."}),y&&t.jsx(d.Z,{onClick:()=>r.push(`/stores/${a.username}/products/create`),className:"mt-4",children:"Add Your First Product"})]})]})]})]}):t.jsx("div",{className:"min-h-screen p-6 md:p-12",children:(0,t.jsxs)("div",{className:"max-w-6xl mx-auto text-center",children:[t.jsx("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:"Store Not Found"}),t.jsx("p",{className:"mb-6",children:"The store you're looking for doesn't exist."}),t.jsx(d.Z,{onClick:()=>r.push("/stores"),children:"Back to Stores"})]})})}},10110:(e,s,r)=>{"use strict";r.r(s),r.d(s,{$$typeof:()=>n,__esModule:()=>l,default:()=>i});var t=r(95153);let a=(0,t.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\stores\[username]\page.tsx`),{__esModule:l,$$typeof:n}=a,d=a.default,i=d}};var s=require("../../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),r=s.X(0,[2103,2765,8042,7783],()=>__webpack_exec__(9148));module.exports=r})();