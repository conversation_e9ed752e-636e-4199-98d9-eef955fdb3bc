# Build stage
FROM node:18-alpine AS build

WORKDIR /app

# Copy package files
COPY services/product-service/package*.json ./
COPY libs/common ./libs/common

# Install dependencies
RUN npm install --legacy-peer-deps

# Copy source code
COPY services/product-service/src ./src
COPY services/product-service/tsconfig.json ./
COPY services/product-service/nest-cli.json ./

# Build the application
RUN npm run build

# Production stage
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY services/product-service/package*.json ./

# Install production dependencies
RUN npm install --only=production --legacy-peer-deps

# Copy built application
COPY --from=build /app/dist ./dist
COPY --from=build /app/libs ./libs

# Create logs directory and set permissions
RUN mkdir -p logs

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001

# Change ownership of logs directory to nestjs user
RUN chown -R nestjs:nodejs logs

USER nestjs

# Expose port
EXPOSE 3004

# Start the application
CMD ["node", "dist/main"]
