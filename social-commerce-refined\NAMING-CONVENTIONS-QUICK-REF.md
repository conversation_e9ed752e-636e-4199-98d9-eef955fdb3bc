# 📏 Service Naming Conventions - Quick Reference

> **⭐ MANDATORY for all AI agents and developers**
> 
> Full guidelines: [docs/guidelines/SERVICE-NAMING-CONVENTIONS.md](./docs/guidelines/SERVICE-NAMING-CONVENTIONS.md)

## 🎯 Quick Patterns

### Database Names
```
Pattern: {service_name}_service
Examples: user_service, store_service, product_service
```

### Docker Images
```
Services: {service-name}-service
Infrastructure: social-commerce-{component}
Examples: user-service, store-service, social-commerce-frontend
```

### Container Names
```
Pattern: social-commerce-{component}
Examples: social-commerce-user-service, social-commerce-postgres
```

### Queue Names
```
Pattern: {service_name}_queue
Examples: user_queue, store_queue, product_queue
```

### Environment Variables
```
Service URLs: {SERVICE_NAME}_SERVICE_URL
Database: DB_DATABASE_{SERVICE_NAME}
Examples: USER_SERVICE_URL, DB_DATABASE_USER
```

## 🚀 Service Creation Example

For a new **Product Service**:

```bash
# Database
product_service

# Docker Image
product-service

# Container
social-commerce-product-service

# Queue
product_queue

# Environment Variables
PRODUCT_SERVICE_URL=http://product-service:3003/api
PRODUCT_SERVICE_PORT=3003
DB_DATABASE_PRODUCT=product_service
RABBITMQ_PRODUCT_QUEUE=product_queue
```

## 📋 Service Creation Steps

1. **Check naming guidelines**: [SERVICE-NAMING-CONVENTIONS.md](./docs/guidelines/SERVICE-NAMING-CONVENTIONS.md)
2. **Use service template**: [docs/templates/service-template/](./docs/templates/service-template/)
3. **Follow checklist**: [SERVICE-CREATION-CHECKLIST.md](./docs/templates/service-template/SERVICE-CREATION-CHECKLIST.md)

## ✅ Valid Examples

```bash
# ✅ CORRECT
user_service                    # Database
user-service                    # Docker image
social-commerce-user-service    # Container
user_queue                      # Queue
USER_SERVICE_URL               # Environment variable

# ❌ INCORRECT
User-Service                    # Wrong case, wrong separator
user_service_container          # Wrong pattern
userQueue                       # Wrong case
user-service-url               # Wrong case
```

## 🚨 Before Creating Any Service

**MANDATORY CHECKLIST:**
- [ ] Read [SERVICE-NAMING-CONVENTIONS.md](./docs/guidelines/SERVICE-NAMING-CONVENTIONS.md)
- [ ] Use [service template](./docs/templates/service-template/)
- [ ] Follow [creation checklist](./docs/templates/service-template/SERVICE-CREATION-CHECKLIST.md)
- [ ] Verify naming compliance

---

**Status:** ✅ **MANDATORY** - All services must follow these conventions
**Last Updated:** 2025-05-26
