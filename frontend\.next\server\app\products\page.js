(()=>{var e={};e.id=5286,e.ids=[5286],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},93002:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>l.a,__next_app__:()=>g,originalPathname:()=>m,pages:()=>n,routeModule:()=>u,tree:()=>c});var s=t(67096),a=t(16132),i=t(37284),l=t.n(i),o=t(32564),d={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let c=["",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,54302)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\products\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9291,23)),"next/dist/client/components/not-found-error"]}],n=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\products\\page.tsx"],m="/products/page",g={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/products/page",pathname:"/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},738:(e,r,t)=>{Promise.resolve().then(t.bind(t,75410))},75410:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>ProductsPage});var s=t(30784),a=t(9885),i=t(27870),l=t(14379),o=t(64564),d=t(99529),c=t(59872),n=t(86372);let m=n.g.injectEndpoints({endpoints:e=>({getCategories:e.query({query:()=>"/categories",providesTags:["Category"]}),getCategoryById:e.query({query:e=>`/categories/${e}`,providesTags:(e,r,t)=>[{type:"Category",id:t}]}),getCategoryBySlug:e.query({query:e=>`/categories/slug/${e}`,providesTags:(e,r,t)=>[{type:"Category",id:e?.id}]}),getSubcategories:e.query({query:e=>`/categories/parent/${e}`,providesTags:["Category"]}),createCategory:e.mutation({query:e=>({url:"/categories",method:"POST",body:e}),invalidatesTags:["Category"]}),updateCategory:e.mutation({query:({id:e,data:r})=>({url:`/categories/${e}`,method:"PATCH",body:r}),invalidatesTags:(e,r,{id:t})=>[{type:"Category",id:t}]}),deleteCategory:e.mutation({query:e=>({url:`/categories/${e}`,method:"DELETE"}),invalidatesTags:["Category"]})})}),{useGetCategoriesQuery:g,useGetCategoryByIdQuery:u,useGetCategoryBySlugQuery:x,useGetSubcategoriesQuery:p,useCreateCategoryMutation:h,useUpdateCategoryMutation:y,useDeleteCategoryMutation:v}=m,CategoryItem=({category:e,allCategories:r,selectedCategoryIds:t,onToggle:a,isRtl:i,level:l=0})=>{let o=t.includes(e.id),d=r.filter(r=>r.parentId===e.id),c=d.length>0,n=[...d].sort((e,r)=>e.order-r.order);return(0,s.jsxs)("div",{className:`${l>0?"ml-4":""} ${i?"mr-4 ml-0":""}`,children:[(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("input",{type:"checkbox",id:`category-${e.id}`,checked:o,onChange:()=>a(e.id),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),s.jsx("label",{htmlFor:`category-${e.id}`,className:`${i?"mr-2":"ml-2"} text-sm cursor-pointer`,children:e.name})]}),c&&s.jsx("div",{className:"mt-1 space-y-1",children:n.map(e=>s.jsx(CategoryItem,{category:e,allCategories:r,selectedCategoryIds:t,onToggle:a,isRtl:i,level:l+1},e.id))})]})},product_CategoryFilter=({selectedCategoryIds:e,onCategoryChange:r})=>{let{t}=(0,i.$G)("product"),{isRtl:a}=(0,l.g)(),{data:o,isLoading:d,error:c}=g(),handleCategoryToggle=t=>{e.includes(t)?r(e.filter(e=>e!==t)):r([...e,t])};if(d)return(0,s.jsxs)("div",{className:"animate-pulse",children:[s.jsx("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2.5"}),s.jsx("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-2.5"}),s.jsx("div",{className:"h-4 bg-gray-200 rounded w-2/3 mb-2.5"}),s.jsx("div",{className:"h-4 bg-gray-200 rounded w-1/3 mb-2.5"})]});if(c||!o)return s.jsx("div",{className:"text-red-500",children:t("common.error","Error loading categories")});let n=o.filter(e=>!e.parentId),m=[...n].sort((e,r)=>e.order-r.order);return(0,s.jsxs)("div",{className:`${a?"text-right":"text-left"}`,children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[s.jsx("h3",{className:"font-semibold text-lg",children:t("search.categories")}),e.length>0&&s.jsx("button",{onClick:()=>{r([])},className:"text-sm text-primary-600 hover:text-primary-800",children:t("search.clearAll","Clear All")})]}),s.jsx("div",{className:"space-y-2",children:m.map(r=>s.jsx(CategoryItem,{category:r,allCategories:o,selectedCategoryIds:e,onToggle:handleCategoryToggle,isRtl:a},r.id))})]})},product_PriceRangeFilter=({minPrice:e,maxPrice:r,onPriceChange:t})=>{let{t:o}=(0,i.$G)("product"),{isRtl:d}=(0,l.g)(),[n,m]=(0,a.useState)(e?.toString()||""),[g,u]=(0,a.useState)(r?.toString()||""),[x,p]=(0,a.useState)(null);return(0,a.useEffect)(()=>{m(e?.toString()||""),u(r?.toString()||"")},[e,r]),(0,s.jsxs)("div",{className:`${d?"text-right":"text-left"}`,children:[s.jsx("h3",{className:"font-semibold text-lg mb-4",children:o("search.priceRange")}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[s.jsx("label",{htmlFor:"min-price",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:o("search.minPrice","Min Price")}),s.jsx("input",{type:"number",id:"min-price",value:n,onChange:e=>m(e.target.value),min:"0",step:"0.01",className:`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 border-gray-300 dark:border-gray-700 ${d?"text-right":"text-left"}`,dir:d?"rtl":"ltr"})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{htmlFor:"max-price",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:o("search.maxPrice","Max Price")}),s.jsx("input",{type:"number",id:"max-price",value:g,onChange:e=>u(e.target.value),min:"0",step:"0.01",className:`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 border-gray-300 dark:border-gray-700 ${d?"text-right":"text-left"}`,dir:d?"rtl":"ltr"})]}),x&&s.jsx("div",{className:"text-red-500 text-sm",children:x}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[s.jsx(c.Z,{onClick:()=>{let e=n?parseFloat(n):void 0,r=g?parseFloat(g):void 0;if(void 0!==e&&void 0!==r&&e>r){p(o("validation.minGreaterThanMax","Minimum price cannot be greater than maximum price"));return}p(null),t(e,r)},size:"sm",className:"flex-1",children:o("search.apply","Apply")}),s.jsx(c.Z,{onClick:()=>{m(""),u(""),p(null),t(void 0,void 0)},variant:"outline",size:"sm",className:"flex-1",children:o("search.clear","Clear")})]})]})]})},product_SortByFilter=({sortBy:e,sortOrder:r,onSortChange:t})=>{let{t:a}=(0,i.$G)("product"),{isRtl:o}=(0,l.g)(),d=[{value:"createdAt-desc",label:a("search.sortOptions.newest","Newest")},{value:"createdAt-asc",label:a("search.sortOptions.oldest","Oldest")},{value:"price-asc",label:a("search.sortOptions.priceLowToHigh","Price: Low to High")},{value:"price-desc",label:a("search.sortOptions.priceHighToLow","Price: High to Low")},{value:"likeCount-desc",label:a("search.sortOptions.mostPopular","Most Popular")},{value:"title-asc",label:a("search.sortOptions.nameAZ","Name: A-Z")},{value:"title-desc",label:a("search.sortOptions.nameZA","Name: Z-A")}];return(0,s.jsxs)("div",{className:`${o?"text-right":"text-left"}`,children:[s.jsx("label",{htmlFor:"sort-by",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:a("search.sortBy")}),s.jsx("select",{id:"sort-by",value:`${e}-${r}`,onChange:e=>{let[r,s]=e.target.value.split("-");t(r,s)},className:`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 border-gray-300 dark:border-gray-700 ${o?"text-right":"text-left"}`,dir:o?"rtl":"ltr",children:d.map(e=>s.jsx("option",{value:e.value,children:e.label},e.value))})]})},product_TagsFilter=({selectedTags:e,onTagsChange:r,popularTags:t=[]})=>{let{t:o}=(0,i.$G)("product"),{isRtl:d}=(0,l.g)(),[n,m]=(0,a.useState)(""),handleAddTag=()=>{n.trim()&&!e.includes(n.trim())&&(r([...e,n.trim()]),m(""))},handleRemoveTag=t=>{r(e.filter(e=>e!==t))},handlePopularTagClick=t=>{e.includes(t)?handleRemoveTag(t):r([...e,t])};return(0,s.jsxs)("div",{className:`${d?"text-right":"text-left"}`,children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[s.jsx("h3",{className:"font-semibold text-lg",children:o("search.tags")}),e.length>0&&s.jsx("button",{onClick:()=>{r([])},className:"text-sm text-primary-600 hover:text-primary-800",children:o("search.clearAll","Clear All")})]}),s.jsx("div",{className:"mb-4",children:(0,s.jsxs)("div",{className:"flex",children:[s.jsx("input",{type:"text",value:n,onChange:e=>m(e.target.value),onKeyDown:e=>{"Enter"===e.key&&(e.preventDefault(),handleAddTag())},placeholder:o("search.addTag","Add tag..."),className:`flex-1 px-3 py-2 border rounded-${d?"r":"l"}-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 border-gray-300 dark:border-gray-700 ${d?"text-right":"text-left"}`,dir:d?"rtl":"ltr"}),s.jsx(c.Z,{onClick:handleAddTag,className:`rounded-${d?"l":"r"}-md rounded-${d?"r":"l"}-none`,disabled:!n.trim(),children:o("search.add","Add")})]})}),e.length>0&&(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("div",{className:"text-sm font-medium mb-2",children:[o("search.selectedTags","Selected Tags"),":"]}),s.jsx("div",{className:"flex flex-wrap gap-2",children:e.map(e=>(0,s.jsxs)("div",{className:"bg-primary-100 text-primary-800 px-2 py-1 rounded-md text-sm flex items-center",children:[s.jsx("span",{children:e}),s.jsx("button",{onClick:()=>handleRemoveTag(e),className:`${d?"mr-1":"ml-1"} text-primary-600 hover:text-primary-800`,children:"\xd7"})]},e))})]}),t.length>0&&(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"text-sm font-medium mb-2",children:[o("search.popularTags","Popular Tags"),":"]}),s.jsx("div",{className:"flex flex-wrap gap-2",children:t.map(r=>s.jsx("button",{onClick:()=>handlePopularTagClick(r),className:`px-2 py-1 rounded-md text-sm ${e.includes(r)?"bg-primary-600 text-white":"bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-200"}`,children:r},r))})]})]})},product_ProductFilters=({filters:e,onFiltersChange:r,popularTags:t=[],isMobile:o=!1})=>{let{t:d}=(0,i.$G)("product"),{isRtl:n}=(0,l.g)(),[m,g]=(0,a.useState)(!o),u=e.categoryIds.length>0||e.tags.length>0||void 0!==e.minPrice||void 0!==e.maxPrice;return(0,s.jsxs)("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 ${n?"text-right":"text-left"}`,children:[o&&s.jsx("div",{className:"mb-4",children:s.jsx(c.Z,{onClick:()=>{g(!m)},variant:"outline",fullWidth:!0,children:m?d("search.hideFilters","Hide Filters"):d("search.showFilters","Show Filters")})}),m&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[s.jsx("h2",{className:"text-xl font-bold",children:d("search.filters")}),u&&s.jsx("button",{onClick:()=>{r({categoryIds:[],tags:[],minPrice:void 0,maxPrice:void 0,sortBy:"createdAt",sortOrder:"desc"})},className:"text-sm text-primary-600 hover:text-primary-800",children:d("search.resetFilters","Reset Filters")})]}),(0,s.jsxs)("div",{className:"space-y-8",children:[s.jsx(product_SortByFilter,{sortBy:e.sortBy,sortOrder:e.sortOrder,onSortChange:(t,s)=>{r({...e,sortBy:t,sortOrder:s})}}),s.jsx(product_CategoryFilter,{selectedCategoryIds:e.categoryIds,onCategoryChange:t=>{r({...e,categoryIds:t})}}),s.jsx(product_PriceRangeFilter,{minPrice:e.minPrice,maxPrice:e.maxPrice,onPriceChange:(t,s)=>{r({...e,minPrice:t,maxPrice:s})}}),s.jsx(product_TagsFilter,{selectedTags:e.tags,onTagsChange:t=>{r({...e,tags:t})},popularTags:t})]})]})]})};var b=t(48042);function ProductsPage(){let[e,r]=(0,a.useState)(""),[t,n]=(0,a.useState)(!1),[m,g]=(0,a.useState)(!1),{t:u}=(0,i.$G)("product"),{isRtl:x}=(0,l.g)(),[p,h]=(0,a.useState)({categoryIds:[],tags:[],minPrice:void 0,maxPrice:void 0,sortBy:"createdAt",sortOrder:"desc"}),y=p.categoryIds.length>0||p.tags.length>0||void 0!==p.minPrice||void 0!==p.maxPrice||"createdAt"!==p.sortBy||"desc"!==p.sortOrder,{data:v,isLoading:f}=(0,b.Au)(10,{skip:t||y}),{data:j,isLoading:N}=(0,b.tf)(e,{skip:!t||!e||y}),{data:C,isLoading:P}=(0,b.Mq)(p,{skip:!y}),w=[],k=!1;return y?(w=C,k=P):t?(w=j,k=N):(w=v,k=f),s.jsx("div",{className:`min-h-screen p-6 md:p-12 ${x?"text-right":"text-left"}`,children:(0,s.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,s.jsxs)("div",{className:`flex flex-col md:flex-row md:items-center md:justify-between mb-8 gap-4 ${x?"md:flex-row-reverse":""}`,children:[s.jsx("h1",{className:"text-3xl font-bold",children:u(t?"search.searchResults":y?"search.advancedSearch":"search.title")}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2",children:[s.jsx(d.Z,{onSearch:e=>{r(e),n(!!e),e&&h({categoryIds:[],tags:[],minPrice:void 0,maxPrice:void 0,sortBy:"createdAt",sortOrder:"desc"})},initialQuery:e}),s.jsx(c.Z,{variant:"outline",onClick:()=>{g(!m)},className:"whitespace-nowrap",children:u(m?"search.hideFilters":"search.showFilters")})]})]}),(0,s.jsxs)("div",{className:"flex flex-col md:flex-row gap-6",children:[m&&s.jsx("div",{className:"md:w-1/4",children:s.jsx(product_ProductFilters,{filters:p,onFiltersChange:e=>{h(e),(e.categoryIds.length>0||e.tags.length>0||void 0!==e.minPrice||void 0!==e.maxPrice||"createdAt"!==e.sortBy||"desc"!==e.sortOrder)&&(r(""),n(!1))},popularTags:["sale","new","trending","bestseller","limited"],isMobile:!1})}),s.jsx("div",{className:m?"md:w-3/4":"w-full",children:s.jsx(o.Z,{products:w||[],isLoading:k,emptyMessage:t?u("search.noResults",{query:e}):y?u("search.noFilterResults","No products match your filters"):u("common.noProducts")})})]})]})})}},54302:(e,r,t)=>{"use strict";t.r(r),t.d(r,{$$typeof:()=>l,__esModule:()=>i,default:()=>d});var s=t(95153);let a=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\products\page.tsx`),{__esModule:i,$$typeof:l}=a,o=a.default,d=o}};var r=require("../../webpack-runtime.js");r.C(e);var __webpack_exec__=e=>r(r.s=e),t=r.X(0,[2103,2765,706,8042,8845,9529,4564],()=>__webpack_exec__(93002));module.exports=t})();