import { BaseEvent } from '../base-event.interface';

/**
 * Event emitted when a product's inventory is updated
 */
export class InventoryUpdatedEvent implements BaseEvent<InventoryUpdatedPayload> {
  id: string;
  type: string = 'product.inventory.updated';
  version: string = '1.0';
  timestamp: string;
  producer: string = 'product-service';
  payload: InventoryUpdatedPayload;

  constructor(payload: InventoryUpdatedPayload) {
    this.id = payload.id;
    this.timestamp = new Date().toISOString();
    this.payload = payload;
  }
}

/**
 * Payload for InventoryUpdatedEvent
 */
export interface InventoryUpdatedPayload {
  /**
   * Product ID
   */
  id: string;

  /**
   * Store ID
   */
  storeId: string;

  /**
   * Previous inventory count
   */
  previousInventory: number;

  /**
   * New inventory count
   */
  newInventory: number;

  /**
   * Update timestamp
   */
  updatedAt: string;
}
