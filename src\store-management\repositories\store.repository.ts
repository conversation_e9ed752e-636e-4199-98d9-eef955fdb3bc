import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Store } from '../entities/store.entity';
import { CreateStoreDto } from '../dto/create-store.dto';
import { UpdateStoreDto } from '../dto/update-store.dto';

@Injectable()
export class StoreRepository {
  private readonly logger = new Logger(StoreRepository.name);

  constructor(
    @InjectRepository(Store)
    private readonly repository: Repository<Store>,
  ) {}

  async findAll(): Promise<Store[]> {
    this.logger.log('Finding all stores');
    return this.repository.find({ relations: ['products'] });
  }

  async findOne(id: string): Promise<Store> {
    this.logger.log(`Finding store with ID: ${id}`);
    const store = await this.repository.findOne({ 
      where: { id },
      relations: ['products'],
    });

    if (!store) {
      throw new NotFoundException(`Store with ID ${id} not found`);
    }

    return store;
  }

  async findByOwnerId(ownerId: string): Promise<Store[]> {
    this.logger.log(`Finding stores for owner with ID: ${ownerId}`);
    return this.repository.find({ 
      where: { ownerId },
      relations: ['products'],
    });
  }

  async create(createStoreDto: CreateStoreDto): Promise<Store> {
    this.logger.log(`Creating store with name: ${createStoreDto.name}`);
    
    const store = this.repository.create(createStoreDto);
    
    return this.repository.save(store);
  }

  async update(id: string, updateStoreDto: UpdateStoreDto): Promise<Store> {
    this.logger.log(`Updating store with ID: ${id}`);
    
    const store = await this.findOne(id);
    
    // Update store properties
    Object.assign(store, updateStoreDto);
    
    return this.repository.save(store);
  }

  async remove(id: string): Promise<void> {
    this.logger.log(`Removing store with ID: ${id}`);
    
    const store = await this.findOne(id);
    
    await this.repository.remove(store);
  }

  async incrementFollowerCount(id: string): Promise<Store> {
    this.logger.log(`Incrementing follower count for store with ID: ${id}`);
    
    const store = await this.findOne(id);
    
    store.followerCount += 1;
    
    return this.repository.save(store);
  }

  async decrementFollowerCount(id: string): Promise<Store> {
    this.logger.log(`Decrementing follower count for store with ID: ${id}`);
    
    const store = await this.findOne(id);
    
    if (store.followerCount > 0) {
      store.followerCount -= 1;
    }
    
    return this.repository.save(store);
  }

  async updateRating(id: string, rating: number, reviewCount: number): Promise<Store> {
    this.logger.log(`Updating rating for store with ID: ${id}`);
    
    const store = await this.findOne(id);
    
    store.rating = rating;
    store.reviewCount = reviewCount;
    
    return this.repository.save(store);
  }
}
