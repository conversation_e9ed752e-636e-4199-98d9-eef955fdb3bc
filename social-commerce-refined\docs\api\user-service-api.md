# User Service API Documentation

## 🎯 Overview
Complete API documentation for the User Service, including authentication, profiles, and verification.

**Base URL**: `http://localhost:3002/api` (Direct) or `http://localhost:3000/api` (via API Gateway)
**Authentication**: Bearer JWT Token
**Content-Type**: `application/json`

## 🔐 Authentication
All endpoints require JWT authentication except registration, login, and health checks.

## 📋 Authentication Endpoints

### **1. Register User**
```http
POST /api/auth/register
```

**Request Body**:
```json
{
  "username": "johndoe",
  "email": "<EMAIL>",
  "password": "securePassword123",
  "phone": "+**********"
}
```

**Response** (201 Created):
```json
{
  "id": "user-uuid",
  "username": "johndoe",
  "email": "<EMAIL>",
  "isEmailVerified": false,
  "role": "USER",
  "createdAt": "2025-05-30T17:00:00.000Z"
}
```

### **2. Login User**
```http
POST /api/auth/login
```

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

**Response** (200 OK):
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "user-uuid",
    "username": "johndoe",
    "email": "<EMAIL>",
    "role": "USER"
  }
}
```

### **3. Get Profile**
```http
GET /api/auth/profile
Authorization: Bearer <token>
```

**Response** (200 OK):
```json
{
  "id": "user-uuid",
  "username": "johndoe",
  "email": "<EMAIL>",
  "isEmailVerified": true,
  "role": "USER",
  "createdAt": "2025-05-30T17:00:00.000Z"
}
```

## 👤 Profile Management Endpoints

### **1. Get All Profiles**
```http
GET /api/profiles
Authorization: Bearer <token>
```

### **2. Get Profile by ID**
```http
GET /api/profiles/:id
Authorization: Bearer <token>
```

### **3. Get Profile by User ID**
```http
GET /api/profiles/user/:userId
Authorization: Bearer <token>
```

### **4. Create Profile**
```http
POST /api/profiles/user/:userId
Authorization: Bearer <token>
```

**Request Body**:
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "bio": "Software developer",
  "location": "New York, NY"
}
```

### **5. Update Profile**
```http
PUT /api/profiles/:id
Authorization: Bearer <token>
```

### **6. Delete Profile**
```http
DELETE /api/profiles/:id
Authorization: Bearer <token>
```

## ✉️ Verification Endpoints

### **1. Send Email Verification**
```http
POST /api/verification/email/:userId
Authorization: Bearer <token>
```

### **2. Verify Email**
```http
POST /api/verification/verify-email
```

**Request Body**:
```json
{
  "token": "verification-token-here"
}
```

### **3. Send Phone Verification**
```http
POST /api/verification/phone/:userId
Authorization: Bearer <token>
```

### **4. Verify Phone**
```http
POST /api/verification/verify-phone/:userId
Authorization: Bearer <token>
```

### **5. Forgot Password**
```http
POST /api/verification/forgot-password
```

**Request Body**:
```json
{
  "email": "<EMAIL>"
}
```

### **6. Reset Password**
```http
POST /api/verification/reset-password
```

**Request Body**:
```json
{
  "token": "reset-token-here",
  "password": "newSecurePassword123"
}
```

## 🧪 Testing Examples

### **Register User Test**
```bash
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "testPassword123",
    "phone": "+**********"
  }'
```

### **Login User Test**
```bash
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "testPassword123"
  }'
```

### **Get Profile Test**
```bash
curl -X GET http://localhost:3000/api/auth/profile \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## ❌ Error Responses
- `400`: Bad Request - Invalid input data
- `401`: Unauthorized - Missing or invalid JWT token
- `404`: Not Found - User/Profile not found
- `409`: Conflict - User already exists

---

**API Version**: 1.0
**Last Updated**: May 30, 2025
**Swagger Documentation**: Available at `/api/docs`
