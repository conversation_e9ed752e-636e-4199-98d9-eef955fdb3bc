exports.id=2765,exports.ids=[2765],exports.modules={1014:(e,t,r)=>{var s={"./en/affiliate.json":[11003,1003],"./en/analytics.json":[33572,3572],"./en/auth.json":[60291,291],"./en/common.json":[11471,1471],"./en/errors.json":[13914,3914],"./en/export.json":[79809,9809],"./en/groupBuying.json":[31285,1285],"./en/import.json":[7393,7393],"./en/navigation.json":[56822,6822],"./en/product.json":[50888,888],"./en/social.json":[28731,8731],"./fa/auth.json":[75207,5207],"./fa/common.json":[80109,109],"./fa/errors.json":[95703,5703],"./fa/navigation.json":[31987,1987],"./fa/product.json":[65226,5226]};function webpackAsyncContext(e){if(!r.o(s,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=s[e],a=t[0];return r.e(t[1]).then(()=>r.t(a,19))}webpackAsyncContext.keys=()=>Object.keys(s),webpackAsyncContext.id=1014,e.exports=webpackAsyncContext},73090:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,13724,23)),Promise.resolve().then(r.t.bind(r,35365,23)),Promise.resolve().then(r.t.bind(r,44900,23)),Promise.resolve().then(r.t.bind(r,44714,23)),Promise.resolve().then(r.t.bind(r,45392,23)),Promise.resolve().then(r.t.bind(r,8898,23))},73291:(e,t,r)=>{Promise.resolve().then(r.bind(r,42823)),Promise.resolve().then(r.bind(r,56133))},42823:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Providers:()=>Providers});var s=r(30784),a=r(87771),i=r(17914),o=r(86372),n=r(86490),l=r(21233),d=r(34087);let c={shippingAddress:void 0,billingAddress:void 0,paymentMethod:void 0,shippingMethod:void 0,sameAsShipping:!0,step:d.p.SHIPPING},u=(0,i.oM)({name:"checkout",initialState:c,reducers:{setShippingAddress:(e,t)=>{e.shippingAddress=t.payload,e.sameAsShipping&&(e.billingAddress=t.payload)},setBillingAddress:(e,t)=>{e.billingAddress=t.payload},setPaymentMethod:(e,t)=>{e.paymentMethod=t.payload},setShippingMethod:(e,t)=>{e.shippingMethod=t.payload},setSameAsShipping:(e,t)=>{e.sameAsShipping=t.payload,t.payload&&e.shippingAddress&&(e.billingAddress=e.shippingAddress)},setCheckoutStep:(e,t)=>{e.step=t.payload},nextStep:e=>{switch(e.step){case d.p.SHIPPING:e.step=d.p.PAYMENT;break;case d.p.PAYMENT:e.step=d.p.REVIEW;break;case d.p.REVIEW:e.step=d.p.CONFIRMATION}},previousStep:e=>{switch(e.step){case d.p.PAYMENT:e.step=d.p.SHIPPING;break;case d.p.REVIEW:e.step=d.p.PAYMENT;break;case d.p.CONFIRMATION:e.step=d.p.REVIEW}},resetCheckout:()=>c}}),{setShippingAddress:h,setBillingAddress:m,setPaymentMethod:g,setShippingMethod:p,setSameAsShipping:x,setCheckoutStep:y,nextStep:f,previousStep:v,resetCheckout:w}=u.actions,k=u.reducer,j=(0,i.xC)({reducer:{[o.g.reducerPath]:o.g.reducer,auth:n.ZP,cart:l.ZP,checkout:k},middleware:e=>e().concat(o.g.middleware),devTools:!1});var N=r(27870),b=r(41887),C=r(57963),T=r(32666),L=r(89915);b.ZP.use(N.Db).use(T.Z).use((0,C.Z)((e,t)=>r(1014)(`./${e}/${t}.json`))).init({...(0,L.FW)(),detection:{order:["cookie","localStorage","navigator","htmlTag"],caches:["cookie","localStorage"],cookieExpirationDate:new Date(Date.now()+31536e6)}});let E=b.ZP;var P=r(14379),S=r(11483),M=r(37566),I=r(17560);function Providers({children:e}){return s.jsx(a.zt,{store:j,children:s.jsx(N.a3,{i18n:E,children:s.jsx(P._,{children:s.jsx(S.ZP,{children:s.jsx(M.ZP,{children:s.jsx(I.ZP,{children:e})})})})})})}},56133:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>layout_Layout});var s=r(30784),a=r(9885),i=r(11440),o=r.n(i),n=r(27870),l=r(38898);let notifications_NotificationBadge=({count:e,maxCount:t=99,className:r=""})=>{let{t:a}=(0,n.$G)("notifications");if(e<=0)return null;let i=e>t?`${t}+`:e.toString();return s.jsx("span",{className:`inline-flex items-center justify-center min-w-[1.25rem] h-5 px-1 text-xs font-medium rounded-full bg-red-500 text-white ${r}`,"aria-label":a("unreadNotifications","{{count}} unread notifications",{count:e}),children:i})};var d=r(37566),c=r(53717);let notifications_NotificationDropdown=({className:e=""})=>{let{t}=(0,n.$G)("notifications"),{unreadCount:r}=(0,d.zn)(),[i,u]=(0,a.useState)(!1),h=(0,a.useRef)(null),{data:m,isLoading:g,refetch:p}=(0,c.mI)({page:1,limit:5,unreadOnly:!1}),[x]=(0,c.j6)(),[y]=(0,c.JF)(),[f]=(0,c.sJ)();(0,a.useEffect)(()=>{let handleClickOutside=e=>{h.current&&!h.current.contains(e.target)&&u(!1)};return document.addEventListener("mousedown",handleClickOutside),()=>{document.removeEventListener("mousedown",handleClickOutside)}},[]);let handleMarkAsRead=async e=>{try{await x(e).unwrap(),p()}catch(e){console.error("Failed to mark notification as read:",e)}},handleMarkAllAsRead=async()=>{try{await y().unwrap(),p()}catch(e){console.error("Failed to mark all notifications as read:",e)}},handleDeleteNotification=async e=>{try{await f(e).unwrap(),p()}catch(e){console.error("Failed to delete notification:",e)}};return(0,s.jsxs)("div",{className:`relative ${e}`,ref:h,children:[(0,s.jsxs)("button",{className:"relative p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none",onClick:()=>{u(!i)},"aria-label":t("notifications","Notifications"),children:[s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"})}),r>0&&s.jsx(notifications_NotificationBadge,{count:r,className:"absolute top-0 right-0 transform translate-x-1/2 -translate-y-1/2"})]}),i&&(0,s.jsxs)("div",{className:"absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden z-50",children:[(0,s.jsxs)("div",{className:"p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center",children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:t("notifications","Notifications")}),s.jsx(o(),{href:"/notifications",className:"text-sm text-primary-600 dark:text-primary-400 hover:underline",onClick:()=>u(!1),children:t("viewAll","View All")})]}),s.jsx("div",{className:"max-h-96 overflow-y-auto",children:s.jsx(l.Z,{notifications:m?.notifications||[],isLoading:g,onMarkAsRead:handleMarkAsRead,onMarkAllAsRead:handleMarkAllAsRead,onDelete:handleDeleteNotification,className:"p-2"})})]})]})};var u=r(57114),h=r(92299);let search_SearchBar=({initialQuery:e="",placeholder:t="Search for products, stores, or users...",onSearch:r,className:i="",showSuggestions:o=!0})=>{let[n,l]=(0,a.useState)(e),[d,c]=(0,a.useState)(!1),m=(0,a.useRef)(null),g=(0,u.useRouter)(),{data:p=[]}=(0,h.Rs)(void 0,{skip:!o}),{data:x=[]}=(0,h.TX)(void 0,{skip:!o}),[y]=(0,h.Ve)();(0,a.useEffect)(()=>{let handleClickOutside=e=>{m.current&&!m.current.contains(e.target)&&c(!1)};return document.addEventListener("mousedown",handleClickOutside),()=>{document.removeEventListener("mousedown",handleClickOutside)}},[]);let handleSuggestionClick=e=>{l(e),y(e),r?r(e):g.push(`/search?q=${encodeURIComponent(e)}`),c(!1)};return(0,s.jsxs)("div",{className:`relative ${i}`,ref:m,children:[(0,s.jsxs)("form",{onSubmit:e=>{e.preventDefault(),n.trim()&&(y(n.trim()),r?r(n.trim()):g.push(`/search?q=${encodeURIComponent(n.trim())}`),c(!1))},className:"relative",children:[s.jsx("input",{type:"text",value:n,onChange:e=>{l(e.target.value),e.target.value.length>0?c(!0):c(!1)},onFocus:()=>n.length>0&&c(!0),placeholder:t,className:"w-full px-4 py-2 pl-10 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"}),s.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:s.jsx("svg",{className:"h-5 w-5 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),(0,s.jsxs)("button",{type:"submit",className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:[s.jsx("span",{className:"sr-only",children:"Search"}),s.jsx("svg",{className:"h-5 w-5 text-gray-400 hover:text-gray-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M14 5l7 7m0 0l-7 7m7-7H3"})})]})]}),o&&d&&(0,s.jsxs)("div",{className:"absolute z-10 mt-1 w-full bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 max-h-60 overflow-y-auto",children:[n.length>0&&s.jsx("div",{className:"p-2 border-b border-gray-200 dark:border-gray-700",children:(0,s.jsxs)("button",{onClick:()=>handleSuggestionClick(n),className:"w-full text-left px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md flex items-center",children:[s.jsx("svg",{className:"h-5 w-5 text-gray-400 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})}),(0,s.jsxs)("span",{children:['Search for "',s.jsx("strong",{children:n}),'"']})]})}),p.length>0&&(0,s.jsxs)("div",{className:"p-2",children:[s.jsx("h3",{className:"text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase px-3 mb-1",children:"Recent Searches"}),s.jsx("ul",{children:p.map((e,t)=>s.jsx("li",{children:(0,s.jsxs)("button",{onClick:()=>handleSuggestionClick(e),className:"w-full text-left px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md flex items-center",children:[s.jsx("svg",{className:"h-5 w-5 text-gray-400 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),s.jsx("span",{children:e})]})},`recent-${t}`))})]}),x.length>0&&(0,s.jsxs)("div",{className:"p-2",children:[s.jsx("h3",{className:"text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase px-3 mb-1",children:"Popular Searches"}),s.jsx("ul",{children:x.map((e,t)=>s.jsx("li",{children:(0,s.jsxs)("button",{onClick:()=>handleSuggestionClick(e),className:"w-full text-left px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md flex items-center",children:[s.jsx("svg",{className:"h-5 w-5 text-gray-400 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})}),s.jsx("span",{children:e})]})},`popular-${t}`))})]})]})]})};var m=r(14379);let components_LanguageSwitcher=({disabled:e=!1})=>{let{i18n:t,t:r}=(0,n.$G)(),a=(0,u.useRouter)(),{isRtl:i}=(0,m.g)(),changeLanguage=e=>{t.changeLanguage(e),a.refresh()};return(0,s.jsxs)("div",{className:`language-switcher ${i?"text-right":"text-left"}`,children:[(0,s.jsxs)("select",{value:t.language,onChange:e=>changeLanguage(e.target.value),disabled:e,"aria-label":r("language.select","Select language"),className:"language-select px-2 py-1 rounded border border-gray-300 bg-white",children:[s.jsx("option",{value:"en",children:r("language.en","English")}),s.jsx("option",{value:"fa",children:r("language.fa","فارسی")})]}),e&&s.jsx("div",{className:"language-switcher-tooltip mt-1 text-sm text-gray-500",children:s.jsx("small",{children:r("language.disabled","Language switching will be enabled in a future update")})})]})};var g=r(2280),p=r(86867);let layout_Header=()=>{let{data:e=0}=(0,g._C)(),{data:t=[]}=(0,p.aK)(),{t:r}=(0,n.$G)("navigation"),{isRtl:a}=(0,m.g)();return s.jsx("header",{className:"bg-white dark:bg-gray-800 shadow-sm",children:s.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between h-16",children:[(0,s.jsxs)("div",{className:"flex",children:[s.jsx("div",{className:"flex-shrink-0 flex items-center",children:s.jsx(o(),{href:"/",className:"text-xl font-bold text-primary-600",children:"SocialCommerce"})}),(0,s.jsxs)("nav",{className:`hidden md:ml-6 md:flex ${a?"md:space-x-reverse md:space-x-4":"md:space-x-4"}`,children:[s.jsx(o(),{href:"/products",className:"px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white",children:r("products")}),s.jsx(o(),{href:"/stores",className:"px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white",children:r("stores")}),s.jsx(o(),{href:"/feed",className:"px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white",children:r("social","Social")}),s.jsx(o(),{href:"/activity/feed",className:"px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white",children:r("activity","Activity")}),s.jsx(o(),{href:"/group-buying",className:"px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white",children:r("groupBuys","Group Buys")}),s.jsx(o(),{href:"/affiliate",className:"px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white",children:r("affiliate","Affiliate")})]}),s.jsx("div",{className:"hidden md:block md:ml-6 md:w-64",children:s.jsx(search_SearchBar,{placeholder:"Search..."})})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("div",{className:"ml-4",children:s.jsx(notifications_NotificationDropdown,{})}),s.jsx("div",{className:"ml-4",children:(0,s.jsxs)(o(),{href:"/wishlist",className:"relative p-2 text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white",children:[s.jsx("span",{className:"sr-only",children:"Wishlist"}),s.jsx("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})}),t.length>0&&s.jsx("span",{className:"absolute top-0 right-0 -mt-1 -mr-1 bg-red-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center",children:t.length>99?"99+":t.length})]})}),s.jsx("div",{className:"ml-4",children:(0,s.jsxs)(o(),{href:"/cart",className:"relative p-2 text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white",children:[s.jsx("span",{className:"sr-only",children:"Cart"}),s.jsx("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"})}),e>0&&s.jsx("span",{className:"absolute top-0 right-0 -mt-1 -mr-1 bg-primary-600 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center",children:e>99?"99+":e})]})}),s.jsx("div",{className:"ml-4 relative",children:(0,s.jsxs)(o(),{href:"/profile",className:"relative p-2 text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white",children:[s.jsx("span",{className:"sr-only",children:"User menu"}),s.jsx("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})]})}),s.jsx("div",{className:"ml-4 relative",children:(0,s.jsxs)(o(),{href:"/settings",className:"relative p-2 text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white",children:[s.jsx("span",{className:"sr-only",children:"Settings"}),(0,s.jsxs)("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]})]})}),s.jsx("div",{className:"ml-4 relative",children:s.jsx(components_LanguageSwitcher,{})})]})]})})})};var x=r(88908),y=r(13210),f=r(52451),v=r.n(f),w=r(73531);let notification_NotificationToast=({notification:e,onClose:t,autoClose:r=!0,autoCloseDelay:i=5e3})=>{let[n,l]=(0,a.useState)(!0);(0,a.useEffect)(()=>{if(r){let e=setTimeout(()=>{l(!1),setTimeout(t,300)},i);return()=>clearTimeout(e)}},[r,i,t]);let d=(0,w.Z)(new Date(e.createdAt),{addSuffix:!0});return s.jsx("div",{className:`fixed bottom-4 right-4 max-w-sm bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden transition-all duration-300 ${n?"opacity-100 translate-y-0":"opacity-0 translate-y-4"}`,children:s.jsx("div",{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-start",children:[s.jsx("div",{className:"flex-shrink-0",children:e.data?.senderProfileImage?s.jsx("div",{className:"relative h-10 w-10 rounded-full overflow-hidden",children:s.jsx(v(),{src:e.data.senderProfileImage,alt:e.data.senderUsername||"User",fill:!0,className:"object-cover"})}):(()=>{switch(e.type){case y.k.FOLLOW:return s.jsx("div",{className:"bg-blue-100 dark:bg-blue-900 p-2 rounded-full",children:s.jsx("svg",{className:"h-5 w-5 text-blue-500 dark:text-blue-300",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"})})});case y.k.LIKE:return s.jsx("div",{className:"bg-red-100 dark:bg-red-900 p-2 rounded-full",children:s.jsx("svg",{className:"h-5 w-5 text-red-500 dark:text-red-300",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})})});case y.k.COMMENT:return s.jsx("div",{className:"bg-green-100 dark:bg-green-900 p-2 rounded-full",children:s.jsx("svg",{className:"h-5 w-5 text-green-500 dark:text-green-300",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"})})});default:return s.jsx("div",{className:"bg-gray-100 dark:bg-gray-700 p-2 rounded-full",children:s.jsx("svg",{className:"h-5 w-5 text-gray-500 dark:text-gray-300",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})})}})()}),s.jsx("div",{className:"ml-3 flex-1",children:(0,s.jsxs)(o(),{href:e.data?.url?e.data.url:e.data?.entityType==="product"&&e.data?.entityId?`/products/${e.data.entityId}`:e.data?.entityType==="store"&&e.data?.entityId?`/stores/${e.data.entityId}`:e.data?.senderUsername?`/profile/${e.data.senderUsername}`:"/notifications",className:"block",children:[s.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e.title}),s.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.message}),s.jsx("p",{className:"text-xs text-gray-400 dark:text-gray-500 mt-1",children:d})]})}),s.jsx("div",{className:"ml-3 flex-shrink-0",children:(0,s.jsxs)("button",{onClick:t,className:"text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400",children:[s.jsx("span",{className:"sr-only",children:"Close"}),s.jsx("svg",{className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})]})})]})})})};var k=r(11483);let notification_NotificationToastContainer=()=>{let[e,t]=(0,a.useState)([]),[r,i]=(0,a.useState)(!1),{lastMessage:o}=(0,k.sO)();(0,a.useEffect)(()=>(i(!0),()=>i(!1)),[]),(0,a.useEffect)(()=>{if(o&&"notification"===o.type){let e=o.payload;t(t=>[e,...t].slice(0,5))}},[o]);let handleRemoveNotification=e=>{t(t=>t.filter(t=>t.id!==e))};return r?(0,x.createPortal)(s.jsx("div",{className:"fixed bottom-4 right-4 z-50 space-y-4",children:e.map(e=>s.jsx(notification_NotificationToast,{notification:e,onClose:()=>handleRemoveNotification(e.id)},e.id))}),document.body):null},layout_Layout=({children:e})=>(0,s.jsxs)("div",{className:"min-h-screen bg-gray-100 dark:bg-gray-900",children:[s.jsx(layout_Header,{}),s.jsx("main",{children:e}),s.jsx(notification_NotificationToastContainer,{})]})},38898:(e,t,r)=>{"use strict";r.d(t,{Z:()=>notifications_NotificationList});var s=r(30784);r(9885);var a=r(27870),i=r(14379),o=r(11440),n=r.n(o),l=r(52451),d=r.n(l),c=r(73531),u=r(13210);let notifications_NotificationItem=({notification:e,onMarkAsRead:t,onDelete:r,className:o=""})=>{let{t:l}=(0,a.$G)("notifications"),{isRtl:h}=(0,i.g)();return s.jsx(n(),{href:(()=>{if(e.data?.url)return e.data.url;switch(e.type){case u.k.LIKE:case u.k.COMMENT:return e.data?.entityType==="product"?`/products/${e.data.entityId}`:`/social/posts/${e.data?.entityId}`;case u.k.FOLLOW:return e.data?.entityType==="store"?`/stores/${e.data.entityId}`:`/profile/${e.data?.senderUsername}`;case u.k.MENTION:return e.data?.entityType==="product"?`/products/${e.data.entityId}`:e.data?.entityType==="store"?`/stores/${e.data.entityId}`:`/social/posts/${e.data?.entityId}`;case u.k.ORDER:return`/orders/${e.data?.entityId}`;case u.k.PAYMENT:return"/payments";case u.k.SYSTEM:return"/notifications";default:return"#"}})(),className:`block ${o}`,onClick:()=>{!e.isRead&&t&&t(e.id)},children:(0,s.jsxs)("div",{className:`flex items-start p-4 ${e.isRead?"bg-white dark:bg-gray-800":"bg-blue-50 dark:bg-blue-900/20"} rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors group`,children:[s.jsx("div",{className:"flex-shrink-0 mr-3",children:e.data?.senderProfileImage?s.jsx("div",{className:"relative w-10 h-10 rounded-full overflow-hidden",children:s.jsx(d(),{src:e.data.senderProfileImage,alt:e.data.senderUsername||"",fill:!0,className:"object-cover"})}):s.jsx("div",{className:"w-10 h-10 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center",children:(()=>{switch(e.type){case u.k.LIKE:return s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-red-500",viewBox:"0 0 20 20",fill:"currentColor",children:s.jsx("path",{fillRule:"evenodd",d:"M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z",clipRule:"evenodd"})});case u.k.COMMENT:return s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-blue-500",viewBox:"0 0 20 20",fill:"currentColor",children:s.jsx("path",{fillRule:"evenodd",d:"M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z",clipRule:"evenodd"})});case u.k.FOLLOW:return s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-green-500",viewBox:"0 0 20 20",fill:"currentColor",children:s.jsx("path",{d:"M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z"})});case u.k.MENTION:return s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-purple-500",viewBox:"0 0 20 20",fill:"currentColor",children:s.jsx("path",{fillRule:"evenodd",d:"M14.243 5.757a6 6 0 10-.986 9.284 1 1 0 111.087 1.678A8 8 0 1118 10a3 3 0 01-4.8 2.401A4 4 0 1114 10a1 1 0 102 0c0-1.537-.586-3.07-1.757-4.243zM12 10a2 2 0 10-4 0 2 2 0 004 0z",clipRule:"evenodd"})});default:return s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-gray-500",viewBox:"0 0 20 20",fill:"currentColor",children:s.jsx("path",{d:"M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"})})}})()})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[s.jsx("p",{className:"text-sm text-gray-900 dark:text-gray-100",children:e.message}),s.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:(e=>{try{return(0,c.Z)(new Date(e),{addSuffix:!0})}catch(t){return e}})(e.createdAt)})]}),(0,s.jsxs)("div",{className:"ml-3 flex-shrink-0 flex items-center",children:[!e.isRead&&s.jsx("div",{className:"w-2 h-2 rounded-full bg-primary-500 mr-3"}),r&&s.jsx("button",{type:"button",className:"text-gray-400 hover:text-red-500 opacity-0 group-hover:opacity-100 focus:opacity-100 transition-opacity",onClick:t=>{t.preventDefault(),t.stopPropagation(),r&&r(e.id)},"aria-label":l("delete","Delete"),children:s.jsx("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})]})})};var h=r(59872);let notifications_NotificationList=({notifications:e,isLoading:t=!1,hasMore:r=!1,onLoadMore:i,onMarkAsRead:o,onMarkAllAsRead:n,onDelete:l,onDeleteAll:d,className:c=""})=>{let{t:u}=(0,a.$G)("notifications");if(t&&0===e.length)return s.jsx("div",{className:`space-y-4 ${c}`,children:Array.from({length:3}).map((e,t)=>(0,s.jsxs)("div",{className:"flex items-start p-4 bg-white dark:bg-gray-800 rounded-lg animate-pulse",children:[s.jsx("div",{className:"w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full mr-3"}),(0,s.jsxs)("div",{className:"flex-1",children:[s.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"}),s.jsx("div",{className:"h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/4"})]})]},t))});if(0===e.length)return(0,s.jsxs)("div",{className:`p-6 bg-white dark:bg-gray-800 rounded-lg text-center ${c}`,children:[s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-12 w-12 text-gray-400 dark:text-gray-600 mx-auto mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"})}),s.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:u("noNotifications","No notifications yet")})]});let m=e.some(e=>!e.isRead);return(0,s.jsxs)("div",{className:c,children:[(m||e.length>0)&&(0,s.jsxs)("div",{className:"flex justify-between mb-4",children:[s.jsx("div",{children:e.length>0&&d&&s.jsx(h.Z,{variant:"outline",size:"sm",onClick:d,className:"text-red-500 border-red-500 hover:bg-red-50 dark:hover:bg-red-900/20",children:u("deleteAll","Delete All")})}),s.jsx("div",{children:m&&n&&s.jsx(h.Z,{variant:"outline",size:"sm",onClick:n,children:u("markAllAsRead","Mark all as read")})})]}),s.jsx("div",{className:"space-y-2",children:e.map(e=>s.jsx(notifications_NotificationItem,{notification:e,onMarkAsRead:o,onDelete:l},e.id))}),r&&s.jsx("div",{className:"mt-4 flex justify-center",children:s.jsx(h.Z,{variant:"outline",onClick:i,isLoading:t,disabled:t,children:u("loadMore","Load More")})})]})}},14379:(e,t,r)=>{"use strict";r.d(t,{_:()=>DirectionProvider,g:()=>useDirection});var s=r(30784),a=r(9885),i=r(27870),o=r(89915);let n=(0,a.createContext)({direction:"ltr",isRtl:!1}),DirectionProvider=({children:e})=>{let{i18n:t}=(0,i.$G)(),[r,l]=(0,a.useState)((0,o.Fp)(t.language)?"rtl":"ltr");return(0,a.useEffect)(()=>{let e=(0,o.Fp)(t.language)?"rtl":"ltr";l(e),document.documentElement.dir=e,document.documentElement.lang=t.language,document.body.dir=e,"rtl"===e?document.body.classList.add("rtl"):document.body.classList.remove("rtl")},[t.language]),s.jsx(n.Provider,{value:{direction:r,isRtl:"rtl"===r},children:e})},useDirection=()=>(0,a.useContext)(n)},37566:(e,t,r)=>{"use strict";r.d(t,{ZP:()=>providers_NotificationProvider,zn:()=>useNotifications});var s=r(30784),a=r(9885),i=r(53717),o=r(27870),n=r(11440),l=r.n(n),d=r(52451),c=r.n(d),u=r(13210);let notifications_NotificationToast=({notification:e,onClose:t,autoCloseDelay:r=5e3,className:i=""})=>{let{t:n}=(0,o.$G)("notifications"),[d,h]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{let e=setTimeout(()=>{h(!1),setTimeout(t,300)},r);return()=>clearTimeout(e)},[r,t]),s.jsx("div",{className:`fixed bottom-4 right-4 max-w-sm w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden transition-all duration-300 ${d?"opacity-100 translate-y-0":"opacity-0 translate-y-4"} ${i}`,children:s.jsx(l(),{href:(()=>{if(e.data?.url)return e.data.url;switch(e.type){case u.k.FOLLOW:return e.data?.senderId?`/profile/${e.data.senderUsername}`:"#";case u.k.LIKE:case u.k.COMMENT:case u.k.MENTION:return e.data?.entityId?`/social/posts/${e.data.entityId}`:"#";case u.k.ORDER:return e.data?.entityId?`/orders/${e.data.entityId}`:"/orders";case u.k.PAYMENT:return"/payments";default:return"#"}})(),onClick:t,children:(0,s.jsxs)("div",{className:"p-4 flex items-start",children:[s.jsx("div",{className:"flex-shrink-0 mr-3",children:e.data?.senderProfileImage?s.jsx("div",{className:"relative w-10 h-10 rounded-full overflow-hidden",children:s.jsx(c(),{src:e.data.senderProfileImage,alt:e.data.senderUsername||"",fill:!0,className:"object-cover"})}):s.jsx("div",{className:"w-10 h-10 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center",children:(()=>{switch(e.type){case u.k.LIKE:return s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-red-500",viewBox:"0 0 20 20",fill:"currentColor",children:s.jsx("path",{fillRule:"evenodd",d:"M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z",clipRule:"evenodd"})});case u.k.COMMENT:return s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-blue-500",viewBox:"0 0 20 20",fill:"currentColor",children:s.jsx("path",{fillRule:"evenodd",d:"M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z",clipRule:"evenodd"})});case u.k.FOLLOW:return s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-green-500",viewBox:"0 0 20 20",fill:"currentColor",children:s.jsx("path",{d:"M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z"})});case u.k.MENTION:return s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-purple-500",viewBox:"0 0 20 20",fill:"currentColor",children:s.jsx("path",{fillRule:"evenodd",d:"M14.243 5.757a6 6 0 10-.986 9.284 1 1 0 111.087 1.678A8 8 0 1118 10a3 3 0 01-4.8 2.401A4 4 0 1114 10a1 1 0 102 0c0-1.537-.586-3.07-1.757-4.243zM12 10a2 2 0 10-4 0 2 2 0 004 0z",clipRule:"evenodd"})});default:return s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-gray-500",viewBox:"0 0 20 20",fill:"currentColor",children:s.jsx("path",{d:"M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"})})}})()})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[s.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e.title}),s.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:e.message})]}),s.jsx("button",{type:"button",className:"ml-4 text-gray-400 hover:text-gray-500 dark:hover:text-gray-300",onClick:e=>{e.preventDefault(),e.stopPropagation(),h(!1),setTimeout(t,300)},"aria-label":n("close","Close"),children:s.jsx("svg",{className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})})})},h=(0,a.createContext)(void 0),useNotifications=()=>{let e=(0,a.useContext)(h);if(void 0===e)throw Error("useNotifications must be used within a NotificationProvider");return e},providers_NotificationProvider=({children:e,pollingInterval:t=3e4})=>{let[r,o]=(0,a.useState)([]),{data:n=0,refetch:l}=(0,i.Zr)(void 0,{pollingInterval:t}),d=(0,a.useCallback)(e=>{o(t=>[...t,e])},[]),c=(0,a.useCallback)(()=>{o([])},[]),u=(0,a.useCallback)(e=>{o(t=>t.filter(t=>t.id!==e))},[]);return(0,a.useEffect)(()=>{0===r.length&&l()},[r.length,l]),(0,s.jsxs)(h.Provider,{value:{unreadCount:n,showNotification:d,clearNotifications:c},children:[e,r.map(e=>s.jsx(notifications_NotificationToast,{notification:e,onClose:()=>u(e.id)},e.id))]})}},17560:(e,t,r)=>{"use strict";r.d(t,{ZP:()=>__WEBPACK_DEFAULT_EXPORT__,pm:()=>useToast});var s=r(30784),a=r(9885),i=r(42075);let o=(0,a.createContext)({showToast:()=>{}}),useToast=()=>(0,a.useContext)(o),__WEBPACK_DEFAULT_EXPORT__=({children:e})=>{let[t,r]=(0,a.useState)([]),n=(0,a.useCallback)((e,t="info",s=3e3)=>{let a=Math.random().toString(36).substring(2,9);r(r=>[...r,{id:a,message:e,type:t,duration:s}])},[]),l=(0,a.useCallback)(e=>{r(t=>t.filter(t=>t.id!==e))},[]);return(0,s.jsxs)(o.Provider,{value:{showToast:n},children:[e,s.jsx("div",{className:"toast-container",children:t.map(e=>s.jsx(i.Z,{message:e.message,type:e.type,duration:e.duration,onClose:()=>l(e.id)},e.id))})]})}},11483:(e,t,r)=>{"use strict";r.d(t,{ZP:()=>providers_WebSocketProvider,sO:()=>useWebSocket});var s=r(30784),a=r(9885),i=r(19923),o=r(86372),n=r(87771);let useAppDispatch=()=>(0,n.I0)(),l=(0,a.createContext)({isConnected:!1,lastMessage:null,sendMessage:()=>{}}),useWebSocket=()=>(0,a.useContext)(l),providers_WebSocketProvider=({children:e})=>{let[t,r]=(0,a.useState)(!1),[n,d]=(0,a.useState)(null),c=(0,a.useRef)(null),{data:u}=(0,i.Mx)(),h=useAppDispatch(),m=(0,a.useCallback)(()=>{if(!u?.id)return;c.current&&c.current.close();let e=new WebSocket(`ws://localhost:3001/ws?userId=${u.id}`);e.onopen=()=>{console.log("WebSocket connected"),r(!0)},e.onmessage=e=>{try{let t=JSON.parse(e.data);console.log("WebSocket message received:",t),d(t),"notification"===t.type&&handleNotification(t.payload)}catch(e){console.error("Error parsing WebSocket message:",e)}},e.onclose=()=>{console.log("WebSocket disconnected"),r(!1),setTimeout(()=>{u?.id&&m()},5e3)},e.onerror=t=>{console.error("WebSocket error:",t),e.close()},c.current=e},[u?.id]),handleNotification=e=>{h(o.g.util.invalidateTags(["Notifications"])),"Notification"in window&&"granted"===Notification.permission&&new Notification(e.title,{body:e.message,icon:"/logo.png"})};return(0,a.useEffect)(()=>(u?.id&&m(),()=>{c.current&&c.current.close()}),[u?.id,m]),(0,a.useEffect)(()=>{"Notification"in window&&"denied"!==Notification.permission&&Notification.requestPermission()},[]),s.jsx(l.Provider,{value:{isConnected:t,lastMessage:n,sendMessage:e=>{c.current&&t&&c.current.send(JSON.stringify(e))}},children:e})}},59872:(e,t,r)=>{"use strict";r.d(t,{Z:()=>__WEBPACK_DEFAULT_EXPORT__});var s=r(30784);r(9885);var a=r(14379);let __WEBPACK_DEFAULT_EXPORT__=({children:e,variant:t="primary",size:r="md",isLoading:i=!1,fullWidth:o=!1,className:n="",disabled:l,...d})=>{let{isRtl:c}=(0,a.g)();return(0,s.jsxs)("button",{className:`inline-flex items-center justify-center rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 ${{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500",secondary:"bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500",outline:"bg-transparent border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-primary-500 dark:border-gray-600 dark:text-gray-200 dark:hover:bg-gray-800",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500"}[t]} ${{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-base",lg:"px-6 py-3 text-lg"}[r]} ${o?"w-full":""} ${i?"opacity-70 cursor-not-allowed":""} ${n}`,disabled:l||i,...d,children:[i&&(0,s.jsxs)("svg",{className:`animate-spin ${c?"-mr-1 ml-2":"-ml-1 mr-2"} h-4 w-4 text-current`,xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[s.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),s.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),e]})}},42075:(e,t,r)=>{"use strict";r.d(t,{Z:()=>__WEBPACK_DEFAULT_EXPORT__});var s=r(30784),a=r(9885);let __WEBPACK_DEFAULT_EXPORT__=({message:e,type:t="info",duration:r=3e3,onClose:i,isVisible:o=!0})=>{let[n,l]=(0,a.useState)(o);return((0,a.useEffect)(()=>{l(o)},[o]),(0,a.useEffect)(()=>{if(n&&r>0){let e=setTimeout(()=>{l(!1),i?.()},r);return()=>clearTimeout(e)}},[n,r,i]),n)?s.jsx("div",{className:"fixed bottom-4 right-4 z-50 animate-fade-in",children:(0,s.jsxs)("div",{className:`${(()=>{switch(t){case"success":return"bg-green-500";case"error":return"bg-red-500";case"warning":return"bg-yellow-500";default:return"bg-blue-500"}})()} text-white px-4 py-3 rounded-lg shadow-lg flex items-center max-w-md`,children:[s.jsx("div",{className:"mr-3 flex-shrink-0",children:(()=>{switch(t){case"success":return s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})});case"error":return s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})});case"warning":return s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})});default:return s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}})()}),s.jsx("div",{className:"mr-2 flex-1",children:e}),s.jsx("button",{onClick:()=>{l(!1),i?.()},className:"flex-shrink-0 ml-2 text-white focus:outline-none","aria-label":"Close",children:s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:s.jsx("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]})}):null}},89915:(e,t,r)=>{"use strict";r.d(t,{FW:()=>getOptions,Fp:()=>isRtlLanguage});let s=["fa","ar","he"],isRtlLanguage=e=>s.includes(e);function getOptions(e="en",t="common"){return{supportedLngs:["en","fa"],fallbackLng:"en",lng:e,ns:t,defaultNS:"common",fallbackNS:"common",interpolation:{escapeValue:!1},react:{useSuspense:!1}}}},86372:(e,t,r)=>{"use strict";r.d(t,{g:()=>o});var s=r(31011),a=r(10813);let i=(0,s.fetchBaseQuery)({baseUrl:"http://localhost:3001",prepareHeaders:(e,{getState:t})=>{let r=t().auth.token;return r&&e.set("authorization",`Bearer ${r}`),e.has("Content-Type")||e.set("Content-Type","application/json"),e},credentials:"include"}),enhancedBaseQuery=async(e,t,r)=>{console.log("API Request:",{url:"string"==typeof e?e:e.url,method:e.method,body:e.body?"(body present)":"(no body)",headers:e.headers});let s=await i(e,t,r);return s.error?console.error("API Error:",{status:s.error.status,data:s.error.data,error:s.error}):console.log("API Success:",{url:"string"==typeof e?e:e.url,status:s.meta?.response?.status,hasData:!!s.data}),s},o=(0,a.createApi)({reducerPath:"api",baseQuery:enhancedBaseQuery,tagTypes:["User","Store","Product","Category","Variant","VariantOption","InventorySettings","InventoryLevel","InventoryHistory","Media","AttributeDefinition","AttributeValue","AttributeGroup","PriceTier","Discount","SEO","RelatedItem","RecommendationRule","Recommendation","Review","Reviews","ReviewSummary","ReviewReply","Export","ExportFields","ExportTemplates","ExportHistory","Import","ImportTemplates","ImportHistory","Analytics","Reports","ReportHistory","BulkActions","UserProfile","UserConnections","UserActivity","Feed","Posts","Comments","Reactions","Notifications","Connections","GroupBuying","GroupBuyingParticipants","UserGroupBuying","AffiliateSettings","AffiliatePrograms","AffiliateProgram","AffiliateAccounts","AffiliateAccount","AffiliateStats","AffiliateCommissions","AffiliatePayments","AffiliateReferrals","AffiliateLinks"],endpoints:e=>({})})},2280:(e,t,r)=>{"use strict";r.d(t,{$B:()=>l,_C:()=>o});var s=r(86372);let a=s.g.injectEndpoints({endpoints:e=>({getCart:e.query({query:()=>"/cart",providesTags:["Cart"]}),getCartItemCount:e.query({query:()=>"/cart/count",providesTags:["Cart"]}),getCartSummary:e.query({query:()=>"/cart/summary",providesTags:["Cart"]}),addToCart:e.mutation({query:e=>({url:"/cart/items",method:"POST",body:e}),invalidatesTags:["Cart"]}),updateCartItem:e.mutation({query:({cartItemId:e,...t})=>({url:`/cart/items/${e}`,method:"PUT",body:t}),invalidatesTags:["Cart"]}),removeFromCart:e.mutation({query:e=>({url:`/cart/items/${e}`,method:"DELETE"}),invalidatesTags:["Cart"]}),clearCart:e.mutation({query:()=>({url:"/cart",method:"DELETE"}),invalidatesTags:["Cart"]}),applyCoupon:e.mutation({query:e=>({url:"/cart/coupon",method:"POST",body:e}),invalidatesTags:["Cart"]}),removeCoupon:e.mutation({query:()=>({url:"/cart/coupon",method:"DELETE"}),invalidatesTags:["Cart"]})})}),{useGetCartQuery:i,useGetCartItemCountQuery:o,useGetCartSummaryQuery:n,useAddToCartMutation:l,useUpdateCartItemMutation:d,useRemoveFromCartMutation:c,useClearCartMutation:u,useApplyCouponMutation:h,useRemoveCouponMutation:m}=a},53717:(e,t,r)=>{"use strict";r.d(t,{H9:()=>u,JF:()=>l,Jm:()=>c,Zr:()=>o,j6:()=>n,mI:()=>i,mT:()=>h,sJ:()=>d});var s=r(86372);let a=s.g.injectEndpoints({endpoints:e=>({getNotifications:e.query({query:()=>"/notifications",providesTags:["Notifications"]}),getUnreadNotificationsCount:e.query({query:()=>"/notifications/unread/count",providesTags:["Notifications"]}),markNotificationAsRead:e.mutation({query:e=>({url:`/notifications/${e}/read`,method:"POST"}),invalidatesTags:["Notifications"]}),markAllNotificationsAsRead:e.mutation({query:()=>({url:"/notifications/read-all",method:"POST"}),invalidatesTags:["Notifications"]}),deleteNotification:e.mutation({query:e=>({url:`/notifications/${e}`,method:"DELETE"}),invalidatesTags:["Notifications"]}),deleteAllNotifications:e.mutation({query:()=>({url:"/notifications",method:"DELETE"}),invalidatesTags:["Notifications"]}),getNotificationSettings:e.query({query:()=>"/notifications/settings",providesTags:["NotificationSettings"]}),updateNotificationSettings:e.mutation({query:e=>({url:"/notifications/settings",method:"PUT",body:e}),invalidatesTags:["NotificationSettings"]})})}),{useGetNotificationsQuery:i,useGetUnreadNotificationsCountQuery:o,useMarkNotificationAsReadMutation:n,useMarkAllNotificationsAsReadMutation:l,useDeleteNotificationMutation:d,useDeleteAllNotificationsMutation:c,useGetNotificationSettingsQuery:u,useUpdateNotificationSettingsMutation:h}=a},92299:(e,t,r)=>{"use strict";r.d(t,{A3:()=>i,Rs:()=>c,TX:()=>d,Ve:()=>u,hi:()=>l,ve:()=>g,ye:()=>m});var s=r(86372);let a=s.g.injectEndpoints({endpoints:e=>({search:e.query({query:e=>`/search?q=${encodeURIComponent(e)}`}),searchProducts:e.query({query:e=>`/search/products?q=${encodeURIComponent(e)}`}),searchStores:e.query({query:e=>`/search/stores?q=${encodeURIComponent(e)}`}),searchUsers:e.query({query:e=>`/search/users?q=${encodeURIComponent(e)}`}),getPopularSearches:e.query({query:()=>"/search/popular"}),getRecentSearches:e.query({query:()=>"/search/recent"}),saveSearch:e.mutation({query:e=>({url:"/search/save",method:"POST",body:{query:e}})}),clearRecentSearches:e.mutation({query:()=>({url:"/search/recent",method:"DELETE"})}),getTrendingHashtags:e.query({query:(e=10)=>`/search/trending-hashtags?limit=${e}`,providesTags:["TrendingHashtags"]}),searchAutocomplete:e.query({query:e=>`/search/autocomplete?q=${encodeURIComponent(e)}&limit=5`,transformResponse:e=>{if(!e){let e=[];return e.push({id:"1",type:"product",title:`Product matching "${query}"`,description:"This is a product description",imageUrl:"https://via.placeholder.com/150",url:"/products/1",createdAt:new Date().toISOString(),price:99.99,discountPrice:79.99,rating:4.5,reviewsCount:10}),e.push({id:"2",type:"store",title:`Store with "${query}"`,description:"This is a store description",imageUrl:"https://via.placeholder.com/150",url:"/stores/2",createdAt:new Date().toISOString(),isVerified:!0,productsCount:42,followersCount:1200}),e.push({id:"3",type:"user",title:"John Doe",description:"User bio here",imageUrl:"https://via.placeholder.com/150",url:"/profile/johndoe",createdAt:new Date().toISOString(),username:"johndoe",displayName:"John Doe",isVerified:!1,followersCount:500}),e}return e}})})}),{useSearchQuery:i,useSearchProductsQuery:o,useSearchStoresQuery:n,useSearchUsersQuery:l,useGetPopularSearchesQuery:d,useGetRecentSearchesQuery:c,useSaveSearchMutation:u,useClearRecentSearchesMutation:h,useGetTrendingHashtagsQuery:m,useSearchAutocompleteQuery:g}=a},19923:(e,t,r)=>{"use strict";r.d(t,{$i:()=>p,Fb:()=>h,Mk:()=>g,Mx:()=>d,PF:()=>u,QC:()=>m,RR:()=>f,TG:()=>c,Xv:()=>v,YA:()=>o,_y:()=>l,gL:()=>y,l4:()=>n,qQ:()=>w});var s=r(86372),a=r(86490);let i=s.g.injectEndpoints({endpoints:e=>({login:e.mutation({query:e=>(console.log("Sending login request with credentials:",{usernameOrEmail:e.usernameOrEmail,hasPassword:!!e.password,passwordLength:e.password?.length}),{url:"/auth/login",method:"POST",body:e}),async onQueryStarted(e,{dispatch:t,queryFulfilled:r}){console.log("Login query started with:",{usernameOrEmail:e.usernameOrEmail,hasPassword:!!e.password});try{let e=await r;console.log("Login successful, received data:",{hasUser:!!e.data.user,hasToken:!!e.data.token,userId:e.data.user?.id,username:e.data.user?.username}),t((0,a.Dj)(e.data))}catch(e){console.error("Login failed:",e),console.error("Login error details:",{status:e.status,data:e.data,error:e.error,message:e.message,stack:e.stack})}}}),register:e.mutation({query:e=>({url:"/auth/register",method:"POST",body:e})}),logout:e.mutation({query:()=>({url:"/auth/logout",method:"POST"}),async onQueryStarted(e,{dispatch:t,queryFulfilled:r}){try{await r,t((0,a.kS)())}catch(e){console.error("Logout failed:",e)}}}),getProfile:e.query({query:()=>"/users/profile",providesTags:["User"]}),updateProfile:e.mutation({query:e=>({url:"/users/profile",method:"PATCH",body:e}),invalidatesTags:["User"]}),getUserByUsername:e.query({query:e=>`/users/${e}`,providesTags:(e,t,r)=>[{type:"UserProfile",id:r}]}),getFollowers:e.query({query:e=>`/users/${e}/followers`,providesTags:(e,t,r)=>[{type:"Followers",id:r}]}),getFollowing:e.query({query:e=>`/users/${e}/following`,providesTags:(e,t,r)=>[{type:"Following",id:r}]}),followUser:e.mutation({query:e=>({url:`/users/${e}/follow`,method:"POST"}),invalidatesTags:(e,t,r)=>[{type:"UserProfile",id:r},"User",{type:"Following",id:"me"},{type:"Followers",id:r}]}),unfollowUser:e.mutation({query:e=>({url:`/users/${e}/unfollow`,method:"POST"}),invalidatesTags:(e,t,r)=>[{type:"UserProfile",id:r},"User",{type:"Following",id:"me"},{type:"Followers",id:r}]}),searchUsers:e.query({query:e=>`/users/search?q=${encodeURIComponent(e)}`}),resetPassword:e.mutation({query:e=>({url:"/auth/reset-password",method:"POST",body:e})}),verifyResetToken:e.query({query:e=>`/auth/verify-reset-token/${e}`}),setNewPassword:e.mutation({query:e=>({url:"/auth/set-new-password",method:"POST",body:e})}),verifyEmail:e.mutation({query:e=>({url:"/auth/verify-email",method:"POST",body:e}),invalidatesTags:["User"]})})}),{useLoginMutation:o,useRegisterMutation:n,useLogoutMutation:l,useGetProfileQuery:d,useUpdateProfileMutation:c,useGetUserByUsernameQuery:u,useGetFollowersQuery:h,useGetFollowingQuery:m,useFollowUserMutation:g,useUnfollowUserMutation:p,useSearchUsersQuery:x,useResetPasswordMutation:y,useVerifyResetTokenQuery:f,useSetNewPasswordMutation:v,useVerifyEmailMutation:w}=i},86867:(e,t,r)=>{"use strict";r.d(t,{L$:()=>g,Qw:()=>p,TK:()=>y,_x:()=>o,aK:()=>l,cd:()=>n,dN:()=>m,lh:()=>d});var s=r(86372);let a=s.g.injectEndpoints({endpoints:e=>({getUserWishlists:e.query({query:()=>"/wishlists",providesTags:["Wishlists"]}),getWishlistById:e.query({query:e=>`/wishlists/${e}`,providesTags:(e,t,r)=>[{type:"Wishlist",id:r}]}),getWishlistItems:e.query({query:e=>`/wishlists/${e}/items`,providesTags:(e,t,r)=>[{type:"WishlistItems",id:r}]}),getDefaultWishlistItems:e.query({query:()=>"/wishlists/default/items",providesTags:["DefaultWishlistItems"]}),createWishlist:e.mutation({query:e=>({url:"/wishlists",method:"POST",body:e}),invalidatesTags:["Wishlists"]}),updateWishlist:e.mutation({query:({wishlistId:e,...t})=>({url:`/wishlists/${e}`,method:"PUT",body:t}),invalidatesTags:(e,t,{wishlistId:r})=>[{type:"Wishlist",id:r},"Wishlists"]}),deleteWishlist:e.mutation({query:e=>({url:`/wishlists/${e}`,method:"DELETE"}),invalidatesTags:["Wishlists"]}),addToWishlist:e.mutation({query:({wishlistId:e,productId:t})=>({url:`/wishlists/${e}/items`,method:"POST",body:{productId:t}}),invalidatesTags:(e,t,{wishlistId:r})=>[{type:"WishlistItems",id:r},{type:"Wishlist",id:r},"Wishlists",..."default"===r?["DefaultWishlistItems"]:[]]}),addToDefaultWishlist:e.mutation({query:e=>({url:"/wishlists/default/items",method:"POST",body:{productId:e}}),invalidatesTags:["DefaultWishlistItems","Wishlists"]}),removeFromWishlist:e.mutation({query:({wishlistId:e,itemId:t})=>({url:`/wishlists/${e}/items/${t}`,method:"DELETE"}),invalidatesTags:(e,t,{wishlistId:r})=>[{type:"WishlistItems",id:r},{type:"Wishlist",id:r},"Wishlists",..."default"===r?["DefaultWishlistItems"]:[]]}),removeFromDefaultWishlist:e.mutation({query:e=>({url:`/wishlists/default/items/${e}`,method:"DELETE"}),invalidatesTags:["DefaultWishlistItems","Wishlists"]}),isProductInWishlist:e.query({query:({wishlistId:e,productId:t})=>`/wishlists/${e}/items/check?productId=${t}`,providesTags:(e,t,{wishlistId:r,productId:s})=>[{type:"WishlistItem",id:`${r}-${s}`}]}),isProductInDefaultWishlist:e.query({query:e=>`/wishlists/default/items/check?productId=${e}`,providesTags:(e,t,r)=>[{type:"DefaultWishlistItem",id:r}]})})}),{useGetUserWishlistsQuery:i,useGetWishlistByIdQuery:o,useGetWishlistItemsQuery:n,useGetDefaultWishlistItemsQuery:l,useCreateWishlistMutation:d,useUpdateWishlistMutation:c,useDeleteWishlistMutation:u,useAddToWishlistMutation:h,useAddToDefaultWishlistMutation:m,useRemoveFromWishlistMutation:g,useRemoveFromDefaultWishlistMutation:p,useIsProductInWishlistQuery:x,useIsProductInDefaultWishlistQuery:y}=a},86490:(e,t,r)=>{"use strict";r.d(t,{Dj:()=>i,ZP:()=>n,kS:()=>o});var s=r(17914);let a=(0,s.oM)({name:"auth",initialState:{user:null,token:null,isAuthenticated:!1},reducers:{setCredentials:(e,t)=>{e.user=t.payload.user,e.token=t.payload.token,e.isAuthenticated=!0},logout:e=>{e.user=null,e.token=null,e.isAuthenticated=!1}}}),{setCredentials:i,logout:o}=a.actions,n=a.reducer},21233:(e,t,r)=>{"use strict";r.d(t,{$R:()=>o,LL:()=>l,ZP:()=>h,cl:()=>n});var s=r(17914);let a=(0,s.oM)({name:"cart",initialState:{items:[],isOpen:!1},reducers:{addItem:(e,t)=>{let r=e.items.findIndex(e=>e.productId===t.payload.productId&&(!e.variantId&&!t.payload.variantId||e.variantId===t.payload.variantId));r>=0?e.items[r].quantity+=t.payload.quantity:e.items.push(t.payload)},updateQuantity:(e,t)=>{let{id:r,quantity:s}=t.payload,a=e.items.findIndex(e=>e.id===r);a>=0&&(e.items[a].quantity=Math.max(1,s))},removeItem:(e,t)=>{e.items=e.items.filter(e=>e.id!==t.payload)},clearCart:e=>{e.items=[]},openCart:e=>{e.isOpen=!0},closeCart:e=>{e.isOpen=!1},toggleCart:e=>{e.isOpen=!e.isOpen}}}),{addItem:i,updateQuantity:o,removeItem:n,clearCart:l,openCart:d,closeCart:c,toggleCart:u}=a.actions,h=a.reducer},34087:(e,t,r)=>{"use strict";var s,a;r.d(t,{i:()=>a,p:()=>s}),function(e){e.SHIPPING="shipping",e.PAYMENT="payment",e.REVIEW="review",e.CONFIRMATION="confirmation"}(s||(s={})),function(e){e.PENDING="pending",e.PROCESSING="processing",e.SHIPPED="shipped",e.DELIVERED="delivered",e.CANCELLED="cancelled",e.REFUNDED="refunded"}(a||(a={}))},13210:(e,t,r)=>{"use strict";var s;r.d(t,{k:()=>s}),function(e){e.FOLLOW="follow",e.LIKE="like",e.COMMENT="comment",e.MENTION="mention",e.ORDER="order",e.PAYMENT="payment",e.SYSTEM="system"}(s||(s={}))},36303:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>RootLayout,metadata:()=>v});var s=r(4656),a=r(40632),i=r.n(a),o=r(28769),n=r.n(o);r(5023);var l=r(95153);let d=(0,l.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\providers.tsx`),{__esModule:c,$$typeof:u}=d;d.default;let h=(0,l.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\providers.tsx#Providers`),m=(0,l.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\components\layout\Layout.tsx`),{__esModule:g,$$typeof:p}=m,x=m.default;var y=r(47420),f=r(94386);let v={title:"Social Commerce Platform",description:"A social-centric e-commerce platform"};function RootLayout({children:e}){let t=(0,y.G)(),r=(0,f.Fp)(t)?"rtl":"ltr";return s.jsx("html",{lang:t,dir:r,children:s.jsx("body",{className:`${i().variable} ${n().variable} font-sans`,children:s.jsx(h,{children:s.jsx(x,{children:e})})})})}},47420:(e,t,r)=>{"use strict";r.d(t,{G:()=>getLanguage,i:()=>getTranslation});var s=r(41234),a=r(73685),i=r(34440),o=r(94386),n=r(24596);let initI18next=async(e,t)=>{let n=(0,s.Fs)();return await n.use(i.D).use((0,a.Z)((e,t)=>r(1014)(`./${e}/${t}.json`))).init({...(0,o.FW)(),lng:e,ns:t}),n};async function getTranslation(e,t="common",r={}){let s=await initI18next(e,t);return{t:s.getFixedT(e,t,r),i18n:s}}function getLanguage(){let e=(0,n.cookies)(),t=e.get("i18next")?.value||"en";return t}},94386:(e,t,r)=>{"use strict";r.d(t,{FW:()=>getOptions,Fp:()=>isRtlLanguage});let s=["fa","ar","he"],isRtlLanguage=e=>s.includes(e);function getOptions(e="en",t="common"){return{supportedLngs:["en","fa"],fallbackLng:"en",lng:e,ns:t,defaultNS:"common",fallbackNS:"common",interpolation:{escapeValue:!1},react:{useSuspense:!1}}}},5023:()=>{}};