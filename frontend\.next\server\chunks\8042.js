"use strict";exports.id=8042,exports.ids=[8042],exports.modules={48042:(e,t,r)=>{r.d(t,{Au:()=>q,C$:()=>s,Mq:()=>y,My:()=>p,VJ:()=>u,p8:()=>g,qX:()=>a,tf:()=>m,vL:()=>i,wE:()=>c});var o=r(86372);let d=o.g.injectEndpoints({endpoints:e=>({getProducts:e.query({query:()=>"/products",providesTags:["Product"]}),getProductById:e.query({query:e=>`/products/${e}`,providesTags:["Product"]}),getProductsByStore:e.query({query:e=>`/products/store/${e}`,providesTags:["Product"]}),createProduct:e.mutation({query:e=>({url:"/products",method:"POST",body:e}),invalidatesTags:["Product"]}),updateProduct:e.mutation({query:({id:e,data:t})=>({url:`/products/${e}`,method:"PATCH",body:t}),invalidatesTags:["Product"]}),deleteProduct:e.mutation({query:e=>({url:`/products/${e}`,method:"DELETE"}),invalidatesTags:["Product"]}),likeProduct:e.mutation({query:e=>({url:`/products/${e}/like`,method:"POST"}),invalidatesTags:["Product"]}),unlikeProduct:e.mutation({query:e=>({url:`/products/${e}/unlike`,method:"POST"}),invalidatesTags:["Product"]}),searchProducts:e.query({query:e=>`/products/search?q=${encodeURIComponent(e)}`,providesTags:["Product"]}),getProductsByCategory:e.query({query:e=>`/products/category/${e}`,providesTags:["Product"]}),getProductsByTag:e.query({query:e=>`/products/tag/${encodeURIComponent(e)}`,providesTags:["Product"]}),getProductsAdvanced:e.query({query:e=>{let t=new URLSearchParams;return e.categoryIds?.length&&e.categoryIds.forEach(e=>t.append("categoryId",e)),e.tags?.length&&e.tags.forEach(e=>t.append("tag",e)),void 0!==e.minPrice&&t.append("minPrice",e.minPrice.toString()),void 0!==e.maxPrice&&t.append("maxPrice",e.maxPrice.toString()),e.sortBy&&t.append("sortBy",e.sortBy),e.sortOrder&&t.append("sortOrder",e.sortOrder),void 0!==e.page&&t.append("page",e.page.toString()),void 0!==e.limit&&t.append("limit",e.limit.toString()),`/products/advanced?${t.toString()}`},providesTags:["Product"]}),getPersonalizedRecommendations:e.query({query:({limit:e=10})=>`/recommendations/personalized?limit=${e}`,providesTags:["Recommendation"],transformResponse:e=>{if(!e){let e=[];for(let t=1;t<=10;t++)e.push({id:`mock-product-${t}`,storeId:`mock-store-${t%3+1}`,title:`Recommended Product ${t}`,description:`This is a mock recommended product ${t} for development purposes.`,price:19.99+10*t,mediaUrls:[`https://via.placeholder.com/300?text=Product+${t}`],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),isActive:!0});return e}return e}}),getTrendingProducts:e.query({query:({limit:e=10,categoryId:t})=>{let r=`/products/trending?limit=${e}`;return t&&(r+=`&categoryId=${t}`),r},providesTags:["Product"],transformResponse:e=>{if(!e){let e=[];for(let t=1;t<=10;t++)e.push({id:`mock-trending-${t}`,storeId:`mock-store-${t%3+1}`,title:`Trending Product ${t}`,description:`This is a mock trending product ${t} for development purposes.`,price:29.99+5*t,mediaUrls:[`https://via.placeholder.com/300?text=Trending+${t}`],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),isActive:!0});return e}return e}}),getFeaturedProducts:e.query({query:({limit:e=10})=>`/products/featured?limit=${e}`,providesTags:["Product"],transformResponse:e=>{if(!e){let e=[];for(let t=1;t<=10;t++)e.push({id:`mock-featured-${t}`,storeId:`mock-store-${t%3+1}`,title:`Featured Product ${t}`,description:`This is a mock featured product ${t} for development purposes.`,price:39.99+15*t,mediaUrls:[`https://via.placeholder.com/300?text=Featured+${t}`],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),isActive:!0});return e}return e}})})}),{useGetProductsQuery:s,useGetProductByIdQuery:i,useGetProductsByStoreQuery:u,useCreateProductMutation:a,useUpdateProductMutation:c,useDeleteProductMutation:n,useLikeProductMutation:p,useUnlikeProductMutation:g,useSearchProductsQuery:m,useGetProductsByCategoryQuery:l,useGetProductsByTagQuery:P,useGetProductsAdvancedQuery:y,useGetPersonalizedRecommendationsQuery:v,useGetTrendingProductsQuery:$,useGetFeaturedProductsQuery:q}=d}};