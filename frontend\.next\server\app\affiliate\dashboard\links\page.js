(()=>{var e={};e.id=5079,e.ids=[5079],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},82946:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>g,tree:()=>c});var r=t(67096),s=t(16132),i=t(37284),l=t.n(i),n=t(32564),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(a,d);let c=["",{children:["affiliate",{children:["dashboard",{children:["links",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,30472)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\affiliate\\dashboard\\links\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9291,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\affiliate\\dashboard\\links\\page.tsx"],x="/affiliate/dashboard/links/page",m={require:t,loadChunk:()=>Promise.resolve()},g=new r.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/affiliate/dashboard/links/page",pathname:"/affiliate/dashboard/links",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},26406:(e,a,t)=>{Promise.resolve().then(t.bind(t,87509))},87509:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>AffiliateLinksPage});var r=t(30784),s=t(9885),i=t(27870),l=t(11440),n=t.n(l),d=t(59872),c=t(94820),o=t(14379),x=t(56663);let affiliate_AffiliateLinksTable=({links:e,isLoading:a=!1,onCopyLink:t,onDelete:s,isDeleting:l=!1,className:n=""})=>{let{t:c}=(0,i.$G)("affiliate"),{isRtl:m}=(0,o.g)();return a?r.jsx("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden ${n}`,children:(0,r.jsxs)("div",{className:"animate-pulse",children:[r.jsx("div",{className:"h-12 bg-gray-100 dark:bg-gray-700"}),Array.from({length:3}).map((e,a)=>r.jsx("div",{className:"h-16 border-t border-gray-200 dark:border-gray-700 px-4 py-3",children:(0,r.jsxs)("div",{className:"grid grid-cols-6 gap-4",children:[r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-600 rounded col-span-2"}),r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-600 rounded"}),r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-600 rounded"}),r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-600 rounded"}),r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-600 rounded"})]})},a))]})}):0===e.length?(0,r.jsxs)("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 text-center ${n}`,children:[r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-12 w-12 text-gray-400 dark:text-gray-600 mx-auto mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"})}),r.jsx("p",{className:"text-gray-500 dark:text-gray-400 mb-4",children:c("links.noLinks","No affiliate links found")})]}):r.jsx("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden ${n}`,children:r.jsx("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[r.jsx("thead",{className:"bg-gray-50 dark:bg-gray-900",children:(0,r.jsxs)("tr",{children:[r.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:c("links.product","Product")}),r.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:c("links.code","Code")}),r.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:c("links.clicks","Clicks")}),r.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:c("links.conversions","Conversions")}),r.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:c("links.earnings","Earnings")}),r.jsx("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:c("links.actions","Actions")})]})}),r.jsx("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:e.map(e=>(0,r.jsxs)("tr",{children:[r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100",children:e.productId}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:e.code}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:e.clicks}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:e.conversions}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-green-600 dark:text-green-400",children:(0,x.xG)(e.earnings)}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right",children:(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[r.jsx(d.Z,{variant:"outline",size:"sm",onClick:()=>t?.(`${window.location.origin}/ref/${e.code}`),children:c("links.copyLink","Copy")}),r.jsx(d.Z,{variant:"outline",size:"sm",className:"text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300",onClick:()=>s?.(e.id),isLoading:l,disabled:l,children:c("common.delete","Delete")})]})})]},e.id))})]})})})};var m=t(706),g=t(71942),h=t(3619);let affiliate_AffiliateLinkGenerator=({affiliateAccounts:e,onSuccess:a})=>{let{t}=(0,i.$G)("affiliate"),[l,n]=(0,s.useState)(""),[c,o]=(0,s.useState)(e[0]?.id||""),[x,p]=(0,s.useState)(""),[u,k]=(0,s.useState)(!1),[f,{isLoading:y}]=(0,h.b)(),handleSubmit=async e=>{if(e.preventDefault(),l&&c)try{let e=await f({accountId:c,targetUrl:l,name:x||void 0,customSlug:u?x:void 0}).unwrap();n(""),p(""),a?.(e.id)}catch(e){console.error("Failed to create affiliate link:",e)}};return(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6",children:[r.jsx("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:t("links.createNew","Create New Affiliate Link")}),r.jsx("form",{onSubmit:handleSubmit,children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"url",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[t("links.targetUrl","Target URL")," ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx(m.Z,{id:"url",type:"url",value:l,onChange:e=>n(e.target.value),placeholder:"https://example.com/products/123",required:!0}),r.jsx("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:t("links.targetUrlHelp","Enter the URL you want to create an affiliate link for")})]}),e.length>1&&(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"accountId",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[t("links.affiliateAccount","Affiliate Account")," ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx(g.Z,{id:"accountId",value:c,onChange:e=>o(e.target.value),required:!0,children:e.map(e=>r.jsx("option",{value:e.id,children:e.program.name},e.id))})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:t("links.name","Link Name")}),r.jsx(m.Z,{id:"name",value:x,onChange:e=>p(e.target.value),placeholder:t("links.namePlaceholder","Summer Campaign")}),r.jsx("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:t("links.nameHelp","Optional name to help you identify this link")})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("input",{id:"customUrl",type:"checkbox",checked:u,onChange:e=>k(e.target.checked),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),r.jsx("label",{htmlFor:"customUrl",className:"ml-2 block text-sm text-gray-700 dark:text-gray-300",children:t("links.useCustomUrl","Use custom URL slug")})]}),r.jsx("div",{className:"pt-2",children:r.jsx(d.Z,{type:"submit",isLoading:y,disabled:y||!l||!c,fullWidth:!0,children:t("links.createLink","Create Affiliate Link")})})]})})]})};function AffiliateLinksPage(){let{t:e}=(0,i.$G)("affiliate"),[a,t]=(0,s.useState)(!1),{data:l}=(0,h.nK)({}),o=l?.accounts?.[0],{data:x,isLoading:m,refetch:g}=(0,h.YR)(void 0,{skip:!o}),[p,{isLoading:u}]=(0,h.ro)(),handleDeleteLink=async e=>{try{await p(e).unwrap(),g(),console.log("Affiliate link deleted successfully")}catch(e){console.error("Failed to delete affiliate link:",e)}};return r.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2",children:e("links.title","Affiliate Links")}),r.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:e("links.description","Create and manage your affiliate links")})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[r.jsx("div",{className:"lg:col-span-1",children:r.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4",children:r.jsx(c.Z,{})})}),r.jsx("div",{className:"lg:col-span-3",children:o?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[r.jsx("h2",{className:"text-xl font-bold text-gray-900 dark:text-gray-100",children:e("links.yourLinks","Your Affiliate Links")}),r.jsx(d.Z,{onClick:()=>t(!a),variant:a?"outline":"primary",children:a?e("common.cancel","Cancel"):e("links.createLink","Create Link")})]}),a&&r.jsx("div",{className:"mb-6",children:r.jsx(affiliate_AffiliateLinkGenerator,{affiliateAccounts:l?.accounts||[],onSuccess:()=>{g(),t(!1)}})}),r.jsx(affiliate_AffiliateLinksTable,{links:x||[],isLoading:m,onCopyLink:e=>{navigator.clipboard.writeText(e),console.log("Link copied to clipboard")},onDelete:handleDeleteLink,isDeleting:u})]}):r.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6",children:(0,r.jsxs)("div",{className:"text-center py-8",children:[r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-16 w-16 text-gray-400 dark:text-gray-600 mx-auto mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"})}),r.jsx("h3",{className:"text-xl font-medium text-gray-900 dark:text-gray-100 mb-2",children:e("noAffiliateAccount","No Affiliate Account")}),r.jsx("p",{className:"text-gray-500 dark:text-gray-400 mb-6 max-w-md mx-auto",children:e("joinProgramFirst","Join an affiliate program to create and manage affiliate links")}),r.jsx(n(),{href:"/affiliate/programs",children:r.jsx(d.Z,{variant:"primary",children:e("browsePrograms","Browse Programs")})})]})})})]})]})})}},30472:(e,a,t)=>{"use strict";t.r(a),t.d(a,{$$typeof:()=>l,__esModule:()=>i,default:()=>d});var r=t(95153);let s=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\affiliate\dashboard\links\page.tsx`),{__esModule:i,$$typeof:l}=s,n=s.default,d=n}};var a=require("../../../../webpack-runtime.js");a.C(e);var __webpack_exec__=e=>a(a.s=e),t=a.X(0,[2103,2765,706,3619,9522,6663,1942],()=>__webpack_exec__(82946));module.exports=t})();