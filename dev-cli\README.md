# Social Commerce Development CLI

A command-line tool to simplify development workflow for the Social Commerce Platform.

## Installation

1. Navigate to the `dev-cli` directory:
   ```bash
   cd dev-cli
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Link the CLI tool globally:
   ```bash
   npm link
   ```

## Usage

Once installed, you can use the `dev` command from anywhere in your terminal:

```bash
dev --help
```

### Available Commands

#### Start Services

Start all or specific services:

```bash
# Start all services
dev start

# Start specific service
dev start user
dev start main
dev start frontend

# Start in detached mode
dev start --detached
```

#### Stop Services

Stop all or specific services:

```bash
# Stop all services
dev stop

# Stop specific service
dev stop user
dev stop main
dev stop frontend
```

#### Check Service Status

Check the status of all services:

```bash
dev status
```

#### Install Dependencies

Install dependencies for all or specific services:

```bash
# Install all dependencies
dev install

# Install specific service dependencies
dev install backend
dev install frontend

# Clean install (remove node_modules first)
dev install --clean
```

#### Build Services

Build all or specific services:

```bash
# Build all services
dev build

# Build specific service
dev build backend
dev build frontend
```

## Troubleshooting

If you encounter any issues with the CLI tool, try the following:

1. Make sure you have the latest version of Node.js and npm installed.
2. Try unlinking and relinking the CLI tool:
   ```bash
   npm unlink -g dev-cli
   cd dev-cli
   npm link
   ```
3. Check the logs for any errors.

## Development

To modify the CLI tool, edit the files in the `commands` directory. Each command is defined in its own file.

To add a new command, create a new file in the `commands` directory and update the `index.js` file to include it.
