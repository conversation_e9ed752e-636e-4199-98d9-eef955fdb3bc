(()=>{var e={};e.id=6074,e.ids=[6074],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},67056:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var t=r(67096),a=r(16132),n=r(37284),l=r.n(n),i=r(32564),o={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(s,o);let d=["",{children:["stores",{children:["[username]",{children:["products",{children:["[productId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,84413)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\stores\\[username]\\products\\[productId]\\page.tsx"]}]},{}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9291,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\stores\\[username]\\products\\[productId]\\page.tsx"],m="/stores/[username]/products/[productId]/page",u={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/stores/[username]/products/[productId]/page",pathname:"/stores/[username]/products/[productId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},13511:(e,s,r)=>{Promise.resolve().then(r.bind(r,95982))},95982:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>ProductPage});var t=r(30784),a=r(9885),n=r(52451),l=r.n(n),i=r(57114),o=r(59872),d=r(77783),c=r(48042),m=r(34952);function ProductPage({params:e}){var s;let{username:r,productId:n}=e,u=(0,i.useRouter)(),[x,p]=(0,a.useState)(0),{data:h,isLoading:g}=(0,d.YU)(r),{data:f,isLoading:v}=(0,c.vL)(n,{skip:!h}),[j]=(0,c.My)(),[N]=(0,c.p8)(),handleLikeToggle=async()=>{if(f)try{await j(f.id).unwrap()}catch(e){console.error("Failed to like/unlike product:",e)}};return g||v?t.jsx("div",{className:"min-h-screen p-6 md:p-12",children:(0,t.jsxs)("div",{className:"max-w-6xl mx-auto text-center",children:[t.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600 mx-auto"}),t.jsx("p",{className:"mt-4 text-lg",children:"Loading product..."})]})}):h&&f?t.jsx("div",{className:"min-h-screen p-6 md:p-12",children:(0,t.jsxs)("div",{className:"max-w-6xl mx-auto",children:[t.jsx("div",{className:"mb-6",children:(0,t.jsxs)("button",{onClick:()=>u.push(`/stores/${r}`),className:"text-primary-600 hover:text-primary-700 flex items-center",children:[t.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-1",viewBox:"0 0 20 20",fill:"currentColor",children:t.jsx("path",{fillRule:"evenodd",d:"M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z",clipRule:"evenodd"})}),"Back to ",h.displayName||h.username]})}),t.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden",children:(0,t.jsxs)("div",{className:"md:flex",children:[(0,t.jsxs)("div",{className:"md:w-1/2",children:[(0,t.jsxs)("div",{className:"relative h-64 md:h-96 bg-gray-200 dark:bg-gray-700",children:[f.mediaUrls&&f.mediaUrls.length>0?t.jsx(l(),{src:f.mediaUrls[x],alt:f.title,fill:!0,className:"object-contain"}):t.jsx("div",{className:"flex items-center justify-center h-full text-gray-400",children:"No Image Available"}),f.postType===m.h.GROUP_BUY&&t.jsx("div",{className:"absolute top-4 left-4 bg-secondary-600 text-white text-sm font-bold px-3 py-1 rounded-full",children:"Group Buy"})]}),f.mediaUrls&&f.mediaUrls.length>1&&t.jsx("div",{className:"flex p-2 overflow-x-auto",children:f.mediaUrls.map((e,s)=>t.jsx("button",{onClick:()=>p(s),className:`relative w-16 h-16 mr-2 border-2 rounded ${x===s?"border-primary-500":"border-transparent"}`,children:t.jsx(l(),{src:e,alt:`Thumbnail ${s+1}`,fill:!0,className:"object-cover rounded"})},s))})]}),(0,t.jsxs)("div",{className:"md:w-1/2 p-6",children:[t.jsx("h1",{className:"text-2xl md:text-3xl font-bold",children:f.title}),t.jsx("div",{className:"mt-4",children:t.jsx("p",{className:"text-2xl font-bold text-primary-600",children:void 0===(s=f.price)?"Contact for price":new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(s)})}),f.description&&(0,t.jsxs)("div",{className:"mt-6",children:[t.jsx("h2",{className:"text-lg font-semibold mb-2",children:"Description"}),t.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:f.description})]}),(0,t.jsxs)("div",{className:"mt-8 flex flex-wrap gap-4",children:[t.jsx(o.Z,{onClick:()=>{alert("Added to cart!")},fullWidth:!0,children:"Add to Cart"}),(0,t.jsxs)(o.Z,{variant:"outline",onClick:handleLikeToggle,fullWidth:!0,children:["Like"," (",f.likeCount,")"]})]}),(0,t.jsxs)("div",{className:"mt-6 flex items-center text-sm text-gray-500 dark:text-gray-400",children:[(0,t.jsxs)("span",{className:"mr-4",children:[t.jsx("span",{className:"font-medium",children:f.likeCount})," likes"]}),(0,t.jsxs)("span",{children:[t.jsx("span",{className:"font-medium",children:f.commentCount})," comments"]})]})]})]})})]})}):t.jsx("div",{className:"min-h-screen p-6 md:p-12",children:(0,t.jsxs)("div",{className:"max-w-6xl mx-auto text-center",children:[t.jsx("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:"Product Not Found"}),t.jsx("p",{className:"mb-6",children:"The product you're looking for doesn't exist."}),t.jsx(o.Z,{onClick:()=>u.push(`/stores/${r}`),children:"Back to Store"})]})})}},34952:(e,s,r)=>{"use strict";var t,a;r.d(s,{Y:()=>a,h:()=>t}),function(e){e.REGULAR="REGULAR",e.GROUP_BUY="GROUP_BUY"}(t||(t={})),function(e){e.FIXED="FIXED",e.PERCENTAGE="PERCENTAGE"}(a||(a={}))},84413:(e,s,r)=>{"use strict";r.r(s),r.d(s,{$$typeof:()=>l,__esModule:()=>n,default:()=>o});var t=r(95153);let a=(0,t.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\stores\[username]\products\[productId]\page.tsx`),{__esModule:n,$$typeof:l}=a,i=a.default,o=i}};var s=require("../../../../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),r=s.X(0,[2103,2765,8042,7783],()=>__webpack_exec__(67056));module.exports=r})();