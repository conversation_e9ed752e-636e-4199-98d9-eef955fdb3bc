"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createPaginationResponse = createPaginationResponse;
exports.calculateSkip = calculateSkip;
const pagination_response_dto_1 = require("../dto/pagination-response.dto");
function createPaginationResponse(items, total, paginationDto) {
    const { page = 1, limit = 10 } = paginationDto;
    return new pagination_response_dto_1.PaginationResponseDto(items, total, page, limit);
}
function calculateSkip(paginationDto) {
    const { page = 1, limit = 10 } = paginationDto;
    return (page - 1) * limit;
}
//# sourceMappingURL=pagination.util.js.map