"use strict";exports.id=2022,exports.ids=[2022],exports.modules={74109:(e,i,t)=>{t.d(i,{Z:()=>__WEBPACK_DEFAULT_EXPORT__});var r=t(30784),s=t(9885),a=t.n(s);let __WEBPACK_DEFAULT_EXPORT__=({rating:e,maxRating:i=5,size:t="md",color:s="text-yellow-400",interactive:l=!1,onChange:d,className:v=""})=>{let[u,o]=a().useState(0),y={sm:"h-4 w-4",md:"h-5 w-5",lg:"h-6 w-6"},handleClick=e=>{l&&d&&d(e)},handleMouseEnter=e=>{l&&o(e)},handleMouseLeave=()=>{l&&o(0)},n=u>0?u:e;return r.jsx("div",{className:`flex ${v}`,children:[...Array(i)].map((e,i)=>{let a=i+1,d=a<=n,v=!d&&a-.5<=n;return r.jsx("span",{className:`${l?"cursor-pointer":""}`,onClick:()=>handleClick(a),onMouseEnter:()=>handleMouseEnter(a),onMouseLeave:handleMouseLeave,children:d?r.jsx("svg",{className:`${y[t]} ${s}`,fill:"currentColor",viewBox:"0 0 20 20",children:r.jsx("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})}):v?(0,r.jsxs)("div",{className:"relative",children:[r.jsx("svg",{className:`${y[t]} text-gray-300 dark:text-gray-600`,fill:"currentColor",viewBox:"0 0 20 20",children:r.jsx("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})}),r.jsx("div",{className:"absolute inset-0 overflow-hidden w-1/2",children:r.jsx("svg",{className:`${y[t]} ${s}`,fill:"currentColor",viewBox:"0 0 20 20",children:r.jsx("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})})})]}):r.jsx("svg",{className:`${y[t]} text-gray-300 dark:text-gray-600`,fill:"currentColor",viewBox:"0 0 20 20",children:r.jsx("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})})},i)})})}},46929:(e,i,t)=>{t.d(i,{C2:()=>d,EK:()=>n,F3:()=>u,VV:()=>l,Yk:()=>w,Z2:()=>a,hI:()=>y});var r=t(86372);let s=r.g.injectEndpoints({endpoints:e=>({getProductReviews:e.query({query:({productId:e,page:i=1,limit:t=10,filter:r})=>{let s=`/products/${e}/reviews?page=${i}&limit=${t}`;return r&&(void 0!==r.minRating&&(s+=`&minRating=${r.minRating}`),void 0!==r.maxRating&&(s+=`&maxRating=${r.maxRating}`),r.verifiedPurchaseOnly&&(s+="&verifiedPurchaseOnly=true"),r.withImagesOnly&&(s+="&withImagesOnly=true"),r.sortBy&&(s+=`&sortBy=${r.sortBy}`)),s},providesTags:(e,i,{productId:t})=>[{type:"Reviews",id:t},...e?.items?e.items.map(e=>({type:"Review",id:e.id})):[]]}),getReviewSummary:e.query({query:e=>`/products/${e}/reviews/summary`,providesTags:(e,i,t)=>[{type:"ReviewSummary",id:t}]}),getUserReviews:e.query({query:()=>"/users/me/reviews",providesTags:["UserReviews"]}),getReviewById:e.query({query:e=>`/reviews/${e}`,providesTags:(e,i,t)=>[{type:"Review",id:t}]}),createReview:e.mutation({query:e=>({url:"/reviews",method:"POST",body:e}),invalidatesTags:(e,i,{productId:t})=>[{type:"Reviews",id:t},{type:"ReviewSummary",id:t},"UserReviews"]}),updateReview:e.mutation({query:({reviewId:e,...i})=>({url:`/reviews/${e}`,method:"PUT",body:i}),invalidatesTags:(e,i,{reviewId:t})=>[{type:"Review",id:t},...e?[{type:"Reviews",id:e.productId},{type:"ReviewSummary",id:e.productId}]:[],"UserReviews"]}),deleteReview:e.mutation({query:e=>({url:`/reviews/${e}`,method:"DELETE"}),invalidatesTags:(e,i,t)=>[{type:"Review",id:t},"UserReviews"]}),markReviewHelpful:e.mutation({query:e=>({url:`/reviews/${e}/helpful`,method:"POST"}),invalidatesTags:(e,i,t)=>[{type:"Review",id:t}]}),removeReviewHelpful:e.mutation({query:e=>({url:`/reviews/${e}/helpful`,method:"DELETE"}),invalidatesTags:(e,i,t)=>[{type:"Review",id:t}]}),voteReview:e.mutation({query:e=>({url:`/reviews/${e.reviewId}/vote`,method:"POST",body:{isHelpful:e.isHelpful}}),invalidatesTags:(e,i,{reviewId:t})=>[{type:"Review",id:t}]}),getReviewReplies:e.query({query:e=>`/reviews/${e}/replies`,providesTags:(e,i,t)=>[{type:"ReviewReply",id:t},...e?e.map(e=>({type:"ReviewReply",id:e.id})):[]]}),createReviewReply:e.mutation({query:e=>({url:`/reviews/${e.reviewId}/replies`,method:"POST",body:{content:e.content}}),invalidatesTags:(e,i,{reviewId:t})=>[{type:"ReviewReply",id:t},{type:"Review",id:t}]}),updateReviewReply:e.mutation({query:({replyId:e,...i})=>({url:`/reviews/replies/${e}`,method:"PATCH",body:i}),invalidatesTags:(e,i,{replyId:t})=>[{type:"ReviewReply",id:t},{type:"ReviewReply",id:e?.reviewId},{type:"Review",id:e?.reviewId}]}),deleteReviewReply:e.mutation({query:e=>({url:`/reviews/replies/${e}`,method:"DELETE"}),invalidatesTags:(e,i,t)=>[{type:"ReviewReply",id:t}]})})}),{useGetProductReviewsQuery:a,useGetReviewSummaryQuery:l,useGetUserReviewsQuery:d,useGetReviewByIdQuery:v,useCreateReviewMutation:u,useUpdateReviewMutation:o,useDeleteReviewMutation:y,useMarkReviewHelpfulMutation:n,useRemoveReviewHelpfulMutation:w,useVoteReviewMutation:p,useGetReviewRepliesQuery:c,useCreateReviewReplyMutation:m,useUpdateReviewReplyMutation:R,useDeleteReviewReplyMutation:h}=s}};