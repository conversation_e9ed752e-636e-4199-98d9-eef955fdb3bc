import React from 'react';
import {
  Box,
  Flex,
  Text,
  Stack,
  Avatar,
  useColorModeValue,
  Heading,
  Badge,
} from '@chakra-ui/react';

interface ActivityItem {
  id: string;
  type: 'order' | 'store' | 'product' | 'review';
  title: string;
  description: string;
  time: string;
  status?: 'success' | 'pending' | 'error';
}

interface RecentActivityProps {
  activities: ActivityItem[];
}

const RecentActivity = ({ activities }: RecentActivityProps) => {
  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'success':
        return 'green';
      case 'pending':
        return 'orange';
      case 'error':
        return 'red';
      default:
        return 'gray';
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'order':
        return '🛒';
      case 'store':
        return '🏪';
      case 'product':
        return '📦';
      case 'review':
        return '⭐';
      default:
        return '📝';
    }
  };

  return (
    <Box
      bg={useColorModeValue('white', 'gray.700')}
      borderRadius="lg"
      shadow="md"
      borderWidth="1px"
      p={5}
    >
      <Heading size="md" mb={4}>
        Recent Activity
      </Heading>
      
      <Stack spacing={4}>
        {activities.length === 0 ? (
          <Text color="gray.500">No recent activity</Text>
        ) : (
          activities.map((activity) => (
            <Flex key={activity.id} alignItems="center" p={2} borderRadius="md" _hover={{ bg: 'gray.50' }}>
              <Box mr={4} fontSize="xl">
                {getActivityIcon(activity.type)}
              </Box>
              
              <Box flex="1">
                <Flex justifyContent="space-between" alignItems="center">
                  <Text fontWeight="bold">{activity.title}</Text>
                  <Text fontSize="sm" color="gray.500">
                    {activity.time}
                  </Text>
                </Flex>
                
                <Text fontSize="sm" color="gray.600">
                  {activity.description}
                </Text>
                
                {activity.status && (
                  <Badge colorScheme={getStatusColor(activity.status)} mt={1}>
                    {activity.status}
                  </Badge>
                )}
              </Box>
            </Flex>
          ))
        )}
      </Stack>
    </Box>
  );
};

export default RecentActivity;
