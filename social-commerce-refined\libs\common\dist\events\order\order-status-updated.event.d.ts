import { BaseEvent } from '../base-event.interface';
export declare class OrderStatusUpdatedEvent implements BaseEvent<OrderStatusUpdatedPayload> {
    id: string;
    type: string;
    version: string;
    timestamp: string;
    producer: string;
    payload: OrderStatusUpdatedPayload;
    constructor(payload: OrderStatusUpdatedPayload);
}
export interface OrderStatusUpdatedPayload {
    id: string;
    userId: string;
    previousStatus: string;
    newStatus: string;
    updatedAt: string;
}
