(()=>{var e={};e.id=4456,e.ids=[4456],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},76212:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=t(67096),i=t(16132),a=t(37284),n=t.n(a),o=t(32564),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(s,l);let c=["",{children:["wishlist",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,70121)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\wishlist\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9291,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\wishlist\\page.tsx"],u="/wishlist/page",p={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/wishlist/page",pathname:"/wishlist",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},51243:(e,s,t)=>{Promise.resolve().then(t.bind(t,20079))},20079:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>WishlistPage});var r=t(30784);t(9885);var i=t(57114),a=t(21022),n=t(59872),o=t(86867);function WishlistPage(){let e=(0,i.useRouter)(),{data:s,isLoading:t}=(0,o.aK)(),[l]=(0,o.Qw)(),handleRemoveItem=async e=>{await l(e).unwrap()},handleMoveToCart=async s=>{e.push(`/products/${s}`)},handleClearWishlist=async()=>{if(s)for(let e of s)await l(e.id).unwrap()};return r.jsx("div",{className:"min-h-screen p-6",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[r.jsx("h1",{className:"text-2xl font-bold",children:"My Wishlist"}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx(n.Z,{variant:"outline",onClick:()=>e.push("/wishlist/manage"),children:"Manage Wishlists"}),r.jsx(n.Z,{variant:"outline",onClick:()=>e.push("/wishlist/create"),children:"Create Wishlist"}),r.jsx(n.Z,{variant:"outline",onClick:()=>e.push("/products"),children:"Continue Shopping"})]})]}),r.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6",children:r.jsx(a.Z,{items:s||[],isLoading:t,onRemoveItem:handleRemoveItem,onMoveToCart:handleMoveToCart,onClearWishlist:handleClearWishlist,emptyMessage:"Your wishlist is empty. Add products to your wishlist to save them for later."})})]})})}},70121:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>n,__esModule:()=>a,default:()=>l});var r=t(95153);let i=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\wishlist\page.tsx`),{__esModule:a,$$typeof:n}=i,o=i.default,l=o}};var s=require("../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),t=s.X(0,[2103,2765,1022],()=>__webpack_exec__(76212));module.exports=t})();