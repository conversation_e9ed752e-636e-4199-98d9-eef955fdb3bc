import { BaseEvent } from './base.event';

/**
 * Event emitted when a user is created
 */
export class UserCreatedEvent extends BaseEvent {
  readonly type = 'user.created';

  constructor(
    public readonly userId: string,
    public readonly email: string,
    public readonly profile?: {
      firstName?: string;
      lastName?: string;
    },
  ) {
    super();
  }
}

/**
 * Event emitted when a user is updated
 */
export class UserUpdatedEvent extends BaseEvent {
  readonly type = 'user.updated';

  constructor(
    public readonly userId: string,
    public readonly email: string,
    public readonly changes: Record<string, any>,
  ) {
    super();
  }
}

/**
 * Event emitted when a user is deleted
 */
export class UserDeletedEvent extends BaseEvent {
  readonly type = 'user.deleted';

  constructor(
    public readonly userId: string,
    public readonly email: string,
  ) {
    super();
  }
}

/**
 * Event emitted when a user's email is verified
 */
export class UserEmailVerifiedEvent extends BaseEvent {
  readonly type = 'user.email.verified';

  constructor(
    public readonly userId: string,
    public readonly email: string,
  ) {
    super();
  }
}

/**
 * Event emitted when a user's phone is verified
 */
export class UserPhoneVerifiedEvent extends BaseEvent {
  readonly type = 'user.phone.verified';

  constructor(
    public readonly userId: string,
    public readonly phone: string,
  ) {
    super();
  }
}
