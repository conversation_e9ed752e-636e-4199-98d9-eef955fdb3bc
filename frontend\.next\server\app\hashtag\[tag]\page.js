(()=>{var e={};e.id=1379,e.ids=[1379],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},22400:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>g,pages:()=>d,routeModule:()=>x,tree:()=>c});var t=a(67096),r=a(16132),n=a(37284),o=a.n(n),i=a(32564),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);a.d(s,l);let c=["",{children:["hashtag",{children:["[tag]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,35366)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\hashtag\\[tag]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,9291,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\hashtag\\[tag]\\page.tsx"],g="/hashtag/[tag]/page",p={require:a,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/hashtag/[tag]/page",pathname:"/hashtag/[tag]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},27116:(e,s,a)=>{Promise.resolve().then(a.bind(a,48996))},48996:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>HashtagPage});var t=a(30784),r=a(9885),n=a(27870),o=a(57114),i=a(95499),l=a(29850),c=a(56945),d=a(51020),g=a(11440),p=a.n(g);function HashtagPage(){let{t:e}=(0,n.$G)("social"),s=(0,o.useParams)(),a=(0,o.useRouter)(),g=s.tag,[x,h]=(0,r.useState)({hashtags:[g]}),[m,u]=(0,r.useState)(1),{data:y,isLoading:_}=(0,d.ws)({page:m,limit:10,filters:x}),[f,{isLoading:v}]=(0,d.CH)();return t.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 py-8",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsxs)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100 flex items-center",children:[t.jsx("span",{className:"text-primary-600 dark:text-primary-400 mr-2",children:"#"}),g]}),t.jsx("p",{className:"text-gray-500 dark:text-gray-400 mt-1",children:e("hashtagDescription","Explore posts with the hashtag #{{tag}}",{tag:g})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[t.jsx("div",{className:"lg:col-span-1",children:t.jsx(l.Z,{filters:x,onFilterChange:e=>{h({...e,hashtags:[g]}),u(1)},className:"sticky top-8"})}),t.jsx("div",{className:"lg:col-span-2",children:t.jsx(i.Z,{posts:y?.posts||[],isLoading:_,hasMore:!!y&&y.total>10*m,onLoadMore:()=>{u(e=>e+1)},onLike:(e,s)=>{f({postId:e,type:s})},onComment:e=>{a.push(`/social/posts/${e}`)},onShare:e=>{console.log("Share post:",e)}})}),t.jsx("div",{className:"lg:col-span-1",children:(0,t.jsxs)("div",{className:"space-y-6 sticky top-8",children:[t.jsx(c.Z,{}),(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4",children:[t.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4",children:e("relatedHashtags","Related Hashtags")}),t.jsx("div",{className:"flex flex-wrap gap-2",children:["fashion","style","clothing","accessories","outfit"].map(e=>(0,t.jsxs)(p(),{href:`/hashtag/${e}`,className:"px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-full text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600",children:["#",e]},e))})]})]})})]})]})})}},35366:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>o,__esModule:()=>n,default:()=>l});var t=a(95153);let r=(0,t.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\hashtag\[tag]\page.tsx`),{__esModule:n,$$typeof:o}=r,i=r.default,l=i}};var s=require("../../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),a=s.X(0,[2103,2765,2763,3902,1020,2522,4694,4154,5499,9850,6945],()=>__webpack_exec__(22400));module.exports=a})();