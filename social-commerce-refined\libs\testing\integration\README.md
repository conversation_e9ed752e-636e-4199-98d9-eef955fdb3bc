# Integration Tests

This directory contains integration tests for the Social Commerce Platform.

## Overview

The integration tests verify that the microservices work together correctly. They test the complete user journey from registration to purchase, ensuring that all microservices communicate correctly and that the API Gateway routes requests properly.

## Test Flows

- **User and Store Flow**: Tests user registration, login, store creation, and product management.

## Running the Tests

1. Start all microservices:
   ```bash
   # Start the API Gateway
   cd ../services/api-gateway
   npm run start:dev

   # Start the User Service
   cd ../services/user-service
   npm run start:dev

   # Start the Store Service
   cd ../services/store-service
   npm run start:dev
   ```

2. Run the integration tests:
   ```bash
   npm test
   ```

## Configuration

The integration tests use environment variables for configuration. You can modify these variables in the `.env` file:

- `API_GATEWAY_URL`: The URL of the API Gateway
- `USER_SERVICE_URL`: The URL of the User Service
- `STORE_SERVICE_URL`: The URL of the Store Service
- `TEST_USER_EMAIL`: The email to use for test users
- `TEST_USER_PASSWORD`: The password to use for test users

## Adding New Tests

To add a new integration test:

1. Create a new file in the `src` directory with a `.spec.ts` extension
2. Import the `ApiClient` from `./helpers/api.helper`
3. Write your test using Jest's testing functions
4. Run the tests to verify that your new test works correctly

## Best Practices

- Use a unique email for each test run to avoid conflicts
- Clean up test data after tests complete
- Use descriptive test names
- Test the complete user journey
- Test error cases as well as success cases
