import { Module, MiddlewareConsumer, RequestMethod, NestModule } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { ClientsModule, Transport } from '@nestjs/microservices';
// import { MessagingModule } from '@app/messaging'; // TODO: Re-enable when messaging is fixed
import { StoreManagementModule } from './store-management/store-management.module';
import { ProductManagementModule } from './product-management/product-management.module';
import { SharedModule } from './shared/shared.module';
import { UserEventsListener } from './listeners/user-events.listener';
import { CorrelationIdMiddleware } from './shared/middleware/correlation-id.middleware';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { LoggingInterceptor } from './shared/interceptors/logging.interceptor';

// Import entities explicitly (like User Service)
import { Store } from './store-management/entities/store.entity';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: `.env.${process.env.NODE_ENV || 'development'}`,
    }),

    // Database
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get<string>('DB_HOST', 'localhost'),
        port: configService.get<number>('DB_PORT', 5432),
        username: configService.get<string>('DB_USERNAME', 'postgres'),
        password: configService.get<string>('DB_PASSWORD', '1111'),
        database: configService.get<string>('DB_DATABASE', 'store_service'),
        entities: [Store],
        synchronize: configService.get<boolean>('DB_SYNCHRONIZE', true),
        logging: configService.get<boolean>('DB_LOGGING', true),
      }),
    }),

    // JWT
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET', 'your-secret-key'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN', '1h'),
        },
      }),
    }),

    // Messaging - TODO: Re-enable when messaging is fixed
    // MessagingModule.register({
    //   rabbitmqUrl: process.env.RABBITMQ_URL || 'amqp://admin:admin@localhost:5672',
    //   serviceName: 'store-service',
    // }),

    // Microservices - Temporarily disabled for testing
    // ClientsModule.registerAsync([
    //   {
    //     name: 'USER_SERVICE',
    //     imports: [ConfigModule],
    //     inject: [ConfigService],
    //     useFactory: (configService: ConfigService) => ({
    //       transport: Transport.RMQ,
    //       options: {
    //         urls: [configService.get<string>('RABBITMQ_URL', 'amqp://admin:admin@localhost:5672')],
    //         queue: configService.get<string>('USER_QUEUE', 'user_queue'),
    //         queueOptions: {
    //           durable: true,
    //         },
    //       },
    //     }),
    //   },
    //   {
    //     name: 'NOTIFICATION_SERVICE',
    //     imports: [ConfigModule],
    //     inject: [ConfigService],
    //     useFactory: (configService: ConfigService) => ({
    //       transport: Transport.RMQ,
    //       options: {
    //         urls: [configService.get<string>('RABBITMQ_URL', 'amqp://admin:admin@localhost:5672')],
    //         queue: configService.get<string>('NOTIFICATION_QUEUE', 'notification_queue'),
    //         queueOptions: {
    //           durable: true,
    //         },
    //       },
    //     }),
    //   },
    // ]),

    // Feature modules
    StoreManagementModule,
    ProductManagementModule,
    SharedModule,
  ],
  providers: [
    UserEventsListener,
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggingInterceptor,
    },
  ],
  // exports: [ClientsModule], // Temporarily disabled since ClientsModule is not imported
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(CorrelationIdMiddleware)
      .forRoutes({ path: '*', method: RequestMethod.ALL });
  }
}
