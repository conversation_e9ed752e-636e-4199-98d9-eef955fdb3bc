import { IsS<PERSON>, <PERSON><PERSON><PERSON>N<PERSON>ber, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class SendSmsDto {
  @ApiProperty({
    description: 'Recipient phone number',
    example: '+1234567890',
  })
  @IsPhoneNumber()
  to: string;

  @ApiProperty({
    description: 'SMS message content',
    example: 'Your verification code is: 123456',
  })
  @IsString()
  message: string;

  @ApiPropertyOptional({
    description: 'SMS template variables',
    example: { code: '123456', userName: 'John' },
  })
  @IsOptional()
  variables?: Record<string, any>;
}
