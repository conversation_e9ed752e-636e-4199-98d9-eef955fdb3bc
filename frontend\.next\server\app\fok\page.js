(()=>{var e={};e.id=8364,e.ids=[8364],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},39963:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>l.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=t(67096),a=t(16132),i=t(37284),l=t.n(i),n=t(32564),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(r,o);let d=["",{children:["fok",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,53064)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\fok\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9291,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\fok\\page.tsx"],u="/fok/page",p={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/fok/page",pathname:"/fok",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},41914:(e,r,t)=>{Promise.resolve().then(t.bind(t,7374))},7374:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>GroupBuyPage});var s=t(30784),a=t(9885),i=t(57114),l=t(52451),n=t.n(l),o=t(59872),d=t(73531);let groupbuy_GroupBuyCard=({groupBuy:e})=>{var r;let t=(0,i.useRouter)(),a=(0,d.Z)(new Date(e.expiresAt),{addSuffix:!0});return(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden",children:[(0,s.jsxs)("div",{className:"relative h-48 bg-gray-200 dark:bg-gray-700",children:[e.imageUrl?s.jsx(n(),{src:e.imageUrl,alt:e.title,fill:!0,className:"object-cover"}):s.jsx("div",{className:"flex items-center justify-center h-full text-gray-400",children:"No Image"}),s.jsx("div",{className:"absolute top-2 right-2",children:s.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${(e=>{switch(e){case"active":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"completed":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";case"expired":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}})(e.status)}`,children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})})]}),(0,s.jsxs)("div",{className:"p-4",children:[s.jsx("h3",{className:"text-lg font-semibold line-clamp-1",children:e.title}),s.jsx("p",{className:"text-primary-600 font-medium mt-1",children:(r=e.price,new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(r))}),(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[s.jsx("span",{children:"Progress"}),(0,s.jsxs)("span",{children:[e.currentParticipants,"/",e.minParticipants," joined"]})]}),s.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5",children:s.jsx("div",{className:"bg-primary-600 h-2.5 rounded-full",style:{width:`${Math.min(100,Math.round(e.currentParticipants/e.minParticipants*100))}%`}})}),s.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-2",children:"active"===e.status?`Ends ${a}`:"completed"===e.status?"Group buy completed!":"Group buy expired"})]}),s.jsx("div",{className:"mt-4",children:s.jsx(o.Z,{onClick:()=>t.push(`/fok/${e.id}`),fullWidth:!0,disabled:"active"!==e.status,children:"active"===e.status?"Join Group Buy":"completed"===e.status?"View Details":"Expired"})})]})]})},groupbuy_GroupBuyList=({groupBuys:e,isLoading:r=!1,emptyMessage:t="No group buys available"})=>r?(0,s.jsxs)("div",{className:"text-center py-12",children:[s.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600 mx-auto"}),s.jsx("p",{className:"mt-4 text-lg",children:"Loading group buys..."})]}):e&&0!==e.length?s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(e=>s.jsx(groupbuy_GroupBuyCard,{groupBuy:e},e.id))}):s.jsx("div",{className:"text-center py-12 bg-gray-50 dark:bg-gray-800 rounded-lg",children:s.jsx("p",{className:"text-lg text-gray-600 dark:text-gray-300",children:t})});var c=t(84105);function GroupBuyPage(){let e=(0,i.useRouter)(),[r,t]=(0,a.useState)("all"),{data:l,isLoading:n,error:d}=(0,c.AP)({status:"all"===r?void 0:r}),u=l?.groupBuys||[];return s.jsx("div",{className:"min-h-screen p-6 md:p-12",children:(0,s.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,s.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between mb-8 gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[s.jsx("h1",{className:"text-3xl font-bold",children:"Group Buys (FOK)"}),s.jsx(o.Z,{onClick:()=>e.push("/fok/create"),size:"sm",children:"Create Group Buy"})]}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-2",children:[s.jsx(o.Z,{variant:"all"===r?"primary":"outline",size:"sm",onClick:()=>t("all"),children:"All"}),s.jsx(o.Z,{variant:"active"===r?"primary":"outline",size:"sm",onClick:()=>t("active"),children:"Active"}),s.jsx(o.Z,{variant:"completed"===r?"primary":"outline",size:"sm",onClick:()=>t("completed"),children:"Completed"}),s.jsx(o.Z,{variant:"expired"===r?"primary":"outline",size:"sm",onClick:()=>t("expired"),children:"Expired"})]})]}),s.jsx(groupbuy_GroupBuyList,{groupBuys:u,isLoading:n,emptyMessage:`No ${r} group buys available`})]})})}},53064:(e,r,t)=>{"use strict";t.r(r),t.d(r,{$$typeof:()=>l,__esModule:()=>i,default:()=>o});var s=t(95153);let a=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\fok\page.tsx`),{__esModule:i,$$typeof:l}=a,n=a.default,o=n}};var r=require("../../webpack-runtime.js");r.C(e);var __webpack_exec__=e=>r(r.s=e),t=r.X(0,[2103,2765,4105],()=>__webpack_exec__(39963));module.exports=t})();