"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var EventSubscriberExplorerService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventSubscriberExplorerService = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const rabbitmq_service_1 = require("./rabbitmq.service");
const event_subscriber_decorator_1 = require("../decorators/event-subscriber.decorator");
let EventSubscriberExplorerService = EventSubscriberExplorerService_1 = class EventSubscriberExplorerService {
    constructor(discoveryService, metadataScanner, reflector, rabbitMQService) {
        this.discoveryService = discoveryService;
        this.metadataScanner = metadataScanner;
        this.reflector = reflector;
        this.rabbitMQService = rabbitMQService;
        this.logger = new common_1.Logger(EventSubscriberExplorerService_1.name);
        this.exchange = 'events';
    }
    async onModuleInit() {
        await this.explore();
    }
    async explore() {
        const providers = this.discoveryService.getProviders();
        const controllers = this.discoveryService.getControllers();
        const wrappers = [...providers, ...controllers];
        for (const wrapper of wrappers) {
            await this.exploreWrapper(wrapper);
        }
    }
    async exploreWrapper(wrapper) {
        if (!wrapper.instance || !wrapper.metatype) {
            return;
        }
        const instance = wrapper.instance;
        const prototype = Object.getPrototypeOf(instance);
        this.metadataScanner.scanFromPrototype(instance, prototype, async (methodName) => {
            await this.exploreMethod(instance, methodName);
        });
    }
    async exploreMethod(instance, methodName) {
        const options = this.reflector.get(event_subscriber_decorator_1.EVENT_SUBSCRIBER_METADATA, instance[methodName]);
        if (!options) {
            return;
        }
        await this.registerEventSubscriber(instance, methodName, options);
    }
    async registerEventSubscriber(instance, methodName, options) {
        const { event, queue } = options;
        try {
            await this.rabbitMQService.createExchange(this.exchange, 'topic', {
                durable: true,
            });
            await this.rabbitMQService.createQueue(queue, {
                durable: true,
            });
            await this.rabbitMQService.bindQueue(queue, this.exchange, event);
            await this.rabbitMQService.consume(queue, async (msg) => {
                try {
                    const content = msg.content.toString();
                    const eventData = JSON.parse(content);
                    this.logger.debug(`Received event ${event} in queue ${queue}`);
                    await instance[methodName](eventData);
                    this.logger.debug(`Processed event ${event} in queue ${queue}`);
                }
                catch (error) {
                    this.logger.error(`Error processing event ${event} in queue ${queue}: ${error.message}`);
                    throw error;
                }
            });
            this.logger.log(`Registered event subscriber for ${event} in queue ${queue}`);
        }
        catch (error) {
            this.logger.error(`Error registering event subscriber for ${event} in queue ${queue}: ${error.message}`);
        }
    }
};
exports.EventSubscriberExplorerService = EventSubscriberExplorerService;
exports.EventSubscriberExplorerService = EventSubscriberExplorerService = EventSubscriberExplorerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.DiscoveryService,
        core_1.MetadataScanner,
        core_1.Reflector,
        rabbitmq_service_1.RabbitMQService])
], EventSubscriberExplorerService);
//# sourceMappingURL=event-subscriber-explorer.service.js.map