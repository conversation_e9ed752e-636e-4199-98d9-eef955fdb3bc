const chalk = require('chalk');
const spawn = require('cross-spawn');
const ora = require('ora');
const path = require('path');
const fs = require('fs');

/**
 * Stop command implementation
 * @param {Object} program - Commander program instance
 */
module.exports = (program) => {
  program
    .command('stop')
    .description('Stop services')
    .argument('[service]', 'Service to stop (all, user, main, frontend)', 'all')
    .action((service) => {
      const spinner = ora('Stopping services...').start();
      
      try {
        const rootDir = path.resolve(__dirname, '../../');
        
        if (service === 'all') {
          // Stop all services using Docker Compose
          spinner.text = 'Stopping all services with Docker Compose...';
          
          const result = spawn.sync('docker-compose', ['down'], {
            cwd: rootDir,
            stdio: 'inherit'
          });
          
          if (result.status === 0) {
            spinner.succeed(chalk.green('All services stopped successfully'));
          } else {
            spinner.fail(chalk.red('Failed to stop services'));
            console.error(chalk.red('Error: Docker Compose failed to stop services'));
          }
        } else if (service === 'user' || service === 'main' || service === 'frontend') {
          // For individual services, we'll use Docker Compose to stop just that service
          spinner.text = `Stopping ${service} service...`;
          
          const result = spawn.sync('docker-compose', ['stop', service], {
            cwd: rootDir,
            stdio: 'inherit'
          });
          
          if (result.status === 0) {
            spinner.succeed(chalk.green(`${service} service stopped successfully`));
          } else {
            spinner.fail(chalk.red(`Failed to stop ${service} service`));
          }
        } else {
          spinner.fail(chalk.red(`Unknown service: ${service}`));
          console.error(chalk.yellow('Available services: all, user, main, frontend'));
        }
      } catch (error) {
        spinner.fail(chalk.red('Error stopping services'));
        console.error(chalk.red(error.message));
      }
    });
};
