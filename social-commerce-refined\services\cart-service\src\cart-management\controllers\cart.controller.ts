import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { CartService } from '../services/cart.service';
import { CreateCartDto } from '../dto/create-cart.dto';
import { AddToCartDto } from '../dto/add-to-cart.dto';
import { UpdateCartItemDto } from '../dto/update-cart-item.dto';
import { CartResponseDto } from '../dto/cart-response.dto';

@ApiTags('carts')
@Controller('api/carts')
export class CartController {
  constructor(private readonly cartService: CartService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new cart' })
  @ApiResponse({
    status: 201,
    description: 'Cart created successfully',
    type: CartResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async createCart(@Body() createCartDto: CreateCartDto): Promise<CartResponseDto> {
    return await this.cartService.createCart(createCartDto);
  }

  @Get('current')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get or create current user cart' })
  @ApiResponse({
    status: 200,
    description: 'Current cart retrieved successfully',
    type: CartResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getCurrentCart(@Request() req): Promise<CartResponseDto> {
    return await this.cartService.getOrCreateCart(req.user.userId);
  }

  @Get('guest')
  @ApiOperation({ summary: 'Get or create guest cart' })
  @ApiQuery({
    name: 'sessionId',
    description: 'Session ID for guest cart',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Guest cart retrieved successfully',
    type: CartResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Session ID is required' })
  async getGuestCart(@Query('sessionId') sessionId: string): Promise<CartResponseDto> {
    return await this.cartService.getOrCreateCart(undefined, sessionId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get cart by ID' })
  @ApiParam({ name: 'id', description: 'Cart ID' })
  @ApiResponse({
    status: 200,
    description: 'Cart retrieved successfully',
    type: CartResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Cart not found' })
  async getCart(@Param('id') id: string): Promise<CartResponseDto> {
    return await this.cartService.getCart(id);
  }

  @Post(':id/items')
  @ApiOperation({ summary: 'Add item to cart' })
  @ApiParam({ name: 'id', description: 'Cart ID' })
  @ApiResponse({
    status: 201,
    description: 'Item added to cart successfully',
    type: CartResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Cart or product not found' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async addToCart(
    @Param('id') cartId: string,
    @Body() addToCartDto: AddToCartDto,
  ): Promise<CartResponseDto> {
    return await this.cartService.addToCart(cartId, addToCartDto);
  }

  @Put(':id/items/:itemId')
  @ApiOperation({ summary: 'Update cart item' })
  @ApiParam({ name: 'id', description: 'Cart ID' })
  @ApiParam({ name: 'itemId', description: 'Cart item ID' })
  @ApiResponse({
    status: 200,
    description: 'Cart item updated successfully',
    type: CartResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Cart or cart item not found' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async updateCartItem(
    @Param('id') cartId: string,
    @Param('itemId') itemId: string,
    @Body() updateCartItemDto: UpdateCartItemDto,
  ): Promise<CartResponseDto> {
    return await this.cartService.updateCartItem(cartId, itemId, updateCartItemDto);
  }

  @Delete(':id/items/:itemId')
  @ApiOperation({ summary: 'Remove item from cart' })
  @ApiParam({ name: 'id', description: 'Cart ID' })
  @ApiParam({ name: 'itemId', description: 'Cart item ID' })
  @ApiResponse({
    status: 200,
    description: 'Item removed from cart successfully',
    type: CartResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Cart or cart item not found' })
  async removeCartItem(
    @Param('id') cartId: string,
    @Param('itemId') itemId: string,
  ): Promise<CartResponseDto> {
    return await this.cartService.removeCartItem(cartId, itemId);
  }

  @Delete(':id/items')
  @ApiOperation({ summary: 'Clear all items from cart' })
  @ApiParam({ name: 'id', description: 'Cart ID' })
  @ApiResponse({
    status: 200,
    description: 'Cart cleared successfully',
    type: CartResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Cart not found' })
  async clearCart(@Param('id') cartId: string): Promise<CartResponseDto> {
    return await this.cartService.clearCart(cartId);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete cart' })
  @ApiParam({ name: 'id', description: 'Cart ID' })
  @ApiResponse({ status: 204, description: 'Cart deleted successfully' })
  @ApiResponse({ status: 404, description: 'Cart not found' })
  async deleteCart(@Param('id') cartId: string): Promise<void> {
    return await this.cartService.deleteCart(cartId);
  }
}
