"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CartItemAddedEvent = void 0;
class CartItemAddedEvent {
    constructor(payload) {
        this.type = 'cart.item.added';
        this.version = '1.0';
        this.producer = 'cart-service';
        this.id = `${payload.cartId}-${payload.productId}-${Date.now()}`;
        this.timestamp = new Date().toISOString();
        this.payload = payload;
    }
}
exports.CartItemAddedEvent = CartItemAddedEvent;
//# sourceMappingURL=cart-item-added.event.js.map