import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, IsOptional, IsObject, IsBoolean, IsUrl } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateStoreDto {
  @ApiProperty({
    description: 'The name of the store',
    example: 'My Awesome Store',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiPropertyOptional({
    description: 'The description of the store',
    example: 'This is a store that sells awesome products',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'The ID of the store owner',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  @IsNotEmpty()
  ownerId: string;

  @ApiPropertyOptional({
    description: 'The URL of the store logo',
    example: 'https://example.com/logo.png',
  })
  @IsUrl()
  @IsOptional()
  logoUrl?: string;

  @ApiPropertyOptional({
    description: 'The URL of the store banner',
    example: 'https://example.com/banner.png',
  })
  @IsUrl()
  @IsOptional()
  bannerUrl?: string;

  @ApiPropertyOptional({
    description: 'Whether the store is active',
    example: true,
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'The contact information of the store',
    example: {
      email: '<EMAIL>',
      phone: '+1234567890',
      website: 'https://example.com',
      address: {
        street: '123 Main St',
        city: 'New York',
        state: 'NY',
        zipCode: '10001',
        country: 'USA',
      },
    },
  })
  @IsObject()
  @IsOptional()
  contactInfo?: {
    email?: string;
    phone?: string;
    website?: string;
    address?: {
      street?: string;
      city?: string;
      state?: string;
      zipCode?: string;
      country?: string;
    };
  };

  @ApiPropertyOptional({
    description: 'The social links of the store',
    example: {
      facebook: 'https://facebook.com/mystore',
      twitter: 'https://twitter.com/mystore',
      instagram: 'https://instagram.com/mystore',
    },
  })
  @IsObject()
  @IsOptional()
  socialLinks?: Record<string, string>;

  @ApiPropertyOptional({
    description: 'The business hours of the store',
    example: {
      monday: { open: '09:00', close: '17:00' },
      tuesday: { open: '09:00', close: '17:00' },
      wednesday: { open: '09:00', close: '17:00' },
      thursday: { open: '09:00', close: '17:00' },
      friday: { open: '09:00', close: '17:00' },
      saturday: { open: '10:00', close: '15:00' },
      sunday: { open: '10:00', close: '15:00' },
    },
  })
  @IsObject()
  @IsOptional()
  businessHours?: {
    monday?: { open: string; close: string };
    tuesday?: { open: string; close: string };
    wednesday?: { open: string; close: string };
    thursday?: { open: string; close: string };
    friday?: { open: string; close: string };
    saturday?: { open: string; close: string };
    sunday?: { open: string; close: string };
  };

  @ApiPropertyOptional({
    description: 'The settings of the store',
    example: {
      theme: 'light',
      currency: 'USD',
      language: 'en',
      notifications: {
        email: true,
        sms: false,
        push: true,
      },
    },
  })
  @IsObject()
  @IsOptional()
  settings?: {
    theme?: string;
    currency?: string;
    language?: string;
    notifications?: {
      email?: boolean;
      sms?: boolean;
      push?: boolean;
    };
  };
}
