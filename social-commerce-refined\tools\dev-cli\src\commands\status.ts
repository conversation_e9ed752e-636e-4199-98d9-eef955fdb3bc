import { Command } from 'commander';
import * as chalk from 'chalk';
import { getServicesStatus } from '../utils/docker';

export function statusCommand(program: Command): void {
  program
    .command('status')
    .description('Check service status')
    .action(async () => {
      try {
        console.log(chalk.blue('Checking service status...'));
        
        const status = await getServicesStatus();
        
        console.log(chalk.green('Service Status:'));
        console.log(status);
      } catch (error) {
        console.error(chalk.red(`Error: ${error.message}`));
        process.exit(1);
      }
    });
}
