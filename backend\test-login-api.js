const fetch = require('node-fetch');

async function testLoginApi() {
  try {
    console.log('Testing login API...');

    const credentials = {
      usernameOrEmail: 'simpletestuser',
      password: 'password123'
    };

    console.log('Credentials:', {
      usernameOrEmail: credentials.usernameOrEmail,
      passwordLength: credentials.password.length
    });

    // Test login with the test server
    console.log('\nTesting login with test server (port 3010)...');
    try {
      const response = await fetch('http://localhost:3010/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      console.log('Response status:', response.status);
      const data = await response.json().catch(() => ({ error: 'Failed to parse JSON response' }));
      console.log('Response data:', data);

      if (response.ok) {
        console.log('Login successful with test server!');
      } else {
        console.log('<PERSON><PERSON> failed with test server.');
      }
    } catch (error) {
      console.error('Error testing login with test server:', error);
    }

    // Test login with the original backend
    console.log('\nTesting login with original backend (port 3001)...');
    try {
      const response = await fetch('http://localhost:3001/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      console.log('Response status:', response.status);
      const data = await response.json().catch(() => ({ error: 'Failed to parse JSON response' }));
      console.log('Response data:', data);

      if (response.ok) {
        console.log('Login successful with original backend!');
      } else {
        console.log('Login failed with original backend.');
      }
    } catch (error) {
      console.error('Error testing login with original backend:', error);
    }

    console.log('\nLogin test completed.');
  } catch (error) {
    console.error('Error:', error);
  }
}

testLoginApi();
