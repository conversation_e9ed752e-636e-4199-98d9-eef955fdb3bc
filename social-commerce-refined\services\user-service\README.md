# User Service

The User Service is responsible for user authentication, profile management, and verification in the Social Commerce Platform.

## Features

- **Authentication**: User registration, login, and JWT-based authentication
- **Profile Management**: User profile creation, update, and retrieval
- **Verification**: Email and phone verification, password reset

## Architecture

The User Service follows a feature-based organization pattern:

```
src/
├── authentication/       # Authentication feature
│   ├── controllers/      # REST API controllers
│   ├── services/         # Business logic
│   ├── repositories/     # Data access
│   ├── entities/         # Database entities
│   ├── dto/              # Data transfer objects
│   ├── guards/           # Authentication guards
│   └── strategies/       # Passport strategies
├── profile-management/   # Profile management feature
│   ├── controllers/
│   ├── services/
│   ├── repositories/
│   ├── entities/
│   └── dto/
├── verification/         # Verification feature
│   ├── controllers/
│   ├── services/
│   ├── repositories/
│   ├── entities/
│   └── dto/
└── shared/               # Shared modules and utilities
    ├── controllers/
    ├── guards/
    ├── decorators/
    ├── filters/
    ├── interceptors/
    ├── middleware/
    └── utils/
```

## API Endpoints

### Authentication

- `POST /auth/register` - Register a new user
- `POST /auth/login` - Login a user
- `GET /auth/profile` - Get user profile

### Profile Management

- `GET /profiles` - Get all profiles
- `GET /profiles/:id` - Get a profile by ID
- `GET /profiles/user/:userId` - Get a profile by user ID
- `POST /profiles/user/:userId` - Create a profile for a user
- `PUT /profiles/:id` - Update a profile
- `PUT /profiles/user/:userId` - Update a profile by user ID
- `DELETE /profiles/:id` - Delete a profile

### Verification

- `POST /verification/email/:userId` - Send email verification token
- `POST /verification/verify-email` - Verify email with token
- `POST /verification/phone/:userId` - Send phone verification token
- `POST /verification/verify-phone/:userId` - Verify phone with token
- `POST /verification/forgot-password` - Request password reset
- `POST /verification/reset-password` - Reset password with token

### Health

- `GET /health` - Check service health

## Microservice Endpoints

The User Service also exposes microservice endpoints for internal communication:

### Authentication

- `register_user` - Register a new user
- `validate_user` - Validate user credentials
- `find_user_by_email` - Find a user by email
- `find_user_by_id` - Find a user by ID

### Profile Management

- `find_profile_by_user_id` - Find a profile by user ID
- `create_profile` - Create a profile
- `update_profile` - Update a profile

### Verification

- `verify_email` - Verify email with token
- `forgot_password` - Request password reset
- `reset_password` - Reset password with token

## Getting Started

### Prerequisites

- Node.js (v18 or later)
- PostgreSQL (v14 or later)

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   cd services/user-service
   npm install
   ```
3. Configure environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```
4. Start the service:
   ```bash
   npm run start:dev
   ```

### Docker

You can also run the service using Docker:

```bash
docker build -t user-service .
docker run -p 3001:3001 user-service
```

## Testing

```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# Test coverage
npm run test:cov
```

## Documentation

API documentation is available at `/api/docs` when the service is running.

## License

MIT
