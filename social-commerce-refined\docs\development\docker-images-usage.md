# Docker Images & Usage Documentation

## Overview
This document provides comprehensive information about all Docker images used in the social commerce platform, their locations, and how to build, start, and use them.

## Docker Images Inventory

### 1. **PostgreSQL Database**
**Image:** `postgres:15-alpine`
**Container Name:** `social-commerce-postgres`
**Purpose:** Primary database for all microservices

**Configuration:**
```bash
# Environment Variables
POSTGRES_DB=user_service
POSTGRES_USER=postgres
POSTGRES_PASSWORD=1111
POSTGRES_HOST_AUTH_METHOD=trust

# Ports
Container Port: 5432
Host Port: 5432
```

**Usage:**
```bash
# Start PostgreSQL container
docker run -d \
  --name social-commerce-postgres \
  --network social-commerce-refined_social-commerce-network \
  -p 5432:5432 \
  -e POSTGRES_DB=user_service \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=1111 \
  -e POSTGRES_HOST_AUTH_METHOD=trust \
  postgres:15-alpine

# Connect to database
docker exec -it social-commerce-postgres psql -U postgres -d user_service

# Check database status
docker logs social-commerce-postgres
```

### 2. **RabbitMQ Message Broker**
**Image:** `rabbitmq:3-management-alpine`
**Container Name:** `social-commerce-rabbitmq`
**Purpose:** Message queue for microservices communication

**Configuration:**
```bash
# Environment Variables
RABBITMQ_DEFAULT_USER=admin
RABBITMQ_DEFAULT_PASS=admin

# Ports
Container Port: 5672 (AMQP)
Host Port: 5672
Container Port: 15672 (Management UI)
Host Port: 15672
```

**Usage:**
```bash
# Start RabbitMQ container
docker run -d \
  --name social-commerce-rabbitmq \
  --network social-commerce-refined_social-commerce-network \
  -p 5672:5672 \
  -p 15672:15672 \
  -e RABBITMQ_DEFAULT_USER=admin \
  -e RABBITMQ_DEFAULT_PASS=admin \
  rabbitmq:3-management-alpine

# Access Management UI
# URL: http://localhost:15672
# Username: admin
# Password: admin

# Check RabbitMQ status
docker logs social-commerce-rabbitmq
```

### 3. **User Service**
**Image:** `user-service:latest`
**Container Name:** `user-service`
**Purpose:** Handles user authentication, registration, and profile management

**Build Location:** `services/user-service/`
**Dockerfile:** `services/user-service/Dockerfile`

**Configuration:**
```bash
# Environment Variables
DB_HOST=postgres
DB_PASSWORD=1111
DB_DATABASE=user_service
RABBITMQ_URL=amqp://admin:admin@rabbitmq:5672
HTTP_PORT=3001
MICROSERVICE_PORT=3002

# Ports
Container Port: 3001 (HTTP API)
Host Port: 3001
Container Port: 3002 (Microservice)
Host Port: 3002
```

**Build & Usage:**
```bash
# Build user service image
cd /c/Users/<USER>/Documents/augment/social-commerce/social-commerce-refined
docker build -f services/user-service/Dockerfile -t user-service .

# Start user service container
docker run -d \
  --name user-service \
  --network social-commerce-refined_social-commerce-network \
  -p 3001:3001 \
  -e DB_HOST=postgres \
  -e DB_PASSWORD=1111 \
  -e DB_DATABASE=user_service \
  -e RABBITMQ_URL=amqp://admin:admin@rabbitmq:5672 \
  -e HTTP_PORT=3001 \
  -e MICROSERVICE_PORT=3002 \
  user-service

# Check service status
docker logs user-service --tail 20

# Access API documentation
# URL: http://localhost:3001/api/docs
```

### 4. **Frontend Application**
**Image:** `social-commerce-frontend:latest`
**Container Name:** `social-commerce-frontend`
**Purpose:** Next.js frontend application

**Build Location:** `frontend/`
**Dockerfile:** `frontend/Dockerfile`

**Configuration:**
```bash
# Environment Variables
NEXT_PUBLIC_API_URL=http://localhost:3000/api

# Ports
Container Port: 3000
Host Port: 3000
```

**Build & Usage:**
```bash
# Build frontend image
cd /c/Users/<USER>/Documents/augment/social-commerce/social-commerce-refined
docker build -f frontend/Dockerfile -t social-commerce-frontend ./frontend

# Start frontend container
docker run -d \
  --name social-commerce-frontend \
  --network social-commerce-refined_social-commerce-network \
  -p 3000:3000 \
  -e NEXT_PUBLIC_API_URL=http://localhost:3000/api \
  social-commerce-frontend

# Check frontend status
docker logs social-commerce-frontend

# Access application
# URL: http://localhost:3000
```

## Docker Network

### Network Configuration
**Network Name:** `social-commerce-refined_social-commerce-network`
**Type:** Bridge network
**Purpose:** Enables communication between all containers

**Create Network:**
```bash
# Create custom network
docker network create social-commerce-refined_social-commerce-network

# List networks
docker network ls

# Inspect network
docker network inspect social-commerce-refined_social-commerce-network
```

## Complete Startup Sequence

### 1. **Start Infrastructure Services**
```bash
# Start PostgreSQL
docker run -d \
  --name social-commerce-postgres \
  --network social-commerce-refined_social-commerce-network \
  -p 5432:5432 \
  -e POSTGRES_DB=user_service \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=1111 \
  -e POSTGRES_HOST_AUTH_METHOD=trust \
  postgres:15-alpine

# Start RabbitMQ
docker run -d \
  --name social-commerce-rabbitmq \
  --network social-commerce-refined_social-commerce-network \
  -p 5672:5672 \
  -p 15672:15672 \
  -e RABBITMQ_DEFAULT_USER=admin \
  -e RABBITMQ_DEFAULT_PASS=admin \
  rabbitmq:3-management-alpine

# Wait for services to be ready (30 seconds)
sleep 30
```

### 2. **Start Application Services**
```bash
# Start User Service
docker run -d \
  --name user-service \
  --network social-commerce-refined_social-commerce-network \
  -p 3001:3001 \
  -e DB_HOST=postgres \
  -e DB_PASSWORD=1111 \
  -e DB_DATABASE=user_service \
  -e RABBITMQ_URL=amqp://admin:admin@rabbitmq:5672 \
  -e HTTP_PORT=3001 \
  -e MICROSERVICE_PORT=3002 \
  user-service

# Start Frontend
docker run -d \
  --name social-commerce-frontend \
  --network social-commerce-refined_social-commerce-network \
  -p 3000:3000 \
  -e NEXT_PUBLIC_API_URL=http://localhost:3000/api \
  social-commerce-frontend
```

### 3. **Verify All Services**
```bash
# Check all containers
docker ps

# Check service health
curl http://localhost:3001/api/health  # User Service
curl http://localhost:3000             # Frontend

# Check logs
docker logs user-service --tail 10
docker logs social-commerce-frontend --tail 10
```

## Development Workflow

### 1. **Building Images**
```bash
# Build user service (after code changes)
cd /c/Users/<USER>/Documents/augment/social-commerce/social-commerce-refined
docker build -f services/user-service/Dockerfile -t user-service .

# Build frontend (after code changes)
docker build -f frontend/Dockerfile -t social-commerce-frontend ./frontend
```

### 2. **Updating Running Services**
```bash
# Stop and remove old container
docker rm -f user-service

# Start new container with updated image
docker run -d \
  --name user-service \
  --network social-commerce-refined_social-commerce-network \
  -p 3001:3001 \
  -e DB_HOST=postgres \
  -e DB_PASSWORD=1111 \
  -e DB_DATABASE=user_service \
  -e RABBITMQ_URL=amqp://admin:admin@rabbitmq:5672 \
  -e HTTP_PORT=3001 \
  -e MICROSERVICE_PORT=3002 \
  user-service
```

### 3. **Debugging**
```bash
# View real-time logs
docker logs -f user-service

# Execute commands in container
docker exec -it user-service sh

# Connect to database
docker exec -it social-commerce-postgres psql -U postgres -d user_service
```

## Troubleshooting

### Common Issues & Solutions

**1. Container Won't Start**
```bash
# Check container logs
docker logs container-name

# Check if ports are available
netstat -an | grep :3001

# Check if network exists
docker network ls
```

**2. Database Connection Issues**
```bash
# Verify PostgreSQL is running
docker ps | grep postgres

# Test database connection
docker exec -it social-commerce-postgres psql -U postgres -d user_service -c "SELECT 1;"

# Check database logs
docker logs social-commerce-postgres
```

**3. Service Communication Issues**
```bash
# Check if containers are on same network
docker network inspect social-commerce-refined_social-commerce-network

# Test connectivity between containers
docker exec -it user-service ping postgres
docker exec -it user-service ping rabbitmq
```

## Cleanup Commands

### Stop All Services
```bash
# Stop all containers
docker stop social-commerce-frontend user-service social-commerce-rabbitmq social-commerce-postgres

# Remove all containers
docker rm social-commerce-frontend user-service social-commerce-rabbitmq social-commerce-postgres

# Remove network
docker network rm social-commerce-refined_social-commerce-network
```

### Clean Up Images
```bash
# Remove custom images
docker rmi user-service social-commerce-frontend

# Remove unused images
docker image prune -f

# Remove all unused resources
docker system prune -f
```

---

**Last Updated:** 2025-05-26
**Status:** ✅ **ACTIVE** - All images documented and tested
**Maintainer:** Development Team
