import { Injectable, Inject, Logger } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';

/**
 * Service for caching data
 */
@Injectable()
export class CacheService {
  private readonly logger = new Logger(CacheService.name);
  private readonly defaultTtl = 60 * 1000; // 1 minute

  constructor(@Inject(CACHE_MANAGER) private readonly cacheManager: Cache) {}

  /**
   * Get a value from the cache
   * @param key The cache key
   * @returns The cached value or null if not found
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await this.cacheManager.get<T>(key);

      if (value) {
        this.logger.debug(`Cache hit for key: ${key}`);
        return value;
      }

      this.logger.debug(`Cache miss for key: ${key}`);
      return null;
    } catch (error) {
      this.logger.error(`Error getting value from cache for key ${key}: ${error.message}`);
      return null;
    }
  }

  /**
   * Set a value in the cache
   * @param key The cache key
   * @param value The value to cache
   * @param ttl Time to live in milliseconds (optional)
   */
  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    try {
      await this.cacheManager.set(key, value, ttl || this.defaultTtl);
      this.logger.debug(`Cached value for key: ${key} with TTL: ${ttl || this.defaultTtl}ms`);
    } catch (error) {
      this.logger.error(`Error setting value in cache for key ${key}: ${error.message}`);
    }
  }

  /**
   * Delete a value from the cache
   * @param key The cache key
   */
  async delete(key: string): Promise<void> {
    try {
      await this.cacheManager.del(key);
      this.logger.debug(`Deleted cache for key: ${key}`);
    } catch (error) {
      this.logger.error(`Error deleting value from cache for key ${key}: ${error.message}`);
    }
  }

  /**
   * Clear the entire cache
   * Note: clear() method not available in current cache-manager version
   */
  async clear(): Promise<void> {
    try {
      // await this.cacheManager.clear(); // Not available in current version
      this.logger.debug('Cache clear requested (not implemented in current cache-manager version)');
    } catch (error) {
      this.logger.error(`Error clearing cache: ${error.message}`);
    }
  }

  /**
   * Get a value from the cache or set it if not found
   * @param key The cache key
   * @param factory A function that returns the value to cache
   * @param ttl Time to live in milliseconds (optional)
   * @returns The cached value or the result of the factory function
   */
  async getOrSet<T>(key: string, factory: () => Promise<T>, ttl?: number): Promise<T> {
    const cachedValue = await this.get<T>(key);

    if (cachedValue !== null) {
      return cachedValue;
    }

    const value = await factory();
    await this.set(key, value, ttl);
    return value;
  }
}
