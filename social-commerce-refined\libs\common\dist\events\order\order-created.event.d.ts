import { BaseEvent } from '../base-event.interface';
export declare class OrderCreatedEvent implements BaseEvent<OrderCreatedPayload> {
    id: string;
    type: string;
    version: string;
    timestamp: string;
    producer: string;
    payload: OrderCreatedPayload;
    constructor(payload: OrderCreatedPayload);
}
export interface OrderCreatedPayload {
    id: string;
    userId: string;
    totalAmount: number;
    status: string;
    items: OrderItemPayload[];
    createdAt: string;
}
export interface OrderItemPayload {
    productId: string;
    productName: string;
    quantity: number;
    price: number;
}
