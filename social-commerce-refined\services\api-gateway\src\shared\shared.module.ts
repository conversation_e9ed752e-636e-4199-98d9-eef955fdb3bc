import { Module, Global } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { TerminusModule } from '@nestjs/terminus';
import { HttpModule } from '@nestjs/axios';
import { HealthController } from './controllers/health.controller';
import { AllExceptionsFilter } from './filters/all-exceptions.filter';
import { LoggingInterceptor } from './interceptors/logging.interceptor';
import { CircuitBreakerService } from '../shared/services/circuit-breaker.service';
import { RateLimitGuard } from './guards/rate-limit.guard';
import { RateLimitFactory } from './guards/rate-limit.factory';
import { CacheService } from './services/cache.service';
import { CacheInterceptor } from './interceptors/cache.interceptor';

@Global()
@Module({
  imports: [
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET', 'your-secret-key'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN', '1h'),
        },
      }),
    }),
    TerminusModule,
    HttpModule,
  ],
  controllers: [HealthController],
  providers: [
    AllExceptionsFilter,
    LoggingInterceptor,
    CircuitBreakerService,
    CacheService,
    CacheInterceptor,
    {
      provide: 'AUTH_RATE_LIMIT_GUARD',
      useFactory: () => RateLimitFactory.createAuthRateLimitGuard(),
    },
    {
      provide: 'API_RATE_LIMIT_GUARD',
      useFactory: () => RateLimitFactory.createApiRateLimitGuard(),
    },
    {
      provide: 'STORE_RATE_LIMIT_GUARD',
      useFactory: () => RateLimitFactory.createStoreRateLimitGuard(),
    },
    {
      provide: 'PRODUCT_RATE_LIMIT_GUARD',
      useFactory: () => RateLimitFactory.createProductRateLimitGuard(),
    },
  ],
  exports: [
    JwtModule,
    AllExceptionsFilter,
    LoggingInterceptor,
    CircuitBreakerService,
    CacheService,
    CacheInterceptor,
    'AUTH_RATE_LIMIT_GUARD',
    'API_RATE_LIMIT_GUARD',
    'STORE_RATE_LIMIT_GUARD',
    'PRODUCT_RATE_LIMIT_GUARD',
  ],
})
export class SharedModule {}
