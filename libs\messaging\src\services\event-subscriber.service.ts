import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { BaseEvent } from '../events/base.event';

type EventHandler = (event: any) => Promise<void>;

@Injectable()
export class EventSubscriberService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(EventSubscriberService.name);
  private readonly handlers: Map<string, EventHandler[]> = new Map();

  constructor() {}

  onModuleInit() {
    this.logger.log('Event subscriber service initialized');
  }

  onModuleDestroy() {
    this.logger.log('Event subscriber service destroyed');
    this.handlers.clear();
  }

  /**
   * Subscribe to an event
   * @param eventType The type of event to subscribe to
   * @param handler The handler function to call when the event is received
   */
  subscribe(eventType: string, handler: EventHandler): void {
    this.logger.log(`Subscribing to event: ${eventType}`);
    
    if (!this.handlers.has(eventType)) {
      this.handlers.set(eventType, []);
    }
    
    this.handlers.get(eventType).push(handler);
  }

  /**
   * Handle an event
   * @param event The event to handle
   */
  async handleEvent(event: BaseEvent): Promise<void> {
    this.logger.log(`Handling event: ${event.type}`);
    
    const handlers = this.handlers.get(event.type) || [];
    
    if (handlers.length === 0) {
      this.logger.warn(`No handlers registered for event: ${event.type}`);
      return;
    }
    
    try {
      await Promise.all(handlers.map(handler => handler(event)));
      this.logger.log(`Event handled successfully: ${event.type}`);
    } catch (error) {
      this.logger.error(`Failed to handle event: ${error.message}`, error.stack);
      throw error;
    }
  }
}
