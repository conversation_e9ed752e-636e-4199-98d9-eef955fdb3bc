# Microservices Dependency Resolution

## Issue Summary

**Date**: May 27, 2025  
**Issue**: Store Service and User Service failing to start due to missing NOTIFICATION_SERVICE dependency  
**Resolution**: Systematic implementation of Notification Service from scratch  

## Root Cause Analysis

### Problem Description
Both User Service and Store Service were failing with dependency injection errors:

```
ERROR [ExceptionHandler] Nest can't resolve dependencies of the StoreService (StoreRepository, ?). 
Please make sure that the argument "USER_SERVICE" at index [1] is available in the StoreManagementModule context.

ERROR [ExceptionHandler] Nest can't resolve dependencies of the AuthenticationService (UserRepository, JwtService, ?). 
Please make sure that the argument "NOTIFICATION_SERVICE" at index [2] is available in the AuthenticationModule context.
```

### Root Cause
1. **Store Service** was trying to inject `USER_SERVICE` but ClientsModule was not properly configured
2. **User Service** was trying to inject `NOTIFICATION_SERVICE` which didn't exist yet
3. **Missing Service**: The Notification Service was planned but not implemented (Phase 1.5 in roadmap)

## Systematic Solution Approach

### Phase 1: Notification Service Implementation

#### 1.1 Service Creation Strategy
- **✅ CORRECT**: Created from scratch using User Service as reference template
- **❌ AVOIDED**: Direct copying of user-service directory (maintains service-specific functionality)

#### 1.2 Service Structure
```
notification-service/
├── src/
│   ├── main.ts                    # Application entry point
│   ├── app.module.ts              # Root module
│   ├── notification/              # Core notification functionality
│   │   ├── controllers/
│   │   ├── services/
│   │   ├── dto/
│   │   └── notification.module.ts
│   └── health/                    # Health check endpoints
├── package.json                   # Dependencies
├── Dockerfile                     # Container configuration
└── tsconfig.json                  # TypeScript configuration
```

#### 1.3 Key Features Implemented
- **Email Service**: Nodemailer integration with SMTP configuration
- **SMS Service**: Mock implementation (ready for Twilio/AWS SNS integration)
- **Message Patterns**: RabbitMQ microservices communication
- **HTTP Endpoints**: REST API for direct notification sending
- **Health Checks**: Terminus integration for monitoring

### Phase 2: Dependency Configuration

#### 2.1 Store Service Fix
**Issue**: ClientsModule not properly imported in StoreManagementModule

**Solution**:
```typescript
// store-management.module.ts
import { ClientsModule } from '@nestjs/microservices';

@Module({
  imports: [
    // ... other imports
    ClientsModule, // Import from AppModule
  ],
  // ...
})
```

#### 2.2 User Service Fix
**Issue**: ClientsModule configuration was commented out

**Solution**:
```typescript
// app.module.ts
import { ClientsModule, Transport } from '@nestjs/microservices';

@Module({
  imports: [
    // ... other imports
    ClientsModule.registerAsync([
      {
        name: 'NOTIFICATION_SERVICE',
        imports: [ConfigModule],
        inject: [ConfigService],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.RMQ,
          options: {
            urls: [configService.get<string>('RABBITMQ_URL')],
            queue: configService.get<string>('NOTIFICATION_QUEUE'),
            queueOptions: { durable: true },
          },
        }),
      },
    ]),
  ],
})
```

## Technical Implementation Details

### Naming Conventions Applied
- **Database**: `social_commerce_notification_db`
- **Docker Image**: `social-commerce-refined-notification-service`
- **Container**: `social-commerce-notification-service`
- **Port**: `3003`
- **Queue**: `notification_queue`

### Docker Configuration
```yaml
# docker-compose.yml
notification-service:
  build:
    context: .
    dockerfile: services/notification-service/Dockerfile
  container_name: social-commerce-notification-service
  environment:
    NODE_ENV: development
    HTTP_PORT: 3003
    RABBITMQ_URL: amqp://admin:admin@rabbitmq:5672
    NOTIFICATION_QUEUE: notification_queue
  ports:
    - "3003:3003"
  depends_on:
    rabbitmq:
      condition: service_healthy
```

### Build Issues Resolved

#### Issue 1: Missing Dependencies
**Error**: `Cannot find module '@nestjs/terminus'`
**Fix**: Added missing dependencies to package.json:
```json
{
  "dependencies": {
    "@nestjs/axios": "^3.0.0",
    "@nestjs/terminus": "^10.0.0",
    "axios": "^1.5.0"
  }
}
```

#### Issue 2: Incorrect Nodemailer Method
**Error**: `Property 'createTransporter' does not exist`
**Fix**: Changed to correct method name:
```typescript
// Before
this.transporter = nodemailer.createTransporter(config);

// After
this.transporter = nodemailer.createTransport(config);
```

#### Issue 3: Dockerfile Entry Point
**Error**: `Cannot find module '/app/dist/main'`
**Fix**: Added .js extension:
```dockerfile
# Before
CMD ["node", "dist/main"]

# After
CMD ["node", "dist/main.js"]
```

## Verification Steps

### 1. Build Verification
```bash
docker-compose build notification-service
docker-compose build user-service
docker-compose build store-service
```

### 2. Service Status Check
```bash
docker images | grep social-commerce
# Should show all three services built successfully
```

### 3. Integration Testing (Next Phase)
```bash
docker-compose up -d notification-service user-service store-service
docker-compose logs user-service
docker-compose logs store-service
```

## Lessons Learned

### Best Practices Applied
1. **Systematic Root Cause Analysis**: Identified missing service rather than patching symptoms
2. **Proper Service Templating**: Created from scratch with service-specific functionality
3. **Consistent Architecture**: Followed established patterns and naming conventions
4. **Incremental Testing**: Built and tested each component systematically

### Anti-Patterns Avoided
1. **Direct Service Copying**: Would have included unnecessary user-specific code
2. **Temporary Workarounds**: Could have disabled dependency injection temporarily
3. **Quick Fixes**: Could have mocked the service without proper implementation

## Future Considerations

### Production Readiness
- [ ] Implement real SMS provider (Twilio/AWS SNS)
- [ ] Add email template system
- [ ] Implement notification queuing and retry logic
- [ ] Add monitoring and alerting

### Service Communication
- [ ] Test end-to-end microservices communication
- [ ] Implement circuit breaker patterns
- [ ] Add distributed tracing

## Related Documentation
- [Service Architecture Guidelines](../architecture/service-guidelines.md)
- [Docker Development Workflow](./docker-development.md)
- [Microservices Communication Patterns](../architecture/microservices-patterns.md)
