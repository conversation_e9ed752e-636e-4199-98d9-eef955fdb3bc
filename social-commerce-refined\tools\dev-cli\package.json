{"name": "dev-cli", "version": "1.0.0", "description": "Development CLI for Social Commerce Platform", "main": "dist/index.js", "bin": {"dev": "./dist/index.js"}, "scripts": {"build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "test": "echo \"Error: no test specified\" && exit 1", "prepare": "npm run build && chmod +x dist/index.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"chalk": "^5.4.1", "commander": "^14.0.0", "docker-compose": "^1.2.0", "dotenv": "^16.5.0", "figlet": "^1.8.1", "fs-extra": "^11.3.0", "inquirer": "^12.6.1"}, "devDependencies": {"@types/figlet": "^1.7.0", "@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.8", "@types/node": "^22.15.21", "typescript": "^5.8.3"}}