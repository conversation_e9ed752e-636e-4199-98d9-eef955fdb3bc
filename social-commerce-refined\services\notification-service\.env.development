# Server Configuration
NODE_ENV=development
HTTP_PORT=3001

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=1111
DB_DATABASE=user_service
DB_SYNCHRONIZE=true
DB_LOGGING=true

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=1h

# Microservice Configuration
MICROSERVICE_HOST=localhost
MICROSERVICE_PORT=3001

# RabbitMQ Configuration
RABBITMQ_URL=amqp://admin:admin@localhost:5672
RABBITMQ_QUEUE=user_queue
NOTIFICATION_QUEUE=notification_queue

# Frontend URL (for email verification links)
FRONTEND_URL=http://localhost:3000
