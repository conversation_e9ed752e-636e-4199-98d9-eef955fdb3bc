import * as childProcess from 'child_process';
import * as chalk from 'chalk';
import { getDockerComposePath } from './paths';

/**
 * Run a Docker Compose command
 * @param args Command arguments
 * @returns Promise that resolves when the command completes
 */
export function dockerCompose(args: string[]): Promise<void> {
  return new Promise((resolve, reject) => {
    const dockerComposePath = getDockerComposePath();
    const command = `docker-compose -f ${dockerComposePath} ${args.join(' ')}`;
    
    console.log(chalk.blue(`Running: ${command}`));
    
    const process = childProcess.exec(command);
    
    process.stdout?.on('data', (data) => {
      console.log(data.toString().trim());
    });
    
    process.stderr?.on('data', (data) => {
      console.error(chalk.red(data.toString().trim()));
    });
    
    process.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Docker Compose command failed with code ${code}`));
      }
    });
  });
}

/**
 * Start Docker Compose services
 * @param services Services to start (empty for all)
 * @returns Promise that resolves when the services are started
 */
export function startServices(services: string[] = []): Promise<void> {
  const args = ['up', '-d'];
  
  if (services.length > 0) {
    args.push(...services);
  }
  
  return dockerCompose(args);
}

/**
 * Stop Docker Compose services
 * @param services Services to stop (empty for all)
 * @returns Promise that resolves when the services are stopped
 */
export function stopServices(services: string[] = []): Promise<void> {
  const args = ['stop'];
  
  if (services.length > 0) {
    args.push(...services);
  }
  
  return dockerCompose(args);
}

/**
 * Get the status of Docker Compose services
 * @returns Promise that resolves with the status output
 */
export function getServicesStatus(): Promise<string> {
  return new Promise((resolve, reject) => {
    const dockerComposePath = getDockerComposePath();
    const command = `docker-compose -f ${dockerComposePath} ps`;
    
    childProcess.exec(command, (error, stdout, stderr) => {
      if (error) {
        reject(error);
        return;
      }
      
      if (stderr) {
        reject(new Error(stderr));
        return;
      }
      
      resolve(stdout);
    });
  });
}
