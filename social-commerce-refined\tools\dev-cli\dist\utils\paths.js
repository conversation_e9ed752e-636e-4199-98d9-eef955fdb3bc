"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getRootDir = getRootDir;
exports.getServicesDir = getServicesDir;
exports.getInfrastructureDir = getInfrastructureDir;
exports.getDockerComposePath = getDockerComposePath;
exports.getServiceDir = getServiceDir;
exports.getAllServices = getAllServices;
exports.serviceExists = serviceExists;
const path = require("path");
const fs = require("fs-extra");
function getRootDir() {
    let currentDir = process.cwd();
    while (currentDir !== path.parse(currentDir).root) {
        const packageJsonPath = path.join(currentDir, 'package.json');
        if (fs.existsSync(packageJsonPath)) {
            const packageJson = fs.readJsonSync(packageJsonPath);
            if (packageJson.name === 'social-commerce-refined') {
                return currentDir;
            }
        }
        currentDir = path.dirname(currentDir);
    }
    return process.cwd();
}
function getServicesDir() {
    return path.join(getRootDir(), 'services');
}
function getInfrastructureDir() {
    return path.join(getRootDir(), 'infrastructure');
}
function getDockerComposePath() {
    return path.join(getInfrastructureDir(), 'docker', 'docker-compose.yml');
}
function getServiceDir(service) {
    return path.join(getServicesDir(), `${service}-service`);
}
function getAllServices() {
    const servicesDir = getServicesDir();
    if (!fs.existsSync(servicesDir)) {
        return [];
    }
    return fs
        .readdirSync(servicesDir)
        .filter(dir => dir.endsWith('-service'))
        .map(dir => dir.replace('-service', ''));
}
function serviceExists(service) {
    const serviceDir = getServiceDir(service);
    return fs.existsSync(serviceDir);
}
//# sourceMappingURL=paths.js.map