{"context":"LoggingInterceptor","level":"error","message":"[req-1748072632992-tv0p7hcok] [dc1c2c60-ef6b-4ab9-bac5-dfb3bd332250] GET /api/health 500 - 47ms - The following path is invalid (should be X:\\...): /","stack":[null],"timestamp":"2025-05-24T07:43:53.040Z"}
{"context":"AllExceptionsFilter","level":"error","message":"GET /api/health 500 - The following path is invalid (should be X:\\...): /","stack":["InvalidPathError: The following path is invalid (should be X:\\...): /\n    at checkWin32 (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\check-disk-space\\dist\\check-disk-space.cjs:155:35)\n    at DiskHealthIndicator.checkDiskSpace (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\check-disk-space\\dist\\check-disk-space.cjs:202:16)\n    at DiskHealthIndicator.<anonymous> (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\@nestjs\\terminus\\dist\\health-indicator\\disk\\disk.health.js:75:47)\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\@nestjs\\terminus\\dist\\health-indicator\\disk\\disk.health.js:20:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\@nestjs\\terminus\\dist\\health-indicator\\disk\\disk.health.js:16:12)\n    at DiskHealthIndicator.checkStorage (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\@nestjs\\terminus\\dist\\health-indicator\\disk\\disk.health.js:73:16)\n    at C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\dist\\main.js:1481:35\n    at HealthCheckExecutor.<anonymous> (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\@nestjs\\terminus\\dist\\health-check\\health-check-executor.service.js:64:135)"],"timestamp":"2025-05-24T07:43:53.042Z"}
{"context":"RoutingService","level":"error","message":"Error forwarding request to http://localhost:3001/api/users: ","stack":["AggregateError\n    at AxiosError.from (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\axios\\dist\\node\\axios.cjs:863:14)\n    at RedirectableRequest.handleRequestError (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\axios\\dist\\node\\axios.cjs:3187:25)\n    at RedirectableRequest.emit (node:events:518:28)\n    at eventHandlers.<computed> (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\follow-redirects\\index.js:49:24)\n    at ClientRequest.emit (node:events:518:28)\n    at emitErrorEvent (node:_http_client:104:11)\n    at Socket.socketErrorListener (node:_http_client:518:5)\n    at Socket.emit (node:events:518:28)\n    at emitErrorNT (node:internal/streams/destroy:170:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:129:3)\n    at Axios.request (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CircuitBreakerService.execute (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\dist\\main.js:585:28)"],"timestamp":"2025-05-24T08:44:14.903Z"}
{"context":"LoggingInterceptor","level":"error","message":"[req-1748076254877-n7jpfutjl] [0a6acde0-8d30-4cda-a35e-22928b96d00a] GET /api/users 500 - 27ms - Internal Server Error","stack":[null],"timestamp":"2025-05-24T08:44:14.905Z"}
{"context":"AllExceptionsFilter","level":"error","message":"GET /api/users 500 - Internal Server Error","stack":["HttpException: Internal Server Error\n    at C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\dist\\main.js:506:49\n    at Observable.init [as _subscribe] (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\rxjs\\dist\\cjs\\internal\\observable\\throwError.js:8:64)\n    at Observable._trySubscribe (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\rxjs\\dist\\cjs\\internal\\Observable.js:41:25)\n    at C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\rxjs\\dist\\cjs\\internal\\Observable.js:35:31\n    at Object.errorContext (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\rxjs\\dist\\cjs\\internal\\util\\errorContext.js:22:9)\n    at Observable.subscribe (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\rxjs\\dist\\cjs\\internal\\Observable.js:26:24)\n    at C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\rxjs\\dist\\cjs\\internal\\operators\\catchError.js:17:31\n    at OperatorSubscriber._this._error (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\rxjs\\dist\\cjs\\internal\\operators\\OperatorSubscriber.js:43:21)\n    at Subscriber.error (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\rxjs\\dist\\cjs\\internal\\Subscriber.js:60:18)\n    at Subscriber._error (C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\social-commerce-refined\\services\\api-gateway\\node_modules\\rxjs\\dist\\cjs\\internal\\Subscriber.js:84:30)"],"timestamp":"2025-05-24T08:44:14.906Z"}
