import { Modu<PERSON> } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { UserController } from './controllers/user.controller';
import { StoreController } from './controllers/store.controller';
import { ProductController } from './controllers/product.controller';
import { CartController } from './controllers/cart.controller';
import { OrderController } from './controllers/order.controller';
import { RoutingService } from './services/routing.service';

@Module({
  imports: [
    HttpModule,
    ConfigModule,
  ],
  controllers: [
    UserController,
    StoreController,
    ProductController,
    CartController,
    OrderController,
  ],
  providers: [
    RoutingService,
  ],
  exports: [
    RoutingService,
  ],
})
export class RoutingModule {}
