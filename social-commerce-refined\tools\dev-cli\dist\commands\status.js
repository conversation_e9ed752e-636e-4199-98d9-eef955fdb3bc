"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.statusCommand = statusCommand;
const chalk = require("chalk");
const docker_1 = require("../utils/docker");
function statusCommand(program) {
    program
        .command('status')
        .description('Check service status')
        .action(async () => {
        try {
            console.log(chalk.blue('Checking service status...'));
            const status = await (0, docker_1.getServicesStatus)();
            console.log(chalk.green('Service Status:'));
            console.log(status);
        }
        catch (error) {
            console.error(chalk.red(`Error: ${error.message}`));
            process.exit(1);
        }
    });
}
//# sourceMappingURL=status.js.map