import { Injectable, CanActivate, ExecutionContext, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { Observable } from 'rxjs';
import { Request } from 'express';

interface RateLimitOptions {
  /**
   * The time window in milliseconds
   */
  windowMs: number;
  
  /**
   * The maximum number of requests allowed in the time window
   */
  max: number;
  
  /**
   * The message to return when rate limit is exceeded
   */
  message?: string;
}

/**
 * A guard that implements rate limiting for specific routes
 */
@Injectable()
export class RateLimitGuard implements CanActivate {
  private readonly logger = new Logger(RateLimitGuard.name);
  private readonly requestMap = new Map<string, number[]>();
  private readonly options: RateLimitOptions;

  constructor(options?: Partial<RateLimitOptions>) {
    this.options = {
      windowMs: options?.windowMs || 60 * 1000, // 1 minute
      max: options?.max || 100, // 100 requests per minute
      message: options?.message || 'Too many requests, please try again later.',
    };
    
    // Clean up old requests every minute
    setInterval(() => this.cleanupOldRequests(), 60 * 1000);
  }

  /**
   * Check if the request can proceed
   * @param context The execution context
   * @returns Whether the request can proceed
   */
  canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    const key = this.generateKey(request);
    
    // Get the current time
    const now = Date.now();
    
    // Get the requests for this key
    const requests = this.requestMap.get(key) || [];
    
    // Filter out requests outside the time window
    const recentRequests = requests.filter(time => time > now - this.options.windowMs);
    
    // Check if the number of recent requests exceeds the limit
    if (recentRequests.length >= this.options.max) {
      this.logger.warn(`Rate limit exceeded for ${key}`);
      throw new HttpException(this.options.message, HttpStatus.TOO_MANY_REQUESTS);
    }
    
    // Add the current request
    recentRequests.push(now);
    this.requestMap.set(key, recentRequests);
    
    return true;
  }

  /**
   * Generate a key for the request
   * @param request The request
   * @returns A key for the request
   */
  private generateKey(request: Request): string {
    // Use IP address as the key
    const ip = request.ip || 
               request.connection.remoteAddress || 
               request.headers['x-forwarded-for'] || 
               'unknown';
    
    return `${ip}:${request.path}`;
  }

  /**
   * Clean up old requests
   */
  private cleanupOldRequests(): void {
    const now = Date.now();
    
    for (const [key, requests] of this.requestMap.entries()) {
      // Filter out requests outside the time window
      const recentRequests = requests.filter(time => time > now - this.options.windowMs);
      
      if (recentRequests.length === 0) {
        // Remove the key if there are no recent requests
        this.requestMap.delete(key);
      } else {
        // Update the requests for this key
        this.requestMap.set(key, recentRequests);
      }
    }
  }
}
