import { Module, DynamicModule } from '@nestjs/common';
import { DiscoveryModule, MetadataScanner, Reflector } from '@nestjs/core';
import { RabbitMQModule } from './rabbitmq.module';
import { EventSubscriberExplorerService } from './event-subscriber-explorer.service';
import { RabbitMQOptions } from '../interfaces/rabbitmq-options.interface';

@Module({})
export class EventSubscriberModule {
  static register(options?: RabbitMQOptions): DynamicModule {
    return {
      module: EventSubscriberModule,
      imports: [DiscoveryModule, RabbitMQModule.register(options)],
      providers: [
        EventSubscriberExplorerService,
        MetadataScanner,
        Reflector,
      ],
      exports: [],
    };
  }

  static registerAsync(options: {
    useFactory: (...args: any[]) => Promise<RabbitMQOptions> | RabbitMQOptions;
    inject?: any[];
  }): DynamicModule {
    return {
      module: EventSubscriberModule,
      imports: [DiscoveryModule, RabbitMQModule.registerAsync(options)],
      providers: [
        EventSubscriberExplorerService,
        MetadataScanner,
        Reflector,
      ],
      exports: [],
    };
  }
}
