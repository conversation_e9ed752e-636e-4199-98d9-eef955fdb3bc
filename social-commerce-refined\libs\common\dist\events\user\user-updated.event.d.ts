import { BaseEvent } from '../base-event.interface';
export declare class UserUpdatedEvent implements BaseEvent<UserUpdatedPayload> {
    id: string;
    type: string;
    version: string;
    timestamp: string;
    producer: string;
    payload: UserUpdatedPayload;
    constructor(payload: UserUpdatedPayload);
}
export interface UserUpdatedPayload {
    id: string;
    email: string;
    name: string;
    isEmailVerified: boolean;
    updatedAt: string;
}
