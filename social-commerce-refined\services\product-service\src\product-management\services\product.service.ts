import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
  Inject,
} from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom, timeout } from 'rxjs';
import { ProductRepository, ProductFilters, ProductSearchOptions } from '../repositories/product.repository';
import { Product, ProductStatus } from '../entities/product.entity';
import { CreateProductDto, UpdateProductDto } from '../dto';

@Injectable()
export class ProductService {
  private readonly logger = new Logger(ProductService.name);
  private readonly MAX_PRODUCTS_PER_STORE = 100; // Business rule: Maximum products per store

  constructor(
    private readonly productRepository: ProductRepository,
    @Inject('STORE_SERVICE') private readonly storeService: ClientProxy,
  ) {}

  /**
   * Create a new product
   */
  async create(createProductDto: CreateProductDto, userId: string): Promise<Product> {
    this.logger.log(`Creating product for store ${createProductDto.storeId} by user ${userId}`);

    // Verify store exists and user owns it
    await this.verifyStoreOwnership(createProductDto.storeId, userId);

    // Business Rule: Check maximum products per store limit
    const storeProductCount = await this.productRepository.countByStoreId(createProductDto.storeId);
    if (storeProductCount >= this.MAX_PRODUCTS_PER_STORE) {
      this.logger.warn(`Store ${createProductDto.storeId} attempted to create product but has reached limit (${storeProductCount}/${this.MAX_PRODUCTS_PER_STORE})`);
      throw new BadRequestException(
        `Maximum ${this.MAX_PRODUCTS_PER_STORE} products allowed per store. You currently have ${storeProductCount} products.`
      );
    }

    // Validate SKU uniqueness if provided
    if (createProductDto.sku) {
      await this.validateSkuUniqueness(createProductDto.sku, createProductDto.storeId);
    }

    // Create product
    const product = this.productRepository.create({
      ...createProductDto,
      status: createProductDto.status || ProductStatus.ACTIVE,
      stock: createProductDto.stock || 0,
      images: createProductDto.images || [],
      rating: 0,
      reviewCount: 0,
      salesCount: 0,
      viewCount: 0,
      isActive: true,
    });

    const savedProduct = await this.productRepository.save(product);
    this.logger.log(`Product created successfully with ID: ${savedProduct.id}`);

    return savedProduct;
  }

  /**
   * Find all products with filtering and pagination
   */
  async findAll(
    filters: ProductFilters = {},
    options: ProductSearchOptions = {},
  ): Promise<{ products: Product[]; total: number; page: number; limit: number }> {
    this.logger.log('Finding products with filters', { filters, options });

    const { page = 1, limit = 10 } = options;
    const result = await this.productRepository.searchProducts(filters, options);

    return {
      ...result,
      page,
      limit,
    };
  }

  /**
   * Find products by store ID
   */
  async findByStore(storeId: string): Promise<Product[]> {
    this.logger.log(`Finding products for store: ${storeId}`);

    // Verify store exists
    await this.verifyStoreExists(storeId);

    return this.productRepository.findActiveByStoreId(storeId);
  }

  /**
   * Find one product by ID
   */
  async findOne(id: string): Promise<Product> {
    this.logger.log(`Finding product with ID: ${id}`);

    const product = await this.productRepository.findOne({
      where: { id, isActive: true },
    });

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    // Increment view count
    await this.productRepository.incrementViewCount(id);

    return product;
  }

  /**
   * Update a product
   */
  async update(id: string, updateProductDto: UpdateProductDto, userId: string): Promise<Product> {
    this.logger.log(`Updating product ${id} by user ${userId}`);

    const product = await this.productRepository.findOne({
      where: { id, isActive: true },
    });

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    // Verify user owns the store
    await this.verifyStoreOwnership(product.storeId, userId);

    // Validate SKU uniqueness if being updated
    if (updateProductDto.sku && updateProductDto.sku !== product.sku) {
      await this.validateSkuUniqueness(updateProductDto.sku, product.storeId, id);
    }

    // Update product properties
    Object.assign(product, updateProductDto);

    const updatedProduct = await this.productRepository.save(product);
    this.logger.log(`Product updated successfully: ${id}`);

    return updatedProduct;
  }

  /**
   * Remove a product (soft delete)
   */
  async remove(id: string, userId: string): Promise<void> {
    this.logger.log(`Removing product ${id} by user ${userId}`);

    const product = await this.productRepository.findOne({
      where: { id, isActive: true },
    });

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    // Verify user owns the store
    await this.verifyStoreOwnership(product.storeId, userId);

    // Soft delete by setting isActive to false
    product.isActive = false;
    await this.productRepository.save(product);

    this.logger.log(`Product removed successfully: ${id}`);
  }

  /**
   * Search products
   */
  async search(
    query: string,
    filters: ProductFilters = {},
    options: ProductSearchOptions = {},
  ): Promise<{ products: Product[]; total: number; page: number; limit: number }> {
    this.logger.log(`Searching products with query: "${query}"`);

    const searchFilters = { ...filters, search: query };
    return this.findAll(searchFilters, options);
  }

  /**
   * Get product limits and usage information for a store
   */
  async getProductLimits(storeId: string): Promise<{
    maxProducts: number;
    currentProducts: number;
    remainingProducts: number;
    canCreateMore: boolean;
  }> {
    const currentProducts = await this.productRepository.countByStoreId(storeId);
    const remainingProducts = Math.max(0, this.MAX_PRODUCTS_PER_STORE - currentProducts);
    
    return {
      maxProducts: this.MAX_PRODUCTS_PER_STORE,
      currentProducts,
      remainingProducts,
      canCreateMore: currentProducts < this.MAX_PRODUCTS_PER_STORE,
    };
  }

  /**
   * Verify store exists via Store Service
   */
  private async verifyStoreExists(storeId: string): Promise<void> {
    this.logger.log(`Verifying store exists: ${storeId}`);
    
    try {
      const response = await firstValueFrom(
        this.storeService.send('find_store_by_id', storeId).pipe(timeout(5000))
      );

      if (!response || !response.id) {
        this.logger.warn(`Store verification failed: Store ${storeId} not found`);
        throw new NotFoundException('Store not found');
      }
      
      this.logger.log(`Store verification successful: ${storeId}`);
    } catch (error) {
      this.logger.error(`Failed to verify store ${storeId}: ${error.message}`);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Failed to verify store - Store Service unavailable');
    }
  }

  /**
   * Verify store ownership via Store Service
   */
  private async verifyStoreOwnership(storeId: string, userId: string): Promise<void> {
    this.logger.log(`Verifying store ownership: store ${storeId} by user ${userId}`);
    
    try {
      const response = await firstValueFrom(
        this.storeService.send('verify_store_ownership', { storeId, userId }).pipe(timeout(5000))
      );

      if (!response || !response.isOwner) {
        this.logger.warn(`Store ownership verification failed: User ${userId} does not own store ${storeId}`);
        throw new ForbiddenException('You do not have permission to manage this store');
      }
      
      this.logger.log(`Store ownership verification successful: ${storeId}`);
    } catch (error) {
      this.logger.error(`Failed to verify store ownership ${storeId}: ${error.message}`);
      if (error instanceof ForbiddenException || error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Failed to verify store ownership - Store Service unavailable');
    }
  }

  /**
   * Validate SKU uniqueness within store
   */
  private async validateSkuUniqueness(sku: string, storeId: string, excludeProductId?: string): Promise<void> {
    const existingProduct = await this.productRepository.findOne({
      where: { sku, storeId, isActive: true },
    });

    if (existingProduct && existingProduct.id !== excludeProductId) {
      throw new BadRequestException(`SKU "${sku}" already exists in this store`);
    }
  }
}
