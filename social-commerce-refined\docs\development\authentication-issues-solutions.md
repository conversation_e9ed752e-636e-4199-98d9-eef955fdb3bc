# Authentication System - Issues & Solutions Documentation

## Overview
This document details all issues encountered and solutions implemented during the User Authentication Flow development for the social commerce platform.

## Critical Issues & Solutions

### 1. **Password Hash Corruption Bug** ⭐ **CRITICAL FIX**

**Problem:** Users could login successfully initially, but subsequent login attempts would fail with "Invalid credentials" after some time.

**Symptoms:**
- Initial login works perfectly
- After user updates (like `lastLogin`), password validation fails
- Backend logs show correct password received but validation returns false
- Issue affects all users over time

**Root Cause:**
The `@BeforeUpdate()` hook in the User entity was re-hashing already hashed passwords every time a user was updated.

**Code Before (Buggy):**
```typescript
// File: services/user-service/src/authentication/entities/user.entity.ts
@BeforeInsert()
@BeforeUpdate()
async hashPassword() {
  // Only hash the password if it has been modified
  if (this.password) {
    const salt = await bcrypt.genSalt();
    this.password = await bcrypt.hash(this.password, salt);
  }
}
```

**Code After (Fixed):**
```typescript
// File: services/user-service/src/authentication/entities/user.entity.ts
@BeforeInsert()
async hashPasswordOnInsert() {
  // Hash password on user creation
  if (this.password) {
    const salt = await bcrypt.genSalt();
    this.password = await bcrypt.hash(this.password, salt);
  }
}

@BeforeUpdate()
async hashPasswordOnUpdate() {
  // Only hash the password if it has been modified and is not already hashed
  if (this.password && !this.password.startsWith('$2b$')) {
    const salt = await bcrypt.genSalt();
    this.password = await bcrypt.hash(this.password, salt);
  }
}
```

**Solution Details:**
1. **Separated lifecycle hooks** - Different methods for insert vs update
2. **Added bcrypt hash detection** - Check for `$2b$` prefix (bcrypt format)
3. **Prevented double-hashing** - Only hash if password is not already hashed
4. **Preserved password integrity** - Maintains hash across user updates

**Impact:**
- ✅ Users can now login multiple times
- ✅ Password hashes remain stable across updates
- ✅ No more authentication failures after time

### 2. **Registration Data Structure Mismatch**

**Problem:** Registration failing with validation errors: "property firstName should not exist, property lastName should not exist".

**Root Cause:**
Frontend was sending profile data at the top level instead of nested in a `profile` object as expected by the backend DTO.

**Frontend Before (Incorrect):**
```javascript
// File: frontend/src/pages/register.tsx
await registerUser({
  firstName: data.firstName,
  lastName: data.lastName,
  email: data.email,
  password: data.password,
});
```

**Frontend After (Correct):**
```javascript
// File: frontend/src/pages/register.tsx
await registerUser({
  email: data.email,
  password: data.password,
  profile: {
    firstName: data.firstName,
    lastName: data.lastName,
  },
});
```

**Backend DTO Structure:**
```typescript
// File: services/user-service/src/authentication/dto/create-user.dto.ts
export class CreateUserDto {
  @IsEmail()
  email: string;

  @IsString()
  @MinLength(8)
  password: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => CreateProfileDto)
  profile?: CreateProfileDto;
}
```

**Solution:** Updated frontend registration to match backend DTO structure with nested profile object.

### 3. **Frontend-Backend API Integration Issue**

**Problem:** Frontend login requests failing with "Invalid credentials" despite correct passwords.

**Debugging Process:**
1. **Added comprehensive logging** to track authentication flow
2. **Implemented persistent debugging** using localStorage
3. **Added password character analysis** to identify data corruption
4. **Enhanced backend request logging** to verify received data

**Key Debugging Tools Implemented:**
```javascript
// Persistent localStorage debugging
localStorage.setItem('loginDebug', JSON.stringify({
  timestamp: new Date().toISOString(),
  email: data.email,
  step: 'form_submitted',
  passwordDebug: {
    length: data.password.length,
    firstChar: data.password.charAt(0),
    lastChar: data.password.charAt(data.password.length - 1),
    hasUppercase: /[A-Z]/.test(data.password),
    hasLowercase: /[a-z]/.test(data.password),
    hasNumbers: /[0-9]/.test(data.password),
    hasSpecialChars: /[!@#$%^&*]/.test(data.password)
  }
}));
```

**Discovery:** Frontend was correctly sending data; the issue was in the backend password validation due to hash corruption.

### 4. **Form Submission Page Refresh Issue**

**Problem:** Login form causing page refreshes that prevented proper error handling and debugging.

**Solution:** Added `e.preventDefault()` to prevent default form submission behavior:

```javascript
// File: frontend/src/pages/login.tsx
<form onSubmit={(e) => {
  e.preventDefault(); // Prevent default form submission
  return handleSubmit(onSubmit)(e);
}}>
```

## Files Modified

### Backend Files:
1. **`services/user-service/src/authentication/entities/user.entity.ts`**
   - Fixed password hashing hooks to prevent double-hashing
   - Added bcrypt hash detection logic

### Frontend Files:
1. **`frontend/src/pages/register.tsx`**
   - Updated registration data structure to match backend DTO
   - Fixed profile data nesting

2. **`frontend/src/pages/login.tsx`**
   - Added comprehensive debugging (can be cleaned up)
   - Added form submission prevention

## Testing Results

### ✅ **Verified Working Features:**
- User Registration with nested profile data
- Initial Login with JWT token generation
- Persistent Login (multiple logins without corruption)
- Dashboard Redirect after successful authentication
- Frontend form validation
- Backend password validation
- User updates without password corruption

### ✅ **Test Cases Passed:**
- Registration with valid data → Success
- Login with correct credentials → Success
- Multiple logins with same user → Success
- Login after user updates (lastLogin) → Success
- Frontend-backend integration → Working perfectly

## Debugging Methodology

### 1. **Systematic Approach:**
- Start with frontend form validation
- Track data through API calls
- Verify backend request processing
- Check database operations
- Validate password hashing/comparison

### 2. **Debugging Tools Used:**
- Browser console logging
- localStorage persistence
- Backend request logging
- Docker container logs
- Database query inspection

### 3. **Key Insights:**
- Always verify data at each layer of the stack
- Use persistent debugging for page refresh scenarios
- Check entity lifecycle hooks for unintended side effects
- Validate DTO structures match frontend data

## Security Considerations

### Current Implementation:
- ✅ Password hashing with bcrypt
- ✅ JWT token authentication
- ✅ Input validation with class-validator
- ✅ Secure password storage

### Recommendations for Enhancement:
- Add rate limiting for login attempts
- Implement account lockout after failed attempts
- Add JWT refresh token mechanism
- Enhance password strength requirements
- Implement email verification flow

## Next Steps

### Immediate Cleanup:
1. Remove debugging code from login.tsx
2. Remove debug logging from AuthContext.tsx
3. Clean up authentication service debug logs

### Future Enhancements:
1. Implement proper error handling for authentication failures
2. Add password strength validation on frontend
3. Implement JWT refresh token mechanism
4. Add comprehensive audit logging

## Additional Technical Details

### JWT Token Configuration
```typescript
// File: services/user-service/src/authentication/services/authentication.service.ts
const payload = {
  sub: user.id,
  email: user.email,
  role: user.role,
};

const accessToken = this.jwtService.sign(payload);
// Token expires in 1 hour (3600 seconds)
```

### Password Validation Flow
```typescript
// File: services/user-service/src/authentication/entities/user.entity.ts
async validatePassword(password: string): Promise<boolean> {
  return bcrypt.compare(password, this.password);
}
```

### Frontend Authentication Context
```typescript
// File: frontend/src/context/AuthContext.tsx
const login = async (email: string, password: string) => {
  const response = await authApi.login(email, password);
  const token = response.accessToken;
  localStorage.setItem('token', token);

  const decoded = jwtDecode(token);
  setUser({
    id: decoded.sub,
    email: decoded.email,
    name: decoded.name || '',
    role: decoded.role || 'user',
  });

  router.push('/dashboard');
};
```

### API Endpoints
- **POST /api/auth/register** - User registration
- **POST /api/auth/login** - User login
- **GET /api/auth/profile** - Get user profile
- **GET /api/health** - Service health check

### Database Schema
```sql
-- Users table
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR UNIQUE NOT NULL,
  password VARCHAR NOT NULL,
  phone VARCHAR,
  role VARCHAR DEFAULT 'user',
  is_email_verified BOOLEAN DEFAULT false,
  is_phone_verified BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  last_login TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Profiles table
CREATE TABLE profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  first_name VARCHAR NOT NULL,
  last_name VARCHAR NOT NULL,
  display_name VARCHAR,
  bio TEXT,
  avatar_url VARCHAR,
  cover_image_url VARCHAR,
  date_of_birth DATE,
  gender VARCHAR,
  location VARCHAR,
  website VARCHAR,
  social_links JSONB,
  preferences JSONB,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

---

**Status:** ✅ **RESOLVED** - Authentication system is fully functional and stable.
**Date:** 2025-05-26
**Impact:** Critical - Enables all user authentication flows for the platform.
