import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SendEmailDto } from '../dto/send-email.dto';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);

  constructor(private readonly configService: ConfigService) {}

  async sendEmail(sendEmailDto: SendEmailDto): Promise<void> {
    this.logger.log(`Sending email to: ${sendEmailDto.to}`);
    this.logger.log(`Subject: ${sendEmailDto.subject}`);

    try {
      // For now, we'll simulate email sending
      // In production, integrate with email service like SendGrid, AWS SES, etc.
      await this.simulateEmailSending(sendEmailDto);
      
      this.logger.log(`Email sent successfully to: ${sendEmailDto.to}`);
    } catch (error) {
      this.logger.error(`Failed to send email to: ${sendEmailDto.to}`, error.stack);
      throw error;
    }
  }

  async sendWelcomeEmail(email: string, name: string): Promise<void> {
    this.logger.log(`Sending welcome email to: ${email}`);

    const welcomeEmailDto: SendEmailDto = {
      to: email,
      subject: 'Welcome to Social Commerce Platform',
      content: `
        <h1>Welcome ${name}!</h1>
        <p>Thank you for joining our Social Commerce Platform.</p>
        <p>We're excited to have you as part of our community!</p>
        <p>You can now:</p>
        <ul>
          <li>Create and manage your store</li>
          <li>Browse and purchase products</li>
          <li>Join group buying opportunities</li>
          <li>Connect with other users</li>
        </ul>
        <p>Best regards,<br>The Social Commerce Team</p>
      `,
    };

    await this.sendEmail(welcomeEmailDto);
  }

  async sendVerificationEmail(email: string, verificationCode: string): Promise<void> {
    this.logger.log(`Sending verification email to: ${email}`);

    const verificationEmailDto: SendEmailDto = {
      to: email,
      subject: 'Verify Your Email Address',
      content: `
        <h1>Email Verification</h1>
        <p>Please verify your email address by using the following verification code:</p>
        <h2 style="color: #007bff; font-family: monospace;">${verificationCode}</h2>
        <p>This code will expire in 15 minutes.</p>
        <p>If you didn't request this verification, please ignore this email.</p>
        <p>Best regards,<br>The Social Commerce Team</p>
      `,
    };

    await this.sendEmail(verificationEmailDto);
  }

  async sendPasswordResetEmail(email: string, resetToken: string): Promise<void> {
    this.logger.log(`Sending password reset email to: ${email}`);

    const resetEmailDto: SendEmailDto = {
      to: email,
      subject: 'Password Reset Request',
      content: `
        <h1>Password Reset</h1>
        <p>You requested a password reset for your Social Commerce Platform account.</p>
        <p>Use the following token to reset your password:</p>
        <h2 style="color: #dc3545; font-family: monospace;">${resetToken}</h2>
        <p>This token will expire in 1 hour.</p>
        <p>If you didn't request this reset, please ignore this email and your password will remain unchanged.</p>
        <p>Best regards,<br>The Social Commerce Team</p>
      `,
    };

    await this.sendEmail(resetEmailDto);
  }

  async sendOrderConfirmationEmail(email: string, orderId: string, orderDetails: any): Promise<void> {
    this.logger.log(`Sending order confirmation email to: ${email}`);

    const orderEmailDto: SendEmailDto = {
      to: email,
      subject: `Order Confirmation - ${orderId}`,
      content: `
        <h1>Order Confirmation</h1>
        <p>Thank you for your order! Your order has been confirmed.</p>
        <p><strong>Order ID:</strong> ${orderId}</p>
        <p><strong>Order Details:</strong></p>
        <pre>${JSON.stringify(orderDetails, null, 2)}</pre>
        <p>We'll send you another email when your order ships.</p>
        <p>Best regards,<br>The Social Commerce Team</p>
      `,
    };

    await this.sendEmail(orderEmailDto);
  }

  private async simulateEmailSending(sendEmailDto: SendEmailDto): Promise<void> {
    // Simulate email sending delay
    await new Promise(resolve => setTimeout(resolve, 100));

    // Log email details for development
    this.logger.debug('=== EMAIL SIMULATION ===');
    this.logger.debug(`To: ${sendEmailDto.to}`);
    this.logger.debug(`Subject: ${sendEmailDto.subject}`);
    this.logger.debug(`Content: ${sendEmailDto.content.substring(0, 100)}...`);
    this.logger.debug('========================');

    // Simulate occasional failures for testing
    const shouldFail = Math.random() < 0.05; // 5% failure rate
    if (shouldFail) {
      throw new Error('Simulated email service failure');
    }
  }

  // TODO: Implement real email service integration
  // private async sendWithSendGrid(sendEmailDto: SendEmailDto): Promise<void> {
  //   // Implementation for SendGrid
  // }
  
  // private async sendWithAWSSES(sendEmailDto: SendEmailDto): Promise<void> {
  //   // Implementation for AWS SES
  // }
}
