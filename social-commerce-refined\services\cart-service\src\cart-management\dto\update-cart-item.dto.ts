import { IsNotEmpty, <PERSON><PERSON><PERSON>ber, <PERSON><PERSON>ptional, Min, IsObject } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateCartItemDto {
  @ApiProperty({
    description: 'New quantity for the cart item',
    example: 3,
    minimum: 1,
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  quantity: number;

  @ApiProperty({
    description: 'Updated selected options',
    example: { size: 'XL', color: 'red' },
    required: false,
  })
  @IsOptional()
  @IsObject()
  selectedOptions?: Record<string, any>;

  @ApiProperty({
    description: 'Updated metadata for the cart item',
    example: { gift_wrap: false },
    required: false,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
