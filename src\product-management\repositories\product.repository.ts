import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Product } from '../entities/product.entity';
import { CreateProductDto } from '../dto/create-product.dto';
import { UpdateProductDto } from '../dto/update-product.dto';

@Injectable()
export class ProductRepository {
  private readonly logger = new Logger(ProductRepository.name);

  constructor(
    @InjectRepository(Product)
    private readonly repository: Repository<Product>,
  ) {}

  async findAll(): Promise<Product[]> {
    this.logger.log('Finding all products');
    return this.repository.find({ relations: ['store'] });
  }

  async findOne(id: string): Promise<Product> {
    this.logger.log(`Finding product with ID: ${id}`);
    const product = await this.repository.findOne({ 
      where: { id },
      relations: ['store'],
    });

    if (!product) {
      throw new NotFoundException(`Product with ID ${id} not found`);
    }

    return product;
  }

  async findByStoreId(storeId: string): Promise<Product[]> {
    this.logger.log(`Finding products for store with ID: ${storeId}`);
    return this.repository.find({ 
      where: { storeId },
      relations: ['store'],
    });
  }

  async create(createProductDto: CreateProductDto): Promise<Product> {
    this.logger.log(`Creating product with name: ${createProductDto.name}`);
    
    const product = this.repository.create(createProductDto);
    
    return this.repository.save(product);
  }

  async update(id: string, updateProductDto: UpdateProductDto): Promise<Product> {
    this.logger.log(`Updating product with ID: ${id}`);
    
    const product = await this.findOne(id);
    
    // Update product properties
    Object.assign(product, updateProductDto);
    
    return this.repository.save(product);
  }

  async remove(id: string): Promise<void> {
    this.logger.log(`Removing product with ID: ${id}`);
    
    const product = await this.findOne(id);
    
    await this.repository.remove(product);
  }

  async updateRating(id: string, rating: number, reviewCount: number): Promise<Product> {
    this.logger.log(`Updating rating for product with ID: ${id}`);
    
    const product = await this.findOne(id);
    
    product.rating = rating;
    product.reviewCount = reviewCount;
    
    return this.repository.save(product);
  }

  async incrementSalesCount(id: string, quantity: number = 1): Promise<Product> {
    this.logger.log(`Incrementing sales count for product with ID: ${id}`);
    
    const product = await this.findOne(id);
    
    product.salesCount += quantity;
    
    return this.repository.save(product);
  }

  async incrementViewCount(id: string): Promise<Product> {
    this.logger.log(`Incrementing view count for product with ID: ${id}`);
    
    const product = await this.findOne(id);
    
    product.viewCount += 1;
    
    return this.repository.save(product);
  }

  async updateQuantity(id: string, quantity: number): Promise<Product> {
    this.logger.log(`Updating quantity for product with ID: ${id}`);
    
    const product = await this.findOne(id);
    
    product.quantity = quantity;
    
    return this.repository.save(product);
  }
}
