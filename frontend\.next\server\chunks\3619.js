"use strict";exports.id=3619,exports.ids=[3619],exports.modules={3619:(i,t,e)=>{e.d(t,{$e:()=>q,Gx:()=>P,J9:()=>L,PF:()=>m,QZ:()=>c,TW:()=>v,YR:()=>r,ZE:()=>s,ZM:()=>p,b:()=>o,d4:()=>k,gd:()=>b,nK:()=>T,ro:()=>u});var a=e(86372);let f=a.g.injectEndpoints({endpoints:i=>({getAffiliateStats:i.query({query:()=>"/affiliate/stats",providesTags:["AffiliateStats"]}),getAffiliateLinks:i.query({query:()=>"/affiliate/links",providesTags:["AffiliateLinks"]}),getAffiliateLink:i.query({query:i=>`/affiliate/links/${i}`,providesTags:(i,t,e)=>[{type:"AffiliateLinks",id:e}]}),createAffiliateLink:i.mutation({query:i=>({url:"/affiliate/links",method:"POST",body:i}),invalidatesTags:["AffiliateLinks","AffiliateStats"]}),deleteAffiliateLink:i.mutation({query:i=>({url:`/affiliate/links/${i}`,method:"DELETE"}),invalidatesTags:["AffiliateLinks","AffiliateStats"]}),getAffiliateCommissions:i.query({query:()=>"/affiliate/commissions",providesTags:["AffiliateCommissions"]}),getAffiliateSettings:i.query({query:()=>"/affiliate/settings",providesTags:["AffiliateSettings"]}),updateAffiliateSettings:i.mutation({query:i=>({url:"/affiliate/settings",method:"PUT",body:i}),invalidatesTags:["AffiliateSettings"]}),requestWithdrawal:i.mutation({query:()=>({url:"/affiliate/withdraw",method:"POST"}),invalidatesTags:["AffiliateCommissions","AffiliateStats"]}),getAffiliatePrograms:i.query({query:({page:i=1,limit:t=10,storeId:e,isActive:a})=>{let f=`/affiliate/programs?page=${i}&limit=${t}`;return e&&(f+=`&storeId=${e}`),void 0!==a&&(f+=`&isActive=${a}`),f},providesTags:["AffiliatePrograms"]}),getAffiliateProgram:i.query({query:i=>`/affiliate/programs/${i}`,providesTags:(i,t,e)=>[{type:"AffiliateProgram",id:e}]}),createAffiliateProgram:i.mutation({query:i=>({url:"/affiliate/programs",method:"POST",body:i}),invalidatesTags:["AffiliatePrograms"]}),updateAffiliateProgram:i.mutation({query:({id:i,data:t})=>({url:`/affiliate/programs/${i}`,method:"PATCH",body:t}),invalidatesTags:(i,t,{id:e})=>[{type:"AffiliateProgram",id:e},"AffiliatePrograms"]}),deleteAffiliateProgram:i.mutation({query:i=>({url:`/affiliate/programs/${i}`,method:"DELETE"}),invalidatesTags:(i,t,e)=>[{type:"AffiliateProgram",id:e},"AffiliatePrograms"]}),getCurrentUserAffiliateAccounts:i.query({query:({page:i=1,limit:t=10})=>`/user/affiliate/accounts?page=${i}&limit=${t}`,providesTags:["AffiliateAccounts"]}),getAffiliateAccount:i.query({query:i=>`/affiliate/accounts/${i}`,providesTags:(i,t,e)=>[{type:"AffiliateAccount",id:e}]}),createAffiliateAccount:i.mutation({query:i=>({url:"/affiliate/accounts",method:"POST",body:i}),invalidatesTags:["AffiliateAccounts"]}),updateAffiliateAccount:i.mutation({query:({id:i,data:t})=>({url:`/affiliate/accounts/${i}`,method:"PATCH",body:t}),invalidatesTags:(i,t,{id:e})=>[{type:"AffiliateAccount",id:e},"AffiliateAccounts"]}),getDetailedAffiliateStats:i.query({query:({accountId:i,startDate:t,endDate:e})=>{let a=`/affiliate/accounts/${i}/stats`,f=new URLSearchParams;t&&f.append("startDate",t),e&&f.append("endDate",e);let s=f.toString();return s&&(a+=`?${s}`),a},providesTags:(i,t,{accountId:e})=>[{type:"AffiliateStats",id:e}]}),getAffiliateReferrals:i.query({query:({accountId:i,status:t,page:e=1,limit:a=10})=>{let f=`/affiliate/accounts/${i}/referrals?page=${e}&limit=${a}`;return t&&(f+=`&status=${t}`),f},providesTags:(i,t,{accountId:e})=>[{type:"AffiliateReferrals",id:e}]}),getDetailedAffiliateCommissions:i.query({query:({accountId:i,status:t,page:e=1,limit:a=10})=>{let f=`/affiliate/accounts/${i}/commissions?page=${e}&limit=${a}`;return t&&(f+=`&status=${t}`),f},providesTags:(i,t,{accountId:e})=>[{type:"AffiliateCommissions",id:e}]}),getAffiliatePayments:i.query({query:({accountId:i,status:t,page:e=1,limit:a=10})=>{let f=`/affiliate/accounts/${i}/payments?page=${e}&limit=${a}`;return t&&(f+=`&status=${t}`),f},providesTags:(i,t,{accountId:e})=>[{type:"AffiliatePayments",id:e}]}),requestAffiliatePayment:i.mutation({query:({accountId:i,amount:t,paymentMethod:e})=>({url:`/affiliate/accounts/${i}/payments/request`,method:"POST",body:{amount:t,paymentMethod:e}}),invalidatesTags:(i,t,{accountId:e})=>[{type:"AffiliatePayments",id:e},{type:"AffiliateAccount",id:e},"AffiliateAccounts"]})})}),{useGetAffiliateStatsQuery:s,useGetAffiliateLinksQuery:r,useGetAffiliateLinkQuery:l,useCreateAffiliateLinkMutation:o,useDeleteAffiliateLinkMutation:u,useGetAffiliateCommissionsQuery:n,useGetAffiliateSettingsQuery:d,useUpdateAffiliateSettingsMutation:g,useRequestWithdrawalMutation:A,useGetAffiliateProgramsQuery:m,useGetAffiliateProgramQuery:y,useCreateAffiliateProgramMutation:c,useUpdateAffiliateProgramMutation:p,useDeleteAffiliateProgramMutation:q,useGetCurrentUserAffiliateAccountsQuery:T,useGetAffiliateAccountQuery:$,useCreateAffiliateAccountMutation:v,useUpdateAffiliateAccountMutation:P,useGetDetailedAffiliateStatsQuery:S,useGetAffiliateReferralsQuery:h,useGetDetailedAffiliateCommissionsQuery:k,useGetAffiliatePaymentsQuery:L,useRequestAffiliatePaymentMutation:b}=f}};