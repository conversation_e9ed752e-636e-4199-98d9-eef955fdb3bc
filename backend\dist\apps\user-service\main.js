/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ([
/* 0 */,
/* 1 */
/***/ ((module) => {

module.exports = require("@nestjs/core");

/***/ }),
/* 2 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.UserModule = void 0;
const common_1 = __webpack_require__(3);
const config_1 = __webpack_require__(4);
const typeorm_1 = __webpack_require__(5);
const microservices_1 = __webpack_require__(6);
const user_controller_1 = __webpack_require__(7);
const user_service_1 = __webpack_require__(8);
const cart_service_1 = __webpack_require__(24);
const user_entity_1 = __webpack_require__(12);
const verification_entity_1 = __webpack_require__(16);
const follower_entity_1 = __webpack_require__(23);
const cart_entity_1 = __webpack_require__(26);
const cart_item_entity_1 = __webpack_require__(27);
const verification_service_1 = __webpack_require__(15);
const follower_service_1 = __webpack_require__(22);
const email_service_1 = __webpack_require__(21);
const common_2 = __webpack_require__(28);
const jwt_1 = __webpack_require__(10);
let UserModule = class UserModule {
};
exports.UserModule = UserModule;
exports.UserModule = UserModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: './.env',
            }),
            common_2.DatabaseModule.forRoot(),
            typeorm_1.TypeOrmModule.forFeature([user_entity_1.User, verification_entity_1.Verification, follower_entity_1.Follower, cart_entity_1.Cart, cart_item_entity_1.CartItem]),
            jwt_1.JwtModule,
            common_2.AuthModule,
            microservices_1.ClientsModule.register([
                {
                    name: common_2.SERVICES.PRODUCT_SERVICE,
                    transport: microservices_1.Transport.TCP,
                    options: {
                        host: process.env.PRODUCT_SERVICE_HOST || 'localhost',
                        port: parseInt(process.env.PRODUCT_SERVICE_PORT) || 3004,
                    },
                },
            ]),
        ],
        controllers: [user_controller_1.UserController],
        providers: [user_service_1.UserService, verification_service_1.VerificationService, follower_service_1.FollowerService, cart_service_1.CartService, email_service_1.EmailService],
    })
], UserModule);


/***/ }),
/* 3 */
/***/ ((module) => {

module.exports = require("@nestjs/common");

/***/ }),
/* 4 */
/***/ ((module) => {

module.exports = require("@nestjs/config");

/***/ }),
/* 5 */
/***/ ((module) => {

module.exports = require("@nestjs/typeorm");

/***/ }),
/* 6 */
/***/ ((module) => {

module.exports = require("@nestjs/microservices");

/***/ }),
/* 7 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5, _6, _7, _8, _9, _10, _11, _12, _13, _14, _15;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.UserController = void 0;
const common_1 = __webpack_require__(3);
const microservices_1 = __webpack_require__(6);
const user_service_1 = __webpack_require__(8);
const verification_service_1 = __webpack_require__(15);
const follower_service_1 = __webpack_require__(22);
const cart_service_1 = __webpack_require__(24);
const verification_entity_1 = __webpack_require__(16);
const user_entity_1 = __webpack_require__(12);
const user_dto_1 = __webpack_require__(13);
const bcrypt = __webpack_require__(11);
let UserController = class UserController {
    constructor(userService, verificationService, followerService, cartService) {
        this.userService = userService;
        this.verificationService = verificationService;
        this.followerService = followerService;
        this.cartService = cartService;
    }
    async create(createUserDto) {
        return this.userService.create(createUserDto);
    }
    async register(registerDto) {
        console.log('User Controller - register called with:', {
            username: registerDto.username,
            email: registerDto.email,
            hasPassword: !!registerDto.password,
            passwordLength: registerDto.password?.length
        });
        try {
            const result = await this.userService.create(registerDto);
            console.log('User Controller - register successful for user:', result.username);
            return result;
        }
        catch (error) {
            console.error('User Controller - register error:', error);
            throw error;
        }
    }
    async createTestUser() {
        console.log('User Controller - createTestUser called');
        try {
            const testUser = {
                username: 'testuser123',
                email: '<EMAIL>',
                password: 'password123',
                role: user_dto_1.UserRole.USER
            };
            console.log('User Controller - createTestUser creating user');
            const result = await this.userService.create(testUser);
            console.log('User Controller - createTestUser successful for user:', result.username);
            return result;
        }
        catch (error) {
            console.error('User Controller - createTestUser error:', error);
            if (error.message && error.message.includes('already exists')) {
                console.log('User Controller - createTestUser user already exists, trying to find it');
                const user = await this.userService.findByUsernameOrEmail('testuser123');
                if (user) {
                    console.log('User Controller - createTestUser found existing user');
                    return this.userService.mapToDto(user);
                }
            }
            throw error;
        }
    }
    async createDirectTestUser() {
        console.log('User Controller - createDirectTestUser called');
        try {
            const passwordHash = await bcrypt.hash('password123', 10);
            console.log('User Controller - createDirectTestUser hashed password:', passwordHash ? 'Hash created' : 'Hash failed');
            const user = new user_entity_1.User();
            user.username = 'directtestuser';
            user.email = '<EMAIL>';
            user.passwordHash = passwordHash;
            user.role = user_dto_1.UserRole.USER;
            user.isEmailVerified = false;
            user.isPhoneVerified = false;
            console.log('User Controller - createDirectTestUser saving user to database');
            const savedUser = await this.userService['userRepository'].save(user);
            console.log('User Controller - createDirectTestUser user saved successfully with ID:', savedUser.id);
            return this.userService.mapToDto(savedUser);
        }
        catch (error) {
            console.error('User Controller - createDirectTestUser error:', error);
            if (error.message && error.message.includes('duplicate key')) {
                console.log('User Controller - createDirectTestUser user already exists, trying to find it');
                const user = await this.userService.findByUsernameOrEmail('directtestuser');
                if (user) {
                    console.log('User Controller - createDirectTestUser found existing user');
                    return this.userService.mapToDto(user);
                }
            }
            throw error;
        }
    }
    async createFixedHashUser() {
        console.log('User Controller - createFixedHashUser called');
        try {
            const fixedPasswordHash = '$2b$10$6XcXQJ.LZqpzfprpKmKCrOBRQjGGZC0gGlEm4KmU/UKY1Ea.Aw5S6';
            console.log('User Controller - createFixedHashUser using fixed password hash');
            const user = new user_entity_1.User();
            user.username = 'fixedhashuser';
            user.email = '<EMAIL>';
            user.passwordHash = fixedPasswordHash;
            user.role = user_dto_1.UserRole.USER;
            user.isEmailVerified = false;
            user.isPhoneVerified = false;
            console.log('User Controller - createFixedHashUser saving user to database');
            const savedUser = await this.userService['userRepository'].save(user);
            console.log('User Controller - createFixedHashUser user saved successfully with ID:', savedUser.id);
            const isPasswordValid = await bcrypt.compare('password123', fixedPasswordHash);
            console.log('User Controller - createFixedHashUser password valid?', isPasswordValid);
            return this.userService.mapToDto(savedUser);
        }
        catch (error) {
            console.error('User Controller - createFixedHashUser error:', error);
            if (error.message && error.message.includes('duplicate key')) {
                console.log('User Controller - createFixedHashUser user already exists, trying to find it');
                const user = await this.userService.findByUsernameOrEmail('fixedhashuser');
                if (user) {
                    console.log('User Controller - createFixedHashUser found existing user');
                    return this.userService.mapToDto(user);
                }
            }
            throw error;
        }
    }
    async createSimpleTestUser() {
        console.log('User Controller - createSimpleTestUser called');
        try {
            const plainPassword = 'password123';
            console.log('User Controller - createSimpleTestUser using plain password:', plainPassword);
            const passwordHash = await bcrypt.hash(plainPassword, 10);
            console.log('User Controller - createSimpleTestUser hashed password:', passwordHash ? 'Hash created' : 'Hash failed');
            const user = new user_entity_1.User();
            user.username = 'simpletestuser';
            user.email = '<EMAIL>';
            user.passwordHash = passwordHash;
            user.role = user_dto_1.UserRole.USER;
            user.isEmailVerified = true;
            user.isPhoneVerified = true;
            console.log('User Controller - createSimpleTestUser saving user to database');
            const savedUser = await this.userService['userRepository'].save(user);
            console.log('User Controller - createSimpleTestUser user saved successfully with ID:', savedUser.id);
            const isPasswordValid = await bcrypt.compare(plainPassword, passwordHash);
            console.log('User Controller - createSimpleTestUser password valid?', isPasswordValid);
            return this.userService.mapToDto(savedUser);
        }
        catch (error) {
            console.error('User Controller - createSimpleTestUser error:', error);
            if (error.message && error.message.includes('duplicate key')) {
                console.log('User Controller - createSimpleTestUser user already exists, trying to find it');
                const user = await this.userService.findByUsernameOrEmail('simpletestuser');
                if (user) {
                    console.log('User Controller - createSimpleTestUser found existing user');
                    return this.userService.mapToDto(user);
                }
            }
            throw error;
        }
    }
    async login(loginUserDto) {
        return this.userService.login(loginUserDto);
    }
    async validateUser(payload) {
        try {
            console.log('User Service - validateUser called with:', { usernameOrEmail: payload.usernameOrEmail });
            const user = await this.userService.findByUsernameOrEmail(payload.usernameOrEmail);
            console.log('User Service - validateUser findByUsernameOrEmail result:', user ? 'User found' : 'User not found');
            if (!user) {
                console.log('User Service - validateUser returning null (user not found)');
                return null;
            }
            console.log('User Service - validateUser attempting login');
            try {
                const loginResult = await this.userService.login({
                    usernameOrEmail: payload.usernameOrEmail,
                    password: payload.password
                });
                console.log('User Service - validateUser login successful');
                return loginResult.user;
            }
            catch (loginError) {
                console.error('User Service - validateUser login error:', loginError);
                return null;
            }
        }
        catch (error) {
            console.error('User Service - validateUser error:', error);
            return null;
        }
    }
    async findAll() {
        console.log('User Service - findAll called');
        const users = await this.userService.findAll();
        console.log('User Service - findAll found users:', users.length);
        users.forEach(user => {
            console.log('User:', {
                id: user.id,
                username: user.username,
                email: user.email,
                role: user.role
            });
        });
        return users;
    }
    async findUserByCredentials(payload) {
        console.log('User Service - findUserByCredentials called with:', payload);
        try {
            const user = await this.userService.findByUsernameOrEmail(payload.username);
            if (!user) {
                console.log('User Service - findUserByCredentials: User not found');
                return null;
            }
            console.log('User Service - findUserByCredentials: User found', {
                id: user.id,
                username: user.username,
                email: user.email,
                role: user.role,
                passwordHash: user.passwordHash ? 'Present (not shown)' : 'Missing'
            });
            return this.userService.mapToDto(user);
        }
        catch (error) {
            console.error('User Service - findUserByCredentials error:', error);
            return null;
        }
    }
    async findByUsernameOrEmailPattern(payload) {
        console.log('User Service - findByUsernameOrEmailPattern called with:', payload);
        try {
            const user = await this.userService.findByUsernameOrEmail(payload.usernameOrEmail);
            if (!user) {
                console.log('User Service - findByUsernameOrEmailPattern: User not found');
                return null;
            }
            console.log('User Service - findByUsernameOrEmailPattern: User found', {
                id: user.id,
                username: user.username,
                email: user.email,
                role: user.role,
                passwordHash: user.passwordHash ? 'Present (not shown)' : 'Missing'
            });
            return this.userService.mapToDto(user);
        }
        catch (error) {
            console.error('User Service - findByUsernameOrEmailPattern error:', error);
            return null;
        }
    }
    async verifyPassword(payload) {
        console.log('User Service - verifyPassword called with:', {
            userId: payload.userId,
            passwordLength: payload.password?.length
        });
        try {
            const user = await this.userService['userRepository'].findOne({ where: { id: payload.userId } });
            if (!user) {
                console.log('User Service - verifyPassword: User not found');
                return false;
            }
            console.log('User Service - verifyPassword: User found', {
                id: user.id,
                username: user.username,
                passwordHash: user.passwordHash ? 'Present (not shown)' : 'Missing'
            });
            const isPasswordValid = await bcrypt.compare(payload.password, user.passwordHash);
            console.log('User Service - verifyPassword: Password valid?', isPasswordValid);
            return isPasswordValid;
        }
        catch (error) {
            console.error('User Service - verifyPassword error:', error);
            return false;
        }
    }
    async testPasswordHash(payload) {
        console.log('User Service - testPasswordHash called with:', {
            username: payload.username,
            hasPassword: !!payload.password,
            passwordLength: payload.password?.length
        });
        try {
            const user = await this.userService.findByUsernameOrEmail(payload.username);
            if (!user) {
                console.log('User Service - testPasswordHash: User not found');
                return { success: false, message: 'User not found' };
            }
            console.log('User Service - testPasswordHash: User found', {
                id: user.id,
                username: user.username,
                passwordHash: user.passwordHash ? 'Present (not shown)' : 'Missing'
            });
            const isPasswordValid = await bcrypt.compare(payload.password, user.passwordHash);
            console.log('User Service - testPasswordHash: Password valid?', isPasswordValid);
            const newHash = await bcrypt.hash(payload.password, 10);
            console.log('User Service - testPasswordHash: New hash created:', newHash ? 'Hash created' : 'Hash failed');
            const hashesMatch = await bcrypt.compare(payload.password, newHash);
            console.log('User Service - testPasswordHash: New hash valid?', hashesMatch);
            return {
                success: true,
                user: {
                    id: user.id,
                    username: user.username,
                    email: user.email,
                },
                passwordTest: {
                    isPasswordValid,
                    storedHashWorks: hashesMatch,
                    storedHashFirstChars: user.passwordHash.substring(0, 10) + '...',
                    newHashFirstChars: newHash.substring(0, 10) + '...',
                }
            };
        }
        catch (error) {
            console.error('User Service - testPasswordHash error:', error);
            return {
                success: false,
                message: error.message || 'Error testing password hash',
                error: error.name,
                stack: error.stack,
            };
        }
    }
    async findOne(id) {
        return this.userService.findOne(id);
    }
    async update(payload) {
        return this.userService.update(payload.id, payload.updateUserDto);
    }
    async remove(id) {
        return this.userService.remove(id);
    }
    async verifyEmail(token) {
        return this.verificationService.verifyToken(token, verification_entity_1.VerificationType.EMAIL);
    }
    async verifyPhone(token) {
        return this.verificationService.verifyToken(token, verification_entity_1.VerificationType.PHONE);
    }
    async resendEmailVerification(userId) {
        const user = await this.userService.findOne(userId);
        if (user && user.email && !user.isEmailVerified) {
            await this.verificationService.sendEmailVerification(userId, user.email);
        }
    }
    async resendPhoneVerification(userId) {
        const user = await this.userService.findOne(userId);
        if (user && user.phone && !user.isPhoneVerified) {
            await this.verificationService.sendSmsVerification(userId, user.phone);
        }
    }
    async followStore(payload) {
        return this.followerService.followStore(payload.userId, payload.storeId);
    }
    async unfollowStore(payload) {
        return this.followerService.unfollowStore(payload.userId, payload.storeId);
    }
    async getUserCart(userId) {
        return this.cartService.getUserCart(userId);
    }
    async getCartItemCount(userId) {
        return this.cartService.getCartItemCount(userId);
    }
    async getCartSummary(userId) {
        return this.cartService.getCartSummary(userId);
    }
    async addToCart(payload) {
        return this.cartService.addToCart(payload.userId, payload.addToCartDto);
    }
    async updateCartItem(payload) {
        return this.cartService.updateCartItem(payload.userId, payload.cartItemId, payload.updateCartItemDto);
    }
    async removeFromCart(payload) {
        return this.cartService.removeFromCart(payload.userId, payload.cartItemId);
    }
    async clearCart(userId) {
        return this.cartService.clearCart(userId);
    }
    async getFollowedStores(userId) {
        return this.followerService.getFollowedStores(userId);
    }
    async getStoreFollowers(storeId) {
        return this.followerService.getStoreFollowers(storeId);
    }
    async isFollowing(payload) {
        return this.followerService.isFollowing(payload.userId, payload.storeId);
    }
    async getFollowerCount(storeId) {
        return this.followerService.getFollowerCount(storeId);
    }
};
exports.UserController = UserController;
__decorate([
    (0, microservices_1.MessagePattern)('create_user'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_e = typeof user_dto_1.CreateUserDto !== "undefined" && user_dto_1.CreateUserDto) === "function" ? _e : Object]),
    __metadata("design:returntype", typeof (_f = typeof Promise !== "undefined" && Promise) === "function" ? _f : Object)
], UserController.prototype, "create", null);
__decorate([
    (0, microservices_1.MessagePattern)('register_user'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_g = typeof user_dto_1.CreateUserDto !== "undefined" && user_dto_1.CreateUserDto) === "function" ? _g : Object]),
    __metadata("design:returntype", typeof (_h = typeof Promise !== "undefined" && Promise) === "function" ? _h : Object)
], UserController.prototype, "register", null);
__decorate([
    (0, microservices_1.MessagePattern)('create_test_user'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_j = typeof Promise !== "undefined" && Promise) === "function" ? _j : Object)
], UserController.prototype, "createTestUser", null);
__decorate([
    (0, microservices_1.MessagePattern)('create_direct_test_user'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_k = typeof Promise !== "undefined" && Promise) === "function" ? _k : Object)
], UserController.prototype, "createDirectTestUser", null);
__decorate([
    (0, microservices_1.MessagePattern)('create_fixed_hash_user'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_l = typeof Promise !== "undefined" && Promise) === "function" ? _l : Object)
], UserController.prototype, "createFixedHashUser", null);
__decorate([
    (0, microservices_1.MessagePattern)('create_simple_test_user'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_m = typeof Promise !== "undefined" && Promise) === "function" ? _m : Object)
], UserController.prototype, "createSimpleTestUser", null);
__decorate([
    (0, microservices_1.MessagePattern)('login_user'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_o = typeof user_dto_1.LoginUserDto !== "undefined" && user_dto_1.LoginUserDto) === "function" ? _o : Object]),
    __metadata("design:returntype", typeof (_p = typeof Promise !== "undefined" && Promise) === "function" ? _p : Object)
], UserController.prototype, "login", null);
__decorate([
    (0, microservices_1.MessagePattern)('validate_user'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", typeof (_q = typeof Promise !== "undefined" && Promise) === "function" ? _q : Object)
], UserController.prototype, "validateUser", null);
__decorate([
    (0, microservices_1.MessagePattern)('find_all_users'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_r = typeof Promise !== "undefined" && Promise) === "function" ? _r : Object)
], UserController.prototype, "findAll", null);
__decorate([
    (0, microservices_1.MessagePattern)('find_user_by_credentials'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", typeof (_s = typeof Promise !== "undefined" && Promise) === "function" ? _s : Object)
], UserController.prototype, "findUserByCredentials", null);
__decorate([
    (0, microservices_1.MessagePattern)('find_by_username_or_email'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", typeof (_t = typeof Promise !== "undefined" && Promise) === "function" ? _t : Object)
], UserController.prototype, "findByUsernameOrEmailPattern", null);
__decorate([
    (0, microservices_1.MessagePattern)('verify_password'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", typeof (_u = typeof Promise !== "undefined" && Promise) === "function" ? _u : Object)
], UserController.prototype, "verifyPassword", null);
__decorate([
    (0, microservices_1.MessagePattern)('test_password_hash'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", typeof (_v = typeof Promise !== "undefined" && Promise) === "function" ? _v : Object)
], UserController.prototype, "testPasswordHash", null);
__decorate([
    (0, microservices_1.MessagePattern)('find_user_by_id'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_w = typeof Promise !== "undefined" && Promise) === "function" ? _w : Object)
], UserController.prototype, "findOne", null);
__decorate([
    (0, microservices_1.MessagePattern)('update_user'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", typeof (_x = typeof Promise !== "undefined" && Promise) === "function" ? _x : Object)
], UserController.prototype, "update", null);
__decorate([
    (0, microservices_1.MessagePattern)('remove_user'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_y = typeof Promise !== "undefined" && Promise) === "function" ? _y : Object)
], UserController.prototype, "remove", null);
__decorate([
    (0, microservices_1.MessagePattern)('verify_email'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_z = typeof Promise !== "undefined" && Promise) === "function" ? _z : Object)
], UserController.prototype, "verifyEmail", null);
__decorate([
    (0, microservices_1.MessagePattern)('verify_phone'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_0 = typeof Promise !== "undefined" && Promise) === "function" ? _0 : Object)
], UserController.prototype, "verifyPhone", null);
__decorate([
    (0, microservices_1.MessagePattern)('resend_email_verification'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_1 = typeof Promise !== "undefined" && Promise) === "function" ? _1 : Object)
], UserController.prototype, "resendEmailVerification", null);
__decorate([
    (0, microservices_1.MessagePattern)('resend_phone_verification'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_2 = typeof Promise !== "undefined" && Promise) === "function" ? _2 : Object)
], UserController.prototype, "resendPhoneVerification", null);
__decorate([
    (0, microservices_1.MessagePattern)('follow_store'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", typeof (_3 = typeof Promise !== "undefined" && Promise) === "function" ? _3 : Object)
], UserController.prototype, "followStore", null);
__decorate([
    (0, microservices_1.MessagePattern)('unfollow_store'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", typeof (_4 = typeof Promise !== "undefined" && Promise) === "function" ? _4 : Object)
], UserController.prototype, "unfollowStore", null);
__decorate([
    (0, microservices_1.MessagePattern)('get_user_cart'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_5 = typeof Promise !== "undefined" && Promise) === "function" ? _5 : Object)
], UserController.prototype, "getUserCart", null);
__decorate([
    (0, microservices_1.MessagePattern)('get_cart_item_count'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_6 = typeof Promise !== "undefined" && Promise) === "function" ? _6 : Object)
], UserController.prototype, "getCartItemCount", null);
__decorate([
    (0, microservices_1.MessagePattern)('get_cart_summary'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_7 = typeof Promise !== "undefined" && Promise) === "function" ? _7 : Object)
], UserController.prototype, "getCartSummary", null);
__decorate([
    (0, microservices_1.MessagePattern)('add_to_cart'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", typeof (_8 = typeof Promise !== "undefined" && Promise) === "function" ? _8 : Object)
], UserController.prototype, "addToCart", null);
__decorate([
    (0, microservices_1.MessagePattern)('update_cart_item'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", typeof (_9 = typeof Promise !== "undefined" && Promise) === "function" ? _9 : Object)
], UserController.prototype, "updateCartItem", null);
__decorate([
    (0, microservices_1.MessagePattern)('remove_from_cart'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", typeof (_10 = typeof Promise !== "undefined" && Promise) === "function" ? _10 : Object)
], UserController.prototype, "removeFromCart", null);
__decorate([
    (0, microservices_1.MessagePattern)('clear_user_cart'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_11 = typeof Promise !== "undefined" && Promise) === "function" ? _11 : Object)
], UserController.prototype, "clearCart", null);
__decorate([
    (0, microservices_1.MessagePattern)('get_followed_stores'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_12 = typeof Promise !== "undefined" && Promise) === "function" ? _12 : Object)
], UserController.prototype, "getFollowedStores", null);
__decorate([
    (0, microservices_1.MessagePattern)('get_store_followers'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_13 = typeof Promise !== "undefined" && Promise) === "function" ? _13 : Object)
], UserController.prototype, "getStoreFollowers", null);
__decorate([
    (0, microservices_1.MessagePattern)('is_following'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", typeof (_14 = typeof Promise !== "undefined" && Promise) === "function" ? _14 : Object)
], UserController.prototype, "isFollowing", null);
__decorate([
    (0, microservices_1.MessagePattern)('get_follower_count'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_15 = typeof Promise !== "undefined" && Promise) === "function" ? _15 : Object)
], UserController.prototype, "getFollowerCount", null);
exports.UserController = UserController = __decorate([
    (0, common_1.Controller)(),
    __metadata("design:paramtypes", [typeof (_a = typeof user_service_1.UserService !== "undefined" && user_service_1.UserService) === "function" ? _a : Object, typeof (_b = typeof verification_service_1.VerificationService !== "undefined" && verification_service_1.VerificationService) === "function" ? _b : Object, typeof (_c = typeof follower_service_1.FollowerService !== "undefined" && follower_service_1.FollowerService) === "function" ? _c : Object, typeof (_d = typeof cart_service_1.CartService !== "undefined" && cart_service_1.CartService) === "function" ? _d : Object])
], UserController);


/***/ }),
/* 8 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var UserService_1;
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.UserService = void 0;
const common_1 = __webpack_require__(3);
const typeorm_1 = __webpack_require__(5);
const typeorm_2 = __webpack_require__(9);
const jwt_1 = __webpack_require__(10);
const bcrypt = __webpack_require__(11);
const user_entity_1 = __webpack_require__(12);
const verification_service_1 = __webpack_require__(15);
const user_dto_1 = __webpack_require__(13);
const errors_1 = __webpack_require__(18);
let UserService = UserService_1 = class UserService {
    constructor(userRepository, jwtService, verificationService) {
        this.userRepository = userRepository;
        this.jwtService = jwtService;
        this.verificationService = verificationService;
        this.logger = new common_1.Logger(UserService_1.name);
    }
    async create(createUserDto) {
        this.logger.log(`Creating new user with username: ${createUserDto.username}`);
        console.log('User Service - create called with:', {
            username: createUserDto.username,
            email: createUserDto.email,
            hasPassword: !!createUserDto.password
        });
        try {
            const existingUser = await this.userRepository.findOne({
                where: [
                    { username: createUserDto.username },
                    { email: createUserDto.email },
                    { phone: createUserDto.phone },
                ],
            });
            console.log('User Service - create existingUser check:', existingUser ? 'User already exists' : 'User does not exist');
            if (existingUser) {
                let conflictField = 'account';
                if (existingUser.username === createUserDto.username) {
                    conflictField = 'username';
                }
                else if (existingUser.email === createUserDto.email) {
                    conflictField = 'email';
                }
                else if (existingUser.phone === createUserDto.phone) {
                    conflictField = 'phone';
                }
                console.log('User Service - create throwing BusinessError: User already exists', { conflictField });
                throw new errors_1.BusinessError({
                    code: errors_1.ErrorCode.ALREADY_EXISTS,
                    message: `A user with this ${conflictField} already exists`,
                    translationKey: 'errors.user.alreadyExists',
                    details: { field: conflictField }
                });
            }
            console.log('User Service - create hashing password');
            const passwordHash = await bcrypt.hash(createUserDto.password, 10);
            console.log('User Service - create password hashed successfully');
            const user = this.userRepository.create({
                username: createUserDto.username,
                email: createUserDto.email,
                phone: createUserDto.phone,
                passwordHash,
                role: createUserDto.role || user_dto_1.UserRole.USER,
            });
            console.log('User Service - create saving user to database');
            await this.userRepository.save(user);
            console.log('User Service - create user saved successfully with ID:', user.id);
            this.logger.log(`User created successfully with ID: ${user.id}`);
            if (user.email) {
                await this.verificationService.sendEmailVerification(user.id, user.email);
                this.logger.log(`Email verification sent to: ${user.email}`);
            }
            if (user.phone) {
                await this.verificationService.sendSmsVerification(user.id, user.phone);
                this.logger.log(`SMS verification sent to: ${user.phone}`);
            }
            return this.mapToDto(user);
        }
        catch (error) {
            if (error instanceof errors_1.AppError) {
                throw error;
            }
            this.logger.error(`Failed to create user: ${error.message}`, error.stack);
            throw new errors_1.AppError({
                code: errors_1.ErrorCode.INTERNAL_ERROR,
                message: 'Failed to create user account',
                translationKey: 'errors.user.createFailed',
                details: error
            }, 500);
        }
    }
    async login(loginUserDto) {
        this.logger.log(`Login attempt for: ${loginUserDto.usernameOrEmail}`);
        console.log(`User Service - login attempt for: ${loginUserDto.usernameOrEmail}`);
        try {
            const user = await this.userRepository.findOne({
                where: [
                    { username: loginUserDto.usernameOrEmail },
                    { email: loginUserDto.usernameOrEmail },
                ],
            });
            console.log('User Service - login findOne result:', user ? 'User found' : 'User not found');
            if (!user) {
                console.log('User Service - login throwing UnauthorizedError: Invalid username or password (user not found)');
                throw new errors_1.UnauthorizedError('Invalid username or password');
            }
            console.log('User Service - login verifying password');
            const isPasswordValid = await bcrypt.compare(loginUserDto.password, user.passwordHash);
            console.log('User Service - login password valid:', isPasswordValid);
            if (!isPasswordValid) {
                this.logger.warn(`Failed login attempt for user: ${user.username}`);
                console.log('User Service - login throwing UnauthorizedError: Invalid username or password (invalid password)');
                throw new errors_1.UnauthorizedError('Invalid username or password');
            }
            const token = this.jwtService.sign({
                sub: user.id,
                username: user.username,
                email: user.email,
            });
            this.logger.log(`User logged in successfully: ${user.username}`);
            console.log('User Service - login successful for user:', user.username);
            return {
                user: this.mapToDto(user),
                token,
            };
        }
        catch (error) {
            if (error instanceof errors_1.AppError) {
                throw error;
            }
            this.logger.error(`Login error: ${error.message}`, error.stack);
            throw new errors_1.AppError({
                code: errors_1.ErrorCode.INTERNAL_ERROR,
                message: 'An error occurred during login',
                translationKey: 'errors.user.loginFailed',
                details: error
            }, 500);
        }
    }
    async findAll() {
        const users = await this.userRepository.find();
        return users.map(user => this.mapToDto(user));
    }
    async findOne(id) {
        this.logger.log(`Finding user with ID: ${id}`);
        try {
            const user = await this.userRepository.findOne({ where: { id } });
            if (!user) {
                throw new errors_1.NotFoundError('User', id);
            }
            return this.mapToDto(user);
        }
        catch (error) {
            if (error instanceof errors_1.AppError) {
                throw error;
            }
            this.logger.error(`Error finding user: ${error.message}`, error.stack);
            throw new errors_1.AppError({
                code: errors_1.ErrorCode.INTERNAL_ERROR,
                message: 'Failed to retrieve user',
                translationKey: 'errors.user.retrieveFailed',
                details: error
            }, 500);
        }
    }
    async findByUsernameOrEmail(usernameOrEmail) {
        this.logger.log(`Finding user by username or email: ${usernameOrEmail}`);
        try {
            const user = await this.userRepository.findOne({
                where: [
                    { username: usernameOrEmail },
                    { email: usernameOrEmail },
                ],
            });
            return user;
        }
        catch (error) {
            this.logger.error(`Error finding user by username/email: ${error.message}`, error.stack);
            return null;
        }
    }
    async update(id, updateUserDto) {
        this.logger.log(`Updating user with ID: ${id}`);
        try {
            const user = await this.userRepository.findOne({ where: { id } });
            if (!user) {
                throw new errors_1.NotFoundError('User', id);
            }
            if (updateUserDto.username || updateUserDto.email) {
                const existingUser = await this.userRepository.findOne({
                    where: [
                        ...(updateUserDto.username ? [{ username: updateUserDto.username }] : []),
                        ...(updateUserDto.email ? [{ email: updateUserDto.email }] : []),
                    ],
                });
                if (existingUser && existingUser.id !== id) {
                    let conflictField = 'account';
                    if (updateUserDto.username && existingUser.username === updateUserDto.username) {
                        conflictField = 'username';
                    }
                    else if (updateUserDto.email && existingUser.email === updateUserDto.email) {
                        conflictField = 'email';
                    }
                    throw new errors_1.BusinessError({
                        code: errors_1.ErrorCode.ALREADY_EXISTS,
                        message: `A user with this ${conflictField} already exists`,
                        translationKey: 'errors.user.alreadyExists',
                        details: { field: conflictField }
                    });
                }
            }
            Object.assign(user, updateUserDto);
            await this.userRepository.save(user);
            this.logger.log(`User updated successfully: ${user.id}`);
            return this.mapToDto(user);
        }
        catch (error) {
            if (error instanceof errors_1.AppError) {
                throw error;
            }
            this.logger.error(`Error updating user: ${error.message}`, error.stack);
            throw new errors_1.AppError({
                code: errors_1.ErrorCode.INTERNAL_ERROR,
                message: 'Failed to update user',
                translationKey: 'errors.user.updateFailed',
                details: error
            }, 500);
        }
    }
    async remove(id) {
        this.logger.log(`Removing user with ID: ${id}`);
        try {
            const user = await this.userRepository.findOne({ where: { id } });
            if (!user) {
                throw new errors_1.NotFoundError('User', id);
            }
            const result = await this.userRepository.delete(id);
            if (result.affected === 0) {
                throw new errors_1.AppError({
                    code: errors_1.ErrorCode.INTERNAL_ERROR,
                    message: `Failed to delete user with ID ${id}`,
                    translationKey: 'errors.user.deleteFailed',
                }, 500);
            }
            this.logger.log(`User deleted successfully: ${id}`);
        }
        catch (error) {
            if (error instanceof errors_1.AppError) {
                throw error;
            }
            this.logger.error(`Error removing user: ${error.message}`, error.stack);
            throw new errors_1.AppError({
                code: errors_1.ErrorCode.INTERNAL_ERROR,
                message: 'Failed to delete user',
                translationKey: 'errors.user.deleteFailed',
                details: error
            }, 500);
        }
    }
    mapToDto(user) {
        return {
            id: user.id,
            username: user.username,
            email: user.email,
            phone: user.phone,
            isEmailVerified: user.isEmailVerified,
            isPhoneVerified: user.isPhoneVerified,
            role: user.role || user_dto_1.UserRole.USER,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
        };
    }
};
exports.UserService = UserService;
exports.UserService = UserService = UserService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof jwt_1.JwtService !== "undefined" && jwt_1.JwtService) === "function" ? _b : Object, typeof (_c = typeof verification_service_1.VerificationService !== "undefined" && verification_service_1.VerificationService) === "function" ? _c : Object])
], UserService);


/***/ }),
/* 9 */
/***/ ((module) => {

module.exports = require("typeorm");

/***/ }),
/* 10 */
/***/ ((module) => {

module.exports = require("@nestjs/jwt");

/***/ }),
/* 11 */
/***/ ((module) => {

module.exports = require("bcrypt");

/***/ }),
/* 12 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.User = void 0;
const typeorm_1 = __webpack_require__(9);
const user_dto_1 = __webpack_require__(13);
let User = class User {
};
exports.User = User;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], User.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ unique: true }),
    __metadata("design:type", String)
], User.prototype, "username", void 0);
__decorate([
    (0, typeorm_1.Column)({ unique: true, nullable: true }),
    __metadata("design:type", String)
], User.prototype, "email", void 0);
__decorate([
    (0, typeorm_1.Column)({ unique: true, nullable: true }),
    __metadata("design:type", String)
], User.prototype, "phone", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], User.prototype, "passwordHash", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], User.prototype, "isEmailVerified", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], User.prototype, "isPhoneVerified", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', default: user_dto_1.UserRole.USER }),
    __metadata("design:type", typeof (_a = typeof user_dto_1.UserRole !== "undefined" && user_dto_1.UserRole) === "function" ? _a : Object)
], User.prototype, "role", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], User.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], User.prototype, "updatedAt", void 0);
exports.User = User = __decorate([
    (0, typeorm_1.Entity)('users')
], User);


/***/ }),
/* 13 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.UserDto = exports.UserResponseDto = exports.UpdateUserDto = exports.LoginDto = exports.LoginUserDto = exports.RegisterDto = exports.CreateUserDto = exports.UserRole = void 0;
const class_validator_1 = __webpack_require__(14);
var UserRole;
(function (UserRole) {
    UserRole["USER"] = "user";
    UserRole["ADMIN"] = "admin";
    UserRole["SELLER"] = "seller";
    UserRole["MODERATOR"] = "moderator";
})(UserRole || (exports.UserRole = UserRole = {}));
class CreateUserDto {
    constructor() {
        this.role = UserRole.USER;
    }
}
exports.CreateUserDto = CreateUserDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(3),
    __metadata("design:type", String)
], CreateUserDto.prototype, "username", void 0);
__decorate([
    (0, class_validator_1.IsEmail)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateUserDto.prototype, "email", void 0);
__decorate([
    (0, class_validator_1.IsPhoneNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateUserDto.prototype, "phone", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(8),
    __metadata("design:type", String)
], CreateUserDto.prototype, "password", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(UserRole),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateUserDto.prototype, "role", void 0);
class RegisterDto extends CreateUserDto {
}
exports.RegisterDto = RegisterDto;
class LoginUserDto {
}
exports.LoginUserDto = LoginUserDto;
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], LoginUserDto.prototype, "usernameOrEmail", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], LoginUserDto.prototype, "password", void 0);
class LoginDto extends LoginUserDto {
}
exports.LoginDto = LoginDto;
class UpdateUserDto {
}
exports.UpdateUserDto = UpdateUserDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateUserDto.prototype, "username", void 0);
__decorate([
    (0, class_validator_1.IsEmail)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateUserDto.prototype, "email", void 0);
__decorate([
    (0, class_validator_1.IsPhoneNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateUserDto.prototype, "phone", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], UpdateUserDto.prototype, "isEmailVerified", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], UpdateUserDto.prototype, "isPhoneVerified", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(UserRole),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateUserDto.prototype, "role", void 0);
class UserResponseDto {
}
exports.UserResponseDto = UserResponseDto;
class UserDto extends UserResponseDto {
}
exports.UserDto = UserDto;


/***/ }),
/* 14 */
/***/ ((module) => {

module.exports = require("class-validator");

/***/ }),
/* 15 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var VerificationService_1;
var _a, _b, _c, _d;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.VerificationService = void 0;
const common_1 = __webpack_require__(3);
const typeorm_1 = __webpack_require__(5);
const typeorm_2 = __webpack_require__(9);
const verification_entity_1 = __webpack_require__(16);
const user_entity_1 = __webpack_require__(12);
const crypto_1 = __webpack_require__(17);
const config_1 = __webpack_require__(4);
const errors_1 = __webpack_require__(18);
const email_service_1 = __webpack_require__(21);
let VerificationService = VerificationService_1 = class VerificationService {
    constructor(verificationRepository, userRepository, configService, emailService) {
        this.verificationRepository = verificationRepository;
        this.userRepository = userRepository;
        this.configService = configService;
        this.emailService = emailService;
        this.logger = new common_1.Logger(VerificationService_1.name);
    }
    async createVerification(userId, type) {
        this.logger.log(`Creating ${type} verification for user: ${userId}`);
        try {
            const user = await this.userRepository.findOne({ where: { id: userId } });
            if (!user) {
                throw new errors_1.NotFoundError('User', userId);
            }
            const token = (0, crypto_1.randomBytes)(32).toString('hex');
            const expiresAt = new Date();
            expiresAt.setHours(expiresAt.getHours() + 24);
            const verification = this.verificationRepository.create({
                userId,
                type,
                token,
                expiresAt,
            });
            const savedVerification = await this.verificationRepository.save(verification);
            this.logger.log(`Verification created for user ${userId}, type: ${type}`);
            return savedVerification;
        }
        catch (error) {
            if (error instanceof errors_1.AppError) {
                throw error;
            }
            this.logger.error(`Error creating verification: ${error.message}`, error.stack);
            throw new errors_1.AppError({
                code: errors_1.ErrorCode.INTERNAL_ERROR,
                message: 'Failed to create verification',
                translationKey: 'errors.verification.createFailed',
                details: error
            }, 500);
        }
    }
    async verifyToken(token, type) {
        this.logger.log(`Verifying ${type} token: ${token.substring(0, 8)}...`);
        try {
            const verification = await this.verificationRepository.findOne({
                where: { token, type },
            });
            if (!verification) {
                this.logger.warn(`Verification token not found: ${token.substring(0, 8)}...`);
                return false;
            }
            if (verification.expiresAt < new Date()) {
                this.logger.warn(`Verification token expired: ${token.substring(0, 8)}...`);
                await this.verificationRepository.remove(verification);
                return false;
            }
            const user = await this.userRepository.findOne({
                where: { id: verification.userId },
            });
            if (!user) {
                this.logger.warn(`User not found for verification: ${verification.userId}`);
                return false;
            }
            if (type === verification_entity_1.VerificationType.EMAIL) {
                user.isEmailVerified = true;
                this.logger.log(`Email verified for user: ${user.id}`);
            }
            else if (type === verification_entity_1.VerificationType.PHONE) {
                user.isPhoneVerified = true;
                this.logger.log(`Phone verified for user: ${user.id}`);
            }
            await this.userRepository.save(user);
            await this.verificationRepository.remove(verification);
            this.logger.log(`Verification successful for user: ${user.id}, type: ${type}`);
            return true;
        }
        catch (error) {
            this.logger.error(`Error verifying token: ${error.message}`, error.stack);
            return false;
        }
    }
    async sendEmailVerification(userId, email) {
        this.logger.log(`Sending email verification to: ${email}`);
        try {
            const verification = await this.createVerification(userId, verification_entity_1.VerificationType.EMAIL);
            const verificationLink = `${this.configService.get('FRONTEND_URL')}/verify-email?token=${verification.token}`;
            await this.emailService.sendEmailVerification(email, verificationLink);
            this.logger.log(`Email verification sent successfully to: ${email}`);
        }
        catch (error) {
            if (error instanceof errors_1.AppError) {
                throw error;
            }
            this.logger.error(`Error sending email verification: ${error.message}`, error.stack);
            throw new errors_1.AppError({
                code: errors_1.ErrorCode.INTERNAL_ERROR,
                message: 'Failed to send email verification',
                translationKey: 'errors.verification.emailSendFailed',
                details: error
            }, 500);
        }
    }
    async sendSmsVerification(userId, phone) {
        this.logger.log(`Sending SMS verification to: ${phone}`);
        try {
            const verification = await this.createVerification(userId, verification_entity_1.VerificationType.PHONE);
            const verificationCode = verification.token.substring(0, 6);
            this.logger.log(`SMS verification code for ${phone}: ${verificationCode}`);
        }
        catch (error) {
            if (error instanceof errors_1.AppError) {
                throw error;
            }
            this.logger.error(`Error sending SMS verification: ${error.message}`, error.stack);
            throw new errors_1.AppError({
                code: errors_1.ErrorCode.INTERNAL_ERROR,
                message: 'Failed to send SMS verification',
                translationKey: 'errors.verification.smsSendFailed',
                details: error
            }, 500);
        }
    }
};
exports.VerificationService = VerificationService;
exports.VerificationService = VerificationService = VerificationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(verification_entity_1.Verification)),
    __param(1, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, typeof (_c = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _c : Object, typeof (_d = typeof email_service_1.EmailService !== "undefined" && email_service_1.EmailService) === "function" ? _d : Object])
], VerificationService);


/***/ }),
/* 16 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.Verification = exports.VerificationType = void 0;
const typeorm_1 = __webpack_require__(9);
var VerificationType;
(function (VerificationType) {
    VerificationType["EMAIL"] = "email";
    VerificationType["PHONE"] = "phone";
})(VerificationType || (exports.VerificationType = VerificationType = {}));
let Verification = class Verification {
};
exports.Verification = Verification;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Verification.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Verification.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: VerificationType,
    }),
    __metadata("design:type", String)
], Verification.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Verification.prototype, "token", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], Verification.prototype, "expiresAt", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], Verification.prototype, "createdAt", void 0);
exports.Verification = Verification = __decorate([
    (0, typeorm_1.Entity)('verifications')
], Verification);


/***/ }),
/* 17 */
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),
/* 18 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
__exportStar(__webpack_require__(19), exports);
__exportStar(__webpack_require__(20), exports);


/***/ }),
/* 19 */
/***/ ((__unused_webpack_module, exports) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.InternalError = exports.ValidationError = exports.ForbiddenError = exports.UnauthorizedError = exports.NotFoundError = exports.BusinessError = exports.AppError = exports.ErrorCode = void 0;
var ErrorCode;
(function (ErrorCode) {
    ErrorCode["API_ERROR"] = "API_ERROR";
    ErrorCode["NETWORK_ERROR"] = "NETWORK_ERROR";
    ErrorCode["UNAUTHORIZED"] = "UNAUTHORIZED";
    ErrorCode["FORBIDDEN"] = "FORBIDDEN";
    ErrorCode["VALIDATION_ERROR"] = "VALIDATION_ERROR";
    ErrorCode["INVALID_INPUT"] = "INVALID_INPUT";
    ErrorCode["NOT_FOUND"] = "NOT_FOUND";
    ErrorCode["ALREADY_EXISTS"] = "ALREADY_EXISTS";
    ErrorCode["CONFLICT"] = "CONFLICT";
    ErrorCode["BUSINESS_RULE_VIOLATION"] = "BUSINESS_RULE_VIOLATION";
    ErrorCode["INSUFFICIENT_FUNDS"] = "INSUFFICIENT_FUNDS";
    ErrorCode["INSUFFICIENT_INVENTORY"] = "INSUFFICIENT_INVENTORY";
    ErrorCode["EXTERNAL_SERVICE_ERROR"] = "EXTERNAL_SERVICE_ERROR";
    ErrorCode["PAYMENT_GATEWAY_ERROR"] = "PAYMENT_GATEWAY_ERROR";
    ErrorCode["INTERNAL_ERROR"] = "INTERNAL_ERROR";
    ErrorCode["NOT_IMPLEMENTED"] = "NOT_IMPLEMENTED";
    ErrorCode["TIMEOUT"] = "TIMEOUT";
    ErrorCode["DATABASE_ERROR"] = "DATABASE_ERROR";
    ErrorCode["INVALID_REFERENCE"] = "INVALID_REFERENCE";
})(ErrorCode || (exports.ErrorCode = ErrorCode = {}));
class AppError extends Error {
    constructor(details, statusCode = 400) {
        super(details.message);
        this.code = details.code;
        this.translationKey = details.translationKey;
        this.details = details.details;
        this.statusCode = statusCode;
        this.name = 'AppError';
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.AppError = AppError;
class BusinessError extends AppError {
    constructor(details) {
        super(details, 400);
        this.name = 'BusinessError';
    }
}
exports.BusinessError = BusinessError;
class NotFoundError extends AppError {
    constructor(resource, id) {
        const message = id
            ? `${resource} with ID ${id} not found`
            : `${resource} not found`;
        super({
            code: ErrorCode.NOT_FOUND,
            message,
            translationKey: 'errors.notFound',
            details: { resource, id }
        }, 404);
        this.name = 'NotFoundError';
    }
}
exports.NotFoundError = NotFoundError;
class UnauthorizedError extends AppError {
    constructor(message = 'Unauthorized access') {
        super({
            code: ErrorCode.UNAUTHORIZED,
            message,
            translationKey: 'errors.unauthorized'
        }, 401);
        this.name = 'UnauthorizedError';
    }
}
exports.UnauthorizedError = UnauthorizedError;
class ForbiddenError extends AppError {
    constructor(message = 'Access forbidden') {
        super({
            code: ErrorCode.FORBIDDEN,
            message,
            translationKey: 'errors.forbidden'
        }, 403);
        this.name = 'ForbiddenError';
    }
}
exports.ForbiddenError = ForbiddenError;
class ValidationError extends AppError {
    constructor(message, field, details) {
        super({
            code: ErrorCode.VALIDATION_ERROR,
            message,
            translationKey: 'errors.validation',
            field,
            details
        }, 400);
        this.name = 'ValidationError';
    }
}
exports.ValidationError = ValidationError;
class InternalError extends AppError {
    constructor(message = 'Internal server error', details) {
        super({
            code: ErrorCode.INTERNAL_ERROR,
            message,
            translationKey: 'errors.internal',
            details
        }, 500);
        this.name = 'InternalError';
    }
}
exports.InternalError = InternalError;


/***/ }),
/* 20 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.handleApiError = handleApiError;
exports.handleDatabaseError = handleDatabaseError;
exports.formatErrorResponse = formatErrorResponse;
const types_1 = __webpack_require__(19);
function handleApiError(error) {
    if (!error.response) {
        return new types_1.AppError({
            code: types_1.ErrorCode.NETWORK_ERROR,
            message: 'Network error. Please check your connection.',
            translationKey: 'errors.network',
        }, 0);
    }
    const status = error.response.status;
    const data = error.response.data;
    switch (status) {
        case 400:
            return new types_1.ValidationError(data?.message || 'Invalid request', data?.field, data);
        case 401:
            return new types_1.UnauthorizedError(data?.message || 'Unauthorized');
        case 403:
            return new types_1.ForbiddenError(data?.message || 'Forbidden');
        case 404:
            return new types_1.NotFoundError(data?.resource || 'Resource', data?.id);
        case 500:
        case 502:
        case 503:
        case 504:
            return new types_1.InternalError(data?.message || 'Server error', data);
        default:
            return new types_1.AppError({
                code: types_1.ErrorCode.API_ERROR,
                message: data?.message || 'Unknown error',
                translationKey: data?.translationKey || 'errors.unknown',
                details: data,
            }, status);
    }
}
function handleDatabaseError(error) {
    if (error.code === '23505') {
        return new types_1.AppError({
            code: types_1.ErrorCode.ALREADY_EXISTS,
            message: 'Resource already exists',
            translationKey: 'errors.alreadyExists',
            details: error
        }, 409);
    }
    if (error.code === '23503') {
        return new types_1.AppError({
            code: types_1.ErrorCode.VALIDATION_ERROR,
            message: 'Referenced resource does not exist',
            translationKey: 'errors.invalidReference',
            details: error
        }, 400);
    }
    return new types_1.InternalError('Database error', error);
}
function formatErrorResponse(error) {
    if (error instanceof types_1.AppError) {
        return {
            statusCode: error.statusCode,
            errorCode: error.code,
            message: error.message,
            details: error.details,
            timestamp: new Date().toISOString(),
        };
    }
    let appError;
    if (error.isAxiosError) {
        appError = handleApiError(error);
    }
    else if (error.code && typeof error.code === 'string' && error.code.match(/^[0-9]{5}$/)) {
        appError = handleDatabaseError(error);
    }
    else {
        appError = new types_1.InternalError(error.message || 'Unknown error', error);
    }
    return {
        statusCode: appError.statusCode,
        errorCode: appError.code,
        message: appError.message,
        details: appError.details,
        timestamp: new Date().toISOString(),
    };
}


/***/ }),
/* 21 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var EmailService_1;
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.EmailService = void 0;
const common_1 = __webpack_require__(3);
const config_1 = __webpack_require__(4);
const nodemailer = __webpack_require__(Object(function webpackMissingModule() { var e = new Error("Cannot find module 'nodemailer'"); e.code = 'MODULE_NOT_FOUND'; throw e; }()));
const errors_1 = __webpack_require__(18);
let EmailService = EmailService_1 = class EmailService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(EmailService_1.name);
        try {
            const host = this.configService.get('EMAIL_HOST');
            const port = this.configService.get('EMAIL_PORT');
            const secure = this.configService.get('EMAIL_SECURE');
            const user = this.configService.get('EMAIL_USER');
            const pass = this.configService.get('EMAIL_PASSWORD');
            if (!host || !port || !user || !pass) {
                this.logger.warn('Missing email configuration, using test account');
                this.setupTestAccount();
            }
            else {
                this.transporter = nodemailer.createTransport({
                    host,
                    port,
                    secure,
                    auth: {
                        user,
                        pass,
                    },
                });
                this.logger.log('Email service initialized with configured account');
            }
        }
        catch (error) {
            this.logger.error(`Failed to initialize email service: ${error.message}`, error.stack);
            this.setupTestAccount();
        }
    }
    async setupTestAccount() {
        try {
            this.logger.log('Setting up test email account');
            const testAccount = await nodemailer.createTestAccount();
            this.transporter = nodemailer.createTransport({
                host: 'smtp.ethereal.email',
                port: 587,
                secure: false,
                auth: {
                    user: testAccount.user,
                    pass: testAccount.pass,
                },
            });
            this.logger.log(`Test email account created: ${testAccount.user}`);
        }
        catch (error) {
            this.logger.error(`Failed to create test email account: ${error.message}`, error.stack);
        }
    }
    async sendEmailVerification(email, verificationLink) {
        this.logger.log(`Sending email verification to: ${email}`);
        const subject = 'Verify Your Email Address';
        const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Verify Your Email Address</h2>
        <p>Thank you for registering! Please click the link below to verify your email address:</p>
        <p>
          <a 
            href="${verificationLink}" 
            style="display: inline-block; background-color: #4F46E5; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;"
          >
            Verify Email
          </a>
        </p>
        <p>Or copy and paste this link in your browser:</p>
        <p>${verificationLink}</p>
        <p>This link will expire in 24 hours.</p>
        <p>If you did not create an account, please ignore this email.</p>
      </div>
    `;
        try {
            const info = await this.transporter.sendMail({
                from: `"Social Commerce" <${this.configService.get('EMAIL_FROM') || '<EMAIL>'}>`,
                to: email,
                subject,
                html,
            });
            this.logger.log(`Email verification sent successfully to: ${email}`);
            if (info.messageId && this.transporter.options.host === 'smtp.ethereal.email') {
                this.logger.log(`Email preview URL: ${nodemailer.getTestMessageUrl(info)}`);
            }
        }
        catch (error) {
            this.logger.error(`Failed to send email verification: ${error.message}`, error.stack);
            throw new errors_1.AppError({
                code: errors_1.ErrorCode.EXTERNAL_SERVICE_ERROR,
                message: 'Failed to send email verification',
                translationKey: 'errors.email.sendVerificationFailed',
                details: { email, error: error.message }
            });
        }
    }
    async sendPasswordResetEmail(email, resetLink) {
        this.logger.log(`Sending password reset email to: ${email}`);
        const subject = 'Reset Your Password';
        const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Reset Your Password</h2>
        <p>We received a request to reset your password. Click the link below to create a new password:</p>
        <p>
          <a 
            href="${resetLink}" 
            style="display: inline-block; background-color: #4F46E5; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;"
          >
            Reset Password
          </a>
        </p>
        <p>Or copy and paste this link in your browser:</p>
        <p>${resetLink}</p>
        <p>This link will expire in 24 hours.</p>
        <p>If you did not request a password reset, please ignore this email.</p>
      </div>
    `;
        try {
            const info = await this.transporter.sendMail({
                from: `"Social Commerce" <${this.configService.get('EMAIL_FROM') || '<EMAIL>'}>`,
                to: email,
                subject,
                html,
            });
            this.logger.log(`Password reset email sent successfully to: ${email}`);
            if (info.messageId && this.transporter.options.host === 'smtp.ethereal.email') {
                this.logger.log(`Email preview URL: ${nodemailer.getTestMessageUrl(info)}`);
            }
        }
        catch (error) {
            this.logger.error(`Failed to send password reset email: ${error.message}`, error.stack);
            throw new errors_1.AppError({
                code: errors_1.ErrorCode.EXTERNAL_SERVICE_ERROR,
                message: 'Failed to send password reset email',
                translationKey: 'errors.email.sendPasswordResetFailed',
                details: { email, error: error.message }
            });
        }
    }
};
exports.EmailService = EmailService;
exports.EmailService = EmailService = EmailService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], EmailService);


/***/ }),
/* 22 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.FollowerService = void 0;
const common_1 = __webpack_require__(3);
const typeorm_1 = __webpack_require__(5);
const typeorm_2 = __webpack_require__(9);
const follower_entity_1 = __webpack_require__(23);
let FollowerService = class FollowerService {
    constructor(followerRepository) {
        this.followerRepository = followerRepository;
    }
    async followStore(userId, storeId) {
        const existingFollow = await this.followerRepository.findOne({
            where: { userId, storeId },
        });
        if (existingFollow) {
            throw new common_1.ConflictException('Already following this store');
        }
        const follower = this.followerRepository.create({
            userId,
            storeId,
        });
        return this.followerRepository.save(follower);
    }
    async unfollowStore(userId, storeId) {
        const result = await this.followerRepository.delete({
            userId,
            storeId,
        });
        if (result.affected === 0) {
            throw new common_1.NotFoundException('Follow relationship not found');
        }
    }
    async getFollowedStores(userId) {
        const followers = await this.followerRepository.find({
            where: { userId },
        });
        return followers.map((follower) => follower.storeId);
    }
    async getStoreFollowers(storeId) {
        const followers = await this.followerRepository.find({
            where: { storeId },
        });
        return followers.map((follower) => follower.userId);
    }
    async isFollowing(userId, storeId) {
        const follower = await this.followerRepository.findOne({
            where: { userId, storeId },
        });
        return !!follower;
    }
    async getFollowerCount(storeId) {
        return this.followerRepository.count({
            where: { storeId },
        });
    }
};
exports.FollowerService = FollowerService;
exports.FollowerService = FollowerService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(follower_entity_1.Follower)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object])
], FollowerService);


/***/ }),
/* 23 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.Follower = void 0;
const typeorm_1 = __webpack_require__(9);
let Follower = class Follower {
};
exports.Follower = Follower;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Follower.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Follower.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Follower.prototype, "storeId", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], Follower.prototype, "createdAt", void 0);
exports.Follower = Follower = __decorate([
    (0, typeorm_1.Entity)('followers'),
    (0, typeorm_1.Unique)(['userId', 'storeId'])
], Follower);


/***/ }),
/* 24 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.CartService = void 0;
const common_1 = __webpack_require__(3);
const typeorm_1 = __webpack_require__(5);
const typeorm_2 = __webpack_require__(9);
const microservices_1 = __webpack_require__(6);
const rxjs_1 = __webpack_require__(25);
const cart_entity_1 = __webpack_require__(26);
const cart_item_entity_1 = __webpack_require__(27);
const common_2 = __webpack_require__(28);
let CartService = class CartService {
    constructor(cartRepository, cartItemRepository, productServiceClient) {
        this.cartRepository = cartRepository;
        this.cartItemRepository = cartItemRepository;
        this.productServiceClient = productServiceClient;
    }
    async getOrCreateCart(userId) {
        let cart = await this.cartRepository.findOne({
            where: { userId },
            relations: ['items'],
        });
        if (!cart) {
            cart = this.cartRepository.create({ userId });
            await this.cartRepository.save(cart);
        }
        return cart;
    }
    async getUserCart(userId) {
        const cart = await this.getOrCreateCart(userId);
        return this.mapToDto(cart);
    }
    async getCartItemCount(userId) {
        const cart = await this.getOrCreateCart(userId);
        if (!cart.items || cart.items.length === 0) {
            return 0;
        }
        return cart.items.reduce((count, item) => count + item.quantity, 0);
    }
    async getCartSummary(userId) {
        const cart = await this.getOrCreateCart(userId);
        if (!cart.items || cart.items.length === 0) {
            return {
                subtotal: 0,
                shipping: 0,
                tax: 0,
                total: 0,
            };
        }
        const subtotal = cart.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const shipping = 0;
        const tax = subtotal * 0.1;
        const total = subtotal + shipping + tax;
        return {
            subtotal,
            shipping,
            tax,
            total,
        };
    }
    async addToCart(userId, addToCartDto) {
        const product = await (0, rxjs_1.firstValueFrom)(this.productServiceClient.send('find_product_by_id', addToCartDto.productId));
        const cart = await this.getOrCreateCart(userId);
        const existingItem = cart.items?.find(item => item.productId === addToCartDto.productId &&
            JSON.stringify(item.selectedOptions) === JSON.stringify(addToCartDto.selectedOptions || {}));
        if (existingItem) {
            existingItem.quantity += addToCartDto.quantity;
            await this.cartItemRepository.save(existingItem);
            return this.mapItemToDto(existingItem);
        }
        const cartItem = this.cartItemRepository.create({
            cartId: cart.id,
            productId: addToCartDto.productId,
            productTitle: product.title,
            productImage: product.mediaUrls?.[0],
            quantity: addToCartDto.quantity,
            price: product.price,
            repostId: addToCartDto.repostId,
            selectedOptions: addToCartDto.selectedOptions || {},
        });
        await this.cartItemRepository.save(cartItem);
        return this.mapItemToDto(cartItem);
    }
    async updateCartItem(userId, cartItemId, updateCartItemDto) {
        const cart = await this.getOrCreateCart(userId);
        const cartItem = cart.items?.find(item => item.id === cartItemId);
        if (!cartItem) {
            throw new common_1.NotFoundException(`Cart item with ID ${cartItemId} not found`);
        }
        cartItem.quantity = updateCartItemDto.quantity;
        if (updateCartItemDto.selectedOptions) {
            cartItem.selectedOptions = updateCartItemDto.selectedOptions;
        }
        await this.cartItemRepository.save(cartItem);
        return this.mapItemToDto(cartItem);
    }
    async removeFromCart(userId, cartItemId) {
        const cart = await this.getOrCreateCart(userId);
        const cartItem = cart.items?.find(item => item.id === cartItemId);
        if (!cartItem) {
            throw new common_1.NotFoundException(`Cart item with ID ${cartItemId} not found`);
        }
        await this.cartItemRepository.remove(cartItem);
    }
    async clearCart(userId) {
        const cart = await this.getOrCreateCart(userId);
        if (cart.items && cart.items.length > 0) {
            await this.cartItemRepository.remove(cart.items);
        }
    }
    mapToDto(cart) {
        const items = cart.items || [];
        const itemCount = items.reduce((count, item) => count + item.quantity, 0);
        const totalAmount = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        return {
            id: cart.id,
            userId: cart.userId,
            items: items.map(item => this.mapItemToDto(item)),
            itemCount,
            subtotal: totalAmount,
            tax: 0,
            shipping: 0,
            discount: 0,
            total: totalAmount,
            createdAt: cart.createdAt,
            updatedAt: cart.updatedAt,
        };
    }
    mapItemToDto(cartItem) {
        const total = cartItem.price * cartItem.quantity;
        return {
            id: cartItem.id,
            cartId: cartItem.cartId,
            productId: cartItem.productId,
            productTitle: cartItem.productTitle,
            productImage: cartItem.productImage,
            quantity: cartItem.quantity,
            price: cartItem.price,
            total: total,
            selectedOptions: cartItem.selectedOptions,
            createdAt: cartItem.createdAt,
            updatedAt: cartItem.updatedAt,
        };
    }
};
exports.CartService = CartService;
exports.CartService = CartService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cart_entity_1.Cart)),
    __param(1, (0, typeorm_1.InjectRepository)(cart_item_entity_1.CartItem)),
    __param(2, (0, common_1.Inject)(common_2.SERVICES.PRODUCT_SERVICE)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, typeof (_c = typeof microservices_1.ClientProxy !== "undefined" && microservices_1.ClientProxy) === "function" ? _c : Object])
], CartService);


/***/ }),
/* 25 */
/***/ ((module) => {

module.exports = require("rxjs");

/***/ }),
/* 26 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.Cart = void 0;
const typeorm_1 = __webpack_require__(9);
const user_entity_1 = __webpack_require__(12);
const cart_item_entity_1 = __webpack_require__(27);
let Cart = class Cart {
};
exports.Cart = Cart;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Cart.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Cart.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'userId' }),
    __metadata("design:type", typeof (_a = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _a : Object)
], Cart.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Cart.prototype, "sessionId", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Cart.prototype, "isSaved", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => cart_item_entity_1.CartItem, (cartItem) => cartItem.cart, {
        cascade: true,
        eager: true,
    }),
    __metadata("design:type", Array)
], Cart.prototype, "items", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], Cart.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], Cart.prototype, "updatedAt", void 0);
exports.Cart = Cart = __decorate([
    (0, typeorm_1.Entity)('carts')
], Cart);


/***/ }),
/* 27 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.CartItem = void 0;
const typeorm_1 = __webpack_require__(9);
const cart_entity_1 = __webpack_require__(26);
let CartItem = class CartItem {
};
exports.CartItem = CartItem;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], CartItem.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], CartItem.prototype, "cartId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => cart_entity_1.Cart, (cart) => cart.items),
    (0, typeorm_1.JoinColumn)({ name: 'cartId' }),
    __metadata("design:type", typeof (_a = typeof cart_entity_1.Cart !== "undefined" && cart_entity_1.Cart) === "function" ? _a : Object)
], CartItem.prototype, "cart", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], CartItem.prototype, "productId", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], CartItem.prototype, "productTitle", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], CartItem.prototype, "productImage", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], CartItem.prototype, "quantity", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 12, scale: 2 }),
    __metadata("design:type", Number)
], CartItem.prototype, "price", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], CartItem.prototype, "repostId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', default: '{}' }),
    __metadata("design:type", typeof (_b = typeof Record !== "undefined" && Record) === "function" ? _b : Object)
], CartItem.prototype, "selectedOptions", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], CartItem.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], CartItem.prototype, "updatedAt", void 0);
exports.CartItem = CartItem = __decorate([
    (0, typeorm_1.Entity)('cart_items')
], CartItem);


/***/ }),
/* 28 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
__exportStar(__webpack_require__(29), exports);
__exportStar(__webpack_require__(39), exports);
__exportStar(__webpack_require__(41), exports);
__exportStar(__webpack_require__(43), exports);
__exportStar(__webpack_require__(47), exports);
__exportStar(__webpack_require__(48), exports);
__exportStar(__webpack_require__(52), exports);
__exportStar(__webpack_require__(13), exports);
__exportStar(__webpack_require__(53), exports);
__exportStar(__webpack_require__(54), exports);
__exportStar(__webpack_require__(55), exports);
__exportStar(__webpack_require__(56), exports);
__exportStar(__webpack_require__(57), exports);
__exportStar(__webpack_require__(58), exports);
__exportStar(__webpack_require__(60), exports);
__exportStar(__webpack_require__(61), exports);
__exportStar(__webpack_require__(62), exports);
__exportStar(__webpack_require__(63), exports);
__exportStar(__webpack_require__(64), exports);
__exportStar(__webpack_require__(18), exports);
__exportStar(__webpack_require__(30), exports);


/***/ }),
/* 29 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.CommonModule = void 0;
const common_1 = __webpack_require__(3);
const config_1 = __webpack_require__(4);
const monitoring_1 = __webpack_require__(30);
let CommonModule = class CommonModule {
};
exports.CommonModule = CommonModule;
exports.CommonModule = CommonModule = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
            }),
        ],
        providers: [monitoring_1.ErrorMonitoringService],
        exports: [monitoring_1.ErrorMonitoringService],
    })
], CommonModule);


/***/ }),
/* 30 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
__exportStar(__webpack_require__(31), exports);
__exportStar(__webpack_require__(32), exports);
__exportStar(__webpack_require__(35), exports);
__exportStar(__webpack_require__(36), exports);
__exportStar(__webpack_require__(37), exports);


/***/ }),
/* 31 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.errorMonitoringConfig = exports.PRODUCTION_ERROR_MONITORING_OPTIONS = exports.QA_ERROR_MONITORING_OPTIONS = exports.DEFAULT_ERROR_MONITORING_OPTIONS = void 0;
const config_1 = __webpack_require__(4);
exports.DEFAULT_ERROR_MONITORING_OPTIONS = {
    enabled: false,
    environment: 'development',
    serviceName: 'unknown',
    sampleRate: 1.0,
    includeStacktrace: true,
    includeRequest: true,
    includeResponse: false,
    ignoredErrorCodes: ['UNAUTHORIZED', 'FORBIDDEN', 'NOT_FOUND', 'VALIDATION_ERROR'],
    ignoredPaths: ['/health', '/metrics', '/favicon.ico'],
    maxBreadcrumbs: 100,
    enablePerformanceMonitoring: false,
    performanceSampleRate: 0.1,
    enableSessionReplay: false,
    sessionReplaySampleRate: 0.01,
};
exports.QA_ERROR_MONITORING_OPTIONS = {
    enabled: true,
    environment: 'qa',
    sampleRate: 1.0,
    includeStacktrace: true,
    includeRequest: true,
    includeResponse: true,
    ignoredErrorCodes: ['UNAUTHORIZED', 'FORBIDDEN'],
    maxBreadcrumbs: 200,
    enablePerformanceMonitoring: true,
    performanceSampleRate: 0.5,
    enableSessionReplay: true,
    sessionReplaySampleRate: 0.1,
};
exports.PRODUCTION_ERROR_MONITORING_OPTIONS = {
    enabled: true,
    environment: 'production',
    sampleRate: 0.1,
    includeStacktrace: true,
    includeRequest: true,
    includeResponse: false,
    ignoredErrorCodes: ['UNAUTHORIZED', 'FORBIDDEN', 'NOT_FOUND', 'VALIDATION_ERROR'],
    maxBreadcrumbs: 50,
    enablePerformanceMonitoring: true,
    performanceSampleRate: 0.01,
    enableSessionReplay: false,
    sessionReplaySampleRate: 0,
};
exports.errorMonitoringConfig = (0, config_1.registerAs)('errorMonitoring', () => {
    const environment = process.env.NODE_ENV || 'development';
    const serviceName = process.env.SERVICE_NAME || 'unknown';
    let config = {
        ...exports.DEFAULT_ERROR_MONITORING_OPTIONS,
        environment,
        serviceName,
    };
    if (environment === 'qa') {
        config = { ...config, ...exports.QA_ERROR_MONITORING_OPTIONS };
    }
    else if (environment === 'production') {
        config = { ...config, ...exports.PRODUCTION_ERROR_MONITORING_OPTIONS };
    }
    if (process.env.ERROR_MONITORING_ENABLED) {
        config.enabled = process.env.ERROR_MONITORING_ENABLED === 'true';
    }
    if (process.env.ERROR_MONITORING_ENDPOINT) {
        config.endpoint = process.env.ERROR_MONITORING_ENDPOINT;
    }
    if (process.env.ERROR_MONITORING_API_KEY) {
        config.apiKey = process.env.ERROR_MONITORING_API_KEY;
    }
    if (process.env.ERROR_MONITORING_SAMPLE_RATE) {
        config.sampleRate = parseFloat(process.env.ERROR_MONITORING_SAMPLE_RATE);
    }
    return config;
});


/***/ }),
/* 32 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ErrorMonitoringService_1;
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ErrorMonitoringService = void 0;
const common_1 = __webpack_require__(3);
const config_1 = __webpack_require__(4);
const errors_1 = __webpack_require__(18);
const Sentry = __webpack_require__(33);
const profiling_node_1 = __webpack_require__(34);
let ErrorMonitoringService = ErrorMonitoringService_1 = class ErrorMonitoringService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(ErrorMonitoringService_1.name);
        this.alertThreshold = 5;
        this.errorCount = new Map();
        this.resetInterval = 60 * 60 * 1000;
        this.initialized = false;
        this.options = this.configService.get('errorMonitoring');
        this.environment = this.options?.environment || this.configService.get('NODE_ENV') || 'development';
        this.serviceName = this.options?.serviceName || this.configService.get('SERVICE_NAME') || 'unknown';
        this.isProduction = this.environment === 'production';
        setInterval(() => this.resetErrorCounts(), this.resetInterval);
    }
    onModuleInit() {
        if (!this.options?.enabled) {
            this.logger.log('Error monitoring is disabled');
            return;
        }
        if (!this.options.apiKey) {
            this.logger.warn('Error monitoring API key is not set, external monitoring will not be initialized');
            return;
        }
        try {
            Sentry.init({
                dsn: this.options.apiKey,
                environment: this.environment,
                release: process.env.npm_package_version || 'unknown',
                tracesSampleRate: this.options.enablePerformanceMonitoring ? this.options.performanceSampleRate : 0,
                sendDefaultPii: true,
                integrations: [
                    (0, profiling_node_1.nodeProfilingIntegration)(),
                ],
                beforeSend: (event, hint) => {
                    const error = hint.originalException;
                    if (error && 'code' in error && this.options.ignoredErrorCodes.includes(error.code)) {
                        return null;
                    }
                    if (!this.options.includeResponse && event.contexts && event.contexts.response) {
                        delete event.contexts.response;
                    }
                    if (!this.options.includeRequest && event.request) {
                        delete event.request;
                    }
                    if (!this.options.includeStacktrace && event.exception) {
                        event.exception.values.forEach(exception => {
                            delete exception.stacktrace;
                        });
                    }
                    return event;
                },
                beforeBreadcrumb: (breadcrumb) => {
                    if (breadcrumb.category === 'http' &&
                        breadcrumb.data &&
                        breadcrumb.data.url &&
                        this.options.ignoredPaths.some(path => breadcrumb.data.url.includes(path))) {
                        return null;
                    }
                    return breadcrumb;
                },
                maxBreadcrumbs: this.options.maxBreadcrumbs,
            });
            this.initialized = true;
            this.logger.log(`Error monitoring initialized for environment: ${this.environment}`);
            this.logger.log(`Performance monitoring enabled: ${this.options.enablePerformanceMonitoring}`);
            this.logger.log(`Session replay enabled: ${this.options.enableSessionReplay}`);
        }
        catch (error) {
            this.logger.error('Failed to initialize error monitoring', error);
        }
    }
    reportError(error, context, metadata) {
        const errorKey = this.getErrorKey(error);
        const currentCount = (this.errorCount.get(errorKey) || 0) + 1;
        this.errorCount.set(errorKey, currentCount);
        this.logger.error(`[${this.serviceName}] ${error.message}`, error.stack, context);
        if (this.initialized && this.options?.enabled) {
            this.captureException(error, { context, ...metadata });
        }
        else if (this.isProduction) {
            this.sendToExternalMonitoring(error, context, metadata);
        }
        if (currentCount >= this.alertThreshold) {
            this.sendAlert(error, currentCount, context);
            this.errorCount.set(errorKey, 0);
        }
    }
    reportAppError(error, context) {
        const metadata = {
            code: error.code,
            statusCode: error.statusCode,
            translationKey: error.translationKey,
            details: error.details,
        };
        this.logger.error(`[${this.serviceName}] ${error.message}`, { ...metadata, stack: error.stack }, context);
        if (this.options?.ignoredErrorCodes?.includes(error.code)) {
            this.logger.debug(`Ignoring error with code ${error.code}: ${error.message}`);
            return;
        }
        if (this.initialized && this.options?.enabled) {
            this.captureException(error, { context, ...metadata });
        }
        else if (this.isProduction) {
            this.sendToExternalMonitoring(error, context, metadata);
        }
        if (error.code === 'INTERNAL_ERROR') {
            const errorKey = this.getErrorKey(error);
            const currentCount = (this.errorCount.get(errorKey) || 0) + 1;
            this.errorCount.set(errorKey, currentCount);
            if (currentCount >= this.alertThreshold) {
                this.sendAlert(error, currentCount, context);
                this.errorCount.set(errorKey, 0);
            }
        }
    }
    setUser(user) {
        if (!this.initialized || !this.options?.enabled) {
            return;
        }
        try {
            Sentry.setUser(user);
        }
        catch (error) {
            this.logger.error('Failed to set user', error);
        }
    }
    addBreadcrumb(breadcrumb) {
        if (!this.initialized || !this.options?.enabled) {
            return;
        }
        try {
            Sentry.addBreadcrumb({
                category: breadcrumb.category,
                message: breadcrumb.message,
                data: breadcrumb.data,
                level: breadcrumb.level,
            });
        }
        catch (error) {
            this.logger.error('Failed to add breadcrumb', error);
        }
    }
    captureException(error, context) {
        if (!this.initialized || !this.options?.enabled) {
            return null;
        }
        if (Math.random() > this.options.sampleRate) {
            return null;
        }
        if (error instanceof errors_1.AppError && this.options.ignoredErrorCodes.includes(error.code)) {
            return null;
        }
        try {
            return Sentry.captureException(error, {
                extra: context,
                tags: {
                    service: this.serviceName,
                    ...(error instanceof errors_1.AppError ? { errorCode: error.code } : {}),
                },
            });
        }
        catch (captureError) {
            this.logger.error('Failed to capture exception', captureError);
            return null;
        }
    }
    captureMessage(message, level = 'info', context) {
        if (!this.initialized || !this.options?.enabled) {
            return null;
        }
        if (Math.random() > this.options.sampleRate) {
            return null;
        }
        try {
            return Sentry.captureMessage(message, {
                level: level,
                extra: context,
                tags: {
                    service: this.serviceName,
                },
            });
        }
        catch (error) {
            this.logger.error('Failed to capture message', error);
            return null;
        }
    }
    resetErrorCounts() {
        this.errorCount.clear();
    }
    getErrorKey(error) {
        const stackLine = error.stack?.split('\n')[1] || '';
        return `${error.name}:${error.message}:${stackLine}`;
    }
    sendToExternalMonitoring(error, context, metadata) {
        const monitoringType = this.configService.get('MONITORING_TYPE');
        if (!monitoringType) {
            this.logger.log(`External monitoring not configured, skipping: ${error.message}`);
            return;
        }
        try {
            switch (monitoringType.toLowerCase()) {
                case 'sentry':
                    this.sendToSentry(error, context, metadata);
                    break;
                case 'datadog':
                    this.sendToDatadog(error, context, metadata);
                    break;
                case 'newrelic':
                    this.sendToNewRelic(error, context, metadata);
                    break;
                default:
                    this.logger.warn(`Unknown monitoring type: ${monitoringType}`);
            }
        }
        catch (monitoringError) {
            this.logger.error(`Failed to send error to external monitoring: ${monitoringError.message}`, monitoringError.stack);
        }
    }
    sendToSentry(error, context, metadata) {
        this.logger.log(`Would send to Sentry: ${error.message}`);
    }
    sendToDatadog(error, context, metadata) {
        this.logger.log(`Would send to Datadog: ${error.message}`);
    }
    sendToNewRelic(error, context, metadata) {
        this.logger.log(`Would send to New Relic: ${error.message}`);
    }
    sendAlert(error, count, context) {
        const alertingType = this.configService.get('ALERTING_TYPE');
        this.logger.warn(`ALERT: Error occurred ${count} times: ${error.message}`, error.stack, context);
        if (!alertingType) {
            this.logger.log(`Alerting not configured, skipping alert for: ${error.message}`);
            return;
        }
        try {
            switch (alertingType.toLowerCase()) {
                case 'email':
                    this.sendEmailAlert(error, count, context);
                    break;
                case 'slack':
                    this.sendSlackAlert(error, count, context);
                    break;
                case 'pagerduty':
                    this.sendPagerDutyAlert(error, count, context);
                    break;
                default:
                    this.logger.warn(`Unknown alerting type: ${alertingType}`);
            }
        }
        catch (alertingError) {
            this.logger.error(`Failed to send alert: ${alertingError.message}`, alertingError.stack);
        }
    }
    sendEmailAlert(error, count, context) {
        this.logger.log(`Would send email alert for: ${error.message}`);
    }
    sendSlackAlert(error, count, context) {
        this.logger.log(`Would send Slack alert for: ${error.message}`);
    }
    sendPagerDutyAlert(error, count, context) {
        this.logger.log(`Would send PagerDuty alert for: ${error.message}`);
    }
};
exports.ErrorMonitoringService = ErrorMonitoringService;
exports.ErrorMonitoringService = ErrorMonitoringService = ErrorMonitoringService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], ErrorMonitoringService);


/***/ }),
/* 33 */
/***/ ((module) => {

module.exports = require("@sentry/node");

/***/ }),
/* 34 */
/***/ ((module) => {

module.exports = require("@sentry/profiling-node");

/***/ }),
/* 35 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var ErrorMonitoringModule_1;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ErrorMonitoringModule = void 0;
const common_1 = __webpack_require__(3);
const config_1 = __webpack_require__(4);
const error_monitoring_service_1 = __webpack_require__(32);
const error_monitoring_config_1 = __webpack_require__(31);
let ErrorMonitoringModule = ErrorMonitoringModule_1 = class ErrorMonitoringModule {
    static forRoot() {
        return {
            module: ErrorMonitoringModule_1,
            imports: [
                config_1.ConfigModule.forFeature(error_monitoring_config_1.errorMonitoringConfig),
            ],
            providers: [
                error_monitoring_service_1.ErrorMonitoringService,
            ],
            exports: [
                error_monitoring_service_1.ErrorMonitoringService,
            ],
            global: true,
        };
    }
    static forRootAsync(options) {
        const optionsProvider = {
            provide: 'ERROR_MONITORING_OPTIONS',
            useFactory: options.useFactory,
            inject: options.inject || [],
        };
        return {
            module: ErrorMonitoringModule_1,
            imports: [
                config_1.ConfigModule.forFeature(error_monitoring_config_1.errorMonitoringConfig),
                ...(options.imports || []),
            ],
            providers: [
                optionsProvider,
                {
                    provide: error_monitoring_service_1.ErrorMonitoringService,
                    useFactory: (configOptions) => {
                        process.env.ERROR_MONITORING_ENABLED = String(configOptions.enabled ?? true);
                        if (configOptions.apiKey) {
                            process.env.ERROR_MONITORING_API_KEY = configOptions.apiKey;
                        }
                        if (configOptions.endpoint) {
                            process.env.ERROR_MONITORING_ENDPOINT = configOptions.endpoint;
                        }
                        if (configOptions.sampleRate !== undefined) {
                            process.env.ERROR_MONITORING_SAMPLE_RATE = String(configOptions.sampleRate);
                        }
                        return new error_monitoring_service_1.ErrorMonitoringService(null);
                    },
                    inject: ['ERROR_MONITORING_OPTIONS'],
                },
            ],
            exports: [
                error_monitoring_service_1.ErrorMonitoringService,
            ],
            global: true,
        };
    }
    static forQA(options = {}) {
        return this.forRootAsync({
            useFactory: () => ({
                enabled: true,
                environment: 'qa',
                sampleRate: 1.0,
                includeStacktrace: true,
                includeRequest: true,
                includeResponse: true,
                ignoredErrorCodes: ['UNAUTHORIZED', 'FORBIDDEN'],
                maxBreadcrumbs: 200,
                enablePerformanceMonitoring: true,
                performanceSampleRate: 0.5,
                enableSessionReplay: true,
                sessionReplaySampleRate: 0.1,
                ...options,
            }),
        });
    }
};
exports.ErrorMonitoringModule = ErrorMonitoringModule;
exports.ErrorMonitoringModule = ErrorMonitoringModule = ErrorMonitoringModule_1 = __decorate([
    (0, common_1.Module)({})
], ErrorMonitoringModule);


/***/ }),
/* 36 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ErrorMonitoringMiddleware_1;
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ErrorMonitoringMiddleware = void 0;
const common_1 = __webpack_require__(3);
const error_monitoring_service_1 = __webpack_require__(32);
const Sentry = __webpack_require__(33);
let ErrorMonitoringMiddleware = ErrorMonitoringMiddleware_1 = class ErrorMonitoringMiddleware {
    constructor(errorMonitoringService) {
        this.errorMonitoringService = errorMonitoringService;
        this.logger = new common_1.Logger(ErrorMonitoringMiddleware_1.name);
    }
    use(req, res, next) {
        Sentry.startSpanManual({
            name: `${req.method} ${req.path}`,
            op: 'http.server',
            forceTransaction: true,
        }, (span) => {
            span.setAttribute('request.method', req.method);
            span.setAttribute('request.url', req.url);
            if (req.user) {
                this.errorMonitoringService.setUser({
                    id: req.user['id'] || req.user['sub'] || 'unknown',
                    email: req.user['email'],
                    username: req.user['username'],
                });
            }
            this.logger.log(`HTTP Request: ${req.method} ${req.path}`);
            this.errorMonitoringService.addBreadcrumb({
                category: 'http',
                message: `${req.method} ${req.url}`,
                data: {
                    method: req.method,
                    url: req.url,
                    status_code: res.statusCode,
                },
                level: 'info',
            });
            res.on('finish', () => {
                span.setAttribute('response.status_code', res.statusCode);
                if (res.statusCode >= 500) {
                    this.logger.error(`Response error: ${req.method} ${req.path} - ${res.statusCode}`);
                }
                else if (res.statusCode >= 400) {
                    this.logger.warn(`Response warning: ${req.method} ${req.path} - ${res.statusCode}`);
                }
                else {
                    this.logger.log(`Response success: ${req.method} ${req.path} - ${res.statusCode}`);
                }
                span.end();
            });
            next();
        });
    }
};
exports.ErrorMonitoringMiddleware = ErrorMonitoringMiddleware;
exports.ErrorMonitoringMiddleware = ErrorMonitoringMiddleware = ErrorMonitoringMiddleware_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof error_monitoring_service_1.ErrorMonitoringService !== "undefined" && error_monitoring_service_1.ErrorMonitoringService) === "function" ? _a : Object])
], ErrorMonitoringMiddleware);


/***/ }),
/* 37 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ErrorMonitoringInterceptor_1;
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ErrorMonitoringInterceptor = void 0;
const common_1 = __webpack_require__(3);
const rxjs_1 = __webpack_require__(25);
const operators_1 = __webpack_require__(38);
const error_monitoring_service_1 = __webpack_require__(32);
let ErrorMonitoringInterceptor = ErrorMonitoringInterceptor_1 = class ErrorMonitoringInterceptor {
    constructor(errorMonitoringService) {
        this.errorMonitoringService = errorMonitoringService;
        this.logger = new common_1.Logger(ErrorMonitoringInterceptor_1.name);
    }
    intercept(context, next) {
        const request = context.switchToHttp().getRequest();
        const { method, url, body, params, query, headers, user } = request;
        this.logger.log(`Handling request: ${method} ${url}`);
        if (user) {
            this.logger.log(`User: ${user.id || user.sub || 'unknown'}`);
        }
        this.errorMonitoringService.addBreadcrumb({
            category: 'handler',
            message: `${context.getClass().name}.${context.getHandler().name}`,
            data: {
                class: context.getClass().name,
                handler: context.getHandler().name,
            },
            level: 'info',
        });
        return next.handle().pipe((0, operators_1.tap)((data) => {
            this.logger.log(`Request completed: ${method} ${url}`);
        }), (0, operators_1.catchError)((error) => {
            this.logger.error(`Request failed: ${method} ${url}`, error.stack);
            this.errorMonitoringService.reportError(error, `${context.getClass().name}.${context.getHandler().name}`, {
                request: {
                    method,
                    url,
                    params,
                    query,
                },
            });
            return (0, rxjs_1.throwError)(() => error);
        }));
    }
};
exports.ErrorMonitoringInterceptor = ErrorMonitoringInterceptor;
exports.ErrorMonitoringInterceptor = ErrorMonitoringInterceptor = ErrorMonitoringInterceptor_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof error_monitoring_service_1.ErrorMonitoringService !== "undefined" && error_monitoring_service_1.ErrorMonitoringService) === "function" ? _a : Object])
], ErrorMonitoringInterceptor);


/***/ }),
/* 38 */
/***/ ((module) => {

module.exports = require("rxjs/operators");

/***/ }),
/* 39 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var DatabaseModule_1;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.DatabaseModule = void 0;
const common_1 = __webpack_require__(3);
const config_1 = __webpack_require__(4);
const typeorm_1 = __webpack_require__(5);
const mongoose_1 = __webpack_require__(40);
let DatabaseModule = DatabaseModule_1 = class DatabaseModule {
    static forRoot() {
        const imports = [
            typeorm_1.TypeOrmModule.forRootAsync({
                imports: [config_1.ConfigModule],
                inject: [config_1.ConfigService],
                useFactory: (configService) => ({
                    type: 'postgres',
                    url: configService.get('DATABASE_URL'),
                    autoLoadEntities: true,
                    synchronize: configService.get('NODE_ENV') !== 'production',
                    logging: configService.get('NODE_ENV') !== 'production',
                }),
            }),
        ];
        if (process.env.DISABLE_MONGODB !== 'true') {
            imports.push(mongoose_1.MongooseModule.forRootAsync({
                imports: [config_1.ConfigModule],
                inject: [config_1.ConfigService],
                useFactory: (configService) => ({
                    uri: configService.get('MONGODB_URI'),
                }),
            }));
        }
        return {
            module: DatabaseModule_1,
            imports,
        };
    }
};
exports.DatabaseModule = DatabaseModule;
exports.DatabaseModule = DatabaseModule = DatabaseModule_1 = __decorate([
    (0, common_1.Module)({})
], DatabaseModule);


/***/ }),
/* 40 */
/***/ ((module) => {

module.exports = require("@nestjs/mongoose");

/***/ }),
/* 41 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var RmqModule_1;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.RmqModule = void 0;
const common_1 = __webpack_require__(3);
const config_1 = __webpack_require__(4);
const microservices_1 = __webpack_require__(6);
const rmq_service_1 = __webpack_require__(42);
let RmqModule = RmqModule_1 = class RmqModule {
    static register({ name }) {
        return {
            module: RmqModule_1,
            imports: [
                microservices_1.ClientsModule.registerAsync([
                    {
                        name,
                        useFactory: (configService) => ({
                            transport: microservices_1.Transport.RMQ,
                            options: {
                                urls: [configService.get('RABBITMQ_URL')],
                                queue: configService.get(`RABBITMQ_${name}_QUEUE`),
                            },
                        }),
                        inject: [config_1.ConfigService],
                    },
                ]),
            ],
            exports: [microservices_1.ClientsModule],
        };
    }
};
exports.RmqModule = RmqModule;
exports.RmqModule = RmqModule = RmqModule_1 = __decorate([
    (0, common_1.Module)({
        providers: [rmq_service_1.RmqService],
        exports: [rmq_service_1.RmqService],
    })
], RmqModule);


/***/ }),
/* 42 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.RmqService = void 0;
const common_1 = __webpack_require__(3);
const config_1 = __webpack_require__(4);
const microservices_1 = __webpack_require__(6);
let RmqService = class RmqService {
    constructor(configService) {
        this.configService = configService;
    }
    getOptions(queue, noAck = false) {
        return {
            transport: microservices_1.Transport.RMQ,
            options: {
                urls: [this.configService.get('RABBITMQ_URL')],
                queue: this.configService.get(`RABBITMQ_${queue}_QUEUE`),
                noAck,
                persistent: true,
            },
        };
    }
    ack(context) {
        const channel = context.getChannelRef();
        const originalMessage = context.getMessage();
        channel.ack(originalMessage);
    }
};
exports.RmqService = RmqService;
exports.RmqService = RmqService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], RmqService);


/***/ }),
/* 43 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AuthModule = void 0;
const common_1 = __webpack_require__(3);
const config_1 = __webpack_require__(4);
const jwt_1 = __webpack_require__(10);
const passport_1 = __webpack_require__(44);
const jwt_strategy_1 = __webpack_require__(45);
let AuthModule = class AuthModule {
};
exports.AuthModule = AuthModule;
exports.AuthModule = AuthModule = __decorate([
    (0, common_1.Module)({
        imports: [
            passport_1.PassportModule.register({ defaultStrategy: 'jwt' }),
            jwt_1.JwtModule.registerAsync({
                imports: [config_1.ConfigModule],
                inject: [config_1.ConfigService],
                useFactory: (configService) => ({
                    secret: configService.get('JWT_SECRET'),
                    signOptions: {
                        expiresIn: configService.get('JWT_EXPIRATION'),
                    },
                }),
            }),
        ],
        providers: [jwt_strategy_1.JwtStrategy],
        exports: [jwt_1.JwtModule],
    })
], AuthModule);


/***/ }),
/* 44 */
/***/ ((module) => {

module.exports = require("@nestjs/passport");

/***/ }),
/* 45 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.JwtStrategy = void 0;
const common_1 = __webpack_require__(3);
const config_1 = __webpack_require__(4);
const passport_1 = __webpack_require__(44);
const passport_jwt_1 = __webpack_require__(46);
let JwtStrategy = class JwtStrategy extends (0, passport_1.PassportStrategy)(passport_jwt_1.Strategy) {
    constructor(configService) {
        super({
            jwtFromRequest: passport_jwt_1.ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: false,
            secretOrKey: configService.get('JWT_SECRET'),
        });
    }
    async validate(payload) {
        if (!payload.sub || !payload.email) {
            throw new common_1.UnauthorizedException();
        }
        return { userId: payload.sub, email: payload.email };
    }
};
exports.JwtStrategy = JwtStrategy;
exports.JwtStrategy = JwtStrategy = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], JwtStrategy);


/***/ }),
/* 46 */
/***/ ((module) => {

module.exports = require("passport-jwt");

/***/ }),
/* 47 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.MediaModule = void 0;
const common_1 = __webpack_require__(3);
const config_1 = __webpack_require__(4);
const media_service_1 = __webpack_require__(48);
let MediaModule = class MediaModule {
};
exports.MediaModule = MediaModule;
exports.MediaModule = MediaModule = __decorate([
    (0, common_1.Module)({
        imports: [config_1.ConfigModule],
        providers: [media_service_1.MediaService],
        exports: [media_service_1.MediaService],
    })
], MediaModule);


/***/ }),
/* 48 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.MediaService = void 0;
const common_1 = __webpack_require__(3);
const config_1 = __webpack_require__(4);
const aws_sdk_1 = __webpack_require__(49);
const uuid_1 = __webpack_require__(50);
const sharp = __webpack_require__(51);
let MediaService = class MediaService {
    constructor(configService) {
        this.configService = configService;
        this.s3 = new aws_sdk_1.S3({
            accessKeyId: this.configService.get('AWS_ACCESS_KEY_ID'),
            secretAccessKey: this.configService.get('AWS_SECRET_ACCESS_KEY'),
            region: this.configService.get('AWS_REGION'),
        });
    }
    async uploadFile(file, mimetype, folder = 'uploads') {
        const key = `${folder}/${(0, uuid_1.v4)()}-${Date.now()}`;
        await this.s3
            .upload({
            Bucket: this.configService.get('AWS_S3_BUCKET'),
            Key: key,
            Body: file,
            ContentType: mimetype,
            ACL: 'public-read',
        })
            .promise();
        return `https://${this.configService.get('AWS_S3_BUCKET')}.s3.amazonaws.com/${key}`;
    }
    async deleteFile(fileUrl) {
        const key = fileUrl.split('.com/')[1];
        await this.s3
            .deleteObject({
            Bucket: this.configService.get('AWS_S3_BUCKET'),
            Key: key,
        })
            .promise();
    }
    async optimizeAndUploadImage(file, mimetype, width = 1200, quality = 80, folder = 'images') {
        const optimizedImage = await sharp(file)
            .resize(width)
            .jpeg({ quality })
            .toBuffer();
        return this.uploadFile(optimizedImage, 'image/jpeg', folder);
    }
    async generateImageVariants(file, mimetype, folder = 'images') {
        const baseKey = `${folder}/${(0, uuid_1.v4)()}-${Date.now()}`;
        const bucket = this.configService.get('AWS_S3_BUCKET');
        const thumbnail = await sharp(file)
            .resize(150)
            .jpeg({ quality: 70 })
            .toBuffer();
        const medium = await sharp(file)
            .resize(600)
            .jpeg({ quality: 80 })
            .toBuffer();
        const large = await sharp(file)
            .resize(1200)
            .jpeg({ quality: 85 })
            .toBuffer();
        const [thumbnailUrl, mediumUrl, largeUrl, originalUrl] = await Promise.all([
            this.s3
                .upload({
                Bucket: bucket,
                Key: `${baseKey}-thumbnail.jpg`,
                Body: thumbnail,
                ContentType: 'image/jpeg',
                ACL: 'public-read',
            })
                .promise()
                .then(data => data.Location),
            this.s3
                .upload({
                Bucket: bucket,
                Key: `${baseKey}-medium.jpg`,
                Body: medium,
                ContentType: 'image/jpeg',
                ACL: 'public-read',
            })
                .promise()
                .then(data => data.Location),
            this.s3
                .upload({
                Bucket: bucket,
                Key: `${baseKey}-large.jpg`,
                Body: large,
                ContentType: 'image/jpeg',
                ACL: 'public-read',
            })
                .promise()
                .then(data => data.Location),
            this.s3
                .upload({
                Bucket: bucket,
                Key: `${baseKey}-original.jpg`,
                Body: file,
                ContentType: mimetype,
                ACL: 'public-read',
            })
                .promise()
                .then(data => data.Location),
        ]);
        return {
            thumbnail: thumbnailUrl,
            medium: mediumUrl,
            large: largeUrl,
            original: originalUrl,
        };
    }
};
exports.MediaService = MediaService;
exports.MediaService = MediaService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], MediaService);


/***/ }),
/* 49 */
/***/ ((module) => {

module.exports = require("aws-sdk");

/***/ }),
/* 50 */
/***/ ((module) => {

module.exports = require("uuid");

/***/ }),
/* 51 */
/***/ ((module) => {

module.exports = require("sharp");

/***/ }),
/* 52 */
/***/ ((__unused_webpack_module, exports) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.SERVICES = void 0;
exports.SERVICES = {
    USER_SERVICE: 'USER_SERVICE',
    STORE_SERVICE: 'STORE_SERVICE',
    PRODUCT_SERVICE: 'PRODUCT_SERVICE',
    ORDER_SERVICE: 'ORDER_SERVICE',
    ANALYTICS_SERVICE: 'ANALYTICS_SERVICE',
    PAYMENT_SERVICE: 'PAYMENT_SERVICE',
    SOCIAL_SERVICE: 'SOCIAL_SERVICE',
    MESSAGING_SERVICE: 'MESSAGING_SERVICE',
    SEARCH_SERVICE: 'SEARCH_SERVICE',
    CART_SERVICE: 'CART_SERVICE',
    RECOMMENDATION_SERVICE: 'RECOMMENDATION_SERVICE',
    GROUP_BUYING_SERVICE: 'GROUP_BUYING_SERVICE',
    AFFILIATE_SERVICE: 'AFFILIATE_SERVICE',
};


/***/ }),
/* 53 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.StoreDto = exports.StoreResponseDto = exports.UpdateStoreDto = exports.CreateStoreDto = void 0;
const class_validator_1 = __webpack_require__(14);
class CreateStoreDto {
}
exports.CreateStoreDto = CreateStoreDto;
__decorate([
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateStoreDto.prototype, "ownerId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(3),
    __metadata("design:type", String)
], CreateStoreDto.prototype, "username", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateStoreDto.prototype, "displayName", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateStoreDto.prototype, "bio", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateStoreDto.prototype, "profileImageUrl", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateStoreDto.prototype, "headerImageUrl", void 0);
__decorate([
    (0, class_validator_1.IsObject)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", typeof (_a = typeof Record !== "undefined" && Record) === "function" ? _a : Object)
], CreateStoreDto.prototype, "themeSettings", void 0);
class UpdateStoreDto {
}
exports.UpdateStoreDto = UpdateStoreDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateStoreDto.prototype, "displayName", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateStoreDto.prototype, "bio", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateStoreDto.prototype, "profileImageUrl", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateStoreDto.prototype, "headerImageUrl", void 0);
__decorate([
    (0, class_validator_1.IsObject)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", typeof (_b = typeof Record !== "undefined" && Record) === "function" ? _b : Object)
], UpdateStoreDto.prototype, "themeSettings", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], UpdateStoreDto.prototype, "consolidationEnabled", void 0);
__decorate([
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], UpdateStoreDto.prototype, "consolidationIntervals", void 0);
__decorate([
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], UpdateStoreDto.prototype, "consolidationProductIds", void 0);
class StoreResponseDto {
}
exports.StoreResponseDto = StoreResponseDto;
class StoreDto extends StoreResponseDto {
}
exports.StoreDto = StoreDto;


/***/ }),
/* 54 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ProductDto = exports.ProductResponseDto = exports.UpdateProductDto = exports.CreateProductDto = exports.CommissionType = exports.PostType = void 0;
const class_validator_1 = __webpack_require__(14);
var PostType;
(function (PostType) {
    PostType["REGULAR"] = "REGULAR";
    PostType["GROUP_BUY"] = "GROUP_BUY";
})(PostType || (exports.PostType = PostType = {}));
var CommissionType;
(function (CommissionType) {
    CommissionType["FIXED"] = "FIXED";
    CommissionType["PERCENTAGE"] = "PERCENTAGE";
})(CommissionType || (exports.CommissionType = CommissionType = {}));
class CreateProductDto {
}
exports.CreateProductDto = CreateProductDto;
__decorate([
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateProductDto.prototype, "storeId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateProductDto.prototype, "title", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateProductDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateProductDto.prototype, "price", void 0);
__decorate([
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], CreateProductDto.prototype, "mediaUrls", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(PostType),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateProductDto.prototype, "postType", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateProductDto.prototype, "isActive", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateProductDto.prototype, "affiliateEnabled", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(CommissionType),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateProductDto.prototype, "commissionType", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateProductDto.prototype, "commissionValue", void 0);
__decorate([
    (0, class_validator_1.IsObject)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", typeof (_a = typeof Record !== "undefined" && Record) === "function" ? _a : Object)
], CreateProductDto.prototype, "rules", void 0);
class UpdateProductDto {
}
exports.UpdateProductDto = UpdateProductDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateProductDto.prototype, "title", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateProductDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateProductDto.prototype, "price", void 0);
__decorate([
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], UpdateProductDto.prototype, "mediaUrls", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(PostType),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateProductDto.prototype, "postType", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], UpdateProductDto.prototype, "isActive", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], UpdateProductDto.prototype, "affiliateEnabled", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(CommissionType),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateProductDto.prototype, "commissionType", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateProductDto.prototype, "commissionValue", void 0);
__decorate([
    (0, class_validator_1.IsObject)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", typeof (_b = typeof Record !== "undefined" && Record) === "function" ? _b : Object)
], UpdateProductDto.prototype, "rules", void 0);
class ProductResponseDto {
}
exports.ProductResponseDto = ProductResponseDto;
class ProductDto extends ProductResponseDto {
}
exports.ProductDto = ProductDto;


/***/ }),
/* 55 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AdjustInventoryDto = exports.UpdateInventoryDto = exports.CreateInventoryDto = exports.InventoryDto = void 0;
const class_validator_1 = __webpack_require__(14);
class InventoryDto {
}
exports.InventoryDto = InventoryDto;
class CreateInventoryDto {
}
exports.CreateInventoryDto = CreateInventoryDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateInventoryDto.prototype, "productId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateInventoryDto.prototype, "variantId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", typeof (_a = typeof Record !== "undefined" && Record) === "function" ? _a : Object)
], CreateInventoryDto.prototype, "variantOptions", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateInventoryDto.prototype, "quantity", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateInventoryDto.prototype, "lowStockThreshold", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateInventoryDto.prototype, "trackInventory", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateInventoryDto.prototype, "allowBackorders", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateInventoryDto.prototype, "isDigital", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateInventoryDto.prototype, "sku", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateInventoryDto.prototype, "barcode", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CreateInventoryDto.prototype, "dimensions", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CreateInventoryDto.prototype, "location", void 0);
class UpdateInventoryDto {
}
exports.UpdateInventoryDto = UpdateInventoryDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], UpdateInventoryDto.prototype, "quantity", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], UpdateInventoryDto.prototype, "lowStockThreshold", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateInventoryDto.prototype, "trackInventory", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateInventoryDto.prototype, "allowBackorders", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateInventoryDto.prototype, "sku", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateInventoryDto.prototype, "barcode", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], UpdateInventoryDto.prototype, "dimensions", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], UpdateInventoryDto.prototype, "location", void 0);
class AdjustInventoryDto {
}
exports.AdjustInventoryDto = AdjustInventoryDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], AdjustInventoryDto.prototype, "quantity", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AdjustInventoryDto.prototype, "notes", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AdjustInventoryDto.prototype, "referenceId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AdjustInventoryDto.prototype, "referenceType", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", typeof (_b = typeof Record !== "undefined" && Record) === "function" ? _b : Object)
], AdjustInventoryDto.prototype, "metadata", void 0);


/***/ }),
/* 56 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.CreateInventoryTransactionDto = exports.InventoryTransactionDto = exports.TransactionType = void 0;
const class_validator_1 = __webpack_require__(14);
var TransactionType;
(function (TransactionType) {
    TransactionType["PURCHASE"] = "purchase";
    TransactionType["SALE"] = "sale";
    TransactionType["RETURN"] = "return";
    TransactionType["ADJUSTMENT"] = "adjustment";
    TransactionType["RESERVATION"] = "reservation";
    TransactionType["RELEASE"] = "release";
    TransactionType["TRANSFER"] = "transfer";
})(TransactionType || (exports.TransactionType = TransactionType = {}));
class InventoryTransactionDto {
}
exports.InventoryTransactionDto = InventoryTransactionDto;
class CreateInventoryTransactionDto {
}
exports.CreateInventoryTransactionDto = CreateInventoryTransactionDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateInventoryTransactionDto.prototype, "inventoryId", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsEnum)(TransactionType),
    __metadata("design:type", String)
], CreateInventoryTransactionDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], CreateInventoryTransactionDto.prototype, "quantity", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateInventoryTransactionDto.prototype, "referenceId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateInventoryTransactionDto.prototype, "referenceType", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateInventoryTransactionDto.prototype, "userId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateInventoryTransactionDto.prototype, "notes", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", typeof (_a = typeof Record !== "undefined" && Record) === "function" ? _a : Object)
], CreateInventoryTransactionDto.prototype, "metadata", void 0);


/***/ }),
/* 57 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.MergeCartsDto = exports.UpdateCartDto = exports.ApplyCouponDto = exports.CartSummaryDto = exports.UpdateCartItemDto = exports.AddToCartDto = exports.CreateCartDto = exports.CartDto = exports.CartItemDto = void 0;
const class_validator_1 = __webpack_require__(14);
class CartItemDto {
}
exports.CartItemDto = CartItemDto;
class CartDto {
}
exports.CartDto = CartDto;
class CreateCartDto {
}
exports.CreateCartDto = CreateCartDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateCartDto.prototype, "userId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateCartDto.prototype, "sessionId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateCartDto.prototype, "isGuestCart", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", typeof (_a = typeof Record !== "undefined" && Record) === "function" ? _a : Object)
], CreateCartDto.prototype, "metadata", void 0);
class AddToCartDto {
}
exports.AddToCartDto = AddToCartDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], AddToCartDto.prototype, "productId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AddToCartDto.prototype, "variantId", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], AddToCartDto.prototype, "quantity", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", typeof (_b = typeof Record !== "undefined" && Record) === "function" ? _b : Object)
], AddToCartDto.prototype, "selectedOptions", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", typeof (_c = typeof Record !== "undefined" && Record) === "function" ? _c : Object)
], AddToCartDto.prototype, "metadata", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], AddToCartDto.prototype, "repostId", void 0);
class UpdateCartItemDto {
}
exports.UpdateCartItemDto = UpdateCartItemDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], UpdateCartItemDto.prototype, "quantity", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", typeof (_d = typeof Record !== "undefined" && Record) === "function" ? _d : Object)
], UpdateCartItemDto.prototype, "selectedOptions", void 0);
class CartSummaryDto {
}
exports.CartSummaryDto = CartSummaryDto;
class ApplyCouponDto {
}
exports.ApplyCouponDto = ApplyCouponDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApplyCouponDto.prototype, "code", void 0);
class UpdateCartDto {
}
exports.UpdateCartDto = UpdateCartDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateCartDto.prototype, "userId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateCartDto.prototype, "couponCode", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateCartDto.prototype, "shippingMethodId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", typeof (_e = typeof Record !== "undefined" && Record) === "function" ? _e : Object)
], UpdateCartDto.prototype, "shippingAddress", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", typeof (_f = typeof Record !== "undefined" && Record) === "function" ? _f : Object)
], UpdateCartDto.prototype, "billingAddress", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateCartDto.prototype, "abandoned", void 0);
class MergeCartsDto {
}
exports.MergeCartsDto = MergeCartsDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], MergeCartsDto.prototype, "sourceCartId", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], MergeCartsDto.prototype, "targetCartId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], MergeCartsDto.prototype, "deleteSourceCart", void 0);


/***/ }),
/* 58 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.FeedQueryDto = exports.UpdateNotificationDto = exports.NotificationDto = exports.ActivityDto = exports.CreateLikeDto = exports.LikeDto = exports.UpdateCommentDto = exports.CreateCommentDto = exports.CommentDto = exports.UpdatePostDto = exports.CreatePostDto = exports.PostDto = exports.CreatePostTagDto = exports.PostTagDto = exports.CreatePostMediaDto = exports.PostMediaDto = exports.UpdateFollowDto = exports.CreateFollowDto = exports.FollowDto = void 0;
const class_validator_1 = __webpack_require__(14);
const class_transformer_1 = __webpack_require__(59);
class FollowDto {
}
exports.FollowDto = FollowDto;
class CreateFollowDto {
}
exports.CreateFollowDto = CreateFollowDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateFollowDto.prototype, "followingId", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(['user', 'store']),
    __metadata("design:type", String)
], CreateFollowDto.prototype, "followingType", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateFollowDto.prototype, "notificationSettings", void 0);
class UpdateFollowDto {
}
exports.UpdateFollowDto = UpdateFollowDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateFollowDto.prototype, "isApproved", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateFollowDto.prototype, "isBlocked", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateFollowDto.prototype, "notificationSettings", void 0);
class PostMediaDto {
}
exports.PostMediaDto = PostMediaDto;
class CreatePostMediaDto {
}
exports.CreatePostMediaDto = CreatePostMediaDto;
__decorate([
    (0, class_validator_1.IsEnum)(['image', 'video', 'gif']),
    __metadata("design:type", String)
], CreatePostMediaDto.prototype, "mediaType", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreatePostMediaDto.prototype, "mediaUrl", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePostMediaDto.prototype, "thumbnailUrl", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePostMediaDto.prototype, "altText", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreatePostMediaDto.prototype, "width", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreatePostMediaDto.prototype, "height", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreatePostMediaDto.prototype, "duration", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", typeof (_a = typeof Record !== "undefined" && Record) === "function" ? _a : Object)
], CreatePostMediaDto.prototype, "metadata", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreatePostMediaDto.prototype, "sortOrder", void 0);
class PostTagDto {
}
exports.PostTagDto = PostTagDto;
class CreatePostTagDto {
}
exports.CreatePostTagDto = CreatePostTagDto;
__decorate([
    (0, class_validator_1.IsEnum)(['product', 'user', 'store', 'hashtag']),
    __metadata("design:type", String)
], CreatePostTagDto.prototype, "tagType", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreatePostTagDto.prototype, "tagId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePostTagDto.prototype, "tagName", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreatePostTagDto.prototype, "xPosition", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreatePostTagDto.prototype, "yPosition", void 0);
class PostDto {
}
exports.PostDto = PostDto;
class CreatePostDto {
}
exports.CreatePostDto = CreatePostDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(500),
    __metadata("design:type", String)
], CreatePostDto.prototype, "content", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePostDto.prototype, "storeId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", typeof (_b = typeof Record !== "undefined" && Record) === "function" ? _b : Object)
], CreatePostDto.prototype, "metadata", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['public', 'followers', 'private']),
    __metadata("design:type", String)
], CreatePostDto.prototype, "visibility", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePostDto.prototype, "location", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => CreatePostMediaDto),
    __metadata("design:type", Array)
], CreatePostDto.prototype, "media", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => CreatePostTagDto),
    __metadata("design:type", Array)
], CreatePostDto.prototype, "tags", void 0);
class UpdatePostDto {
}
exports.UpdatePostDto = UpdatePostDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(500),
    __metadata("design:type", String)
], UpdatePostDto.prototype, "content", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", typeof (_c = typeof Record !== "undefined" && Record) === "function" ? _c : Object)
], UpdatePostDto.prototype, "metadata", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['public', 'followers', 'private']),
    __metadata("design:type", String)
], UpdatePostDto.prototype, "visibility", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdatePostDto.prototype, "isArchived", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdatePostDto.prototype, "location", void 0);
class CommentDto {
}
exports.CommentDto = CommentDto;
class CreateCommentDto {
}
exports.CreateCommentDto = CreateCommentDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateCommentDto.prototype, "postId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateCommentDto.prototype, "parentId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(500),
    __metadata("design:type", String)
], CreateCommentDto.prototype, "content", void 0);
class UpdateCommentDto {
}
exports.UpdateCommentDto = UpdateCommentDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(500),
    __metadata("design:type", String)
], UpdateCommentDto.prototype, "content", void 0);
class LikeDto {
}
exports.LikeDto = LikeDto;
class CreateLikeDto {
}
exports.CreateLikeDto = CreateLikeDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateLikeDto.prototype, "postId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateLikeDto.prototype, "commentId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['like', 'love', 'laugh', 'wow', 'sad', 'angry']),
    __metadata("design:type", String)
], CreateLikeDto.prototype, "reactionType", void 0);
class ActivityDto {
}
exports.ActivityDto = ActivityDto;
class NotificationDto {
}
exports.NotificationDto = NotificationDto;
class UpdateNotificationDto {
}
exports.UpdateNotificationDto = UpdateNotificationDto;
__decorate([
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateNotificationDto.prototype, "isRead", void 0);
class FeedQueryDto {
}
exports.FeedQueryDto = FeedQueryDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FeedQueryDto.prototype, "userId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['global', 'following', 'trending', 'recommended']),
    __metadata("design:type", String)
], FeedQueryDto.prototype, "feedType", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], FeedQueryDto.prototype, "page", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], FeedQueryDto.prototype, "limit", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], FeedQueryDto.prototype, "startDate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", typeof (_e = typeof Date !== "undefined" && Date) === "function" ? _e : Object)
], FeedQueryDto.prototype, "endDate", void 0);


/***/ }),
/* 59 */
/***/ ((module) => {

module.exports = require("class-transformer");

/***/ }),
/* 60 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AutocompleteSuggestionDto = exports.AutocompleteQueryDto = exports.SearchResultDto = exports.PostSearchQueryDto = exports.StoreSearchQueryDto = exports.UserSearchQueryDto = exports.ProductSearchQueryDto = exports.SearchQueryDto = exports.PostSortField = exports.StoreSortField = exports.UserSortField = exports.ProductSortField = exports.SortOrder = exports.SearchType = void 0;
const class_validator_1 = __webpack_require__(14);
const class_transformer_1 = __webpack_require__(59);
var SearchType;
(function (SearchType) {
    SearchType["PRODUCT"] = "product";
    SearchType["USER"] = "user";
    SearchType["STORE"] = "store";
    SearchType["POST"] = "post";
    SearchType["ALL"] = "all";
})(SearchType || (exports.SearchType = SearchType = {}));
var SortOrder;
(function (SortOrder) {
    SortOrder["ASC"] = "asc";
    SortOrder["DESC"] = "desc";
})(SortOrder || (exports.SortOrder = SortOrder = {}));
var ProductSortField;
(function (ProductSortField) {
    ProductSortField["PRICE"] = "price";
    ProductSortField["CREATED_AT"] = "createdAt";
    ProductSortField["POPULARITY"] = "popularity";
    ProductSortField["RELEVANCE"] = "relevance";
})(ProductSortField || (exports.ProductSortField = ProductSortField = {}));
var UserSortField;
(function (UserSortField) {
    UserSortField["CREATED_AT"] = "createdAt";
    UserSortField["FOLLOWERS"] = "followers";
    UserSortField["RELEVANCE"] = "relevance";
})(UserSortField || (exports.UserSortField = UserSortField = {}));
var StoreSortField;
(function (StoreSortField) {
    StoreSortField["CREATED_AT"] = "createdAt";
    StoreSortField["FOLLOWERS"] = "followers";
    StoreSortField["PRODUCTS"] = "products";
    StoreSortField["RELEVANCE"] = "relevance";
})(StoreSortField || (exports.StoreSortField = StoreSortField = {}));
var PostSortField;
(function (PostSortField) {
    PostSortField["CREATED_AT"] = "createdAt";
    PostSortField["LIKES"] = "likes";
    PostSortField["COMMENTS"] = "comments";
    PostSortField["RELEVANCE"] = "relevance";
})(PostSortField || (exports.PostSortField = PostSortField = {}));
class SearchQueryDto {
    constructor() {
        this.type = SearchType.ALL;
        this.page = 1;
        this.limit = 20;
    }
}
exports.SearchQueryDto = SearchQueryDto;
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SearchQueryDto.prototype, "query", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(SearchType),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], SearchQueryDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], SearchQueryDto.prototype, "page", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], SearchQueryDto.prototype, "limit", void 0);
class ProductSearchQueryDto extends SearchQueryDto {
    constructor() {
        super(...arguments);
        this.type = SearchType.PRODUCT;
        this.sortField = ProductSortField.RELEVANCE;
        this.sortOrder = SortOrder.DESC;
    }
}
exports.ProductSearchQueryDto = ProductSearchQueryDto;
__decorate([
    (0, class_validator_1.IsEnum)(SearchType),
    __metadata("design:type", String)
], ProductSearchQueryDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ProductSearchQueryDto.prototype, "category", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], ProductSearchQueryDto.prototype, "minPrice", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], ProductSearchQueryDto.prototype, "maxPrice", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ProductSearchQueryDto.prototype, "brands", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(ProductSortField),
    __metadata("design:type", String)
], ProductSearchQueryDto.prototype, "sortField", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(SortOrder),
    __metadata("design:type", String)
], ProductSearchQueryDto.prototype, "sortOrder", void 0);
class UserSearchQueryDto extends SearchQueryDto {
    constructor() {
        super(...arguments);
        this.type = SearchType.USER;
        this.sortField = UserSortField.RELEVANCE;
        this.sortOrder = SortOrder.DESC;
    }
}
exports.UserSearchQueryDto = UserSearchQueryDto;
__decorate([
    (0, class_validator_1.IsEnum)(SearchType),
    __metadata("design:type", String)
], UserSearchQueryDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(UserSortField),
    __metadata("design:type", String)
], UserSearchQueryDto.prototype, "sortField", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(SortOrder),
    __metadata("design:type", String)
], UserSearchQueryDto.prototype, "sortOrder", void 0);
class StoreSearchQueryDto extends SearchQueryDto {
    constructor() {
        super(...arguments);
        this.type = SearchType.STORE;
        this.sortField = StoreSortField.RELEVANCE;
        this.sortOrder = SortOrder.DESC;
    }
}
exports.StoreSearchQueryDto = StoreSearchQueryDto;
__decorate([
    (0, class_validator_1.IsEnum)(SearchType),
    __metadata("design:type", String)
], StoreSearchQueryDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], StoreSearchQueryDto.prototype, "category", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(StoreSortField),
    __metadata("design:type", String)
], StoreSearchQueryDto.prototype, "sortField", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(SortOrder),
    __metadata("design:type", String)
], StoreSearchQueryDto.prototype, "sortOrder", void 0);
class PostSearchQueryDto extends SearchQueryDto {
    constructor() {
        super(...arguments);
        this.type = SearchType.POST;
        this.sortField = PostSortField.RELEVANCE;
        this.sortOrder = SortOrder.DESC;
    }
}
exports.PostSearchQueryDto = PostSearchQueryDto;
__decorate([
    (0, class_validator_1.IsEnum)(SearchType),
    __metadata("design:type", String)
], PostSearchQueryDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PostSearchQueryDto.prototype, "userId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PostSearchQueryDto.prototype, "storeId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(PostSortField),
    __metadata("design:type", String)
], PostSearchQueryDto.prototype, "sortField", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(SortOrder),
    __metadata("design:type", String)
], PostSearchQueryDto.prototype, "sortOrder", void 0);
class SearchResultDto {
}
exports.SearchResultDto = SearchResultDto;
class AutocompleteQueryDto {
    constructor() {
        this.type = SearchType.ALL;
        this.limit = 5;
    }
}
exports.AutocompleteQueryDto = AutocompleteQueryDto;
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AutocompleteQueryDto.prototype, "query", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(SearchType),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], AutocompleteQueryDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(10),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], AutocompleteQueryDto.prototype, "limit", void 0);
class AutocompleteSuggestionDto {
}
exports.AutocompleteSuggestionDto = AutocompleteSuggestionDto;


/***/ }),
/* 61 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.MessageNotificationDto = exports.ConversationStatsDto = exports.MessageQueryDto = exports.ConversationQueryDto = exports.MarkMessagesAsReadDto = exports.UpdateMessageDto = exports.CreateMessageDto = exports.MessageDto = exports.UpdateConversationDto = exports.CreateConversationDto = exports.ConversationDto = exports.CreateConversationParticipantDto = exports.ConversationParticipantDto = exports.ConversationType = exports.MessageType = void 0;
const class_validator_1 = __webpack_require__(14);
const class_transformer_1 = __webpack_require__(59);
var MessageType;
(function (MessageType) {
    MessageType["TEXT"] = "text";
    MessageType["IMAGE"] = "image";
    MessageType["VIDEO"] = "video";
    MessageType["PRODUCT"] = "product";
    MessageType["STORE"] = "store";
    MessageType["ORDER"] = "order";
    MessageType["SYSTEM"] = "system";
})(MessageType || (exports.MessageType = MessageType = {}));
var ConversationType;
(function (ConversationType) {
    ConversationType["PRIVATE"] = "private";
    ConversationType["GROUP"] = "group";
    ConversationType["STORE_SUPPORT"] = "store_support";
})(ConversationType || (exports.ConversationType = ConversationType = {}));
class ConversationParticipantDto {
}
exports.ConversationParticipantDto = ConversationParticipantDto;
class CreateConversationParticipantDto {
    constructor() {
        this.isAdmin = false;
        this.isMuted = false;
    }
}
exports.CreateConversationParticipantDto = CreateConversationParticipantDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateConversationParticipantDto.prototype, "userId", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateConversationParticipantDto.prototype, "isAdmin", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateConversationParticipantDto.prototype, "isMuted", void 0);
class ConversationDto {
}
exports.ConversationDto = ConversationDto;
class CreateConversationDto {
}
exports.CreateConversationDto = CreateConversationDto;
__decorate([
    (0, class_validator_1.IsEnum)(ConversationType),
    __metadata("design:type", String)
], CreateConversationDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateConversationDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateConversationDto.prototype, "storeId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateConversationDto.prototype, "avatar", void 0);
__decorate([
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => CreateConversationParticipantDto),
    __metadata("design:type", Array)
], CreateConversationDto.prototype, "participants", void 0);
class UpdateConversationDto {
}
exports.UpdateConversationDto = UpdateConversationDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateConversationDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateConversationDto.prototype, "avatar", void 0);
class MessageDto {
}
exports.MessageDto = MessageDto;
class CreateMessageDto {
}
exports.CreateMessageDto = CreateMessageDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateMessageDto.prototype, "conversationId", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(MessageType),
    __metadata("design:type", String)
], CreateMessageDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateMessageDto.prototype, "text", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateMessageDto.prototype, "mediaUrl", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", typeof (_a = typeof Record !== "undefined" && Record) === "function" ? _a : Object)
], CreateMessageDto.prototype, "metadata", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateMessageDto.prototype, "replyToId", void 0);
class UpdateMessageDto {
}
exports.UpdateMessageDto = UpdateMessageDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(2000),
    __metadata("design:type", String)
], UpdateMessageDto.prototype, "text", void 0);
class MarkMessagesAsReadDto {
}
exports.MarkMessagesAsReadDto = MarkMessagesAsReadDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], MarkMessagesAsReadDto.prototype, "conversationId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], MarkMessagesAsReadDto.prototype, "lastMessageId", void 0);
class ConversationQueryDto {
}
exports.ConversationQueryDto = ConversationQueryDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ConversationQueryDto.prototype, "search", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(ConversationType),
    __metadata("design:type", String)
], ConversationQueryDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ConversationQueryDto.prototype, "storeId", void 0);
class MessageQueryDto {
}
exports.MessageQueryDto = MessageQueryDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], MessageQueryDto.prototype, "conversationId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], MessageQueryDto.prototype, "before", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], MessageQueryDto.prototype, "after", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], MessageQueryDto.prototype, "search", void 0);
class ConversationStatsDto {
}
exports.ConversationStatsDto = ConversationStatsDto;
class MessageNotificationDto {
}
exports.MessageNotificationDto = MessageNotificationDto;


/***/ }),
/* 62 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.TrendingItemsRequestDto = exports.SimilarItemsRequestDto = exports.PostRecommendationDto = exports.StoreRecommendationDto = exports.UserRecommendationDto = exports.ProductRecommendationDto = exports.RecommendationDto = exports.PostRecommendationRequestDto = exports.StoreRecommendationRequestDto = exports.UserRecommendationRequestDto = exports.ProductRecommendationRequestDto = exports.RecommendationRequestDto = exports.CreateUserInteractionDto = exports.UserInteractionDto = exports.RecommendationSource = exports.RecommendationType = void 0;
const class_validator_1 = __webpack_require__(14);
const class_transformer_1 = __webpack_require__(59);
var RecommendationType;
(function (RecommendationType) {
    RecommendationType["PRODUCT"] = "product";
    RecommendationType["USER"] = "user";
    RecommendationType["STORE"] = "store";
    RecommendationType["POST"] = "post";
})(RecommendationType || (exports.RecommendationType = RecommendationType = {}));
var RecommendationSource;
(function (RecommendationSource) {
    RecommendationSource["VIEWED"] = "viewed";
    RecommendationSource["PURCHASED"] = "purchased";
    RecommendationSource["LIKED"] = "liked";
    RecommendationSource["FOLLOWED"] = "followed";
    RecommendationSource["SIMILAR"] = "similar";
    RecommendationSource["TRENDING"] = "trending";
    RecommendationSource["COLLABORATIVE"] = "collaborative";
    RecommendationSource["PERSONALIZED"] = "personalized";
})(RecommendationSource || (exports.RecommendationSource = RecommendationSource = {}));
class UserInteractionDto {
}
exports.UserInteractionDto = UserInteractionDto;
class CreateUserInteractionDto {
}
exports.CreateUserInteractionDto = CreateUserInteractionDto;
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateUserInteractionDto.prototype, "userId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateUserInteractionDto.prototype, "itemId", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(RecommendationType),
    __metadata("design:type", String)
], CreateUserInteractionDto.prototype, "itemType", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateUserInteractionDto.prototype, "interactionType", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", typeof (_a = typeof Record !== "undefined" && Record) === "function" ? _a : Object)
], CreateUserInteractionDto.prototype, "metadata", void 0);
class RecommendationRequestDto {
    constructor() {
        this.limit = 10;
        this.includeReasoning = false;
    }
}
exports.RecommendationRequestDto = RecommendationRequestDto;
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RecommendationRequestDto.prototype, "userId", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(RecommendationType),
    __metadata("design:type", String)
], RecommendationRequestDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], RecommendationRequestDto.prototype, "excludeIds", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], RecommendationRequestDto.prototype, "limit", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], RecommendationRequestDto.prototype, "includeReasoning", void 0);
class ProductRecommendationRequestDto extends RecommendationRequestDto {
}
exports.ProductRecommendationRequestDto = ProductRecommendationRequestDto;
__decorate([
    (0, class_validator_1.IsEnum)(RecommendationType),
    __metadata("design:type", String)
], ProductRecommendationRequestDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ProductRecommendationRequestDto.prototype, "category", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], ProductRecommendationRequestDto.prototype, "minPrice", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], ProductRecommendationRequestDto.prototype, "maxPrice", void 0);
class UserRecommendationRequestDto extends RecommendationRequestDto {
}
exports.UserRecommendationRequestDto = UserRecommendationRequestDto;
__decorate([
    (0, class_validator_1.IsEnum)(RecommendationType),
    __metadata("design:type", String)
], UserRecommendationRequestDto.prototype, "type", void 0);
class StoreRecommendationRequestDto extends RecommendationRequestDto {
}
exports.StoreRecommendationRequestDto = StoreRecommendationRequestDto;
__decorate([
    (0, class_validator_1.IsEnum)(RecommendationType),
    __metadata("design:type", String)
], StoreRecommendationRequestDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], StoreRecommendationRequestDto.prototype, "category", void 0);
class PostRecommendationRequestDto extends RecommendationRequestDto {
}
exports.PostRecommendationRequestDto = PostRecommendationRequestDto;
__decorate([
    (0, class_validator_1.IsEnum)(RecommendationType),
    __metadata("design:type", String)
], PostRecommendationRequestDto.prototype, "type", void 0);
class RecommendationDto {
}
exports.RecommendationDto = RecommendationDto;
class ProductRecommendationDto extends RecommendationDto {
}
exports.ProductRecommendationDto = ProductRecommendationDto;
class UserRecommendationDto extends RecommendationDto {
}
exports.UserRecommendationDto = UserRecommendationDto;
class StoreRecommendationDto extends RecommendationDto {
}
exports.StoreRecommendationDto = StoreRecommendationDto;
class PostRecommendationDto extends RecommendationDto {
}
exports.PostRecommendationDto = PostRecommendationDto;
class SimilarItemsRequestDto {
    constructor() {
        this.limit = 10;
    }
}
exports.SimilarItemsRequestDto = SimilarItemsRequestDto;
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SimilarItemsRequestDto.prototype, "itemId", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(RecommendationType),
    __metadata("design:type", String)
], SimilarItemsRequestDto.prototype, "itemType", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], SimilarItemsRequestDto.prototype, "limit", void 0);
class TrendingItemsRequestDto {
    constructor() {
        this.limit = 10;
        this.timeWindowDays = 7;
    }
}
exports.TrendingItemsRequestDto = TrendingItemsRequestDto;
__decorate([
    (0, class_validator_1.IsEnum)(RecommendationType),
    __metadata("design:type", String)
], TrendingItemsRequestDto.prototype, "itemType", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TrendingItemsRequestDto.prototype, "category", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], TrendingItemsRequestDto.prototype, "limit", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(30),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], TrendingItemsRequestDto.prototype, "timeWindowDays", void 0);


/***/ }),
/* 63 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.GroupDealStatsDto = exports.CreateGroupDealInviteDto = exports.GroupDealInviteDto = exports.GroupDealQueryDto = exports.UpdateGroupDealParticipantDto = exports.CreateGroupDealParticipantDto = exports.GroupDealParticipantDto = exports.UpdateGroupDealDto = exports.CreateGroupDealDto = exports.GroupDealDto = exports.CreateGroupDealTierDto = exports.GroupDealTierDto = exports.ParticipantStatus = exports.GroupDealStatus = void 0;
const class_validator_1 = __webpack_require__(14);
const class_transformer_1 = __webpack_require__(59);
var GroupDealStatus;
(function (GroupDealStatus) {
    GroupDealStatus["DRAFT"] = "draft";
    GroupDealStatus["ACTIVE"] = "active";
    GroupDealStatus["COMPLETED"] = "completed";
    GroupDealStatus["EXPIRED"] = "expired";
    GroupDealStatus["CANCELLED"] = "cancelled";
})(GroupDealStatus || (exports.GroupDealStatus = GroupDealStatus = {}));
var ParticipantStatus;
(function (ParticipantStatus) {
    ParticipantStatus["PENDING"] = "pending";
    ParticipantStatus["PAID"] = "paid";
    ParticipantStatus["CANCELLED"] = "cancelled";
    ParticipantStatus["REFUNDED"] = "refunded";
})(ParticipantStatus || (exports.ParticipantStatus = ParticipantStatus = {}));
class GroupDealTierDto {
}
exports.GroupDealTierDto = GroupDealTierDto;
class CreateGroupDealTierDto {
}
exports.CreateGroupDealTierDto = CreateGroupDealTierDto;
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(2),
    __metadata("design:type", Number)
], CreateGroupDealTierDto.prototype, "participantCount", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], CreateGroupDealTierDto.prototype, "discountPercentage", void 0);
class GroupDealDto {
}
exports.GroupDealDto = GroupDealDto;
class CreateGroupDealDto {
}
exports.CreateGroupDealDto = CreateGroupDealDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateGroupDealDto.prototype, "title", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateGroupDealDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateGroupDealDto.prototype, "productId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateGroupDealDto.prototype, "storeId", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateGroupDealDto.prototype, "originalPrice", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(2),
    __metadata("design:type", Number)
], CreateGroupDealDto.prototype, "minParticipants", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(2),
    __metadata("design:type", Number)
], CreateGroupDealDto.prototype, "maxParticipants", void 0);
__decorate([
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Type)(() => Date),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], CreateGroupDealDto.prototype, "startDate", void 0);
__decorate([
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Type)(() => Date),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], CreateGroupDealDto.prototype, "endDate", void 0);
__decorate([
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => CreateGroupDealTierDto),
    __metadata("design:type", Array)
], CreateGroupDealDto.prototype, "tiers", void 0);
class UpdateGroupDealDto {
}
exports.UpdateGroupDealDto = UpdateGroupDealDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateGroupDealDto.prototype, "title", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateGroupDealDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateGroupDealDto.prototype, "originalPrice", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(2),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateGroupDealDto.prototype, "minParticipants", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(2),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateGroupDealDto.prototype, "maxParticipants", void 0);
__decorate([
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Type)(() => Date),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], UpdateGroupDealDto.prototype, "startDate", void 0);
__decorate([
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Type)(() => Date),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], UpdateGroupDealDto.prototype, "endDate", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(GroupDealStatus),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateGroupDealDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => CreateGroupDealTierDto),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], UpdateGroupDealDto.prototype, "tiers", void 0);
class GroupDealParticipantDto {
}
exports.GroupDealParticipantDto = GroupDealParticipantDto;
class CreateGroupDealParticipantDto {
}
exports.CreateGroupDealParticipantDto = CreateGroupDealParticipantDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateGroupDealParticipantDto.prototype, "groupDealId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateGroupDealParticipantDto.prototype, "userId", void 0);
class UpdateGroupDealParticipantDto {
}
exports.UpdateGroupDealParticipantDto = UpdateGroupDealParticipantDto;
__decorate([
    (0, class_validator_1.IsEnum)(ParticipantStatus),
    __metadata("design:type", String)
], UpdateGroupDealParticipantDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateGroupDealParticipantDto.prototype, "paymentId", void 0);
class GroupDealQueryDto {
    constructor() {
        this.page = 1;
        this.limit = 10;
    }
}
exports.GroupDealQueryDto = GroupDealQueryDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GroupDealQueryDto.prototype, "storeId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(GroupDealStatus),
    __metadata("design:type", String)
], GroupDealQueryDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], GroupDealQueryDto.prototype, "active", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], GroupDealQueryDto.prototype, "page", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], GroupDealQueryDto.prototype, "limit", void 0);
class GroupDealInviteDto {
}
exports.GroupDealInviteDto = GroupDealInviteDto;
class CreateGroupDealInviteDto {
}
exports.CreateGroupDealInviteDto = CreateGroupDealInviteDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateGroupDealInviteDto.prototype, "groupDealId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateGroupDealInviteDto.prototype, "inviteeId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateGroupDealInviteDto.prototype, "inviteeEmail", void 0);
class GroupDealStatsDto {
}
exports.GroupDealStatsDto = GroupDealStatsDto;


/***/ }),
/* 64 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ReferralQueryDto = exports.CommissionQueryDto = exports.AffiliateQueryDto = exports.AffiliateStatsDto = exports.UpdateReferralDto = exports.CreateReferralDto = exports.ReferralDto = exports.UpdateCommissionDto = exports.CreateCommissionDto = exports.CommissionDto = exports.UpdateCommissionRateDto = exports.CreateCommissionRateDto = exports.CommissionRateDto = exports.UpdateAffiliateDto = exports.CreateAffiliateDto = exports.AffiliateDto = exports.ReferralStatus = exports.CommissionStatus = exports.AffiliateCommissionType = exports.AffiliateStatus = void 0;
const class_validator_1 = __webpack_require__(14);
const class_transformer_1 = __webpack_require__(59);
var AffiliateStatus;
(function (AffiliateStatus) {
    AffiliateStatus["PENDING"] = "pending";
    AffiliateStatus["APPROVED"] = "approved";
    AffiliateStatus["REJECTED"] = "rejected";
    AffiliateStatus["SUSPENDED"] = "suspended";
})(AffiliateStatus || (exports.AffiliateStatus = AffiliateStatus = {}));
var AffiliateCommissionType;
(function (AffiliateCommissionType) {
    AffiliateCommissionType["PERCENTAGE"] = "percentage";
    AffiliateCommissionType["FIXED"] = "fixed";
})(AffiliateCommissionType || (exports.AffiliateCommissionType = AffiliateCommissionType = {}));
var CommissionStatus;
(function (CommissionStatus) {
    CommissionStatus["PENDING"] = "pending";
    CommissionStatus["APPROVED"] = "approved";
    CommissionStatus["REJECTED"] = "rejected";
    CommissionStatus["PAID"] = "paid";
})(CommissionStatus || (exports.CommissionStatus = CommissionStatus = {}));
var ReferralStatus;
(function (ReferralStatus) {
    ReferralStatus["PENDING"] = "pending";
    ReferralStatus["CONVERTED"] = "converted";
    ReferralStatus["EXPIRED"] = "expired";
})(ReferralStatus || (exports.ReferralStatus = ReferralStatus = {}));
class AffiliateDto {
}
exports.AffiliateDto = AffiliateDto;
class CreateAffiliateDto {
}
exports.CreateAffiliateDto = CreateAffiliateDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateAffiliateDto.prototype, "userId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateAffiliateDto.prototype, "bio", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateAffiliateDto.prototype, "website", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", typeof (_a = typeof Record !== "undefined" && Record) === "function" ? _a : Object)
], CreateAffiliateDto.prototype, "socialMediaLinks", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", typeof (_b = typeof Record !== "undefined" && Record) === "function" ? _b : Object)
], CreateAffiliateDto.prototype, "paymentDetails", void 0);
class UpdateAffiliateDto {
}
exports.UpdateAffiliateDto = UpdateAffiliateDto;
__decorate([
    (0, class_validator_1.IsEnum)(AffiliateStatus),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateAffiliateDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateAffiliateDto.prototype, "bio", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateAffiliateDto.prototype, "website", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", typeof (_c = typeof Record !== "undefined" && Record) === "function" ? _c : Object)
], UpdateAffiliateDto.prototype, "socialMediaLinks", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", typeof (_d = typeof Record !== "undefined" && Record) === "function" ? _d : Object)
], UpdateAffiliateDto.prototype, "paymentDetails", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateAffiliateDto.prototype, "defaultCommissionRate", void 0);
class CommissionRateDto {
}
exports.CommissionRateDto = CommissionRateDto;
class CreateCommissionRateDto {
    constructor() {
        this.isActive = true;
    }
}
exports.CreateCommissionRateDto = CreateCommissionRateDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateCommissionRateDto.prototype, "affiliateId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCommissionRateDto.prototype, "productId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCommissionRateDto.prototype, "categoryId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCommissionRateDto.prototype, "storeId", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(AffiliateCommissionType),
    __metadata("design:type", String)
], CreateCommissionRateDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateCommissionRateDto.prototype, "rate", void 0);
__decorate([
    (0, class_validator_1.IsDate)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Date),
    __metadata("design:type", typeof (_e = typeof Date !== "undefined" && Date) === "function" ? _e : Object)
], CreateCommissionRateDto.prototype, "startDate", void 0);
__decorate([
    (0, class_validator_1.IsDate)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Date),
    __metadata("design:type", typeof (_f = typeof Date !== "undefined" && Date) === "function" ? _f : Object)
], CreateCommissionRateDto.prototype, "endDate", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateCommissionRateDto.prototype, "isActive", void 0);
class UpdateCommissionRateDto {
}
exports.UpdateCommissionRateDto = UpdateCommissionRateDto;
__decorate([
    (0, class_validator_1.IsEnum)(AffiliateCommissionType),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateCommissionRateDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateCommissionRateDto.prototype, "rate", void 0);
__decorate([
    (0, class_validator_1.IsDate)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Date),
    __metadata("design:type", typeof (_g = typeof Date !== "undefined" && Date) === "function" ? _g : Object)
], UpdateCommissionRateDto.prototype, "startDate", void 0);
__decorate([
    (0, class_validator_1.IsDate)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Date),
    __metadata("design:type", typeof (_h = typeof Date !== "undefined" && Date) === "function" ? _h : Object)
], UpdateCommissionRateDto.prototype, "endDate", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], UpdateCommissionRateDto.prototype, "isActive", void 0);
class CommissionDto {
}
exports.CommissionDto = CommissionDto;
class CreateCommissionDto {
    constructor() {
        this.status = CommissionStatus.PENDING;
    }
}
exports.CreateCommissionDto = CreateCommissionDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateCommissionDto.prototype, "affiliateId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateCommissionDto.prototype, "orderId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateCommissionDto.prototype, "productId", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateCommissionDto.prototype, "amount", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateCommissionDto.prototype, "commissionAmount", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateCommissionDto.prototype, "commissionRate", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(AffiliateCommissionType),
    __metadata("design:type", String)
], CreateCommissionDto.prototype, "commissionType", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(CommissionStatus),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCommissionDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCommissionDto.prototype, "notes", void 0);
class UpdateCommissionDto {
}
exports.UpdateCommissionDto = UpdateCommissionDto;
__decorate([
    (0, class_validator_1.IsEnum)(CommissionStatus),
    __metadata("design:type", String)
], UpdateCommissionDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateCommissionDto.prototype, "notes", void 0);
__decorate([
    (0, class_validator_1.IsDate)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Date),
    __metadata("design:type", typeof (_j = typeof Date !== "undefined" && Date) === "function" ? _j : Object)
], UpdateCommissionDto.prototype, "processedAt", void 0);
class ReferralDto {
}
exports.ReferralDto = ReferralDto;
class CreateReferralDto {
}
exports.CreateReferralDto = CreateReferralDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateReferralDto.prototype, "affiliateId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateReferralDto.prototype, "referralCode", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateReferralDto.prototype, "referredUserId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateReferralDto.prototype, "referredEmail", void 0);
__decorate([
    (0, class_validator_1.IsDate)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Date),
    __metadata("design:type", typeof (_k = typeof Date !== "undefined" && Date) === "function" ? _k : Object)
], CreateReferralDto.prototype, "expiresAt", void 0);
class UpdateReferralDto {
}
exports.UpdateReferralDto = UpdateReferralDto;
__decorate([
    (0, class_validator_1.IsEnum)(ReferralStatus),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateReferralDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateReferralDto.prototype, "referredUserId", void 0);
__decorate([
    (0, class_validator_1.IsDate)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Date),
    __metadata("design:type", typeof (_l = typeof Date !== "undefined" && Date) === "function" ? _l : Object)
], UpdateReferralDto.prototype, "convertedAt", void 0);
class AffiliateStatsDto {
}
exports.AffiliateStatsDto = AffiliateStatsDto;
class AffiliateQueryDto {
    constructor() {
        this.page = 1;
        this.limit = 10;
    }
}
exports.AffiliateQueryDto = AffiliateQueryDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(AffiliateStatus),
    __metadata("design:type", String)
], AffiliateQueryDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], AffiliateQueryDto.prototype, "page", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], AffiliateQueryDto.prototype, "limit", void 0);
class CommissionQueryDto {
    constructor() {
        this.page = 1;
        this.limit = 10;
    }
}
exports.CommissionQueryDto = CommissionQueryDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CommissionQueryDto.prototype, "affiliateId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(CommissionStatus),
    __metadata("design:type", String)
], CommissionQueryDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Type)(() => Date),
    __metadata("design:type", typeof (_m = typeof Date !== "undefined" && Date) === "function" ? _m : Object)
], CommissionQueryDto.prototype, "startDate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Type)(() => Date),
    __metadata("design:type", typeof (_o = typeof Date !== "undefined" && Date) === "function" ? _o : Object)
], CommissionQueryDto.prototype, "endDate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], CommissionQueryDto.prototype, "page", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], CommissionQueryDto.prototype, "limit", void 0);
class ReferralQueryDto {
    constructor() {
        this.page = 1;
        this.limit = 10;
    }
}
exports.ReferralQueryDto = ReferralQueryDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ReferralQueryDto.prototype, "affiliateId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(ReferralStatus),
    __metadata("design:type", String)
], ReferralQueryDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Type)(() => Date),
    __metadata("design:type", typeof (_p = typeof Date !== "undefined" && Date) === "function" ? _p : Object)
], ReferralQueryDto.prototype, "startDate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Type)(() => Date),
    __metadata("design:type", typeof (_q = typeof Date !== "undefined" && Date) === "function" ? _q : Object)
], ReferralQueryDto.prototype, "endDate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], ReferralQueryDto.prototype, "page", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], ReferralQueryDto.prototype, "limit", void 0);


/***/ }),
/* 65 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
__exportStar(__webpack_require__(66), exports);


/***/ }),
/* 66 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var AppExceptionFilter_1;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AppExceptionFilter = void 0;
const common_1 = __webpack_require__(3);
const microservices_1 = __webpack_require__(6);
const rxjs_1 = __webpack_require__(25);
const errors_1 = __webpack_require__(18);
let AppExceptionFilter = AppExceptionFilter_1 = class AppExceptionFilter {
    constructor() {
        this.logger = new common_1.Logger(AppExceptionFilter_1.name);
    }
    catch(exception, host) {
        this.logger.error(`Exception caught: ${exception.message}`, exception.stack);
        if (exception instanceof microservices_1.RpcException) {
            return (0, rxjs_1.throwError)(() => exception);
        }
        if (exception instanceof errors_1.AppError) {
            return (0, rxjs_1.throwError)(() => new microservices_1.RpcException({
                code: exception.code,
                message: exception.message,
                translationKey: exception.translationKey,
                details: exception.details,
                statusCode: exception.statusCode,
            }));
        }
        if (exception instanceof common_1.HttpException) {
            const status = exception.getStatus();
            const response = exception.getResponse();
            let errorCode = errors_1.ErrorCode.INTERNAL_ERROR;
            let translationKey = 'errors.internal';
            switch (status) {
                case common_1.HttpStatus.NOT_FOUND:
                    errorCode = errors_1.ErrorCode.NOT_FOUND;
                    translationKey = 'errors.notFound';
                    break;
                case common_1.HttpStatus.BAD_REQUEST:
                    errorCode = errors_1.ErrorCode.VALIDATION_ERROR;
                    translationKey = 'errors.validation';
                    break;
                case common_1.HttpStatus.UNAUTHORIZED:
                    errorCode = errors_1.ErrorCode.UNAUTHORIZED;
                    translationKey = 'errors.unauthorized';
                    break;
                case common_1.HttpStatus.FORBIDDEN:
                    errorCode = errors_1.ErrorCode.FORBIDDEN;
                    translationKey = 'errors.forbidden';
                    break;
                case common_1.HttpStatus.CONFLICT:
                    errorCode = errors_1.ErrorCode.CONFLICT;
                    translationKey = 'errors.conflict';
                    break;
            }
            return (0, rxjs_1.throwError)(() => new microservices_1.RpcException({
                code: errorCode,
                message: typeof response === 'object' ? response.message : exception.message,
                translationKey,
                details: typeof response === 'object' ? response : undefined,
                statusCode: status,
            }));
        }
        return (0, rxjs_1.throwError)(() => new microservices_1.RpcException({
            code: errors_1.ErrorCode.INTERNAL_ERROR,
            message: exception.message || 'Internal server error',
            translationKey: 'errors.internal',
            details: exception,
            statusCode: common_1.HttpStatus.INTERNAL_SERVER_ERROR,
        }));
    }
};
exports.AppExceptionFilter = AppExceptionFilter;
exports.AppExceptionFilter = AppExceptionFilter = AppExceptionFilter_1 = __decorate([
    (0, common_1.Catch)()
], AppExceptionFilter);


/***/ })
/******/ 	]);
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other modules in the chunk.
(() => {
var exports = __webpack_exports__;

Object.defineProperty(exports, "__esModule", ({ value: true }));
const core_1 = __webpack_require__(1);
const user_module_1 = __webpack_require__(2);
const microservices_1 = __webpack_require__(6);
const common_1 = __webpack_require__(3);
const filters_1 = __webpack_require__(65);
const errors_1 = __webpack_require__(18);
async function bootstrap() {
    const logger = new common_1.Logger('Bootstrap');
    try {
        const app = await core_1.NestFactory.createMicroservice(user_module_1.UserModule, {
            transport: microservices_1.Transport.TCP,
            options: {
                host: '0.0.0.0',
                port: parseInt(process.env.USER_SERVICE_PORT) || 3002,
            },
        });
        app.useGlobalPipes(new common_1.ValidationPipe({
            whitelist: true,
            transform: true,
            forbidNonWhitelisted: true,
            exceptionFactory: (errors) => {
                const formattedErrors = errors.map(error => ({
                    property: error.property,
                    constraints: error.constraints,
                    value: error.value
                }));
                return new errors_1.ValidationError('Validation failed', null, formattedErrors);
            }
        }));
        app.useGlobalFilters(new filters_1.AppExceptionFilter());
        await app.listen();
        logger.log('User service is listening');
    }
    catch (error) {
        logger.error(`Error starting User service: ${error.message}`, error.stack);
        process.exit(1);
    }
}
bootstrap();

})();

/******/ })()
;