const chalk = require('chalk');
const spawn = require('cross-spawn');
const ora = require('ora');
const path = require('path');
const fs = require('fs');

/**
 * Build command implementation
 * @param {Object} program - Commander program instance
 */
module.exports = (program) => {
  program
    .command('build')
    .description('Build services')
    .argument('[service]', 'Service to build (all, backend, frontend)', 'all')
    .action((service) => {
      const spinner = ora('Building services...').start();
      
      try {
        const rootDir = path.resolve(__dirname, '../../');
        
        if (service === 'all' || service === 'backend') {
          // Build backend
          spinner.text = 'Building backend...';
          
          const backendDir = path.join(rootDir, 'backend');
          if (!fs.existsSync(backendDir)) {
            spinner.warn(chalk.yellow('Backend directory not found'));
          } else {
            const buildResult = spawn.sync('npm', ['run', 'build'], {
              cwd: backendDir,
              stdio: 'inherit'
            });
            
            if (buildResult.status === 0) {
              spinner.succeed(chalk.green('Backend built successfully'));
            } else {
              spinner.fail(chalk.red('Failed to build backend'));
            }
          }
        }
        
        if (service === 'all' || service === 'frontend') {
          // Build frontend
          spinner.text = 'Building frontend...';
          
          const frontendDir = path.join(rootDir, 'frontend');
          if (!fs.existsSync(frontendDir)) {
            spinner.warn(chalk.yellow('Frontend directory not found'));
          } else {
            const buildResult = spawn.sync('npm', ['run', 'build'], {
              cwd: frontendDir,
              stdio: 'inherit'
            });
            
            if (buildResult.status === 0) {
              spinner.succeed(chalk.green('Frontend built successfully'));
            } else {
              spinner.fail(chalk.red('Failed to build frontend'));
            }
          }
        }
        
        if (service !== 'all' && service !== 'backend' && service !== 'frontend') {
          spinner.fail(chalk.red(`Unknown service: ${service}`));
          console.error(chalk.yellow('Available services: all, backend, frontend'));
        }
      } catch (error) {
        spinner.fail(chalk.red('Error building services'));
        console.error(chalk.red(error.message));
      }
    });
};
