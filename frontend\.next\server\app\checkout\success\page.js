(()=>{var e={};e.id=2637,e.ids=[2637],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},57310:e=>{"use strict";e.exports=require("url")},7194:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>d,pages:()=>l,routeModule:()=>m,tree:()=>u});var r=t(67096),n=t(16132),c=t(37284),o=t.n(c),a=t(32564),i={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>a[e]);t.d(s,i);let u=["",{children:["checkout",{children:["success",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,59180)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\checkout\\success\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,36303)),"C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9291,23)),"next/dist/client/components/not-found-error"]}],l=["C:\\Users\\<USER>\\Documents\\augment\\social-commerce\\frontend\\src\\app\\checkout\\success\\page.tsx"],d="/checkout/success/page",p={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/checkout/success/page",pathname:"/checkout/success",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},6030:(e,s,t)=>{Promise.resolve().then(t.bind(t,14540))},14540:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>CheckoutSuccessPage});var r=t(30784);t(9885);var n=t(57114),c=t(59872);function CheckoutSuccessPage(){let e=(0,n.useRouter)();return r.jsx("div",{className:"min-h-screen p-6 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 text-center",children:[r.jsx("div",{className:"mb-6 text-green-500",children:r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-16 w-16 mx-auto",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),r.jsx("h1",{className:"text-2xl font-bold mb-4",children:"Order Placed Successfully!"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-8",children:"Thank you for your purchase. We've sent a confirmation email with your order details."}),(0,r.jsxs)("div",{className:"space-y-3",children:[r.jsx(c.Z,{onClick:()=>e.push("/products"),fullWidth:!0,children:"Continue Shopping"}),r.jsx(c.Z,{variant:"outline",onClick:()=>e.push("/"),fullWidth:!0,children:"Back to Home"})]})]})})}},59180:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>o,__esModule:()=>c,default:()=>i});var r=t(95153);let n=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\augment\social-commerce\frontend\src\app\checkout\success\page.tsx`),{__esModule:c,$$typeof:o}=n,a=n.default,i=a}};var s=require("../../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),t=s.X(0,[2103,2765],()=>__webpack_exec__(7194));module.exports=t})();