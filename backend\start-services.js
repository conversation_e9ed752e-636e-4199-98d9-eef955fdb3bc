const { spawn } = require('child_process');
const path = require('path');

// Configuration for services
const services = [
  {
    name: 'User Service',
    command: 'node',
    args: [path.join(__dirname, 'apps/user-service/src/main.js')],
    env: { ...process.env, USER_SERVICE_PORT: '3002' }
  },
  {
    name: 'Main Gateway',
    command: 'node',
    args: [path.join(__dirname, 'apps/main/src/main.js')],
    env: { ...process.env, PORT: '3001' }
  }
];

// Start each service
services.forEach(service => {
  console.log(`Starting ${service.name}...`);
  
  const proc = spawn(service.command, service.args, {
    env: service.env,
    stdio: 'inherit'
  });
  
  proc.on('error', (error) => {
    console.error(`Error starting ${service.name}:`, error);
  });
  
  proc.on('exit', (code, signal) => {
    if (code !== 0) {
      console.error(`${service.name} exited with code ${code} and signal ${signal}`);
    }
  });
});
