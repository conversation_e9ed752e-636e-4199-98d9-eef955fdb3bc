# Testing Example

This document provides examples of correctly implemented tests following the Social Commerce Platform's patterns and best practices.

## Table of Contents

1. [Unit Testing Services](#unit-testing-services)
2. [Testing Controllers](#testing-controllers)
3. [Integration Testing](#integration-testing)
4. [E2E Testing](#e2e-testing)
5. [Best Practices](#best-practices)

## Unit Testing Services

Here's an example of a unit test for a service:

```typescript
// user.service.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserService } from './user.service';
import { User } from './entities/user.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { AppError } from '@app/common';
import { mockRepository } from '../../test/mocks/repository.mock';

describe('UserService', () => {
  let service: UserService;
  let repository: Repository<User>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserService,
        {
          provide: getRepositoryToken(User),
          useFactory: mockRepository,
        },
      ],
    }).compile();

    service = module.get<UserService>(UserService);
    repository = module.get<Repository<User>>(getRepositoryToken(User));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new user', async () => {
      // Arrange
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      };

      const user = new User();
      user.id = '123';
      user.email = createUserDto.email;
      user.name = createUserDto.name;

      jest.spyOn(repository, 'findOne').mockResolvedValue(null);
      jest.spyOn(repository, 'create').mockReturnValue(user);
      jest.spyOn(repository, 'save').mockResolvedValue(user);

      // Act
      const result = await service.create(createUserDto);

      // Assert
      expect(result).toEqual(user);
      expect(repository.findOne).toHaveBeenCalledWith({
        where: { email: createUserDto.email },
      });
      expect(repository.create).toHaveBeenCalledWith(createUserDto);
      expect(repository.save).toHaveBeenCalledWith(user);
    });

    it('should throw an error if user with email already exists', async () => {
      // Arrange
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      };

      const existingUser = new User();
      existingUser.id = '123';
      existingUser.email = createUserDto.email;

      jest.spyOn(repository, 'findOne').mockResolvedValue(existingUser);

      // Act & Assert
      await expect(service.create(createUserDto)).rejects.toThrow(AppError);
      expect(repository.findOne).toHaveBeenCalledWith({
        where: { email: createUserDto.email },
      });
    });
  });
});
```

## Testing Controllers

Here's an example of a unit test for a controller:

```typescript
// user.controller.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { UserController } from './user.controller';
import { UserService } from './user.service';
import { User } from './entities/user.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { NotFoundException } from '@nestjs/common';

describe('UserController', () => {
  let controller: UserController;
  let service: UserService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserController],
      providers: [
        {
          provide: UserService,
          useValue: {
            create: jest.fn(),
            findAll: jest.fn(),
            findOne: jest.fn(),
            update: jest.fn(),
            remove: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<UserController>(UserController);
    service = module.get<UserService>(UserService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a new user', async () => {
      // Arrange
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      };

      const user = new User();
      user.id = '123';
      user.email = createUserDto.email;
      user.name = createUserDto.name;

      jest.spyOn(service, 'create').mockResolvedValue(user);

      // Act
      const result = await controller.create(createUserDto);

      // Assert
      expect(result).toEqual(user);
      expect(service.create).toHaveBeenCalledWith(createUserDto);
    });
  });

  describe('findOne', () => {
    it('should return a user', async () => {
      // Arrange
      const userId = '123';

      const user = new User();
      user.id = userId;
      user.email = '<EMAIL>';
      user.name = 'Test User';

      jest.spyOn(service, 'findOne').mockResolvedValue(user);

      // Act
      const result = await controller.findOne(userId);

      // Assert
      expect(result).toEqual(user);
      expect(service.findOne).toHaveBeenCalledWith(userId);
    });

    it('should throw NotFoundException if user not found', async () => {
      // Arrange
      const userId = '123';

      jest.spyOn(service, 'findOne').mockResolvedValue(null);

      // Act & Assert
      await expect(controller.findOne(userId)).rejects.toThrow(NotFoundException);
      expect(service.findOne).toHaveBeenCalledWith(userId);
    });
  });
});
```

## Integration Testing

Here's an example of an integration test:

```typescript
// user.integration.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserModule } from './user.module';
import { User } from './entities/user.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { configureApp } from '../main';

describe('UserController (Integration)', () => {
  let app: INestApplication;
  let moduleFixture: TestingModule;

  beforeAll(async () => {
    moduleFixture = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: [User],
          synchronize: true,
        }),
        UserModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    configureApp(app);
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('POST /users', () => {
    it('should create a new user', async () => {
      // Arrange
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      };

      // Act & Assert
      const response = await request(app.getHttpServer())
        .post('/users')
        .send(createUserDto)
        .expect(201);

      expect(response.body).toHaveProperty('id');
      expect(response.body.email).toBe(createUserDto.email);
      expect(response.body.name).toBe(createUserDto.name);
      expect(response.body).not.toHaveProperty('password');
    });

    it('should return 400 if email already exists', async () => {
      // Arrange
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      };

      // Create the user first
      await request(app.getHttpServer())
        .post('/users')
        .send(createUserDto)
        .expect(201);

      // Act & Assert - Try to create the same user again
      const response = await request(app.getHttpServer())
        .post('/users')
        .send(createUserDto)
        .expect(400);

      expect(response.body.message).toContain('already exists');
    });
  });
});
```

## E2E Testing

Here's an example of an end-to-end test:

```typescript
// user.e2e-spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { configureApp } from '../src/main';
import { getConnection } from 'typeorm';

describe('UserController (e2e)', () => {
  let app: INestApplication;
  let jwtToken: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    configureApp(app);
    await app.init();

    // Create a test user and get JWT token for authenticated requests
    const response = await request(app.getHttpServer())
      .post('/auth/login')
      .send({ email: '<EMAIL>', password: 'admin123' })
      .expect(200);

    jwtToken = response.body.token;
  });

  afterAll(async () => {
    // Clean up the database
    const connection = getConnection();
    await connection.dropDatabase();
    await connection.close();
    await app.close();
  });

  describe('User CRUD operations', () => {
    let userId: string;

    it('should create a new user', async () => {
      const response = await request(app.getHttpServer())
        .post('/users')
        .set('Authorization', `Bearer ${jwtToken}`)
        .send({
          email: '<EMAIL>',
          password: 'password123',
          name: 'E2E Test User',
        })
        .expect(201);

      userId = response.body.id;
      expect(response.body.email).toBe('<EMAIL>');
    });

    it('should get a user by ID', async () => {
      const response = await request(app.getHttpServer())
        .get(`/users/${userId}`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200);

      expect(response.body.id).toBe(userId);
      expect(response.body.email).toBe('<EMAIL>');
    });

    it('should update a user', async () => {
      const response = await request(app.getHttpServer())
        .put(`/users/${userId}`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .send({
          name: 'Updated E2E Test User',
        })
        .expect(200);

      expect(response.body.name).toBe('Updated E2E Test User');
    });

    it('should delete a user', async () => {
      await request(app.getHttpServer())
        .delete(`/users/${userId}`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200);

      // Verify the user is deleted
      await request(app.getHttpServer())
        .get(`/users/${userId}`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(404);
    });
  });
});
```

## Best Practices

When writing tests for the Social Commerce Platform, follow these best practices:

1. **Use the AAA pattern**: Arrange, Act, Assert
   - Arrange: Set up the test data and conditions
   - Act: Perform the action being tested
   - Assert: Verify the results

2. **Mock external dependencies**: Use Jest's mocking capabilities to isolate the code being tested

3. **Test both success and failure cases**: Ensure your tests cover both happy paths and error scenarios

4. **Use descriptive test names**: Make it clear what each test is verifying

5. **Keep tests independent**: Each test should be able to run independently of others

6. **Clean up after tests**: Restore any mocked functions and clean up test data

7. **Use test databases for integration tests**: Don't use the production database for testing

8. **Test edge cases**: Include tests for boundary conditions and edge cases

9. **Maintain test coverage**: Aim for at least 80% test coverage for new code

10. **Follow the testing pyramid**:
    - Many unit tests (fast, focused)
    - Fewer integration tests (test interactions between components)
    - Fewest E2E tests (slow, test complete workflows)

By following these patterns and best practices, AI assistants can ensure they write tests that adhere to the project's standards and effectively verify the functionality of the code.