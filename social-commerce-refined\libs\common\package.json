{"name": "@app/common", "version": "1.0.0", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@types/node": "^22.15.21", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "typescript": "^5.8.3"}}