#!/usr/bin/env node

const { program } = require('commander');
const chalk = require('chalk');
const path = require('path');
const fs = require('fs');
const { version } = require('./package.json');

// Import commands
const startCommand = require('./commands/start');
const stopCommand = require('./commands/stop');
const statusCommand = require('./commands/status');
const installCommand = require('./commands/install');
const buildCommand = require('./commands/build');

// Set up the CLI
program
  .name('dev')
  .description('Development CLI tool for Social Commerce Platform')
  .version(version);

// Register commands
startCommand(program);
stopCommand(program);
statusCommand(program);
installCommand(program);
buildCommand(program);

// Add help information
program.on('--help', () => {
  console.log('');
  console.log('Examples:');
  console.log('  $ dev start all         # Start all services');
  console.log('  $ dev start user        # Start only the user service');
  console.log('  $ dev status            # Check the status of all services');
  console.log('  $ dev install           # Install dependencies for all services');
  console.log('  $ dev build             # Build all services');
});

// Handle unknown commands
program.on('command:*', (operands) => {
  console.error(chalk.red(`Error: unknown command '${operands[0]}'`));
  const availableCommands = program.commands.map(cmd => cmd.name());
  console.error(chalk.yellow(`Available commands: ${availableCommands.join(', ')}`));
  process.exitCode = 1;
});

// Parse command line arguments
program.parse(process.argv);

// If no arguments, show help
if (!process.argv.slice(2).length) {
  program.outputHelp();
}
