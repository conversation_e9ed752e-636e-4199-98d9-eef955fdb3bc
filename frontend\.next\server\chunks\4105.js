"use strict";exports.id=4105,exports.ids=[4105],exports.modules={84105:(t,e,r)=>{r.d(e,{AP:()=>o,JG:()=>s,ZD:()=>u,_c:()=>p,cJ:()=>d});var a=r(86372);let i=a.g.injectEndpoints({endpoints:t=>({getGroupBuys:t.query({query:({page:t=1,limit:e=10,status:r})=>{let a=`/group-buys?page=${t}&limit=${e}`;return r&&(a+=`&status=${r}`),a},providesTags:t=>t?[...t.groupBuys.map(({id:t})=>({type:"GroupBuys",id:t})),{type:"GroupBuys",id:"LIST"}]:[{type:"GroupBuys",id:"LIST"}],transformResponse:t=>{if(!t){let t=[{id:"1",productId:"101",title:"Wireless Earbuds",imageUrl:"https://images.unsplash.com/photo-1572569511254-d8f925fe2cbb?ixlib=rb-4.0.3&auto=format&fit=crop&w=1400&q=80",price:49.99,minParticipants:10,maxParticipants:20,currentParticipants:7,expiresAt:new Date(Date.now()+2592e5).toISOString(),status:"active",createdAt:new Date(Date.now()-6048e5).toISOString(),updatedAt:new Date(Date.now()-6048e5).toISOString()},{id:"2",productId:"102",title:"Smart Watch",imageUrl:"https://images.unsplash.com/photo-1579586337278-3befd40fd17a?ixlib=rb-4.0.3&auto=format&fit=crop&w=1400&q=80",price:89.99,minParticipants:15,maxParticipants:30,currentParticipants:15,expiresAt:new Date(Date.now()+864e5).toISOString(),status:"active",createdAt:new Date(Date.now()-864e6).toISOString(),updatedAt:new Date(Date.now()-864e6).toISOString()},{id:"3",productId:"103",title:"Bluetooth Speaker",imageUrl:"https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?ixlib=rb-4.0.3&auto=format&fit=crop&w=1400&q=80",price:39.99,minParticipants:8,maxParticipants:15,currentParticipants:8,expiresAt:new Date(Date.now()-1728e5).toISOString(),status:"completed",createdAt:new Date(Date.now()-12096e5).toISOString(),updatedAt:new Date(Date.now()-1728e5).toISOString()},{id:"4",productId:"104",title:"Mechanical Keyboard",imageUrl:"https://images.unsplash.com/photo-1618384887929-16ec33fab9ef?ixlib=rb-4.0.3&auto=format&fit=crop&w=1400&q=80",price:79.99,minParticipants:12,maxParticipants:25,currentParticipants:5,expiresAt:new Date(Date.now()-864e5).toISOString(),status:"expired",createdAt:new Date(Date.now()-10368e5).toISOString(),updatedAt:new Date(Date.now()-864e5).toISOString()}];return{groupBuys:t,total:t.length}}return t}}),getGroupBuyById:t.query({query:t=>`/group-buys/${t}`,providesTags:(t,e,r)=>[{type:"GroupBuys",id:r}],transformResponse:(t,e,r)=>{if(!t){let t={id:r,productId:"101",title:"Wireless Earbuds Pro",description:"High-quality wireless earbuds with noise cancellation and long battery life. Perfect for music lovers and professionals on the go.",imageUrl:"https://images.unsplash.com/photo-1572569511254-d8f925fe2cbb?ixlib=rb-4.0.3&auto=format&fit=crop&w=1400&q=80",price:49.99,minParticipants:10,maxParticipants:20,currentParticipants:7,expiresAt:new Date(Date.now()+2592e5).toISOString(),status:"active",createdAt:new Date(Date.now()-6048e5).toISOString(),updatedAt:new Date(Date.now()-6048e5).toISOString()};return t}return t}}),getGroupBuyParticipants:t.query({query:({groupBuyId:t,page:e=1,limit:r=10})=>`/group-buys/${t}/participants?page=${e}&limit=${r}`,providesTags:(t,e,{groupBuyId:r})=>[{type:"GroupBuyParticipants",id:r}],transformResponse:t=>{if(!t){let t=Array.from({length:7}).map((t,e)=>({id:`participant-${e+1}`,userId:`user-${e+1}`,username:`user${e+1}`,displayName:`User ${e+1}`,profileImageUrl:e%2==0?`https://i.pravatar.cc/150?u=user${e+1}`:void 0,joinedAt:new Date(Date.now()-(7-e)*432e5).toISOString()}));return{participants:t,total:t.length}}return t}}),createGroupBuy:t.mutation({query:t=>({url:"/group-buys",method:"POST",body:t}),invalidatesTags:[{type:"GroupBuys",id:"LIST"}]}),updateGroupBuy:t.mutation({query:({id:t,data:e})=>({url:`/group-buys/${t}`,method:"PATCH",body:e}),invalidatesTags:(t,e,{id:r})=>[{type:"GroupBuys",id:r},{type:"GroupBuys",id:"LIST"}]}),joinGroupBuy:t.mutation({query:t=>({url:`/group-buys/${t}/join`,method:"POST"}),invalidatesTags:(t,e,r)=>[{type:"GroupBuys",id:r},{type:"GroupBuys",id:"LIST"},{type:"GroupBuyParticipants",id:r}]}),leaveGroupBuy:t.mutation({query:t=>({url:`/group-buys/${t}/leave`,method:"POST"}),invalidatesTags:(t,e,r)=>[{type:"GroupBuys",id:r},{type:"GroupBuys",id:"LIST"},{type:"GroupBuyParticipants",id:r}]})})}),{useGetGroupBuysQuery:o,useGetGroupBuyByIdQuery:s,useGetGroupBuyParticipantsQuery:p,useCreateGroupBuyMutation:u,useUpdateGroupBuyMutation:n,useJoinGroupBuyMutation:d,useLeaveGroupBuyMutation:c}=i}};