# Social Commerce Platform Scripts Documentation

This document provides a comprehensive guide to all the scripts available in the Social Commerce Platform project. Use this as a reference for development, testing, and deployment tasks.

## Table of Contents

- [Development CLI Tool](#development-cli-tool)
- [Backend Scripts](#backend-scripts)
- [Frontend Scripts](#frontend-scripts)
- [Docker Scripts](#docker-scripts)
- [Testing Scripts](#testing-scripts)
- [Deployment Scripts](#deployment-scripts)

## Development CLI Tool

The project includes a custom CLI tool to simplify common development tasks. See the [dev-cli README](./dev-cli/README.md) for detailed usage instructions.

```bash
# Install the CLI tool
cd dev-cli
npm install
npm link

# Start all services
dev start

# Stop all services
dev stop

# Check service status
dev status

# Install dependencies
dev install

# Build services
dev build
```

## Backend Scripts

These scripts are available in the `backend` directory.

### Main Scripts

| Script | Description | Usage |
|--------|-------------|-------|
| `npm run build` | Build all backend services | `cd backend && npm run build` |
| `npm run start` | Start the main gateway | `cd backend && npm run start` |
| `npm run start:dev` | Start the main gateway in development mode | `cd backend && npm run start:dev` |
| `npm run start:user` | Start the user service | `cd backend && npm run start:user` |
| `npm run start:user:dev` | Start the user service in development mode | `cd backend && npm run start:user:dev` |
| `npm run start:all` | Start all backend services | `cd backend && npm run start:all` |

### Testing Scripts

| Script | Description | Usage |
|--------|-------------|-------|
| `npm run test` | Run all tests | `cd backend && npm run test` |
| `npm run test:watch` | Run tests in watch mode | `cd backend && npm run test:watch` |
| `npm run test:cov` | Run tests with coverage | `cd backend && npm run test:cov` |
| `npm run test:e2e` | Run end-to-end tests | `cd backend && npm run test:e2e` |
| `npm run test:user-service` | Run user service tests | `cd backend && npm run test:user-service` |

### Utility Scripts

| Script | Description | Usage |
|--------|-------------|-------|
| `npm run lint` | Lint the codebase | `cd backend && npm run lint` |
| `npm run format` | Format the codebase | `cd backend && npm run format` |

## Frontend Scripts

These scripts are available in the `frontend` directory.

### Main Scripts

| Script | Description | Usage |
|--------|-------------|-------|
| `npm run dev` | Start the frontend in development mode | `cd frontend && npm run dev` |
| `npm run build` | Build the frontend for production | `cd frontend && npm run build` |
| `npm run start` | Start the frontend in production mode | `cd frontend && npm run start` |
| `npm run lint` | Lint the frontend codebase | `cd frontend && npm run lint` |

## Docker Scripts

These scripts use Docker Compose to manage the entire application stack.

| Script | Description | Usage |
|--------|-------------|-------|
| `docker-compose up` | Start all services | `docker-compose up` |
| `docker-compose up -d` | Start all services in detached mode | `docker-compose up -d` |
| `docker-compose down` | Stop all services | `docker-compose down` |
| `docker-compose ps` | List all running services | `docker-compose ps` |
| `docker-compose logs` | View logs from all services | `docker-compose logs` |
| `docker-compose logs -f` | Follow logs from all services | `docker-compose logs -f` |
| `docker-compose logs [service]` | View logs from a specific service | `docker-compose logs user-service` |

## Testing Scripts

These scripts are used for testing the application.

| Script | Description | Usage |
|--------|-------------|-------|
| `npm run test` | Run all tests | `npm run test` |
| `npm run test:e2e` | Run end-to-end tests | `npm run test:e2e` |
| `npm run test:coverage` | Run tests with coverage | `npm run test:coverage` |

## Deployment Scripts

These scripts are used for deploying the application.

| Script | Description | Usage |
|--------|-------------|-------|
| `npm run build:prod` | Build for production | `npm run build:prod` |
| `npm run deploy` | Deploy the application | `npm run deploy` |

## Troubleshooting

If you encounter any issues with these scripts, try the following:

1. Make sure you're in the correct directory when running the script
2. Check that all dependencies are installed
3. Check the logs for any error messages
4. Try running the script with the `--verbose` flag for more information
5. Use the `dev status` command to check the status of all services

For more detailed troubleshooting, refer to the [Troubleshooting Guide](./docs/troubleshooting.md).
